import { getEnv } from '../envUtils'

export function constructSafeguardWhereClauseAndSubstitutions({ withExpiry = false } = {}) {
  const constructExpiredWhere = (condition: string, ageExpiryMs: number) => {
    const dateExpiryMs = Date.now() - ageExpiryMs
    return `(${condition} AND (\${updated} < {${dateExpiryMs}}))`
  }
  const ms24Hours = 1000 * 60 * 60 * 24
  const construct24HourExpiredWhere = (condition: string) =>
    constructExpiredWhere(condition, ms24Hours)

  const constructUsernameWhere = (substitionName: string) => {
    const constructBeginWith = (usernameAttributeName: string) => {
      return `begins_with(\${${usernameAttributeName}}, @{${substitionName}})`
    }
    return constructBeginWith('createdByUsername')
  }

  const etoePrefix = 'etoe-'
  const e2eGeneralWhere = constructUsernameWhere('etoePrefix')
  const e2eGeneralExpiredWhere = construct24HourExpiredWhere(e2eGeneralWhere)
  const e2eGeneralWhereToUse = withExpiry ? e2eGeneralExpiredWhere : e2eGeneralWhere

  const e2eUniqueName = process.env.E2E_UNIQUE_NAME_SUBSTRING
  const etoeUniquePrefix = `${etoePrefix}-${e2eUniqueName}`
  const e2eUniqueNameWhere = constructUsernameWhere('etoeUniquePrefix')

  const notLikelyRealWorldUserIdPrefix = '0000'
  const etoeUserIdPrefix = `${notLikelyRealWorldUserIdPrefix}323073570`
  const e2eUserIdWhere = 'begins_with(${createdByID}, @{etoeUserIdPrefix})'

  const completeNameWhere = `(${e2eUniqueNameWhere} OR ${e2eGeneralWhereToUse})`
  const generatedAssetWhere = `(${e2eUserIdWhere} AND ${completeNameWhere})`

  const oldTestUsernamePrefix = 'tst-'
  const testUsernamePrefix = 'svc-icgautomation'
  const e2eTestUserWhere = `(${constructUsernameWhere(
    'oldTestUsernamePrefix'
  )} OR ${constructUsernameWhere('testUsernamePrefix')})`
  const e2eTestUserExpiredWhere = construct24HourExpiredWhere(e2eTestUserWhere)
  const e2eTestUserWhereToUse = withExpiry ? e2eTestUserExpiredWhere : e2eTestUserWhere

  const debugPrefix = 'DEBUG'
  const ignoreWhere = '(NOT begins_with(${friendlyID}, @{debugPrefix}))'

  // Don't want to delete manually created items
  const includeLocalUsername = getEnv() === 'local' && !withExpiry
  const localUsername = 'mmorales'
  const localAddition = includeLocalUsername ? ` OR ${constructUsernameWhere('localUsername')}` : ''

  const substitutions = {
    etoePrefix,
    etoeUniquePrefix,
    etoeUserIdPrefix,
    oldTestUsernamePrefix,
    testUsernamePrefix,
    debugPrefix,
    ...(includeLocalUsername ? { localUsername } : {}),
  }

  const fullWhere = `${ignoreWhere} AND (${generatedAssetWhere} OR ${e2eTestUserWhereToUse}${localAddition})`

  return {
    where: fullWhere,
    substitutions,
  }
}
