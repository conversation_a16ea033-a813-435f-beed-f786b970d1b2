import type { Request } from '@playwright/test'

export function getTimeToLive() {
  return undefined
  // return Math.ceil(Date.now() / 1000) + 15 * 60 // 15 minute TTL in seconds
}

export function injectTimeToLiveInTrpcRequest(req: Request): string {
  const requestJSON = req.postDataJSON()[0]
  if (!requestJSON) {
    throw new Error(`This tRPC request payload does not have the expected structure.`)
  }

  return JSON.stringify({
    0: {
      ...requestJSON,
      ttl: undefined, //getTimeToLive(),
    },
  })
}
