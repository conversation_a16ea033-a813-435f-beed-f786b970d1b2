import casual from 'casual'
import { E2EAssemblyUnit, E2EAsset, E2ECollection, E2EForm } from '@e2e/lib'
import { E2EDirection } from '@e2e/lib/E2EDirection'
import {
  AssemblyUnitGeneratorProps,
  AssetGeneratorProps,
  CollectionGeneratorProps,
  FormGeneratorProps,
  subjectData,
} from '@shared/data-generation/mock'

export enum DataControllerModel {
  ASSET,
  COLLECTION,
  ASSEMBLY_UNIT,
  DIRECTION,
  FORM,
}

export type E2EDataType = E2EAsset | E2ECollection | E2EAssemblyUnit | E2EForm
export type E2EDataModelProps =
  | AssetGeneratorProps
  | CollectionGeneratorProps
  | AssemblyUnitGeneratorProps
  | FormGeneratorProps

export class DataController {
  #assets: E2EAsset[] = []
  #collections: E2ECollection[] = []
  #assemblyUnits: E2EAssemblyUnit[] = []
  #directions: E2EAsset[] = []
  #forms: E2EForm[] = []
  readonly #validSubjectsForE2E = subjectData

  register(model: DataControllerModel, data: E2EDataType[]) {
    switch (model) {
      case DataControllerModel.ASSET:
        this.#assets.push(...(data as E2EAsset[]))
        break
      case DataControllerModel.COLLECTION:
        this.#collections.push(...(data as E2ECollection[]))
        break
      case DataControllerModel.ASSEMBLY_UNIT:
        this.#assemblyUnits.push(...(data as E2EAssemblyUnit[]))
        break
      case DataControllerModel.DIRECTION:
        this.#directions.push(...(data as E2EAsset[]))
        break
      case DataControllerModel.FORM:
        this.#forms.push(...(data as E2EForm[]))
        break
      default:
        throw new Error(`Model ${model} is not supported by the DataController register() function`)
    }
  }

  async generate(model: DataControllerModel, props: E2EDataModelProps) {
    switch (model) {
      case DataControllerModel.ASSET:
        const assets = await E2EAsset.generate(props)
        if (assets && assets.length > 0) {
          this.register(model, assets)
          return assets
        } else {
          throw new Error('Unable to generate assets')
        }
      case DataControllerModel.COLLECTION:
        const collections = await E2ECollection.generate(props)
        if (collections && collections.length > 0) {
          this.register(model, collections)
          return collections
        } else {
          throw new Error('Unable to generate collections')
        }
      case DataControllerModel.ASSEMBLY_UNIT:
        const assemblyUnits = await E2EAssemblyUnit.generate(props)
        if (assemblyUnits && assemblyUnits.length > 0) {
          this.register(model, assemblyUnits)
          return assemblyUnits
        } else {
          throw new Error('Unable to generate assembly units')
        }
      case DataControllerModel.DIRECTION:
        const directions = await E2EDirection.generate(props)
        if (directions && directions.length > 0) {
          this.register(model, directions)
          return directions
        } else {
          throw new Error('Unable to generate directions')
        }
      case DataControllerModel.FORM:
        const newForms = await E2EForm.generate(props)
        if (newForms && newForms.length > 0) {
          this.register(model, newForms)
          return newForms
        } else {
          throw new Error('Unable to generate forms')
        }
      default:
        throw new Error(`Model ${model} is not supported by the DataController generate() function`)
    }
  }

  get(model: DataControllerModel) {
    switch (model) {
      case DataControllerModel.ASSET:
        return this.#assets
      case DataControllerModel.COLLECTION:
        return this.#collections
      case DataControllerModel.ASSEMBLY_UNIT:
        return this.#assemblyUnits
      case DataControllerModel.DIRECTION:
        return this.#directions
      case DataControllerModel.FORM:
        return this.#forms
      default:
        throw new Error(`Model ${model} is not supported by the DataController get() function`)
    }
  }

  getAllAssetIDs() {
    return ([] as string[]).concat(
      [this.#assets, this.#directions].flatMap((arr) => arr.map(({ id }) => id)),
      [this.#collections, this.#assemblyUnits].flatMap((arr) =>
        arr.flatMap((event) => event.assetIDs.concat(event.directionIDs))
      )
    )
  }

  async delete(model: DataControllerModel) {
    switch (model) {
      case DataControllerModel.ASSET:
        if (this.#assets.length > 0) {
          await E2EAsset.delete(this.#assets.map((item) => item.id))
        }
        break
      case DataControllerModel.COLLECTION:
        if (this.#collections.length > 0) {
          await E2ECollection.delete(this.#collections.map((item) => item.id))
        }
        break
      case DataControllerModel.ASSEMBLY_UNIT:
        if (this.#assemblyUnits.length > 0) {
          await E2EAssemblyUnit.delete(this.#assemblyUnits.map((item) => item.id))
        }
        break
      case DataControllerModel.DIRECTION:
        if (this.#directions.length > 0) {
          await E2EAsset.delete(this.#directions.map((item) => item.id))
        }
        break
      case DataControllerModel.FORM:
        if (this.#forms.length > 0) {
          await E2EForm.delete(this.#forms.map((item) => item.id))
        }
        break
      default:
        throw new Error(`Model ${model} is not supported by the DataController delete() function`)
    }
  }

  getSubjectSelectorFromID(subjectID: string) {
    const subject = this.#validSubjectsForE2E[subjectID]
    if (!subject) {
      throw new Error(`Subject ID ${subjectID} is not valid for E2E`)
    }
    return subject.upperCaseName
  }

  getSubjectIdsForE2E() {
    return [...Object.keys(this.#validSubjectsForE2E)]
  }

  getRandomSubjectID() {
    return casual.random_element(this.getSubjectIdsForE2E())
  }
}
