/* eslint-disable playwright/no-wait-for-timeout */
import type { Page } from '@playwright/test'

/* 
    These are waitForTimeouts with names so we can indicate what they are for.
    While we want to avoid waitForTimeouts at all costs, it's not really possible 
    in some cases without an out of scope change of the codebase/feature.
    
    Naming these will help us in the future when we are ready to remove them
    after said out of scope change.
*/

const defaultTimeoutMs = 3000

export const waitForAssetStateBeforeSave = async (page: Page, timeout?: number) => {
  return page.waitForTimeout(timeout || defaultTimeoutMs)
}
