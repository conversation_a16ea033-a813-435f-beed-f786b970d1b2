import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager'
import { defaultProvider } from '@aws-sdk/credential-provider-node'

const client = new SecretsManagerClient({
  region: 'us-east-1',
  ...(process.env.IS_CICD_E2E === 'true' ? {} : getCredentialsObj()),
})

export async function getPassword(usernameOrEmail: string) {
  const usernameNumber = usernameOrEmail.match(/(?<number>\d{2})(?:@.*)?$/)?.groups?.number
  if (!usernameNumber) {
    throw new Error(
      `Username or email ${usernameOrEmail} is not valid for e2e, cannot obtain password`
    )
  }
  const secretName = `svc-user/icg-automation-${usernameNumber}`
  const response = await client.send(
    new GetSecretValueCommand({
      SecretId: secretName,
    })
  )
  const secret = response.SecretString
  if (!secret) {
    throw new Error(`Could not obtain password for ${usernameOrEmail}`)
  }
  return secret
}

function getCredentialsObj() {
  if (process.env.CI) {
    return {}
  } else {
    return {
      credentials: defaultProvider({ profile: 'cb-itemcloudgreen-nonprod-cli' }),
    }
  }
}
