import type { Locator } from '@playwright/test'

/**
 * Utility that let's you do an Object.assign on and using an object while also
 * first assigned it to a variable, without creating an additional differently
 * named variable in order to satisfy TypeScript. Just don't go reassigning its
 * extended methods to another object, since they may still use the first.
 *
 * @param obj The object to extend
 * @param extender A function that will be called with the object and should return an object that will be `Object.assign`ed to the original
 * @returns The original object with the additiona object assigned to it
 */
export const extendObject = <T extends object, U>(obj: T, extender: (arg: T) => U): T & U => {
  return Object.assign(obj, extender(obj))
}

// TODO - See if there's a way to simplify the visible return type to just look
// something like `ExtendedLocator<L & E>`, except it shows the actual values of L & E
// instead of the current potenntaly confusing `Omit<L & E, 'all' | 'filter'> & { all..., filter..., }`
/**
 * Function to prep a Locator so that its methods to get locators for subsets
 * of the original matching elements are also extended.
 *
 * Basically it just overrides each of these methods so that the returned locators
 * are extended just as this locator is intended to be.
 *
 * This is separated from `extendLocator` in order to make typescript happy/intellisense
 * work with the dynamic types.
 *
 * @param locator The locator to extend
 * @param extender The extender function which returns the additional properties to add to the locator
 * @returns A locator whose methods to get locators for subsets of the original matching elements are also extended
 */
const prepLocator = <L extends Locator, E>(locator: L, extender: (arg: Locator) => E) => {
  const allMethodsToOverride = ['all', 'filter', 'first', 'last', 'nth'] as const
  const originalAll = locator.all.bind(locator)
  const originalFilter = locator.filter.bind(locator)

  // Consolidates functionality for these methods that are meant to point to a specific element
  // Note for such single element locators, it's assumed that all these overridden
  // methods won't be needed again, since such methods are only meant if a Locator
  // could point to multiple. So I use extendObject instead of extendLocator
  const locatorReturningMethodsToOverride = ['first', 'last', 'nth'] as const
  type MethodTypes = (typeof locatorReturningMethodsToOverride)[number]
  const getOverridingMethod = <MethodType extends MethodTypes>(method: MethodType) => {
    const originalMethod = locator[method].bind(locator)
    type argsType = Parameters<L[MethodType]>
    // @ts-expect-error - If I do any of the specific method types, it's fine, but ts for
    // some reason doesn't like multiple in MethodType, even though all definitely tuples
    return (...args: argsType) => extendObject(originalMethod(...args), extender)
  }
  const singleElementOverridingMethods = locatorReturningMethodsToOverride.reduce((acc, method) => {
    return {
      ...acc,
      [method]: getOverridingMethod(method),
    }
  }, {}) as {
    [MethodType in MethodTypes]: ReturnType<typeof getOverridingMethod<MethodType>>
  }

  // Replace all original methods meant to get a locator(s) for subsets
  // of the original matching elements, so their return values would also be extended
  type AllMethodTypes = (typeof allMethodsToOverride)[number]
  type LocatorWithOmits = Omit<L, AllMethodTypes>
  const locatorWithOmits: LocatorWithOmits = locator
  const preppedExtendedLocator = Object.assign(locatorWithOmits, {
    all: async () => (await originalAll()).map((loc) => extendObject(loc, extender)),
    filter: (...args: Parameters<L['filter']>) => prepLocator(originalFilter(...args), extender),
    ...singleElementOverridingMethods,
  })

  return preppedExtendedLocator
}

/**
 * Specialized version of `extendObject` that is specifically for extending Locators
 *
 * For the locator itself, it works just like (in fact uses) `extendObject`, but
 * it also uses prepLocator to override its methods returning subsets of itself
 * so that the returned locators are also extended.
 *
 * @param locator Locator to extend
 * @param extender The extender function which returns the additional properties to add to the locator
 * @returns The extended locator
 */
export const extendLocator = <L extends Locator, E>(locator: L, extender: (arg: Locator) => E) => {
  const preppedLocator = prepLocator(locator, extender)
  return extendObject(preppedLocator, extender)
}
