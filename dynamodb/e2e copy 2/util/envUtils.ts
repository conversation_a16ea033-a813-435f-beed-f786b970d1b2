export function determineBaseUrlFromEnv(env: string = getEnv()) {
  if (typeof env === 'undefined') {
    throw new Error('Cannot determine base url without env passed in or ENV environment variable')
  }

  if (env === 'local') {
    return 'http://local.collegeboard.org:5173'
  } else {
    const envPrefix = ['prod', 'lowprod'].includes(env) ? '' : `${env}-`
    const extPrefix = getIsExternal() ? 'external-' : ''
    const subdomain = `${extPrefix}${envPrefix}hummingbird`

    return `https://${subdomain}.itemcloudgreen-${getEnvGroup(env)}.collegeboard.org`
  }
}

export function getEnv(): string {
  const env = process.env.ENV
  if (!env) {
    throw new Error(
      'No ENV set! Make sure to set the environment variable, whether by command line or .env file!'
    )
  }
  return env
}

export function getEnvGroup(env: string = getEnv()) {
  return env === 'prod' ? 'prod' : 'nonprod'
}

export function getIsExternal() {
  return process.env.IS_EXTERNAL?.toLocaleLowerCase() === 'true'
}
