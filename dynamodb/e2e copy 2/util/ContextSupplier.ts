import type { CBTestFixtures } from '@e2e/cb-test'
import { E2EA<PERSON>t, E2ECollection } from '@e2e/lib'
import test, { <PERSON><PERSON><PERSON>, BrowserContext, Page, Request, Route, expect } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import {
  EventNameLabel,
  EventNamePrefixConstants,
  EventNamePrefixList,
  Snapshot,
} from '@shared/event-sourcing'
import {
  DataController,
  DataControllerModel,
  getTimeToLive,
  injectTimeToLiveInTrpcRequest,
} from './dataUtils'
import { getEnv } from './envUtils'
import type { E2EUser } from './userUtils'

export class ContextSupplier {
  readonly browser: Browser
  dataController?: DataController

  suppliedContexts: BrowserContext[] = []

  constructor({ browser, dataController }: { browser: Browser; dataController?: DataController }) {
    this.browser = browser
    this.dataController = dataController
  }

  async supplyContext({
    options,
    user,
    dataController = this.dataController,
  }: {
    options?: Parameters<Browser['newContext']>[0]
    user: E2EUser
    /**
     * dataController only needs to be given if not provided when explicitly
     * creating a new DataController instance (or want to use a different one)
     */
    dataController?: DataController
  }) {
    const context = await this.browser.newContext(options)
    await this.setupContext({ context, user, dataController })
    this.suppliedContexts.push(context)
    return context
  }

  closeContexts() {
    const closePromise = Promise.all(this.suppliedContexts.map((context) => context.close()))
    this.suppliedContexts = []
    return closePromise
  }

  async setupContext({
    context,
    user,
    dataController = this.dataController,
  }: {
    context: BrowserContext
    user: E2EUser
    dataController?: DataController
  }) {
    if (!dataController) {
      throw new Error('Cannot set up the context without a dataController')
    }

    await this.setupContextForUser({ context, user })

    // Make it so specific setups run every time a new page is created
    const oldNewPage = context.newPage.bind(context)
    context.newPage = async function (...args: Parameters<BrowserContext['newPage']>) {
      const page = await oldNewPage(...args)
      for (const setup of setupsForNewPages) {
        await setup({ page, dataController, user })
      }
      return page
    }
  }

  async setupContextForUser({ context, user }: { context: BrowserContext; user: E2EUser }) {
    if (getEnv() === 'local') {
      // This will ensure the desired jwt (with user and role) will be used
      const jwtHandler: Parameters<
        typeof insertRequiredRouteHandlerToFallbackQueue
      >[0]['handler'] = async ({ request }) => {
        const jwt = user.getFakeLocalJwt()
        const headers = request.headers()
        if (headers.authorization) {
          headers.authorization = `Bearer ${jwt}`
        }
        return { headers }
      }
      const localRouteMatcher = /^http:\/\/local\.collegeboard\.org/

      await insertRequiredRouteHandlerToFallbackQueue({
        contextOrPage: context,
        routeMatcher: localRouteMatcher,
        handler: jwtHandler,
      })
      setupsForNewPages.push(({ page }) =>
        insertRequiredRouteHandlerToFallbackQueue({
          contextOrPage: page,
          routeMatcher: localRouteMatcher,
          handler: jwtHandler,
        })
      )
    }
  }
}

type RouteFallbackOptions = Parameters<Route['fallback']>[0]
/**
 * Used to create route handlers that MUST be run every time.
 *
 * Needed as it turns out `continue` literally makes the network call
 * actually run, not continue to the next handler. Handlers are added to
 * a stack where the last added is run first. This ensures that any desired
 * handlers stay at the top of the stack and `fallback` down the stack.
 */
async function insertRequiredRouteHandlerToFallbackQueue({
  contextOrPage,
  routeMatcher,
  handler,
}: {
  contextOrPage: BrowserContext | Page
  routeMatcher: Parameters<(typeof contextOrPage)['route']>[0]
  handler: ({ request }: { request: Request }) => Promise<RouteFallbackOptions>
}) {
  const fallbackHandler: Parameters<(typeof contextOrPage)['route']>[1] = async (
    route,
    request
  ) => {
    const fallbackOptions = await handler({ request })
    await route.fallback(fallbackOptions)
  }
  await contextOrPage.route(routeMatcher, fallbackHandler)

  const oldRouteMethod = contextOrPage.route.bind(contextOrPage)
  contextOrPage.route = async (...args: Parameters<Page['route']>) => {
    await oldRouteMethod(...args)
    // Ensure it is moved to the top of the route stack in order to ensure
    // that it is run and then fall back
    await contextOrPage.unroute(routeMatcher, fallbackHandler)
    await oldRouteMethod(routeMatcher, fallbackHandler)
  }
}

// With the addition of all these setups, this file could use a restructure, including
// potentially splitting up the file or adding a way to add setups another way
type CreateResponseJson = {
  result: {
    data: {
      failed: string[]
      success: string[]
    }
  }
}[]

const dataModelMap = {
  [EventNamePrefixConstants.Collection]: E2ECollection,
}

const dataControllerModelMap = {
  [EventNamePrefixConstants.AssemblyUnit]: DataControllerModel.ASSEMBLY_UNIT,
  [EventNamePrefixConstants.Collection]: DataControllerModel.COLLECTION,
  [EventNamePrefixConstants.Form]: DataControllerModel.FORM,
}

// TODO: rename Text to Test
const setupsForNewPages: ((
  fixtures: CBTestFixtures<'page' | 'dataController'> & { user: E2EUser }
) => Promise<void> | void)[] = [
  // The 'automatic...' functions are mostly copied from data.fixtures
  async function automaticRegisterUIEndpointAssetsCreateMany({ page, dataController }) {
    page.on('requestfinished', async (req) => {
      const query = req.url().split('/').pop()
      if (req.method() !== 'POST' || !query || !query.startsWith('assetsCreateMany?batch=1')) {
        return
      }

      const res = await req.response()
      if (!res?.ok()) {
        return // Or should it throw? Or at least give a warning?
      }

      const json = (await res.json()) as CreateResponseJson
      if (json[0]) {
        const { failed, success } = json[0].result.data
        if (failed.length !== 0) {
          console.error(
            `Assets creation through the UI failed for the following: ${failed.join(',\n')}`
          )
        }
        if (success.length !== 0) {
          console.info(
            `Automatically detected ${success.length} asset(s) including id ${success[0]} created through UI!`
          )
        } else {
          throw new Error('Asset creation through the UI failed!')
        }
        // we don't care about subjectID or friendlyId, we just need the id to delete the asset later
        const assets = success.map((id) =>
          E2EAsset.create({
            id,
            subjectID: SubjectIdentifier.PRECALCULUS,
            friendlyID: 'friendlyID',
          })
        )
        // May want to convert to use TestAsset instead
        dataController.register(DataControllerModel.ASSET, assets)
      }
    })
  },

  async function registerEventsCreatedThroughUI({ page, dataController }) {
    page.on('request', async (req) => {
      if (req.method() !== 'POST' || !req.url().endsWith('api/write')) {
        return
      }

      const snapshot = JSON.parse(req.postDataJSON().MessageBody) as Snapshot
      const { id, subjectID, name, type } = snapshot

      if (!type) {
        throw new Error('api/write type does not exist!')
      }
      if (!type.includes('Created')) {
        return
      }

      // get the eventNamePrefix
      const eventNamePrefix = EventNamePrefixList.find((prefix) => type.includes(prefix))

      if (!id || !subjectID || !name || !eventNamePrefix) {
        const objToLog = { id, subjectID, name, type, eventNamePrefix }
        console.error('Invalid snapshot info:')
        console.error(objToLog)
        throw new Error(`Couldn't register invalid snapshot creation`)
      }

      console.info(
        `Automatically detected ${EventNameLabel[eventNamePrefix]} with id ${id} created through UI!`
      )

      // verify that the mapping exists for the model and data controller before creating and registering the data
      if (!dataModelMap[eventNamePrefix] || !dataControllerModelMap[eventNamePrefix]) {
        return
      }

      const event = dataModelMap[eventNamePrefix]?.create({
        id,
        subjectID,
        name,
      }) as E2ECollection
      dataController.register(dataControllerModelMap[eventNamePrefix] as DataControllerModel, [
        event,
      ])
    })
  },

  // Mainly copied from data.fixtures
  async function automaticIntjectTimeToLive({ page }) {
    await insertRequiredRouteHandlerToFallbackQueue({
      contextOrPage: page,
      routeMatcher: `**/${getEnv() === 'local' ? 'sqs/queue/default' : 'api/write'}`,
      handler: async ({ request }) => {
        // adds encoded TTL to post data so that sqs can process
        const messageBody = JSON.parse(request.postDataJSON().MessageBody)
        const messageBodyWithTimeToLive = { ...messageBody, ttl: getTimeToLive() }
        const modifiedPostData = `Action=SendMessage&MessageBody=${encodeURIComponent(
          JSON.stringify(messageBodyWithTimeToLive)
        )}`
        return {
          postData: modifiedPostData,
        }
      },
    })

    const trpcRequestRoutes = ['**/*Create*?batch=1', '**/*Update*?batch=1']
    for await (const trpcRoute of trpcRequestRoutes) {
      await insertRequiredRouteHandlerToFallbackQueue({
        contextOrPage: page,
        routeMatcher: trpcRoute,
        handler: async ({ request }) => {
          return {
            postData: injectTimeToLiveInTrpcRequest(request),
          }
        },
      })
    }
  },

  async function addDataSafeguards({ page, dataController }) {
    if (test.info().file.includes('manual/manual.test')) {
      // The manual test file should give a warning
      return
    }

    // Going to a specific data entity's page
    page.on('framenavigated', async (frame) => {
      if (frame !== page.mainFrame()) {
        return
      }
      const url = new URL(page.url())
      if (!url.hostname.endsWith('.collegeboard.org')) {
        return
      }

      const idMatcher = /(?<id>[A-Z]{3}\d{6})/
      const urlMatcher = new RegExp(
        /\/(?<dataTypeFromUrl>\w+)s\//.source + idMatcher.source + /$/.source
      )
      type PossibleDataTypes = 'asset' | 'collection' | 'assembly-unit' | 'form'
      const dataTypeMapper = {
        asset: [DataControllerModel.ASSET, DataControllerModel.DIRECTION],
        collection: [DataControllerModel.COLLECTION],
        'assembly-unit': [DataControllerModel.ASSEMBLY_UNIT],
        form: [DataControllerModel.FORM],
      }
      const { dataTypeFromUrl, id } = url.pathname.match(urlMatcher)?.groups ?? {}

      if (!id) {
        return
      }
      const dataType = dataTypeFromUrl as PossibleDataTypes

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const getIds = () =>
        dataTypeMapper[dataType].flatMap((model) => dataController.get(model).map(({ id }) => id))

      // await expect
      //   .poll(getIds, {
      //     message: `${dataType} with ${id} was not created during this test run!`,
      //     timeout: 1_000,
      //   })
      //   .toContain(id)
    })

    // // Selecting assets in an asset list
    await insertRequiredRouteHandlerToFallbackQueue({
      contextOrPage: page,
      routeMatcher: '**/getAssetMetadata*',
      handler: async ({ request }) => {
        const input = request.url().split('input=').pop()
        if (!input) {
          return
        }
        // Skip the check if the URL contains 'forms'
        if (page.url().includes('forms')) {
          console.log(page.url())
          console.log('Skipping assetReadMany check for forms')
          return // Exit early
        }
        const parsedInput = JSON.parse(decodeURIComponent(input))
        const ids = parsedInput['0']?.['assetIds'] as string[] | undefined
        if (!ids) return

        const e2eIds = dataController.getAllAssetIDs()
        expect(e2eIds, {
          message: `At least some of the selected assets were not created during this test run!`,
        }).toEqual(expect.arrayContaining(ids))

        // Purposefully empty since the request shouldn't be changed at all,
        // the goal here is to catch the ids before it even completes so the test
        // hopefully doesn't have a chance to do any bulk actions
        return {}
      },
    })

    // // Auto-filtering asset lists by the assets created during this run
    await insertRequiredRouteHandlerToFallbackQueue({
      contextOrPage: page,
      routeMatcher: '**/assetsReadMany*',
      handler: async ({ request }) => {
        const url = new URL(request.url())

        // // Skip the check if the URL contains 'forms'
        // if (page.url().includes('forms')) {
        //   console.log(page.url())
        //   console.log('Skipping assetReadMany check for forms')
        //   return // Exit early
        // }

        const input = url.searchParams.get('input')
        if (!input) {
          console.warn('No input on assetsReadMany route')
          return // Shouldn't get here?
        }
        const parsedInput = JSON.parse(input)

        // Make it so only assets created during this run are included
        const e2eIds = dataController.getAllAssetIDs()
        if (parsedInput[0].ids?.length) {
          expect(e2eIds, {
            message: `At least some of the asset ids in this call were not created during this test run!`,
          }).toEqual(expect.arrayContaining(parsedInput[0].ids))
          return {}
        } else {
          parsedInput[0].ids = e2eIds
        }

        url.searchParams.set('input', JSON.stringify(parsedInput))
        return { url: url.toString() }
      },
    })
  },
]
