import jwt from 'jsonwebtoken'
import type { Split } from 'type-fest'
import { testUsers } from '@e2e/../bin/data/users'
import { flattenPermissions } from '@shared/auth/flatten-permissions'
import { Role, roles } from '@shared/rbac'
import { getPassword } from './secretsUtils'

const swapKeyValues = <ObjType extends { [k: string]: string }>(obj: ObjType) => {
  // Disabling 'any' issue  here since typescript will complain about something either way
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result = {} as any
  Object.entries(obj).forEach(([key, value]) => {
    result[value] = key
  })
  return result as { [key in keyof ObjType as ObjType[key]]: key }
}

const RoleMapReversed = swapKeyValues(roles.AzureRoleMapping)

const isNotUndeclaredRole = (role: roles.Role): role is roles.Role => {
  return role !== roles.UNDECLARED_ROLE
}

declare global {
  // Mostly taken from https://stackoverflow.com/questions/65335982/how-to-map-a-typescript-tuple-into-a-new-tuple-using-array-map
  // Had to figure out adding declare global, and the switch to ReadonlyArray since
  // using as const vs specifying the tuple type explicitly
  // This basically forces typescript to realize the mapped array has the same indexes
  interface ReadonlyArray<T> {
    // T[] | [T] enforces a tuple type.
    // {[K in keyof this]: U} keeps a mapped tuple type.
    map<U>(
      callbackfn: (value: T, index: number, tuple: T[] | [T]) => U,
      thisArg?: unknown
    ): { [K in keyof this]: U }
  }
}

// Note that the test users in the seed data is a related place to check when changing
const basicUsers = [
  {
    email: '<EMAIL>',
    roles: [roles.Role.ADMIN, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.CONTENT_CREATOR, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.CONTENT_EDITOR, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.INTERNAL_REVIEWER, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.EXTERNAL_REVIEWER_L1, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.EXTERNAL_REVIEWER_L2, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.FINALIZATION_EDITOR, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [], // I believe has no role, goal is no access, need to verify how it's done and how to use here
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.OPERATIONS_MANAGER, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.CONTENT_MANAGER, roles.Role.TESTER],
  },
  {
    email: '<EMAIL>',
    roles: [roles.Role.FORM_PREVIEWER, roles.Role.CONTENT_EDITOR, roles.Role.TESTER],
  },
  // Currently unused but created, just need to give role and add to db if still needed
  // {
  //   email: '<EMAIL>',
  //   roles: [roles.Role._],
  // },
] as const

const completeUsers = basicUsers.map((basicUserInfo) => {
  // Calling it username here, but the full email is often used as a username
  type Username = Split<(typeof basicUserInfo)['email'], '@'>[0]
  const username = basicUserInfo.email.split('@')[0] as Username

  return {
    ...basicUserInfo,
    permissions: Array.from(
      new Set(
        // Once we upgrade to at least TypeScript 5.2, we should be able to remove
        // 'as unknown as Role[]', according to https://github.com/microsoft/TypeScript/issues/44373#issuecomment-1753481640
        // Tried a more generic example in their playground, and it does seem like it might work
        (basicUserInfo.roles as unknown as Role[]).reduce<ReturnType<typeof flattenPermissions>>(
          (perms, role) => [...perms, ...flattenPermissions(roles.Permissions[role])],
          []
        )
      )
    ),
    getPassword: () => getPassword(basicUserInfo.email),
    storageState: `.auth/${username}.json`,
    getFakeLocalJwt: () => getFakeLocalJwt(basicUserInfo),
    getTestUserData: () => {
      const testUserData = testUsers.find((user) => user.emailAddress === basicUserInfo.email)
      if (!testUserData) {
        throw new Error(`Need to add test user data for ${basicUserInfo.email}`)
      }
      return testUserData
    },
  }
})

export function getUsers() {
  return completeUsers
}

export const getMainUser = () => completeUsers[0]

export type E2EUser = (typeof completeUsers)[number]

function getFakeLocalJwt(user: (typeof basicUsers)[number]) {
  const fakeMorales =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zJZp5zdynOd0My7qayrCr4hIJ_Si6NhOp0BWDgEABP0'
  const decoded = jwt.decode(fakeMorales)
  if (!decoded || typeof decoded === 'string') {
    throw new Error('Fake Morales local jwt got messed up somehow')
  }
  const userModified = {
    ...decoded,
    preferred_username: user.email,
    upn: user.email,
    // Repeat of above, Once we upgrade to at least TypeScript 5.2, we may be able to remove
    // 'as unknown as Role[]', according to https://github.com/microsoft/TypeScript/issues/44373#issuecomment-1753481640
    // Tried a more generic example in their playground, and it does seem like it might work
    roles: (user.roles as unknown as Role[])
      .filter(isNotUndeclaredRole)
      .map((userRole) => RoleMapReversed[userRole]),
  }
  const encoded = jwt.sign(userModified, 'your-256-bit-secret')
  return encoded
}
