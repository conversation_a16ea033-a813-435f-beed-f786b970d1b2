import { expect } from '@playwright/test'

/**
 * Convenient utility method that allows waiting for a specific condition before proceeding
 *
 * This is particularly useful within page objects code where expect/assertions generally
 * shouldn't be used. While this uses expect in its implementation, that's not the
 * main goal nor needs to be known by the caller.
 *
 * @param conditionFunction Function to run that should return a truthy value if the condition is met
 * @param messageOrOptions Message or timing options. See [expect.poll](https://playwright.dev/docs/test-assertions#expectpoll) for details
 */
export async function waitForCondition<ConditionReturnType>(
  conditionFunction: () => ConditionReturnType,
  messageOrOptions?: Parameters<(typeof expect)['poll']>[1]
) {
  let resultOfCondition: Awaited<ConditionReturnType>

  // expect generally shouldn't be used within page objects, except if
  // needed for waiting for a condition, so abstracted this out
  await expect
    .poll(async () => (resultOfCondition = await conditionFunction()), messageOrOptions)
    .toBeTruthy()

  // @ts-expect-error Doesn't think it's assigned, but it is by expect.poll
  return resultOfCondition
}
