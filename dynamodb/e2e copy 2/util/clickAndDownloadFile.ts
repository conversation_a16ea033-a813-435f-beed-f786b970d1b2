import type { Locator, <PERSON> } from '@playwright/test'
import { expect } from '@e2e/cb-test'
import pdfParse from 'pdf-parse'
import fs from 'fs'

export async function clickAndDownloadFile(
  page: Page,
  locator: Locator,
  expectedFileName: string
): Promise<string> {
  const [download] = await Promise.all([page.waitForEvent('download'), locator.click()])
  const suggestedFileName = download.suggestedFilename()
  expect(suggestedFileName).toContain(expectedFileName)
  const downloadPath = `./playwright/downloads/${suggestedFileName}`
  console.log(`Downloaded file path: ${downloadPath}`)
  await download.saveAs(downloadPath)
  return downloadPath
}

export async function verifyPdfContent(
  downloadPath: string,
  expectedContent: string
): Promise<void> {
  const dataBuffer = fs.readFileSync(downloadPath)
  const data = await pdfParse(dataBuffer)

  // Verify PDF content using expect
  expect(data.text).toContain(expectedContent)
}
