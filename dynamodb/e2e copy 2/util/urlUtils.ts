export const formatSubjectForUrl = (subject: string) => subject?.toLowerCase()?.replace(/ /g, '-')

export const endpoints = {
  /** Use this one with BASE_URL or it'll pick up Azure */
  AUTH: 'auth',
  ASSETS_READ_MANY: 'assetsReadMany',
  EVENTS_READ_ONE: 'eventsReadOne',
  EVENTS_READ_ONE_FORM: 'eventsReadOneForm',
  EVENTS_READ_MANY: 'eventsReadMany',
  ASSETS_READ_MANY_BY_IDS: 'assetsReadManyByIds',
  ASSET_CREATE_MANY: 'assetsCreateMany',
  ITEM_SET_UPDATE_ONE: 'itemSetUpdateOne',
  ITEM_SET_READ_ONE: 'itemSetReadOne',
  EVENT_READ_MANY_ITEMS_IN_FORMS: 'eventsReadManyItemsInForm',
  EVENT_WRITE: 'queue',
  ASSET_SEARCH_BY_ID: 'assetsSearchById',
  COMMENTS_READ_GENERAL: 'commentsReadGeneral',
  ATTACHMENTS_READ: 'attachmentsRead',
  ATTACHMENTS_ADD: 'attachmentsAdd',
  ATTACHMENTS_STAGE: 'attachmentsStage',
  ATTACHMENTS_REMOVE: 'attachmentsRemove',
  ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE: 'assemblyUnitsReadByAssemblyUnitType',
  ASSEMBLY_UNIT_READ_BY_ID: 'assemblyUnitsReadById',
  ASSEMBLY_UNIT_READ_ALL_DESCENDANTS_BY_ID: 'assemblyUnitsReadAllDescendantsById',
  ASSEMBLY_UNIT_ADD: 'assemblyUnitsAdd',
  ASSEMBLY_UNIT_RENAME: 'assemblyUnitsRename',
  GET_ASSET_METADATA: 'getAssetMetadata',
  ASSEMBLY_UNIT_ADD_ASSETS: 'assemblyUnitsAddAssets',
  ASSEMBLY_UNIT_ADD_ASSETS_FROM_COLLECTION: 'assemblyUnitsAddAssetsFromCollection',
  ASSEMBLY_UNIT_UNDO_ADD_ASSETS_FROM_COLLECTION: 'assemblyUnitsUndoAddAssetsFromCollection',
  ASSEMBLY_UNIT_ARCHIVE: 'assemblyUnitsArchive',
  ASSEMBLY_UNITS_UPDATE_ATTRIBUTES: 'assemblyUnitsUpdateAttributes',
  ASSETS_TRANSITION_WORKFLOW_MANY: 'assetsTransitionWorkflowMany',
  COLLECTIONS_ADD: 'collectionsAdd',
  COLLECTIONS_READ_ONE: 'collectionsReadOne',
  COLLECTIONS_ADD_ASSETS: 'collectionsAddAssets',
  COLLECTIONS_REORDER_ASSETS: 'collectionsReorderAssets',
  COLLECTIONS_RENAME: 'collectionsRename',
  SEND_TEST_MANIFEST_TO_VAULT: 'sendTestManifestToVault',
  BLUEBOOK_RESPONSE: 'https://bluebook.app.collegeboard.org/',
  METADATA_READ_ONE: 'metadataReadOne',
  SEND_TO_VAULT: 'sendToVault',
  ITEM_SET_VERSION: 'itemSetVersionsVerbose',
  COMMENTS_ADD: 'commentsAdd',
  ASSETS_READ_ONE: 'assetsReadOne',
  FORMS_READ: 'formsRead',
  FORMS_READ_ONE: 'formsReadOne',
  FORMS_UPDATE_FORM_CODES: 'formsUpdateFormCode',
  FORMS_ADD_ASSEMBLY_UNITS: 'formsAddAssemblyUnits',
  FORMS_REMOVE_ASSEMBLY_UNITS: 'formsRemoveAssemblyUnits',
  USERS_READ_MANY: 'usersReadMany',
  EVALUATION_METRICS_READ_ONE: 'evaluationMetricsReadOne',
  METADATA_READ_VERSIONS_ALL: 'metadataReadVersionsAll',
  READ_EVALUATION_METRICS_CALCULATIONS: 'readEvaluationMetricsCalculations',
}

/** Useful for passing as just a string to the builtin page.waitForResponse() */
export const endpointGlobs = Object.fromEntries(
  Object.entries(endpoints).map(([key, value]) => {
    return [key, value.startsWith('http') ? `${value}*` : `**/${value}*`]
  })
) as typeof endpoints
