import { Locator, <PERSON> } from '@playwright/test'

/**
 * @param page The Page instance
 * @param locatorToDrag Locator of element to be dragged
 * @param locatorToDragTo Locator of where to drag the element
 */
export async function dragAndDrop(page: Page, originElement: Locator, destinationElement: Locator) {
  await originElement.hover()
  await page.mouse.down()

  const box = (await destinationElement.boundingBox())!
  await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2)

  await destinationElement.hover()
  await page.mouse.up()
}
