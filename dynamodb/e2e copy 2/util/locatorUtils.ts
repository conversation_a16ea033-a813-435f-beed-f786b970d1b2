import { Locator, expect } from '@playwright/test'

const flooredHalf = (num: number) => Math.floor(num / 2)

const calculateRoundedMidpoint = ({
  x,
  y,
  width,
  height,
}: NonNullable<Awaited<ReturnType<Locator['boundingBox']>>>) => {
  return {
    x: x + flooredHalf(width),
    y: y + flooredHalf(height),
  }
}

// Seems like playwright's build in drag functionality is not working well, at least
// in some cases. This is a custom drag function that should work well in most cases.
// Not intended to be perfect for every scenario, can always be improved.
// For instance, first pass assumes it's okay to click the middle of the element to
// drag it and it keep the drop within the bounds of the destination no matter what.
export const dragElement = async (
  {
    locator,
    destination,
  }: {
    locator: Locator
    destination: Locator | { x: number; y: number }
  },
  // position aligns the top left corner or middle of the locator with the destination
  options?: { position?: 'topleft' | 'middle' }
) => {
  const page = locator.page()
  // Hover needs to be done before getting the boundingBox, so that if it scrolls
  // to it automatically, the boundingBox will then still be accurate
  await locator.hover()

  // If it's visible, then bounding box should be available
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const locatorBB = (await locator.boundingBox())!
  let x, y
  if ('x' in destination && 'y' in destination) {
    ;({ x, y } = destination)
  } else {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const destinationBB = (await destination.boundingBox())!
    ;({ x, y } =
      options?.position === 'topleft' ? destinationBB : calculateRoundedMidpoint(destinationBB))
  }
  if (options?.position === 'topleft') {
    x += flooredHalf(locatorBB.width)
    y += flooredHalf(locatorBB.height)
  }

  await page.mouse.down()
  // Normally, wait for timeouts aren't great, but this seems to need to give
  // the UI just a bit of buffer time to recognize the down event
  // eslint-disable-next-line playwright/no-wait-for-timeout
  await page.waitForTimeout(1000)
  await page.mouse.move(x, y, { steps: 25 })
  await page.mouse.up()
  // Also give a second to recognize the up event before moving on
  // eslint-disable-next-line playwright/no-wait-for-timeout
  await page.waitForTimeout(1000)
}

/**
 * Ensures the locator is within the viewport by making the viewport bigger
 * if it wouldn't fit and then scrolling to it if needed so it's inside it
 *
 * Allows passing in extraCushionHeight, which is an unideal solution to allow
 * specifying extra height to account for from other elements and such on the page
 *
 * @param locator - The locator to ensure is within the viewport
 */
export const ensureWithinViewport = async (locator: Locator, { extraCushionHeight = 0 } = {}) => {
  const page = locator.page()
  await expect(locator).toBeVisible()
  const boundingBox = await locator.boundingBox()
  const viewportSize = page.viewportSize()
  if (!boundingBox || !viewportSize) {
    throw new Error('Should not get here due to expect toBeVisible, making TS happy')
  }
  const { width, height } = boundingBox
  const cushionedHeight = height + extraCushionHeight
  const { width: viewportWidth, height: viewportHeight } = viewportSize

  if (cushionedHeight > viewportHeight || width > viewportWidth) {
    console.log(
      `Width:height of ${width}:${cushionedHeight} does not fit in viewport's ${viewportWidth}:${viewportHeight}, so expanding viewport to fit locator`
    )
    await page.setViewportSize({
      width: Math.max(width, viewportWidth),
      height: Math.max(cushionedHeight, viewportHeight),
    })
  }

  await locator.scrollIntoViewIfNeeded()
}
