import { selectors } from '@playwright/test'

export async function registerCustomSelectorEngines() {
  await selectors.register(
    'cell',
    () => ({
      root: document.body,

      query(root: HTMLElement, columnName: string) {
        this.root = root
        return this.root.querySelector(this.getCellSelector(columnName))
      },

      queryAll(root: HTMLElement, columnName: string) {
        this.root = root
        return this.root.querySelectorAll(this.getCellSelector(columnName))
      },

      getCellSelector(columnName: string) {
        console.log(columnName)
        const columnIndex = this.getColumnIndexByName(columnName)
        return `tbody tr > td:nth-child(${columnIndex + 1})`
      },

      getColumnIndexByName(name: string) {
        // Allow exact match first, in case one column contains the text of the other
        // but comes first
        const columnNames = this.getColumnNames()
        let index = columnNames.indexOf(name)
        if (index === -1) {
          index = columnNames.findIndex((nameAtIndex) => nameAtIndex.includes(name))
        }
        return index
      },

      getColumnNames() {
        const table = this.root.closest('table')
        if (!table) {
          throw new Error('cell selector only usable somewhere within a table element')
        }
        const columnNames = Array.from(table.querySelectorAll('thead th, thead td')).map(
          (th) => th.textContent?.replace(/\s*minus/, '') || ''
        )
        return columnNames
      },
    }),
    { contentScript: true }
  )
}
