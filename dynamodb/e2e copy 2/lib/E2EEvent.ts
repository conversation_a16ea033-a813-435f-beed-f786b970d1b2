import type { Paged } from 'dynamodb-onetable'
import { courseIDsE2E } from '@e2e/global.setup'
import { SubjectIdentifier, SubjectName } from '@shared/config'
import { tables } from '@shared/dynamo-hummingbird'
import type { AssemblyUnitModel } from '@shared/dynamo-hummingbird/tables/split-models'
import type { CollectionModel } from '@shared/dynamo-hummingbird/tables/split-models/collections'
import type { EventType, Snapshot } from '@shared/event-sourcing'

interface GetEventQueryE2EParams {
  eventType: EventType
  usernames: string[]
  modelVersion: number
  subjectID?: string
  next?: object
}

interface E2EEventType {
  id: string
  name: string
  subject: string
  subjectID: SubjectIdentifier
  assetIDs?: string[]
  directionIDs?: string[]
}

const getEventFindQueryE2E = async ({
  eventType,
  subjectID,
  usernames,
  modelVersion,
  next,
}: GetEventQueryE2EParams) => {
  const model = tables.hummingbird.getModel(eventType)
  return model.find(
    // @ts-expect-error: the union type of eventTypes causes confusion in the params so this should silence the error
    { subjectID, modelVersion },
    {
      where: '${creatorID} IN (@{...usernames})',
      substitutions: { usernames },
      limit: 100,
      index: 'subjectIDGSI',
      next,
    }
  )
}

const getEventScanQueryE2E = async ({
  eventType,
  usernames,
  modelVersion,
  next,
}: GetEventQueryE2EParams) => {
  const model = tables.hummingbird.getModel(eventType)
  return model.scan(
    {},
    {
      where:
        '${modelVersion} = @{modelVersion} AND (${creatorID} IN (@{...usernames}) OR ${userID} IN (@{...usernames}))',
      substitutions: {
        usernames,
        modelVersion,
      },
      limit: 100,
      next,
    }
  )
}

export const getEventData = async ({
  eventType,
  usernames,
  modelVersion,
}: GetEventQueryE2EParams) => {
  const data: Event[] = []
  const findableEvents = {
    CollectionSnapshot: true,
    FormSnapshot: true,
  }
  if (eventType in findableEvents) {
    for (const subjectID of courseIDsE2E) {
      let results: Paged<Event> = []
      do {
        // @ts-expect-error: the union type of eventTypes causes confusion in the params so this should silence the error
        results = await getEventFindQueryE2E({
          eventType,
          subjectID,
          usernames,
          modelVersion,
          next: results?.next,
        })
        data.push(...results)
      } while (results.next)
    }
  } else {
    let results: Paged<Event> = []
    do {
      // @ts-expect-error: the union type of eventTypes causes confusion in the params so this should silence the error
      results = await getEventScanQueryE2E({
        eventType,
        usernames,
        modelVersion,
        next: results?.next,
      })
      data.push(...results)
    } while (results.next)
  }
  return data
}

export class E2EEvent {
  readonly id: E2EEventType['id']
  readonly name: E2EEventType['name']
  readonly subject: E2EEventType['subject']
  readonly subjectID: E2EEventType['subjectID']
  readonly assetIDs: NonNullable<E2EEventType['assetIDs']>
  readonly directionIDs: NonNullable<E2EEventType['directionIDs']>

  constructor({ id, name, subject, subjectID, assetIDs = [], directionIDs = [] }: E2EEventType) {
    this.id = id
    this.name = name
    this.subject = subject
    this.subjectID = subjectID
    this.assetIDs = assetIDs
    this.directionIDs = directionIDs
  }

  static create(
    data?:
      | (Snapshot & { courseId?: string })
      | (AssemblyUnitModel & { subjectID?: string })
      | (CollectionModel & { subjectID?: string }),
    assetIDs?: string[],
    directionIDs?: string[]
  ) {
    if (!data) {
      throw new Error('Full Event Snapshot not defined, cannot convert to E2EEvent')
    }

    const { id, subjectID, name, courseId } = data

    if (!id || !(subjectID || courseId) || !name) {
      throw new Error('Full Event Snapshot missing field(s), cannot convert to E2EEvent')
    }

    const _courseId = (subjectID || courseId) as SubjectIdentifier

    const subject = SubjectName[_courseId]
    if (!subject) {
      throw new Error(`Invalid Data: Full Event Snapshot subjectID ${_courseId} is invalid`)
    }

    return new E2EEvent({
      id,
      subject,
      name,
      subjectID: _courseId,
      assetIDs,
      directionIDs,
    })
  }
}
