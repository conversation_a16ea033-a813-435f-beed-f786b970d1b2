import { FriendlyId, tables } from '@shared/dynamo-hummingbird'
import chunk from '@shared/utils/chunk'

export interface E2EFriendlyIDType {
  pk: string
  sk: number
  id: string
}

export class E2EFriendlyID {
  readonly pk: E2EFriendlyIDType['pk']
  readonly sk: E2EFriendlyIDType['sk']
  readonly id: E2EFriendlyIDType['id']

  constructor({ pk, sk, id }: E2EFriendlyIDType) {
    this.pk = pk
    this.sk = sk
    this.id = id
  }

  static async deleteFriendlyIDs(friendlyIDs: FriendlyId[]) {
    const FriendlyIDModel = tables.friendlyId.getModel('FriendlyId')
    const batched: Record<string, boolean> = {}
    const filteredFriendlyIDs = friendlyIDs
      .map((friendlyID) => {
        const batchedKey = `${friendlyID.pk}#${friendlyID.sk}`
        if (!batched[batchedKey]) {
          batched[batchedKey] = true
          return friendlyID
        }
        return null
      })
      .filter((friendlyID) => friendlyID) as FriendlyId[]
    const chunked = chunk(filteredFriendlyIDs, 20)
    await Promise.all(
      chunked.map(async (chunk, chunkIndex) => {
        const batch = {}
        await Promise.all(
          chunk.map((friendlyID) =>
            FriendlyIDModel.remove({ pk: friendlyID.pk, sk: friendlyID.sk }, { batch })
          )
        )
        // batch the remove request
        try {
          return tables.friendlyId.batchWrite(batch)
        } catch (e) {
          console.error(`E2EFriendlyID chunk index: ${chunkIndex}`, e)
          return Promise.resolve(null)
        }
      })
    )
  }

  static async deleteFriendlyIDsByIDs(ids: string[]) {
    try {
      const FriendlyIDModel = tables.friendlyId.getModel('FriendlyId')
      // find all the friendly IDs using the first 3 letter in IDs
      const findQueries = [...new Set(ids)].map((id) =>
        FriendlyIDModel.find({ pk: id.slice(0, 3) })
      )
      // run all the queries in parallel
      const results = await Promise.allSettled(findQueries)
      const friendlyIDs: FriendlyId[] = []
      let failedResults = 0
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          friendlyIDs.push(...result.value)
        } else {
          failedResults++
        }
      })
      if (failedResults) {
        console.log(`Failed to delete ${failedResults} E2E friendly IDs`)
      }
      this.deleteFriendlyIDs(friendlyIDs)
    } catch (e) {
      console.error('Unable to delete E2E friendly IDs', e)
    }
  }
}
