import { AssetComment, tables } from '@shared/dynamo-hummingbird'
import chunk from '@shared/utils/chunk'

export interface E2EAssetCommentType {
  pk: string
  sk: number
  itemId: string
}

export class E2EAssetComment {
  readonly pk: E2EAssetCommentType['pk']
  readonly sk: E2EAssetCommentType['sk']
  readonly itemId: E2EAssetCommentType['itemId']

  constructor({ pk, sk, itemId }: E2EAssetCommentType) {
    this.pk = pk
    this.sk = sk
    this.itemId = itemId
  }

  static async deleteAssetComments(assetComments: AssetComment[]) {
    const AssetCommentModel = tables.itemCloudGreen.getModel('ItemSetComment')
    const batched: Record<string, boolean> = {}
    const filteredAssetComments = assetComments
      .map((asset) => {
        const batchedKey = `${asset.hk}#${asset.sk}`
        if (!batched[batchedKey]) {
          batched[batchedKey] = true
          return asset
        }
        return null
      })
      .filter((asset) => asset) as AssetComment[]
    const chunked = chunk(filteredAssetComments, 20)
    await Promise.all(
      chunked.map(async (chunk, chunkIndex) => {
        const batch = {}
        await Promise.all(
          chunk.map((itemSetComment) =>
            AssetCommentModel.remove({ hk: itemSetComment.hk, sk: itemSetComment.sk }, { batch })
          )
        )
        // batch the remove request
        try {
          return tables.itemCloudGreen.batchWrite(batch)
        } catch (e) {
          console.error(`E2EAssetComment chunk index: ${chunkIndex}`, e)
          return Promise.resolve(null)
        }
      })
    )
    return filteredAssetComments
  }

  static async deleteAssetCommentsByIDs(ids: string[]) {
    try {
      const AssetCommentModel = tables.itemCloudGreen.getModel('ItemSetComment')
      // find all the friendly IDs using the first 3 letter in IDs
      const findQueries = [...new Set(ids)].map((id) =>
        AssetCommentModel.find({ hk: `ItemSet#${id}` })
      )
      // run all the queries in parallel
      const results = await Promise.allSettled(findQueries)
      const assetComments: AssetComment[] = []
      let failedResults = 0
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          assetComments.push(...result.value)
        } else {
          failedResults++
        }
      })
      if (failedResults) {
        console.log(`Failed to delete ${failedResults} E2E asset comments`)
      }
      this.deleteAssetComments(assetComments)
    } catch (e) {
      console.error('Unable to delete E2E friendly IDs')
    }
  }
}
