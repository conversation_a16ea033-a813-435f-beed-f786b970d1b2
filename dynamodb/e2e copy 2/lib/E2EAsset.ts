import type { Paged } from 'dynamodb-onetable'
import { courseIDsE2E } from '@e2e/global.setup'
import { getTimeToLive } from '@e2e/util/dataUtils'
import { SubjectIdentifier, SubjectName } from '@shared/config'
import { AssetGeneratorProps, assetGenerator } from '@shared/data-generation/mock'
import { Asset, User, tables } from '@shared/dynamo-hummingbird'
import chunk from '@shared/utils/chunk'
import { ASSET_MODEL_VERSION } from '@shared/utils/constants/constants'
import { E2EAssetComment } from './E2EAssetComment'
import { E2EFriendlyID } from './E2EFriendlyID'
import { getMainUser } from '@e2e/util/userUtils'

interface GetAssetFindQueryE2EParams {
  subjectID: string
  usernames: string[]
  modelVersion: number
  next?: object
}

const getAssetFindQueryE2E = async ({
  subjectID,
  usernames,
  modelVersion,
  next,
}: GetAssetFindQueryE2EParams) => {
  const AssetModel = tables.itemCloudGreen.getModel('Asset')
  return AssetModel.find(
    { subjectID, modelVersion },
    {
      where: '${createdByUsername} IN (@{...usernames})',
      substitutions: { usernames },
      limit: 100,
      index: 'subjectIDGSI',
      next,
    }
  )
}

export interface E2EAssetType {
  id: string
  displayName: string
  subject: SubjectIdentifier
  username?: string
}

export class E2EAsset {
  readonly id: E2EAssetType['id']
  readonly displayName: E2EAssetType['displayName']
  readonly subject: E2EAssetType['subject']
  readonly username: E2EAssetType['username']

  constructor({ id, displayName, subject, username }: E2EAssetType) {
    this.id = id
    this.displayName = displayName
    this.subject = subject
    this.username = username
  }

  static create(data?: Asset) {
    if (!data) {
      throw new Error('Full Asset not defined, cannot convert to E2EAsset')
    }

    const { id, subjectID, friendlyID, createdByUsername } = data

    if (!id || !subjectID || !friendlyID) {
      throw new Error('Full Asset missing field(s), cannot convert to E2EAsset')
    }

    const subject = SubjectName[subjectID]
    if (!subject) {
      throw new Error(`Invalid Data: Full Asset subjectID ${subjectID} is invalid`)
    }

    return new E2EAsset({
      id,
      subject,
      username: createdByUsername,
      displayName: friendlyID,
    })
  }

  static async generate(props: AssetGeneratorProps) {
    try {
      const user = props.user || getMainUser().getTestUserData()
      const { data } = await assetGenerator({ ...props, user, ttl: getTimeToLive() })
      return data.map((item) => this.create(item as Asset))
    } catch (e) {
      console.info('Unable to generate an E2E Asset', e)
      return null
    }
  }

  static async deleteAssets(assets: Asset[]) {
    const AssetModel = tables.itemCloudGreen.getModel('Asset')
    const batched: Record<string, boolean> = {}
    const filteredAssets = assets
      .map((asset) => {
        const batchedKey = `${asset.hk}#${asset.sk}`
        if (!batched[batchedKey]) {
          batched[batchedKey] = true
          return asset
        }
        return null
      })
      .filter((asset) => asset) as Asset[]
    const chunked = chunk(filteredAssets, 20)
    await Promise.all(
      chunked.map(async (chunk, chunkIndex) => {
        const batch = {}
        // remove all the assets with the asset IDs
        await Promise.all(
          chunk.map((asset) => AssetModel.remove({ hk: asset.hk, sk: asset.sk }, { batch }))
        )
        // batch the remove request
        try {
          return tables.itemCloudGreen.batchWrite(batch)
        } catch (e) {
          console.error(`E2EAsset chunk index: ${chunkIndex}`, e)
          return Promise.resolve(null)
        }
      })
    )
    return filteredAssets
  }

  static async deleteAssetsByIDs(ids: string[]) {
    try {
      const AssetModel = tables.itemCloudGreen.getModel('Asset')
      // find all the assets with the asset IDs
      const findQueries = [...new Set(ids)].map((id) => AssetModel.find({ hk: `Asset#${id}` }))
      // run all the queries in parallel
      const results = await Promise.allSettled(findQueries)
      const assets: Asset[] = []
      let failedResults = 0
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          assets.push(...result.value)
        } else {
          failedResults++
        }
      })
      if (failedResults) {
        console.info(`Failed to find ${failedResults} E2E assets`)
      }
      this.deleteAssets(assets)
    } catch (e) {
      console.error('Unable to delete E2E Assets', e)
    }
  }

  static async deleteAssetsByUsers(users: User[]) {
    try {
      const assets: Asset[] = []
      const usernames = [...new Set(users)].map((user) => user.username as string)
      // find all the assets with the subjectID and user
      for (const subjectID of courseIDsE2E) {
        let results: Paged<Asset> = []
        do {
          results = await getAssetFindQueryE2E({
            subjectID,
            usernames,
            modelVersion: ASSET_MODEL_VERSION,
            next: results?.next,
          })
          assets.push(...results)
        } while (results.next)
      }
      if (assets.length) {
        console.info(`Found ${assets.length} E2E assets and directions to delete`)
      }
      return this.deleteAssets(assets)
    } catch (e) {
      console.error('Unable to delete E2E Assets', e)
      return []
    }
  }

  static async delete(ids: string[]) {
    try {
      this.deleteAssetsByIDs(ids)
      E2EAssetComment.deleteAssetCommentsByIDs(ids)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E assets and friendly IDs', e)
    }
  }

  static async deleteByUsers(users: User[]) {
    try {
      const data = await this.deleteAssetsByUsers(users)
      const ids = data.map((item) => item.id as string)
      E2EAssetComment.deleteAssetCommentsByIDs(ids)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E assets and friendly IDs', e)
    }
  }
}
