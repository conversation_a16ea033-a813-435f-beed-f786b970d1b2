import { getTimeToLive } from '@e2e/util/dataUtils'
import { AssemblyUnitGeneratorProps, assemblyUnitGenerator } from '@shared/data-generation/mock'
import { AssemblyUnitEvent, AssemblyUnitSnapshot, User, tables } from '@shared/dynamo-hummingbird'
import { EventType, assemblyUnitEventTypes } from '@shared/event-sourcing'
import chunk from '@shared/utils/chunk'
import { ASSEMBLY_UNIT_MODEL_VERSION } from '@shared/utils/constants/constants'
import { E2EAsset } from './E2EAsset'
import { E2EEvent, getEventData } from './E2EEvent'
import { E2EFriendlyID } from './E2EFriendlyID'
import { getMainUser } from '@e2e/util/userUtils'

export class E2EAssemblyUnit extends E2EEvent {
  static async generate(props: AssemblyUnitGeneratorProps) {
    try {
      const user = props.user || getMainUser().getTestUserData()
      const { data } = (await assemblyUnitGenerator({
        ...props,
        user,
        ttl: getTimeToLive(),
      })) as { data: AssemblyUnitSnapshot[] }
      console.log(`Generated ${data.length} E2EAssemblyUnits`)
      return data.flatMap((item) => new E2EAssemblyUnit(item))
    } catch (e) {
      console.info('Unable to generate an E2EAssemblyUnit')
      return null
    }
  }

  static async deleteAssemblyUnits(assemblyUnits: AssemblyUnitEvent[], eventType: EventType) {
    const model = tables.hummingbird.getModel(eventType)
    const batched: Record<string, boolean> = {}
    const filteredAssemblyUnits = assemblyUnits
      .map((event) => {
        const batchedKey = `${event.id}#${event.sequenceNum}`
        if (!batched[batchedKey]) {
          batched[batchedKey] = true
          return event
        }
        return null
      })
      .filter((event) => event) as AssemblyUnitEvent[]
    const chunked = chunk(filteredAssemblyUnits, 20)
    await Promise.all(
      chunked.map(async (chunk, chunkIndex) => {
        const batch = {}
        // remove all the assembly units with the assembly unit IDs
        await Promise.all(
          chunk.map((assemblyUnit) =>
            model.remove({ id: assemblyUnit.id, sequenceNum: assemblyUnit.sequenceNum }, { batch })
          )
        )
        // remove all assets associated with each assembly unit
        if (eventType === 'AssemblyUnitSnapshot') {
          try {
            await Promise.all(
              chunk.map((assemblyUnit: AssemblyUnitSnapshot) =>
                E2EAsset.delete(assemblyUnit.assetIDs as string[])
              )
            )
          } catch (e) {
            console.error('Unable to delete E2E Assets', e)
          }
        }
        // batch the remove request
        try {
          return tables.hummingbird.batchWrite(batch)
        } catch (e) {
          console.error(`E2EAssemblyUnit chunk index: ${chunkIndex}`, e)
          return Promise.resolve(null)
        }
      })
    )
    return filteredAssemblyUnits
  }

  static async deleteAssemblyUnitsByIDs(ids: string[]) {
    try {
      const eventTypes = Object.values(assemblyUnitEventTypes)
      for (const eventType of eventTypes) {
        const model = tables.hummingbird.getModel(eventType)
        // find all the assembly units with the assembly unit IDs
        // @ts-expect-error: the union type of eventTypes causes confusion in the params so this should silence the error
        const findQueries = [...new Set(ids)].map((id) => model.find({ id }))
        // run all the queries in parallel
        const results = await Promise.allSettled(findQueries)
        const assemblyUnits: AssemblyUnitEvent[] = []
        let failedResults = 0
        results.forEach((result) => {
          if (result.status === 'fulfilled') {
            assemblyUnits.push(...result.value)
          } else {
            failedResults++
          }
        })
        if (failedResults) {
          console.info(`Failed to find ${failedResults} E2E Assembly Units`)
        }
        this.deleteAssemblyUnits(assemblyUnits, eventType)
      }
    } catch (e) {
      console.error('Unable to delete E2E Assembly Units', e)
    }
  }

  static async deleteAssemblyUnitsByUsers(users: User[]) {
    try {
      const data: AssemblyUnitEvent[] = []
      const eventTypes = Object.values(assemblyUnitEventTypes)
      for (const eventType of eventTypes) {
        const assemblyUnits: AssemblyUnitEvent[] = []
        const usernames = [...new Set(users)].map((user) => user.username as string)
        const eventData = await getEventData({
          eventType,
          usernames,
          modelVersion: ASSEMBLY_UNIT_MODEL_VERSION,
        })
        assemblyUnits.push(...eventData)
        if (assemblyUnits.length) {
          console.info(`Found ${assemblyUnits.length} E2E ${eventType} data to delete`)
        }
        data.push(...(await this.deleteAssemblyUnits(assemblyUnits, eventType)))
      }
      return data
    } catch (e) {
      console.error('Unable to delete E2E Assembly Units', e)
      return []
    }
  }

  static async delete(ids: string[]) {
    try {
      this.deleteAssemblyUnitsByIDs(ids)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E Assembly Units and Friendly IDs', e)
    }
  }

  static async deleteByUsers(users: User[]) {
    try {
      const data = await this.deleteAssemblyUnitsByUsers(users)
      const ids = data.map((item) => item.id as string)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E Assembly Units and Friendly IDs', e)
    }
  }
}
