import { getMainUser } from '@e2e/util/userUtils'
import { FormGeneratorProps, formGenerator } from '@shared/data-generation/mock'
import { FormEvent, tables } from '@shared/dynamo-hummingbird'
import { queryPaginated } from '@shared/dynamo-hummingbird/tables/io/read'
import { writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import type {
  FormAssociationModel,
  FormModel,
  FormSectionModel,
} from '@shared/dynamo-hummingbird/tables/split-models'
import type { Form } from '@shared/schema/split-models/forms'
import { E2EEvent } from './E2EEvent'
import { E2EFriendlyID } from './E2EFriendlyID'

export class E2EForm extends E2EEvent {
  static async generate(props: FormGeneratorProps) {
    try {
      const user = props.user || getMainUser().getTestUserData()
      const { data, assetIdsForE2E, directionIdsForE2E } = await formGenerator({
        ...props,
        user,
      })
      return data.map((item, i) =>
        this.create(item as Form, assetIdsForE2E?.[i], directionIdsForE2E?.[i])
      )
    } catch (e) {
      console.info('Unable to generate an E2EForm', e)
      return null
    }
  }

  static async deleteFormsByIDs(ids: string[]) {
    try {
      for (const formId of ids) {
        const authoringData = await queryPaginated<
          FormModel | FormSectionModel | FormAssociationModel
        >(tables.authoring, `Form#${formId}`, {}, { limit: 'none' })
        const eventData = await queryPaginated<FormEvent>(
          tables.events,
          `Form#${formId}`,
          {},
          { limit: 'none' }
        )

        await writeMany([
          { table: 'authoring', record: authoringData, remove: true },
          { table: 'events', record: eventData, remove: true },
        ])
      }
    } catch (e) {
      console.error('Unable to delete E2E Forms', e)
    }
  }

  static async delete(ids: string[]) {
    try {
      this.deleteFormsByIDs(ids)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E forms and friendly IDs', e)
    }
  }
}
