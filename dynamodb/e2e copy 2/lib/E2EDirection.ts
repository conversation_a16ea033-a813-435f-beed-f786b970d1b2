import {
  AssetGeneratorProps as DirectionGeneratorProps,
  directionGenerator,
} from '@shared/data-generation/mock'
import type { Asset } from '@shared/dynamo-hummingbird'
import { E2EAsset } from './E2EAsset'
import { getTimeToLive } from '@e2e/util/dataUtils'
import { getMainUser } from '@e2e/util/userUtils'

export class E2EDirection extends E2EAsset {
  static override async generate(props: DirectionGeneratorProps) {
    try {
      const user = props.user || getMainUser().getTestUserData()
      const { data } = await directionGenerator({ ...props, user, ttl: getTimeToLive() })
      return data.map((item) => this.create(item as Asset))
    } catch (e) {
      console.info('Unable to generate an E2E Direction', e)
      return null
    }
  }
}
