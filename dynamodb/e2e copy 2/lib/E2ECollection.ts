import { getTimeToLive } from '@e2e/util/dataUtils'
import { getMainUser } from '@e2e/util/userUtils'
import { CollectionGeneratorProps, collectionGenerator } from '@shared/data-generation/mock'
import { CollectionEvent, CollectionSnapshot, User, tables } from '@shared/dynamo-hummingbird'
import { EventType, collectionEventTypes } from '@shared/event-sourcing'
import chunk from '@shared/utils/chunk'
import { COLLECTION_MODEL_VERSION } from '@shared/utils/constants/constants'
import { E2EAsset } from './E2EAsset'
import { E2EEvent, getEventData } from './E2EEvent'
import { E2EFriendlyID } from './E2EFriendlyID'

export class E2ECollection extends E2EEvent {
  static async generate(props: CollectionGeneratorProps) {
    try {
      const user = props.user || getMainUser().getTestUserData()
      const { data, assetIdsForE2E } = await collectionGenerator({
        ...props,
        user,
        ttl: getTimeToLive(),
      })
      return data.map((item, idx) => this.create(item, assetIdsForE2E?.[idx]))
    } catch (e) {
      console.info('Unable to generate an E2ECollection')
      return null
    }
  }

  static async deleteCollections(collections: CollectionEvent[], eventType: EventType) {
    const model = tables.hummingbird.getModel(eventType)
    const batched: Record<string, boolean> = {}
    const filteredCollections = collections
      .map((event) => {
        const batchedKey = `${event.id}#${event.sequenceNum}`
        if (!batched[batchedKey]) {
          batched[batchedKey] = true
          return event
        }
        return null
      })
      .filter((event) => event) as CollectionEvent[]
    const chunked = chunk(filteredCollections, 20)
    await Promise.all(
      chunked.map(async (chunk, chunkIndex) => {
        const batch = {}
        // remove all the collections with the collection IDs
        await Promise.all(
          chunk.map((collection) =>
            model.remove({ id: collection.id, sequenceNum: collection.sequenceNum }, { batch })
          )
        )
        // remove all assets associated with each collection
        if (eventType === 'CollectionSnapshot') {
          try {
            await Promise.all(
              chunk.map((collection: CollectionSnapshot) =>
                E2EAsset.delete(collection.assetIDs as string[])
              )
            )
          } catch (e) {
            console.error('Unable to delete E2E Assets', e)
          }
        }
        // batch the remove request
        try {
          return tables.hummingbird.batchWrite(batch)
        } catch (e) {
          console.error(`E2ECollection chunk index: ${chunkIndex}`, e)
          return Promise.resolve(null)
        }
      })
    )
    return filteredCollections
  }

  static async deleteCollectionsByIDs(ids: string[]) {
    try {
      const eventTypes = Object.values(collectionEventTypes)
      for (const eventType of eventTypes) {
        const model = tables.hummingbird.getModel(eventType)
        // find all the collections with the collection IDs
        // @ts-expect-error: the union type of eventTypes causes confusion in the params so this should silence the error
        const findQueries = [...new Set(ids)].map((id) => model.find({ id }))
        // run all the queries in parallel
        const results = await Promise.allSettled(findQueries)
        const collections: CollectionEvent[] = []
        let failedResults = 0
        results.forEach((result) => {
          if (result.status === 'fulfilled') {
            collections.push(...result.value)
          } else {
            failedResults++
          }
        })
        if (failedResults) {
          console.info(`Failed to find ${failedResults} E2E Collections`)
        }
        this.deleteCollections(collections, eventType)
      }
    } catch (e) {
      console.error('Unable to delete E2E Collections', e)
    }
  }

  static async deleteCollectionsByUsers(users: User[]) {
    try {
      const data: CollectionEvent[] = []
      const eventTypes = Object.values(collectionEventTypes)
      for (const eventType of eventTypes) {
        const collections: CollectionEvent[] = []
        const usernames = [...new Set(users)].map((user) => user.username as string)
        const eventData = await getEventData({
          eventType,
          usernames,
          modelVersion: COLLECTION_MODEL_VERSION,
        })
        collections.push(...eventData)
        if (collections.length) {
          console.info(`Found ${collections.length} E2E ${eventType} data to delete`)
        }
        data.push(...(await this.deleteCollections(collections, eventType)))
      }
      return data
    } catch (e) {
      console.error('Unable to delete E2E Collections', e)
      return []
    }
  }

  static async delete(ids: string[]) {
    try {
      this.deleteCollectionsByIDs(ids)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E collections and friendly IDs', e)
    }
  }

  static async deleteByUsers(users: User[]) {
    try {
      const data = await this.deleteCollectionsByUsers(users)
      const ids = data.map((item) => item.id as string)
      E2EFriendlyID.deleteFriendlyIDsByIDs(ids)
    } catch (e) {
      console.error('Unable to delete E2E collections and friendly IDs', e)
    }
  }
}
