import fs from 'fs'
import { parse } from 'csv-parse/sync'
import { expect, test } from '@e2e/cb-test'
import type { E2EAsset } from '@e2e/lib'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

const newCommentText = 'Test adding a new comment'
const newAssetCommentText = 'Test adding a new asset comment'

let collectionId = ''
let subject = ''
let subjectId = ''

const getDate = function () {
  const currentDate = new Date()
  const day = currentDate.getDate()
  const month = currentDate.toLocaleString('en-US', { month: 'short' }).substring(0, 3)
  const year = currentDate.getFullYear()
  return `${currentDate.toDateString().substring(0, 3)}_${month} ${day
    .toString()
    .padStart(2, '0')} ${year}`
}

test.beforeEach(async ({ collectionPage, randomCollection }) => {
  subject = randomCollection.subject
  subjectId = randomCollection.subjectID
  collectionId = randomCollection.id
  await collectionPage.goToPageWith({ ...randomCollection })
})

test('Collection Comments Only', async ({ collectionPage, page }) => {
  await collectionPage.showCommentsButton.click()
  const { addCommentContainer, commentsThread } = collectionPage.commentsTab.threads

  // add a comment
  await expect(collectionPage.commentsTab.contentContainer).not.toContainText(newCommentText)
  await addCommentContainer.addCommentButton.click()
  await addCommentContainer.editableTextBox.textBox.fill(newCommentText)
  await addCommentContainer.editableTextBox.saveButton.click()
  await waitForResponseFrom(page, endpoints.COMMENTS_ADD)
  await expect(commentsThread.commentsContent.body.root.getByText(newCommentText)).toBeVisible()

  const comments = await (
    await collectionPage.commentsTab.contentContainer.allInnerTexts()
  ).toString()
  expect(comments).toContain(newCommentText)

  // view all comments
  const { downloadCommentsButton, downloadCSVButton } = collectionPage

  await test.step('Download Collection Comments', async () => {
    await downloadCommentsButton.click()
    await expect(collectionPage.downloadCommentsDialog.root).toBeVisible()
    await collectionPage.collectionsOnlyButton.check()

    const downloadPath = await clickAndDownloadFile(
      page,
      downloadCSVButton,
      collectionId + '-COMMENTS-' + getDate() + '.csv'
    )

    const downloadedComments = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })

    await expect(downloadedComments[0].comment).toContain(newCommentText)
  })
})

test('Collection and Asset Comments', async ({
  assetEditorPage,
  collectionPage,
  collectionAddAssetsPage,
  page,
  dataController,
  currentUser,
}) => {
  await test.step('Add asset comment and add asset to collection then download', async () => {
    const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
      user: currentUser.getTestUserData(),
      courseIDs: [subjectId],
      length: 1,
    })) as E2EAsset[]
    if (!asset) {
      throw new Error('Asset generation failed')
    }
    await assetEditorPage.goToPageWith({ subject: subject, id: asset.id })

    const { commentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
    const { addCommentContainer, commentsThread } = commentsTab.content.commentContent.threads
    await commentsTab.open()

    // add a comment
    await expect(commentsTab.contentContainer).not.toContainText(newAssetCommentText)
    await addCommentContainer.addCommentButton.click()
    await addCommentContainer.editableTextBox.textBox.fill(newAssetCommentText)
    await addCommentContainer.editableTextBox.saveButton.click()
    await waitForResponseFrom(assetEditorPage.page, endpoints.COMMENTS_ADD)
    await expect(
      commentsThread.commentsContent.body.root.getByText(newAssetCommentText)
    ).toBeVisible()

    await collectionAddAssetsPage.goToPageWith({ subject: subject, id: collectionId })
    await collectionAddAssetsPage.table.allCheckBoxes.first().check()
    const count = await collectionAddAssetsPage.table.allCheckBoxes.count()
    await collectionAddAssetsPage.getAddAssetsButton({ assetCount: count - 1 }).click()
    await waitForResponseFrom(page, endpoints.COLLECTIONS_ADD_ASSETS)
    await collectionAddAssetsPage.doneButton.click()
    await collectionPage.goToPageWith({ subject: subject, id: collectionId })
    await collectionPage.showCommentsButton.click()

    const { downloadCommentsButton, downloadCSVButton } = collectionPage

    await downloadCommentsButton.click()
    await expect(collectionPage.downloadCommentsDialog.root).toBeVisible()
    await collectionPage.collectionsAndAssetsButton.check()

    const downloadPath = await clickAndDownloadFile(
      page,
      downloadCSVButton,
      collectionId + '_ASSET-COMMENTS-' + getDate() + '.csv'
    )

    const downloadedComments = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })

    await expect(downloadedComments[0].content).toContain(newAssetCommentText)
  })
})
