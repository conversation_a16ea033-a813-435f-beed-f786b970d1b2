import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { dragAndDrop } from '@e2e/util/dragAndDrop'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'

test('Verify reordering assets and items within a collection', async ({
  dataController,
  collectionPage,
}) => {
  await test.step('Generate collection and navigate to page', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      assetLength: 5,
      assetProps: { itemCount: 3, itemType: ItemType.MultipleChoiceQuestion },
      collectionProps: { name: 'Reorder Collection Test' },
    })) as E2ECollection[]
    test.fail(!collection || !collection.id || !collection.subject, 'Failed to create a collection')
    await collectionPage.goToPageWith({
      id: collection?.id as string,
      subject: collection?.subject as string,
    })
  })

  await test.step('Reorder assets', async () => {
    // Same step used in Next Gen AU Reorder testing
    // Convert each row to an object with key-value pairs for each column
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    //test drag and drop reorder mode
    await collectionPage.reorderButton.click()
    await collectionPage.dragAndDrop.click()
    const previousFirstAsset = await collectionPage.allAssets.first().innerText()
    // reorder
    await dragAndDrop(
      collectionPage.page,
      collectionPage.dragBoxes.first(),
      collectionPage.dragBoxes.nth(2)
    )
    const newFirstAsset = await collectionPage.allAssets.first().innerText()
    await collectionPage.applyButton.click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    await expect(collectionPage.allAssets.nth(2)).toHaveText(previousFirstAsset)
    await expect(collectionPage.allAssets.first()).toHaveText(newFirstAsset)
    // verify 'Undo' option on the toast
    await collectionPage.toastDialogLocator.getByRole('link', { name: 'UNDO' }).click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    await expect(collectionPage.allAssets.first()).toHaveText(previousFirstAsset)

    // test edit asset reorder mode
    await collectionPage.reorderButton.click()
    await collectionPage.editAssetOrder.click()
    await collectionPage.allAssets.first().waitFor({ state: 'visible' })
    const previousEditOrderFirstAsset = await collectionPage.allAssets.first().allInnerTexts()
    await collectionPage.editAssetTextBox.first().fill('1000')
    await collectionPage.applyButton.click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    expect(previousEditOrderFirstAsset).toEqual(
      await collectionPage.allAssets.last().allInnerTexts()
    )
    // verify 'Undo' option on the toast
    await collectionPage.toastDialogLocator.getByRole('link', { name: 'UNDO' }).click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    await expect(collectionPage.allAssets.first()).toHaveText(previousEditOrderFirstAsset)
  })

  await test.step('Reorder items', async () => {
    //test drag and drop reorder mode
    await collectionPage.reorderButton.click()
    await collectionPage.dragAndDrop.click()
    // get item locators
    const firstAsset = await collectionPage.allAssets.first().innerText()
    const items = await collectionPage.getItemLocatorsForAsset(firstAsset)
    const previousFirstItem = await items
      .first()
      .getByLabel('id-column-value', { exact: true })
      .innerText()
    const itemDragLocators = items.getByLabel(`draggable-icon`)
    const itemDragLocatorsCount = await itemDragLocators.count()

    //reorder
    await dragAndDrop(
      collectionPage.page,
      itemDragLocators.first(),
      itemDragLocators.nth(itemDragLocatorsCount - 1)
    )
    const newFirstItem = await items
      .first()
      .getByLabel('id-column-value', { exact: true })
      .innerText()
    await collectionPage.applyButton.click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    await expect(items.nth(itemDragLocatorsCount - 1)).toContainText(previousFirstItem)
    await expect(items.first()).toContainText(newFirstItem)

    // verify 'Undo' option on the toast
    await collectionPage.toastDialogLocator.getByRole('link', { name: 'UNDO' }).click()
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_REORDER_ASSETS)
    await waitForResponseFrom(collectionPage.page, endpoints.COLLECTIONS_READ_ONE)
    await expect(items.first()).toContainText(previousFirstItem)
  })
})
