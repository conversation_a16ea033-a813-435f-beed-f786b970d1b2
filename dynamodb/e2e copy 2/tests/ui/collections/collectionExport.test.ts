import fs from 'fs'
import { parse } from 'csv-parse/sync'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import type { Page } from '@playwright/test'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import type { CourseConfiguration } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

async function waitForMetadataVersionOptions(page: Page, baseURL: string | undefined) {
  if (!baseURL) throw new Error('Cannot wait without baseURL')
  const response = await page.waitForResponse(
    (response) =>
      !!(response.url().includes(`${baseURL}/api/${endpoints.AUTH}`) && response.status() === 200),
    { timeout: undefined }
  )
  const body = await response.json()
  const data = body[0].result.data.courseConfig[SubjectIdentifier.PRECALCULUS]
    .metadata as CourseConfiguration['metadata']
  const metadataVersionOptions = data.map(({ archiveNote }) => archiveNote).filter(Boolean)
  return metadataVersionOptions as string[]
}

test('Action Menu opens export modal', async ({
  collectionPage,
  page,
  baseURL,
  dataController,
}) => {
  await test.step('Assert Actions menu works for Downloading Metadata and PDF', async () => {
    const { actionsDropdown, exportMetadataDialog } = collectionPage

    // create the collection that's populated
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'csvExportCollection' },
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    })) as E2ECollection[]

    if (collection) {
      await collectionPage.goToPageWith({
        subject: SubjectKey.PRECALCULUS,
        id: collection.id,
      })
    }
    test.fail(!collection, 'Failed to create a collection')
    const metadataVersionOptions = await waitForMetadataVersionOptions(page, baseURL)

    // Open the dropdown
    await actionsDropdown.button.click()
    // Open the Download submenu
    await actionsDropdown.getOption({ name: 'Download' }).hover()
    // Click the CSV export
    await actionsDropdown.getOption({ name: 'Metadata (.csv)' }).click()
    // check the dialog fields
    await expect(exportMetadataDialog.content.includeRowsForAssetMetadataCheckBox).toBeChecked()
    await expect(exportMetadataDialog.content.includeRowsForItemMetadataCheckBox).toBeChecked()
    await expect(exportMetadataDialog.content.includeRowsForStimuliMetadataCheckBox).toBeChecked()
    // open the metadata version dropdown
    await exportMetadataDialog.content.metadataVersion.root.click()
    await exportMetadataDialog.content.metadataVersion
      .getOptionByIndexOrName({ name: metadataVersionOptions[0] })
      .click()

    // Download the metadata file
    const metadataDownloadPath = await clickAndDownloadFile(
      page,
      exportMetadataDialog.buttons.export,
      'csvExportCollection-metadata'
    )
    await expect(metadataDownloadPath).toContain('csvExportCollection-metadata')
  })
})

test('CSV Collection export respects order of assets', async ({
  collectionPage,
  page,
  dataController,
  baseURL,
}) => {
  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'csvExportCollection' },
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    })) as E2ECollection[]

    if (collection) {
      await collectionPage.goToPageWith({
        subject: SubjectKey.PRECALCULUS,
        id: collection.id,
      })
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  await test.step('Assert order of assets on asset page and ensure exported CSV respects order', async () => {
    const metadataVersionOptions = await waitForMetadataVersionOptions(page, baseURL)

    // Convert each row to an object with key-value pairs for each column
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    const tableRows = await collectionPage.tableRows.all()
    const originalAssetOrder = (
      await Promise.all(
        tableRows.map(async (row) => {
          return row.getByLabel('id-column-value', { exact: true }).allInnerTexts()
        })
      )
    ).flat()

    const { actionsDropdown, exportMetadataDialog } = collectionPage
    await actionsDropdown.button.click()
    await actionsDropdown.getOption({ name: 'Download' }).hover()
    await actionsDropdown.getOption({ name: 'Metadata (.csv)' }).click()

    await exportMetadataDialog.content.includeRowsForItemMetadataCheckBox.uncheck()
    await exportMetadataDialog.content.includeRowsForStimuliMetadataCheckBox.uncheck()
    // open the metadata version dropdown
    await exportMetadataDialog.content.metadataVersion.root.click()
    await exportMetadataDialog.content.metadataVersion
      .getOptionByIndexOrName({ name: metadataVersionOptions[0] })
      .click()

    // If the newly selected version is the same as the previously selected on, awaiting
    // a repeat `getAssetMetadata` call will fail because the call is made when the dialog
    // is opened or when a parameter is changed. However, the button is disabled while
    // `getAssetMetadata` is loading.
    await exportMetadataDialog.buttons.export.isEnabled({ timeout: 2500 })

    const downloadPath = await clickAndDownloadFile(
      page,
      exportMetadataDialog.buttons.export,
      'csvExportCollection-metadata'
    )

    const records = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })

    // Extract all 'Asset ID' values from the parsed records
    const csvAssetOrder = records.map((record: { [x: string]: any }) => record['Asset ID'])

    // Log and compare the orders
    console.log('Original Asset Order:', originalAssetOrder)
    console.log('CSV Asset Order:', csvAssetOrder)

    // Assert that the two arrays are the same
    expect(csvAssetOrder).toEqual(originalAssetOrder)
  })
})

test('PDF export', async ({ collectionPage, page, dataController }) => {
  let collectionId: string

  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'pdfExportCollection' },
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    })) as E2ECollection[]

    test.fail(!collection, 'Failed to create a collection')
    collectionId = collection?.id as string

    await collectionPage.goToPageWith({
      subject: SubjectKey.PRECALCULUS,
      id: collectionId,
    })
  })

  await test.step('Download PDF', async () => {
    const { actionsDropdown, exportPDFDialog } = collectionPage
    // Download the pdf file
    await actionsDropdown.button.click()
    await actionsDropdown.items.download.hover()
    await actionsDropdown.items.exportPdf.click()
    const pdfDownloadPath = await clickAndDownloadFile(
      page,
      exportPDFDialog.buttons.download,
      `${collectionId}.pdf`
    )
    await expect(pdfDownloadPath).toContain(`collection-${collectionId}`)
  })
})
