import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Send single asset to vault', async ({ collectionPage, dataController, page }) => {
  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 1,
      collectionProps: { name: 'SingleAssetToVault' },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
    })) as E2ECollection[]
    if (collection) {
      await collectionPage.goToPageWith(collection)
      await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  await test.step('Click the Assets tab and wait for them to load', async () => {
    await collectionPage.assetsTab.click()
  })

  await test.step('Send assets to vault', async () => {
    await collectionPage.allCheckboxes.nth(1).check() // select first asset, first checkbox is header checkbox
    await collectionPage.actionsDropdown.button.click()
    await collectionPage.actionsDropdown.items.sendAssets.click()
    await collectionPage.actionsDropdown.items.sendAssetsToVault.click()

    await expect(collectionPage.sendToVaultDialog.contentContainer).toContainText(
      'Only assets that are Ready for Assembly will be sent to the vault. To include the manifest, all items must be “Ready for Assembly” or “Sent to Vault”.'
    )

    await expect(collectionPage.toastLocator).toBeHidden()
    await collectionPage.sendToVaultDialog.buttons.send.click()
    await expect(collectionPage.toastLocator).toContainText('✅ 1 assets sent to vault')
  })
})
