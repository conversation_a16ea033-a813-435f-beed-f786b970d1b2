import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import type { Locator } from '@playwright/test'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const checkToastMessages = async ({
  toastLocator,
  expectedText,
  expectedCount,
}: {
  toastLocator: Locator
  expectedText: string | RegExp
  expectedCount: number
}) => {
  const toasts = toastLocator.filter({ hasText: expectedText })
  await expect(toasts).toHaveCount(expectedCount)
  for (const toast of await toasts.all()) {
    await expect(toast).toBeVisible()
    await expect(toast).toContainText(expectedText)
  }
}

test.beforeEach(async ({ collectionPage, dataController, page }) => {
  const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    length: 1,
    assetLength: 10,
    collectionProps: { name: 'e2eCollection' },
    workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW, WorkflowStepID.EXTERNAL_AUTHORING],
  })) as E2ECollection[]

  if (collection) {
    await collectionPage.goToPageWith({ subject: SubjectKey.PRECALCULUS, id: collection.id })
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
  }
  test.fail(!collection, 'Failed to create a collection')
})

test('Negative Test - Send not ready for use assets to vault', async ({ collectionPage, page }) => {
  const { actionsDropdown, sendToVaultDialog, toastLocator } = collectionPage

  await test.step('Click the Assets tab and wait for them to load', async () => {
    await collectionPage.assetsTab.click()
  })

  await test.step('Send assets to vault', async () => {
    await collectionPage.allCheckboxes.first().check() // select all assets
    await actionsDropdown.button.click()
    await actionsDropdown.items.sendAssets.click()
    await actionsDropdown.items.sendAssetsToVault.click()
    await sendToVaultDialog.buttons.send.click()
  })

  await test.step('Assert failure toast message for all assets of collection', async () => {
    await checkToastMessages({
      toastLocator,
      expectedText:
        /Some assets failed to send to vault. Top error: \w{3}\d{6}: \[3-05\] Asset skipped, is not in ReadyForAssembly state/,
      expectedCount: 1,
    })
    await checkToastMessages({
      toastLocator,
      expectedText: /\w{3}\d{6} failed to send to vault/,
      expectedCount: 10,
    })
  })
})

test('Send collection to vault', async ({ collectionPage, page }) => {
  const { actionsDropdown, transitionDialog, toastLocator, sendToVaultDialog } = collectionPage

  await test.step('Click the Assets tab and wait for them to load', async () => {
    await collectionPage.assetsTab.click()
  })

  await test.step('Transition assets to ready', async () => {
    await collectionPage.allCheckboxes.first().check() // select all assets
    await actionsDropdown.button.click()
    await actionsDropdown.items.transition.click()
    await transitionDialog.content.toSelect.selectOption('Ready for Assembly')
    await transitionDialog.buttons.apply.click()
    await expect(toastLocator).toContainText('10 assets transitioned to "Ready for Assembly"')
  })

  await test.step('Send collection to vault and ensure toast message and table column update', async () => {
    await actionsDropdown.button.click()
    await actionsDropdown.items.sendAssets.click()
    await actionsDropdown.items.sendAssetsToVault.click()
    await expect(sendToVaultDialog.root).toContainText(
      'Only assets that are Ready for Assembly will be sent to the vault. To include the manifest, all items must be “Ready for Assembly” or “Sent to Vault”.'
    )
    await expect(toastLocator).toBeHidden()
    await sendToVaultDialog.buttons.send.click()
    await checkToastMessages({
      toastLocator,
      expectedText: '10 assets sent to vault',
      expectedCount: 1,
    })
  })
})
