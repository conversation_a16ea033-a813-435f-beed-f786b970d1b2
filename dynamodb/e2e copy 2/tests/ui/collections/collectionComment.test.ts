import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { retry } from '@e2e/util/retry'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'

const newCommentText = 'Test adding a new comment'
const editCommentText = 'Test editing a new comment'
const replyCommentText = 'Test replying to a comment'

test('Collection Comments Tab', async ({ page, dataController, collectionPage }) => {
  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'Collection Comments' },
    })) as E2ECollection[]
    if (collection) {
      await collectionPage.goToPageWith({ subject: SubjectKey.PRECALCULUS, id: collection.id })
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  await test.step('Open the comments tab', async () => {
    await collectionPage.showCommentsButton.click()
    await expect(collectionPage.commentsTab.root).toBeVisible()
  })

  await test.step('Check for comments top bar labels, close button, and "Add a comment" button', async () => {
    const { topBar, threads } = collectionPage.commentsTab
    await expect(topBar.topBarLabel.getByText('Comments')).toBeVisible()
    await expect(topBar.topBarLabel.getByText('0')).toBeVisible()
    await expect(topBar.topBarMenuCloseButton).toBeVisible()
    await expect(threads.addCommentContainer.root).toBeVisible()
  })

  await test.step('Add a comment', async () => {
    const { addCommentContainer, commentsThread } = collectionPage.commentsTab.threads
    await addCommentContainer.addCommentButton.click()
    await addCommentContainer.editableTextBox.textBox.fill(newCommentText)
    await addCommentContainer.editableTextBox.saveButton.click()
    await waitForResponseFrom(collectionPage.page, 'commentsAdd')
    await expect(commentsThread.commentsContent.body.root.getByText(newCommentText)).toBeVisible()
    await expect(collectionPage.commentsTab.topBar.topBarLabel.getByText('1')).toBeVisible()
  })

  await test.step('Edit a comment', async () => {
    const { header, body } = collectionPage.commentsTab.threads.commentsThread.commentsContent
    await retry(async () => {
      await header.menuButton.click({ timeout: 1000 })
      await expect(header.menuEditButton).toBeVisible()
    })
    await header.menuEditButton.click()
    await body.editableTextBox.textBox.fill(editCommentText)
    // TODO - Adding explicit timeout for quick fix for it not actually
    // saving the changes if saving too quickly after filling text. Look
    // into a better solution later. Note: Only affects editing.
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(250)
    await body.editableTextBox.saveButton.click()
    await waitForResponseFrom(collectionPage.page, 'commentsEdit')
    await expect(body.root.getByText(editCommentText)).toBeVisible()
  })

  await test.step('Reply to a comment', async () => {
    const { body, footer } = collectionPage.commentsTab.threads.commentsThread.commentsContent
    await footer.replyButton.click()
    await expect(footer.editableTextBox.root).toBeVisible()
    await footer.editableTextBox.textBox.fill(replyCommentText)
    await footer.editableTextBox.saveButton.click()
    await waitForResponseFrom(collectionPage.page, 'commentsAdd')
    await expect(body.root.getByText(replyCommentText)).toBeVisible()
    await expect(collectionPage.commentsTab.topBar.topBarLabel.getByText('2')).toBeVisible()
  })

  await test.step('Delete button should be hidden on a parent comment with replies', async () => {
    const { header } = collectionPage.commentsTab.threads.commentsThread.commentsContent
    await retry(async () => {
      await header.menuButton.nth(0).click({ timeout: 1000 })
      await expect(header.menuEditButton).toBeVisible()
      await expect(header.menuDeleteButton).toBeHidden()
    })
  })

  await test.step('Reload the page and open the comments tab', async () => {
    // make sure our data is up-to-date for the next tests
    await page.reload()
    await collectionPage.showCommentsButton.click()
    await expect(collectionPage.commentsTab.root).toBeVisible()
  })

  await test.step('Delete a reply comment', async () => {
    const { header, body } = collectionPage.commentsTab.threads.commentsThread.commentsContent
    await retry(async () => {
      await header.menuButton.nth(1).click({ timeout: 1000 })
      await expect(header.menuDeleteButton).toBeVisible()
    })
    await header.menuDeleteButton.click()
    await expect(header.deleteCommentDialog.root).toBeVisible()
    await header.deleteCommentDialog.buttons.remove.click()
    await waitForResponseFrom(collectionPage.page, 'commentsRemove')
    await expect(collectionPage.commentsTab.root).toBeVisible()
    // check that the reply is gone
    await expect(body.root.getByText(replyCommentText)).toBeHidden()

    await expect(collectionPage.commentsTab.topBar.topBarLabel.getByText('1')).toBeVisible()
  })

  await test.step('Delete a parent comment', async () => {
    const { header, body } = collectionPage.commentsTab.threads.commentsThread.commentsContent
    await retry(async () => {
      await header.menuButton.click({ timeout: 1000 })
      await expect(header.menuDeleteButton).toBeVisible()
    })
    await header.menuDeleteButton.click()
    await expect(header.deleteCommentDialog.root).toBeVisible()
    await header.deleteCommentDialog.buttons.remove.click()
    await waitForResponseFrom(collectionPage.page, 'commentsRemove')
    await expect(collectionPage.commentsTab.root).toBeVisible()
    // check that the reply is gone
    await expect(body.root.getByText(editCommentText)).toBeHidden()

    await expect(collectionPage.commentsTab.topBar.topBarLabel.getByText('0')).toBeVisible()
  })

  await test.step('Close the comments tab', async () => {
    await collectionPage.commentsTab.topBar.topBarMenuCloseButton.click()
    await expect(collectionPage.commentsTab.root).toBeHidden()
  })
})
