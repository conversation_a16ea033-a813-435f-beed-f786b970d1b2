import fs from 'fs'
import path from 'path'
import { ulid } from 'ulid'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { clickAndDownloadFile, verifyPdfContent } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'

const pdfName = 'samplePdf.pdf'
const textFileName = `${ulid()}.txt`
const hummingbirdJpg = 'hummingbird.jpg'
const htmlFile = 'anHtmlFile.html'
const bytesPerMb = 1024 ** 2
test.setTimeout(2 * 60 * 1000)

test('Collection Attachments', async ({ page, dataController, collectionPage }) => {
  const reloadAndReturnToTab = async (delay = 500) => {
    if (delay) {
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
    page.reload()
    const detailsTab = collectionPage.detailsTab
    await detailsTab.click()
    await expect(collectionPage.detailsContainer).toBeVisible()
  }

  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'Collection Attachments' },
    })) as E2ECollection[]
    if (collection) {
      await collectionPage.goToPageWith({ subject: SubjectKey.PRECALCULUS, id: collection.id })
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  const attachmentsContainer = collectionPage.attachmentsContainer
  const detailsTab = collectionPage.detailsTab

  await test.step('Open the Details tab', async () => {
    await reloadAndReturnToTab(0)
  })

  await test.step('Upload an attachment and verify it saved', async () => {
    await attachmentsContainer.uploadButton.click()
    await attachmentsContainer.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', pdfName)
    )

    await expect(attachmentsContainer.uploadDialog.previewArea).toContainText(pdfName)
    await expect(attachmentsContainer.uploadDialog.dialog.buttons.upload).toBeEnabled()
    await attachmentsContainer.uploadDialog.dialog.buttons.upload.click()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_ADD)
    await expect(attachmentsContainer.attachmentInfo.statusMessage).toContainText('Uploading')
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)
    await expect(
      collectionPage.toastLocator
        .filter({
          hasText: 'Attachment added successfully. Finishing up virus scanning.',
        })
        .last()
    ).toBeVisible()
    await expect(attachmentsContainer.attachmentInfo.statusMessage).toContainText(
      'Scanning for viruses'
    )
    const attachmentName = await attachmentsContainer.attachmentInfo.name.innerText()
    expect(attachmentName).toContain(pdfName)
  })

  await test.step('Upload a duplicate attachment and verify the prompt', async () => {
    await attachmentsContainer.uploadButton.click()
    await attachmentsContainer.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', pdfName)
    )

    await expect(attachmentsContainer.uploadDialog.previewArea).toContainText(pdfName)
    await expect(attachmentsContainer.uploadDialog.dialog.buttons.replace).toBeEnabled()
    await attachmentsContainer.uploadDialog.dialog.buttons.replace.click()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_ADD)
    await expect(attachmentsContainer.attachmentInfo.statusMessage.nth(0)).toContainText(
      'Uploading'
    )
    await expect(attachmentsContainer.attachmentInfo.statusMessage.nth(1)).toContainText(
      'Scanning for viruses'
    )
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)
    await expect(
      collectionPage.toastLocator
        .filter({
          hasText: 'Attachment added successfully. Finishing up virus scanning.',
        })
        .last()
    ).toBeVisible()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)
    const attachmentName = await attachmentsContainer.attachmentInfo.name.innerText()
    expect(attachmentName).toContain(pdfName)
  })

  await test.step('Download an attachment successfully', async () => {
    await reloadAndReturnToTab()
    attachmentsContainer.actions.downloadButton.isVisible()
    await attachmentsContainer.actions.downloadButton.click()
    const downloadPath = await clickAndDownloadFile(
      page,
      attachmentsContainer.downloadAttachmentDialog.buttons.confirm,
      pdfName
    )

    await verifyPdfContent(downloadPath, 'Dummy PDF file')
  })

  await test.step('Upload another attachment and ensure the order is descending', async () => {
    await reloadAndReturnToTab()
    await attachmentsContainer.uploadButton.click()
    await attachmentsContainer.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', hummingbirdJpg)
    )
    await expect(attachmentsContainer.uploadDialog.dialog.buttons.upload).toBeEnabled()
    await attachmentsContainer.uploadDialog.dialog.buttons.upload.click()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_ADD)
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)

    const expectedFileOrder = [hummingbirdJpg, pdfName]

    const fileOrderBeforeReload = await attachmentsContainer.attachmentInfo.name.allInnerTexts()
    expectedFileOrder.forEach((fileName, index) =>
      expect(fileOrderBeforeReload[index]).toContain(fileName)
    )

    const fileOrderAfterReload = await attachmentsContainer.attachmentInfo.name.allInnerTexts()
    expectedFileOrder.forEach((fileName, index) =>
      expect(fileOrderAfterReload[index]).toContain(fileName)
    )
  })

  await test.step('Delete an attachment successfully', async () => {
    await reloadAndReturnToTab(500)
    const secondAttachmentDeleteButton = attachmentsContainer.actions.deleteButton.nth(1)
    await secondAttachmentDeleteButton.click()
    await attachmentsContainer.deleteDialog.buttons.remove.click()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_REMOVE)
    await page.reload()
    await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)
    await detailsTab.click()
    await expect(collectionPage.detailsContainer).toBeVisible()
    const presentAttachments = await attachmentsContainer.attachmentInfo.name.all()
    expect(presentAttachments.length).toBe(1)
  })

  // negative tests
  await test.step('Upload a large file that will fail', async () => {
    const textFilePath = path.join(__dirname, '..', '..', '..', 'uploads', textFileName)
    const fileDesc = fs.openSync(textFilePath, fs.constants.O_CREAT | fs.constants.O_WRONLY)

    if (fileDesc === -1) {
      throw 'Unable to find file'
    }

    if (fileDesc) {
      fs.writeFileSync(fileDesc, Buffer.alloc(51 * bytesPerMb))
    }

    if (!fs.fstatSync(fileDesc).size) throw 'Unable to attachment file.'

    await detailsTab.click()
    await attachmentsContainer.uploadButton.click()
    await attachmentsContainer.uploadDialog.uploadFiles(textFilePath)

    // await expect(attachmentsContainer.uploadDialog.previewArea).toContainText(textFileName)
    await expect(collectionPage.toastLocator).toContainText('Your attachment upload failed.')

    fs.unlinkSync(textFilePath)
  })

  await test.step('Upload a file that is an unsupported file type that will fail', async () => {
    await reloadAndReturnToTab(500)
    await attachmentsContainer.uploadButton.click()
    await attachmentsContainer.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', htmlFile)
    )

    await expect(attachmentsContainer.uploadDialog.previewArea).toContainText(htmlFile)
    await waitForResponseFrom(collectionPage.page, endpoints.ATTACHMENTS_STAGE, 400)

    await expect(collectionPage.toastLocator).toContainText(
      'text/html is not an accepted file type.'
    )
  })
})
