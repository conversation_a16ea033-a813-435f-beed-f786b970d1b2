import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { CollectionsPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Asset associations dialog should display correctly', async ({
  assetListPage,
  context,
  page,
  dataController,
  collectionPage,
}) => {
  const assetLength = 5

  await test.step('Go to page with specific subject and collection', async () => {
    const baseUrl = determineBaseUrlFromEnv()
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength,
      collectionProps: { name: 'associationsCollection' },
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    })) as E2ECollection[]
    if (collection) {
      await page.goto(`${baseUrl}/ap/precalculus/collections/${collection.id}`)
      await waitForResponseFrom(page, endpoints.ATTACHMENTS_READ)
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  await test.step('Click the Assets tab and wait for them to load', async () => {
    await collectionPage.assetsTab.click()
  })

  await test.step('Clicking one of the association column cells should open the association dialog', async () => {
    const randomCell = casual.integer(0, assetLength - 1)
    const randomAssociationsCell = assetListPage.associationsButtons.nth(randomCell)
    await randomAssociationsCell.click()
    await expect(assetListPage.associationsDialog.root).toBeVisible()
  })

  await test.step('The association dialog should display 2 tabs: Assembly Units (#) and Collections (#)', async () => {
    const dialog = assetListPage.associationsDialog.root
    // check for both tabs
    const assemblyUnitsTab = dialog.getByRole('tab', { name: /Assembly Units \([0-9]\)/ })
    await expect(assemblyUnitsTab).toBeVisible()
    const collectionsTab = dialog.getByRole('tab', { name: /Collections \([0-9]\)/ })
    await expect(collectionsTab).toBeVisible()
    // check the content of assembly unit tab
    await assemblyUnitsTab.click()
    await expect(dialog.getByRole('paragraph').getByText('No association to show')).toBeVisible()
    // check the content of collection tab
    await collectionsTab.click()
    const table = dialog.getByRole('table')
    await expect(table).toBeVisible()
    const snapshotLinks = table.locator('tbody tr')
    await expect(snapshotLinks).toHaveCount(1)
    // check that the link works
    const id = await table.locator('tbody tr td').first().innerText()
    const pagePromise = context.waitForEvent('page')
    await snapshotLinks.first().click()
    const newCollectionPage = new CollectionsPage({ page: await pagePromise })
    const newCollectionPageID = newCollectionPage.page.url().split('/').pop()
    expect(newCollectionPageID).toBe(id)
    // check that the close button works
    await assetListPage.associationsDialog.buttons.remove.click()
    await expect(dialog).toBeHidden()
  })
})
