import { pascalCase } from 'case-anything'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EAssemblyUnit, E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { ItemType } from '@shared/data-generation/mock'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
const adminYear = '25'
const questionType = QuestionType.MULTIPLE_CHOICE_QUESTION
test('Verify Adding Assets from a Collection to an Assembly Unit', async ({
  nextGenAssemblyUnitAuthoringPage,
  dataController,
  collectionPage,
  page,
}) => {
  await test.step('Generate an Assembly Unit and Add it to a Collection from the Collections page and Verify item deactivation propagation', async () => {
    // Generate Assembly Units (Base, Sequence, Complete)
    const assemblyUnit = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      sequenceLength: 1,
      completeLength: 1,
      nextGenAssemblyUnitProps: {
        questionType,
        adminYear,
        assemblyUnitType: AssemblyUnitType.BASE,
      },
    })) as E2EAssemblyUnit[]
    test.fail(!assemblyUnit?.[0]?.id, 'Failed to create an assembly unit')
    const [
      { id: baseAUId, name: baseAUName },
      { id: sequenceAUId, name: sequenceAUName },
      { id: completeAUId, name: completeAUName },
    ] = assemblyUnit
    // Generate a Collection with appropriate question type
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      assetLength: 5,
      assetProps: { itemType: pascalCase(questionType) as ItemType },
      collectionProps: { name: 'Add From Collection Test' },
    })) as E2ECollection[]
    test.fail(!collection, 'Failed to create a collection')
    // Navigate to collection page
    await collectionPage.goToPageWith({
      subject: SubjectKey.PRECALCULUS,
      id: collection.id,
    })
    // Get first asset and its first item
    const assetNameOne = collection.assetIDs[0]
    const firstItemOfAssetOne = getFirstItemNameFromAsset(assetNameOne)
    // Expand asset and deactivate first item
    await nextGenAssemblyUnitAuthoringPage.expandRowButton(assetNameOne).click()
    await nextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(
      firstItemOfAssetOne,
      'Deactivate'
    )
    // Verify deactivation in collection
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'Item successfully deactivated',
      })
    ).toBeVisible()
    const deactivatedIconLocator = await nextGenAssemblyUnitAuthoringPage.deactivateItemIcon(
      firstItemOfAssetOne
    )
    await expect(deactivatedIconLocator).toBeVisible()
    // Send collection assets to Assembly Unit
    await collectionPage.allCheckboxes.first().check() // select all assets
    await collectionPage.actionsDropdown.button.click()
    await collectionPage.actionsDropdown.items.sendAssets.hover()
    await collectionPage.actionsDropdown.items.sendAssetsToAU.click()
    await collectionPage.addToAUDialog.content.searchBar.fill(baseAUName)
    await collectionPage.addToAUDialog.content.getSearchResultByIndex(0).click()
    await collectionPage.addToAUDialog.buttons.add.click()
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_ADD_ASSETS_FROM_COLLECTION
    )
    await expect(
      collectionPage.toastDialogLocator.filter({
        hasText: `Successfully saved 5 assets from ${baseAUName}`,
      })
    ).toBeVisible()
    // Verify assets were added
    const allAssetsAfterAdd = await nextGenAssemblyUnitAuthoringPage.allAssets.allInnerTexts()
    expect(allAssetsAfterAdd).toEqual(expect.arrayContaining(collection.assetIDs))
    // Verify deactivation propagation across all Assembly Unit types
    const auTypes = [
      { id: baseAUId, name: baseAUName },
      { id: sequenceAUId, name: sequenceAUName },
      { id: completeAUId, name: completeAUName },
    ]
    for (const auType of auTypes) {
      // Navigate to the Assembly Unit
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SubjectKey.PRECALCULUS,
        id: auType.id,
      })
      if (auType.id !== baseAUId) {
        await waitForResponseFrom(page, endpoints.READ_EVALUATION_METRICS_CALCULATIONS)
      }
      // Expand asset and verify deactivation icon
      await nextGenAssemblyUnitAuthoringPage.expandRowButton(assetNameOne).click()
      const auDeactivatedIconLocator = await nextGenAssemblyUnitAuthoringPage.deactivateItemIcon(
        firstItemOfAssetOne
      )
      await expect(auDeactivatedIconLocator).toBeVisible()
    }
  })
})
/**
 * Extracts the first item name from an asset name by incrementing the numeric suffix
 * @param assetName Asset name to get corresponding item name from
 * @returns First item name derived from asset name
 */
function getFirstItemNameFromAsset(assetName: string): string {
  const numberPart = assetName.match(/\d+$/)?.[0] // Extract the numeric part from the end
  if (numberPart) {
    const incrementedNumber = parseInt(numberPart, 10) + 1 // Increment the number
    const firstPartOfAssetName = assetName.slice(0, -numberPart.length)
    return firstPartOfAssetName + incrementedNumber.toString().padStart(numberPart.length, '0')
  }
  console.error('No numeric part found at the end of the asset name.')
  return assetName // Fallback to original asset name
}
