import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { PreviewPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier, SubjectKey } from '@shared/config'

test('Collection Preview', async ({ context, dataController, collectionPage, page }) => {
  await test.step('Go to page with specific subject and collection', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 5,
      collectionProps: { name: 'csvExportCollection' },
    })) as E2ECollection[]
    if (collection) {
      await collectionPage.goToPageWith({ subject: SubjectKey.PRECALCULUS, id: collection.id })
    }
    test.fail(!collection, 'Failed to create a collection')
  })

  await test.step('Assert order of assets on asset page and ensure order on preview page is maintained in dropdown', async () => {
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    const tableRows = await collectionPage.tableRows.all()
    expect(tableRows.length).toEqual(5)
    const originalAssetOrder = (
      await Promise.all(
        tableRows.map(async (row) => {
          return row.getByLabel('id-column-value', { exact: true }).allInnerTexts()
        })
      )
    ).flat()

    console.log(`Original Asset Order:: ${originalAssetOrder}`)

    const [newPage] = await Promise.all([
      context.waitForEvent('page'),
      await collectionPage.previewButtonGeneric.click(),
    ])
    const collectionPreviewPage = new PreviewPage({ page: newPage })

    await newPage.waitForURL('**/preview/**')
    await expect(collectionPreviewPage.contents.assetTitle).toContainText('Preview Asset:')
    await collectionPreviewPage.contents.assetDropdown.click()

    // TODO - this and others like it can just use allTextContents()
    const options = await collectionPreviewPage.contents.selectOptionDropdown.evaluateAll(
      (options) => options.map((opt) => opt.textContent)
    )

    if (options.length !== 5) {
      throw new Error(`Expected 5 options, but found ${options.length} options.`)
    }

    console.log('Options:', options)

    const cleanedOptions = options.map((option) => option?.split(') ')[1])
    if (originalAssetOrder.length !== 0 && cleanedOptions.length !== 0) {
      for (let i = 0; i < originalAssetOrder.length; i++) {
        if (originalAssetOrder[i] !== cleanedOptions[i]) {
          throw new Error(
            `Mismatch found at position ${i}. Expected: ${originalAssetOrder[i]}, Found: ${cleanedOptions[i]}`
          )
        }
      }
      console.log('Verification successful: Asset order matches options.')
    } else {
      console.error('Asset order or options list is empty.')
    }

    await expect(collectionPreviewPage.contents.nextButton).toBeVisible()
    await expect(collectionPreviewPage.contents.prevButton).toBeVisible()
    await expect(collectionPreviewPage.contents.prevButton).toBeDisabled()

    await collectionPreviewPage.contents.showInfoButton.click()
    async function verifyAssetAndClickNext(assetId: string, isLastAsset: boolean) {
      try {
        const assetIdMatchOnPrefix = new RegExp(`${assetId.slice(0, -6)}[0-9]{6}`)
        const infoPanel = collectionPreviewPage.contents.infoPanel
        await expect(infoPanel).toBeVisible()

        const infoPanelDropdownOptions = infoPanel.dropdown.locator('option')

        const optionsText = await infoPanelDropdownOptions.allTextContents()
        expect(optionsText).not.toHaveLength(0)
        for (const optionText of optionsText) {
          expect(optionText).toMatch(assetIdMatchOnPrefix)
        }

        const nextButton = collectionPreviewPage.contents.nextButton
        await expect(nextButton).toBeVisible()

        if (!isLastAsset) {
          await nextButton.click()
        }
      } catch (error) {
        console.error(`Error verifying asset ${assetId} and clicking next:`, error)
        throw error
      }
    }
    for (const [index, assetId] of originalAssetOrder.entries()) {
      const isLastAsset = index === originalAssetOrder.length - 1
      await verifyAssetAndClickNext(assetId, isLastAsset)
    }
  })
})
