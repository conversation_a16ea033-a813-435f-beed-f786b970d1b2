import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Bulk update metadata of assets in a collection', async ({
  collectionPage,
  page,
  dataController,
}) => {
  const toast = collectionPage.toastLocator
    .filter({
      hasText: `5 assets have been updated to`,
    })
    .first()
  await test.step('Go to page with specific subject and collection', async () => {
    const [bulkMetadataUpdateCollection] = (await dataController.generate(
      DataControllerModel.COLLECTION,
      {
        user: userE2E,
        courseIDs: [SubjectIdentifier.PRECALCULUS],
        length: 1,
        assetLength: 5,
        collectionProps: { name: 'bulkTransitionCollection' },
        workflowSteps: [WorkflowStepID.INTERNAL_REVIEW],
      }
    )) as E2ECollection[]
    if (bulkMetadataUpdateCollection) {
      const pageLoadPromise = waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
      await collectionPage.goToPageWith({
        subject: SubjectKey.PRECALCULUS,
        id: bulkMetadataUpdateCollection.id,
      })
      await pageLoadPromise
    }
    test.fail(!bulkMetadataUpdateCollection, 'Failed to create collection')
  })

  await test.step('Check assets loaded and open bulk metadata update dialog', async () => {
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    const tableRows = await collectionPage.tableRows.all()
    expect(tableRows.length).toEqual(5)

    await collectionPage.allCheckboxes.first().click()
    await collectionPage.actionsDropdown.button.click()
    await expect(collectionPage.actionsDropdown.items.bulkUpdate).toBeVisible()
    await collectionPage.actionsDropdown.items.bulkUpdate.click()
    await expect(collectionPage.bulkUpdateDialog.contentContainer).toContainText('Update metadata')
    await expect(collectionPage.bulkUpdateDialog.contentContainer).toContainText(
      'How would you like to update these 5 assets?'
    )
  })

  await test.step('Bulk Update all assets in collection Question Sub-Type value to Single required', async () => {
    const stimuliOption = 'Question Sub-Type'
    const content = await collectionPage.bulkUpdateDialog.content
    await content.metadataField.selectOption(stimuliOption)
    await content.metadataValue.click()
    await page.getByLabel('Question Sub-Type options').waitFor({ state: 'visible' })
    const optionsList = page.getByLabel('Question Sub-Type options')
    await optionsList.hover()
    await optionsList.getByLabel('MCQ: Single-Select').click()
    await expect(collectionPage.bulkUpdateDialog.buttons.save).toBeEnabled({ timeout: 750 })
    await collectionPage.bulkUpdateDialog.buttons.save.click()
    await collectionPage.mainLocator.getByRole('button', { name: 'Continue' }).click()

    const toastText = await toast.innerText()
    expect(toastText).toContain('"MCQ: Single-Select"')
    await toast.getByRole('button').click()
  })

  await test.step('Bulk Update 5 assets for Assessment Type with multiple items selected', async () => {
    await collectionPage.allCheckboxes.first().check({ timeout: 750 })
    await collectionPage.actionsDropdown.button.click()
    const bulkUpdateButtonLocator = collectionPage.actionsDropdown.items.bulkUpdate
    await expect(bulkUpdateButtonLocator).toBeEnabled({ timeout: 750 })
    await bulkUpdateButtonLocator.click()
    const stimuliOption = 'Assessment Type'
    const content = await collectionPage.bulkUpdateDialog.content
    await content.metadataField.selectOption(stimuliOption)
    await page.locator('.ant-select-selector').dblclick()
    const optionsList = page.getByRole('tree')
    await optionsList.hover()
    await optionsList.getByText('Formative').click()
    await optionsList.getByText('Summative').click()

    await expect(collectionPage.bulkUpdateDialog.buttons.save).toBeEnabled({ timeout: 750 })
    await collectionPage.bulkUpdateDialog.buttons.save.click()
    await collectionPage.mainLocator.getByRole('button', { name: 'Continue' }).click()

    const toastText = await toast.innerText()
    expect(toastText).toContain('"Formative, Summative"')
    await toast.getByRole('button').click()
  })
})
