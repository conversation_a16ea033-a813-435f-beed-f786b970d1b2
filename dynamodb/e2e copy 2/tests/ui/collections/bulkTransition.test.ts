import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
const ASSET_COUNT = 50
const COLLECTION_NAME = 'bulkTransitionCollection'
const TARGET_WORKFLOW = 'Ready for Assembly'
test('Bulk action transition assets of random workflow state to ready for assembly', async ({
  collectionPage,
  page,
  dataController,
}) => {
  let collection: E2ECollection | undefined
  await test.step('Create collection with test assets', async () => {
    const [generatedCollection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: ASSET_COUNT,
      collectionProps: { name: COLLECTION_NAME },
      workflowSteps: [WorkflowStepID.READY_FOR_USE, 'RANDOM'],
    })) as E2ECollection[]
    collection = generatedCollection
    test.fail(!collection, 'Failed to create assets')
  })
  await test.step('Navigate to collection page', async () => {
    if (!collection) return
    const pageLoadPromise = waitForResponseFrom(page, 'assetsReadManyByIds')
    await collectionPage.goToPageWith({
      subject: SubjectKey.PRECALCULUS,
      id: collection.id,
    })
    await pageLoadPromise
  })
  await test.step('Transition assets to ready for assembly and verify results', async () => {
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    const tableRows = await collectionPage.tableRows.all()
    expect(tableRows.length).toEqual(ASSET_COUNT)
    const workflowStepsToIds = await Promise.all(
      tableRows.map(async (row) => ({
        workflowStep: await row
          .getByLabel('workflowStep-column-value', { exact: true })
          .innerText(),
        idValue: await row.getByLabel('id-column-value', { exact: true }).innerText(),
      }))
    )
    const alreadyInTargetState = workflowStepsToIds.filter(
      ({ workflowStep }) => workflowStep === TARGET_WORKFLOW
    )
    expect(alreadyInTargetState.length).not.toEqual(ASSET_COUNT)
    await collectionPage.allCheckboxes.first().check()
    await collectionPage.actionsDropdown.button.click()
    await collectionPage.actionsDropdown.items.transition.click()
    await collectionPage.transitionDialog.content.toSelect.selectOption(TARGET_WORKFLOW)
    await collectionPage.transitionDialog.buttons.apply.click()
    await waitForResponseFrom(page, endpoints.ASSETS_TRANSITION_WORKFLOW_MANY)
    const expectedTransitionedCount = ASSET_COUNT - alreadyInTargetState.length
    const successToast = collectionPage.toastLocator.filter({
      hasText: `${expectedTransitionedCount} assets transitioned to "${TARGET_WORKFLOW}"`,
    })
    await expect(successToast).toBeVisible()
    if (alreadyInTargetState.length > 0) {
      const errorToast = collectionPage.toastLocator.filter({
        hasText: 'Unable to transition assets:',
      })
      await expect(errorToast).toBeVisible()
      for (const { idValue } of alreadyInTargetState) {
        await expect(errorToast).toContainText(idValue)
      }
    }
  })
})
