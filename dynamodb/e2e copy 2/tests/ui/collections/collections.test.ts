import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import type { E2EAsset } from '@e2e/lib'
import type { CollectionListPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import type { Page } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'

const COLLECTIONS_READ = 'collectionsRead'
const newE2ECollection = `E2E ${casual.title}`

const navigateToPrecalPage = async (collectionListPage: CollectionListPage, page: Page) => {
  const responsePromise = waitForResponseFrom(page, COLLECTIONS_READ)
  await collectionListPage.goToPageWith({ subject: 'Precalculus' })
  await responsePromise
}

test.beforeEach(async ({ dataController, currentUser }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    length: 55,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
})

test('Verify all collection cards are visible on collections page', async ({
  collectionPage,
  collectionListPage,
  collectionAddAssetsPage,
  page,
}) => {
  const collectionCardList = collectionListPage.mainLocator.getByTestId('collection-card')
  await test.step('Go to page with specific subject', async () => {
    await navigateToPrecalPage(collectionListPage, page)
  })

  await test.step('Assert Collections Header', async () => {
    const collectionHeaderText = 'AP Precalculus'
    await expect(collectionListPage.titleHeading).toBeVisible()
    const courseHeading = collectionListPage.mainLocator.getByRole('heading', {
      level: 2,
      name: collectionHeaderText,
    })
    await expect(courseHeading).toContainText(collectionHeaderText)
  })

  await test.step('Create collection with assets', async () => {
    await collectionListPage.newCollectionButton.click()
    const { newCollectionDialog } = collectionListPage
    // TODO: remove input.type since it's deprecated
    await newCollectionDialog.content.nameInput.fill(newE2ECollection)
    await newCollectionDialog.buttons.apply.click()
    await waitForResponseFrom(page, endpoints.COLLECTIONS_READ_ONE)
    await expect(collectionPage.previewButtonGeneric).toBeDisabled()
    await collectionAddAssetsPage.doThenWaitForPageLoadNetworkCall(async () => {
      await collectionPage.addAssetsButton.click()

      await expect(collectionAddAssetsPage.getAddAssetsStatusBar(newE2ECollection)).toBeVisible()
    })
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(2000)
    await collectionAddAssetsPage.table.allCheckBoxes.first().check()
    await collectionAddAssetsPage.getAddAssetsButton({ assetCount: 50 }).click({ timeout: 500 })
    await waitForResponseFrom(page, endpoints.COLLECTIONS_READ_ONE)

    await collectionAddAssetsPage.goToPage(2)
    await collectionAddAssetsPage.table.allCheckBoxes.last().check()

    await collectionAddAssetsPage.getAddAssetsButton({ assetCount: 1 }).click({ timeout: 500 })
    await waitForResponseFrom(page, endpoints.COLLECTIONS_READ_ONE)
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(2000)
    page.on('dialog', (dialog) => dialog.accept())
    await collectionAddAssetsPage.doneButton.click()

    // Convert each row to an object with key-value pairs for each column
    await collectionPage.tableRows.last().waitFor({ state: 'visible' })
    const tableRows = await collectionPage.tableRows.all()

    await expect.poll(() => tableRows.length).toBe(51)
  })

  await test.step('Navigate to collections page and ensure newly collection with assets is visible', async () => {
    await navigateToPrecalPage(collectionListPage, page)
    const newCollectionCard = collectionCardList
      .getByRole('region')
      .filter({ hasText: newE2ECollection })
    const newCollectionCount = newCollectionCard.getByText('51 Assets')
    await expect(newCollectionCard).toBeVisible()
    await expect(newCollectionCount).toBeVisible()
  })

  await test.step('Click on newly created collection', async () => {
    const newCollectionCard = collectionCardList
      .getByRole('region')
      .filter({ hasText: newE2ECollection })
    const openButton = newCollectionCard.getByRole('button', { name: 'Open' })
    await openButton.click()
  })

  await test.step('Rename newly created collection', async () => {
    const renamedCollection = 'Renamed E2E Collection'
    // assert existing name
    await expect(collectionPage.topHeading).toHaveText(newE2ECollection)

    await collectionPage.actionsDropdown.button.click()

    //click rename asset
    await collectionPage.actionsDropdown.items.rename.click()

    //input new name
    await collectionPage.renameDialog.content.nameInput.fill(renamedCollection)

    //click apply button
    await collectionPage.renameDialog.buttons.apply.click()
    await waitForResponseFrom(page, endpoints.COLLECTIONS_RENAME)
    await waitForResponseFrom(page, endpoints.COLLECTIONS_READ_ONE)

    //verify title of collection is renamed collection
    await expect(collectionPage.topHeading).toHaveText(renamedCollection)
    await expect(page.getByText('Saved!')).toBeVisible()
    //verify title of collection is renamed collection
    await expect(collectionPage.topHeading).toHaveText(renamedCollection)

    //assert collection still has expected number of assets
    expect(await collectionPage.getAssetCount()).toBe(51)
  })
})
