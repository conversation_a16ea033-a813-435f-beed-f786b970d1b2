import { test } from '@e2e/cb-test'
import { getIsExternal } from '@e2e/util/envUtils'
import { getMainUser } from '@e2e/util/userUtils'

// Don't use the pre-authenticated state
test.use({ storageState: { cookies: [], origins: [] } })

const user = getMainUser()

test('should login and load successfully', async ({ homePage, msLoginPage, zscalerLoginPage }) => {
  test.skip(process.env.ENV === 'local', 'Local works differently for login')

  console.info('Logging in...')
  await homePage.goToPageWith()
  if (getIsExternal()) {
    console.info('Logging in to ZScaler...')
    await zscalerLoginPage.login({
      email: user.email,
    })
  }
  await msLoginPage.login({
    email: user.email,
    password: await user.getPassword(),
  })

  console.info('Waiting for programs page to load/be visible...')
  await homePage.mainLocator.getByText('Programs').waitFor()
})
