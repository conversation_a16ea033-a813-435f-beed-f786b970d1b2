import { test as base, expect } from '@e2e/cb-test'
import type { ConsolidatedFixtures } from '@e2e/fixtures/types'

const test = base.extend<{ accountDropdown: ConsolidatedFixtures['topBar']['accountDropdown'] }>({
  accountDropdown: async ({ topBar }, use) => {
    // Open the menu for use
    await topBar.accountDropdownButton.click()
    const { accountDropdown } = topBar
    await use(accountDropdown)
  },
})

test.beforeEach(async ({ homePage, page }) => {
  const responsePromise = page.waitForResponse(
    (response) => response.url().includes('/auth/auth') && response.status() === 200
  )
  await homePage.goToPageWith()
  await responsePromise
})

test('should open', async ({ topBar }) => {
  const { accountDropdown } = topBar
  await expect(accountDropdown.root).toBeHidden()
  await topBar.accountDropdownButton.click()
  await expect(accountDropdown.root).toBeVisible()
})

test('should close', async ({ topBar, accountDropdown }) => {
  await expect(accountDropdown.root).toBeVisible()
  await topBar.root.click() // Click in empty middle area
  await expect(accountDropdown.root).toBeHidden()
})

// Will probably want to handle different role cases, whether here
// or at a lower level. Or perhaps one other case using the lowest role
test('should display the user role', async ({ accountDropdown }) => {
  await expect(accountDropdown.roleDisplay).toHaveText('Admin')
})
