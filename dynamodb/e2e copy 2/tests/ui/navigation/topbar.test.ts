import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ newAssetPage }) => {
  // Going to a different page than home page in order to test home link
  await newAssetPage.goToPageWith()
})

test('Hummingbird link should redirect back to home', async ({ topBar, page, homePage }) => {
  const homeUrl = homePage.url()
  await expect(page).not.toHaveURL(homeUrl)
  await topBar.homeLink.click()
  await expect(page).toHaveURL(homeUrl)
})

test('Burger button should open side menu', async ({ topBar, sideMenu }) => {
  await expect(sideMenu.root).toBeHidden()
  await topBar.burgerButton.click()
  await expect(sideMenu.root).toBeVisible()
})
