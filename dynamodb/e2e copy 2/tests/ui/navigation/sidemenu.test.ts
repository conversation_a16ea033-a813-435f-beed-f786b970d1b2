import { expect, test } from '@e2e/cb-test'

// Do we want a test to use the accessible(?) burger menu button that seems
// to be hidden with the burger menu high level elements?

test.beforeEach(async ({ homePage, topBar, page }) => {
  const responsePromise = page.waitForResponse(
    (response) => response.url().includes('/auth/auth') && response.status() === 200
  )
  await homePage.goToPageWith()
  await responsePromise
  await topBar.burgerButton.click()
})

test(`should close by clicking X`, async ({ sideMenu }) => {
  await sideMenu.closeButton.click()
  await expect(sideMenu.root).toBeHidden()
})

test(`should close by clicking outside of it`, async ({ sideMenu }) => {
  await sideMenu.clickOutside()
  await expect(sideMenu.root).toBeHidden()
})

test.fixme('should display username correctly', () => {
  throw 'To be implemented'
})

test('Home link should navigate to home correctly', async ({
  sideMenu,
  newAssetPage,
  page,
  topBar,
  homePage,
}) => {
  // Make sure go to another page first
  newAssetPage.goToPageWith()
  await topBar.burgerButton.click()

  const homeUrl = homePage.url()
  await expect(page).not.toHaveURL(homeUrl)
  await sideMenu.homeLink.click()
  await expect(page).toHaveURL(homeUrl)
})

test('Create an Asset link should navigate to new asset page correctly', async ({
  sideMenu,
  newAssetPage,
  page,
}) => {
  await sideMenu.createAssetLink.click()
  await expect(page).toHaveURL(newAssetPage.url())
})
;[{ subject: 'Precalculus' }, { subject: 'Art and Design' }].forEach(({ subject }) => {
  test(`${subject} Assets link should navigate to asset list correctly`, async ({
    sideMenu,
    assetListPage,
    page,
  }) => {
    await sideMenu.getAssetsLinkBySubject(subject).click()
    await expect(page).toHaveURL(assetListPage.url({ subject }))
  })
})

test.fixme('{Subject} Collections link should navigate to collection list correctly', () => {
  return
})
