import casual from 'casual'
import fs from 'fs'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAssemblyUnit, E2EAsset, E2ECollection, E2EForm } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { ItemType } from '@shared/data-generation/mock'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

// Test configuration
const SUBJECT_NAME = 'African American Studies'
const SUBJECT_KEY = SubjectKey.AFRICAN_AMERICAN_STUDIES
const SUBJECT_ID = SubjectIdentifier.AFRICAN_AMERICAN_STUDIES
const ADMIN_YEAR = '25'

// Test asset IDs that will be used throughout the test
const TEST_ASSET_IDS = ['IJW000001', 'IJW000002', 'IJW000003', 'IJW000004', 'IJW000005']

// Assembly Unit names
const BASE_AU_NAME_1 = 'AU_BASE_001'
const BASE_AU_NAME_2 = 'AU_BASE_002'
const SEQ_AU_NAME = 'AU_SEQ_001'
const COMPLETE_AU_NAME_1 = 'AU_COMPLETE_001'
const COMPLETE_AU_NAME_2 = 'AU_COMPLETE_002'

// Use Code values for testing
const USE_CODES = {
  EQUATING: 'Equating',
  EQUATING_2: 'Equating 2',
  EQUATING_3: 'Equating 3',
  OPERATIONAL_PRETEST: 'Operational Pretest',
  OPERATIONAL: 'Operational',
  RESEARCH: 'Research',
  FORMATIVE: 'Formative'
}

// Configure test timeout and retries
test.setTimeout(10 * 60 * 1000) // 10 minutes
test.describe.configure({ retries: 3 })



test.describe('Use Code Feature Workflow', () => {
  let testAssets: E2EAsset[]
  let testCollection: E2ECollection
  let baseAU1: E2EAssemblyUnit
  let baseAU2: E2EAssemblyUnit
  let seqAU: E2EAssemblyUnit
  let completeAU1: E2EAssemblyUnit
  let completeAU2: E2EAssemblyUnit
  let testForm: E2EForm

  test.beforeAll(async ({ dataController }) => {
    // Generate test assets with specific IDs
    testAssets = (await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      assetProps: {
        itemType: ItemType.MultipleChoiceQuestion,
        itemCount: 1
      },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
      length: TEST_ASSET_IDS.length,
      customIds: TEST_ASSET_IDS
    })) as E2EAsset[]

    // Generate test collection
    testCollection = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assetLength: 3,
      collectionProps: { name: 'Use Code Test Collection' },
      workflowSteps: [WorkflowStepID.READY_FOR_USE]
    }))[0] as E2ECollection

    // Pre-generate Base Assembly Units
    baseAU1 = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assemblyUnitProps: {
        name: BASE_AU_NAME_1,
        type: AssemblyUnitType.BASE,
        adminYear: ADMIN_YEAR,
        baseForm: 'A',
        questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
        questionTypeNumber: 1
      },
      assetIds: [TEST_ASSET_IDS[0], TEST_ASSET_IDS[1], TEST_ASSET_IDS[3]] // IJW000001, IJW000002, IJW000004
    }))[0] as E2EAssemblyUnit

    baseAU2 = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assemblyUnitProps: {
        name: BASE_AU_NAME_2,
        type: AssemblyUnitType.BASE,
        adminYear: ADMIN_YEAR,
        baseForm: 'B',
        questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
        questionTypeNumber: 2
      },
      assetIds: [TEST_ASSET_IDS[2], TEST_ASSET_IDS[4]] // IJW000003, IJW000005
    }))[0] as E2EAssemblyUnit

    // Pre-generate Sequence AU (inherits from Base AU 1)
    seqAU = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assemblyUnitProps: {
        name: SEQ_AU_NAME,
        type: AssemblyUnitType.SEQUENCE,
        adminYear: ADMIN_YEAR,
        baseForm: 'A',
        questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
        questionTypeNumber: 1,
        parentAssemblyUnitId: baseAU1.id
      }
    }))[0] as E2EAssemblyUnit

    // Pre-generate Complete AU 1 (inherits from Base AU 1)
    completeAU1 = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assemblyUnitProps: {
        name: COMPLETE_AU_NAME_1,
        type: AssemblyUnitType.COMPLETE,
        adminYear: ADMIN_YEAR,
        baseForm: 'A',
        questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
        questionTypeNumber: 1,
        parentAssemblyUnitId: baseAU1.id
      }
    }))[0] as E2EAssemblyUnit

    // Pre-generate Complete AU 2 (standalone, no inheritance)
    completeAU2 = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      assemblyUnitProps: {
        name: COMPLETE_AU_NAME_2,
        type: AssemblyUnitType.COMPLETE,
        adminYear: ADMIN_YEAR,
        baseForm: 'C',
        questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
        questionTypeNumber: 3
      },
      assetIds: [TEST_ASSET_IDS[0], TEST_ASSET_IDS[1]] // IJW000001, IJW000002
    }))[0] as E2EAssemblyUnit

    // Pre-generate Form tied to Complete AU 1
    testForm = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SUBJECT_ID],
      length: 1,
      formProps: {
        name: `Form for ${COMPLETE_AU_NAME_1}`,
        assemblyUnitId: completeAU1.id
      }
    }))[0] as E2EForm
  })

  test('Complete Use Code Workflow', async ({
    collectionListPage,
    collectionPage,
    nextGenAssemblyUnitListPage,
    nextGenAssemblyUnitAuthoringPage,
    formListPage,
    formPage,
    courseSettingsPage,
    page
  }) => {
    
    await test.step('1. Collections Screen Verification - Use Code column visible', async () => {
      await collectionListPage.goToPageWith({ subject: SUBJECT_NAME })
      await waitForResponseFrom(page, endpoints.COLLECTIONS_READ)
      
      // Navigate to a specific collection to verify Use Code column
      await collectionPage.goToPageWith({ 
        subject: SUBJECT_KEY, 
        id: testCollection.id 
      })
      await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
      
      // Verify Use Code column is visible in the table
      await expect(collectionPage.columnHeader('Use Code')).toBeVisible()
    })

    await test.step('2. Navigate to Assembly Units and verify Use Code column', async () => {
      await nextGenAssemblyUnitListPage.goToPageWith({ subject: SUBJECT_NAME })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)
      
      // Verify Use Code column exists in AU listing
      const useCodeColumn = page.getByRole('columnheader', { name: 'Use Code' })
      await expect(useCodeColumn).toBeVisible()
    })

    await test.step('3. Navigate to pre-generated Base AU and verify Use Code column', async () => {
      // Navigate directly to the pre-generated Base AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: baseAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify Use Code column exists in asset table (initially blank)
      const useCodeColumn = page.getByRole('columnheader', { name: 'Use Code' })
      await expect(useCodeColumn).toBeVisible()

      // Verify Use Code cells are initially blank for all assets
      const assetIds = [TEST_ASSET_IDS[0], TEST_ASSET_IDS[1], TEST_ASSET_IDS[3]] // Assets in baseAU1
      for (const assetId of assetIds) {
        const useCodeCell = page.getByLabel(`${assetId}-use-code-cell`)
        await expect(useCodeCell).toBeEmpty()
      }
    })

    await test.step('4. Individual Item Use Code Assignment - IJW000001 to Equating', async () => {
      // Find the asset row and open kebab menu
      const assetRow = nextGenAssemblyUnitAuthoringPage.assetTableRow('IJW000001')
      const kebabButton = assetRow.getByRole('button', { name: 'Dropdown button IJW000001' })
      await kebabButton.click()

      // Click Edit Use Code option
      const editUseCodeOption = page.getByRole('menuitem', { name: 'Edit Use Code' })
      await editUseCodeOption.click()

      // Select "Equating" from the dropdown
      const useCodeSelect = page.getByLabel('Use Code')
      await useCodeSelect.selectOption(USE_CODES.EQUATING)

      // Save the change
      const saveButton = page.getByRole('button', { name: 'Save' })
      await saveButton.click()

      // Verify the Use Code cell updates
      const useCodeCell = page.getByLabel('IJW000001-use-code-cell')
      await expect(useCodeCell).toHaveText(USE_CODES.EQUATING)

      // Refresh page and confirm persistence
      await page.reload()
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
      await expect(useCodeCell).toHaveText(USE_CODES.EQUATING)
    })

    await test.step('5. Bulk Update Operations - IJW000002 to Operational Pretest', async () => {
      // Select asset using checkbox (only IJW000002 since IJW000003 is in different AU)
      const asset2Checkbox = page.getByLabel('IJW000002-checkbox')
      await asset2Checkbox.check()

      // Open bulk actions dropdown
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
      const bulkUpdateButton = nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.bulkUpdate
      await expect(bulkUpdateButton).toBeEnabled()
      await bulkUpdateButton.click()

      // Select Use Code field and value
      const metadataField = page.getByLabel('Metadata Field')
      await metadataField.selectOption('Use Code')

      const useCodeValue = page.getByLabel('Use Code Value')
      await useCodeValue.selectOption(USE_CODES.OPERATIONAL_PRETEST)

      // Apply the bulk update
      const applyButton = page.getByRole('button', { name: 'Apply' })
      await applyButton.click()

      // Verify IJW000002 shows "Operational Pretest" while IJW000001 remains "Equating"
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')

      await expect(asset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST)
      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING)

      // Refresh and confirm persistence
      await page.reload()
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
      await expect(asset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST)
      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING)
    })

    await test.step('6. Mixed Item-Level Updates', async () => {
      // Update IJW000004 to "Research" via kebab menu
      const asset4Row = nextGenAssemblyUnitAuthoringPage.assetTableRow('IJW000004')
      const kebab4Button = asset4Row.getByRole('button', { name: 'Dropdown button IJW000004' })
      await kebab4Button.click()

      const editUseCode4 = page.getByRole('menuitem', { name: 'Edit Use Code' })
      await editUseCode4.click()

      const useCodeSelect4 = page.getByLabel('Use Code')
      await useCodeSelect4.selectOption(USE_CODES.RESEARCH)

      const saveButton4 = page.getByRole('button', { name: 'Save' })
      await saveButton4.click()

      // Change IJW000001 from "Equating" to "Equating 2"
      const asset1Row = nextGenAssemblyUnitAuthoringPage.assetTableRow('IJW000001')
      const kebab1Button = asset1Row.getByRole('button', { name: 'Dropdown button IJW000001' })
      await kebab1Button.click()

      const editUseCode1 = page.getByRole('menuitem', { name: 'Edit Use Code' })
      await editUseCode1.click()

      const useCodeSelect1 = page.getByLabel('Use Code')
      await useCodeSelect1.selectOption(USE_CODES.EQUATING_2)

      const saveButton1 = page.getByRole('button', { name: 'Save' })
      await saveButton1.click()

      // Verify both changes
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')

      await expect(asset4UseCode).toHaveText(USE_CODES.RESEARCH)
      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_2)

      // Refresh and verify persistence
      await page.reload()
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
      await expect(asset4UseCode).toHaveText(USE_CODES.RESEARCH)
      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_2)
    })

    await test.step('7. Navigate to Base AU 2 and verify no inheritance', async () => {
      // Navigate to the pre-generated second Base AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: baseAU2.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify both items show blank Use Code cells (no inheritance)
      const asset3UseCode = page.getByLabel('IJW000003-use-code-cell')
      const asset5UseCode = page.getByLabel('IJW000005-use-code-cell')
      await expect(asset3UseCode).toBeEmpty()
      await expect(asset5UseCode).toBeEmpty()
    })

    await test.step('8. Navigate to Sequence AU and verify inheritance from Base AU', async () => {
      // Navigate to the pre-generated Sequence AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: seqAU.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify inherited Use Codes from Base AU
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')

      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_2)
      await expect(asset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST)
      await expect(asset4UseCode).toHaveText(USE_CODES.RESEARCH)
    })

    await test.step('9. Navigate to Complete AU and verify inheritance', async () => {
      // Navigate to the pre-generated Complete AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: completeAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify inherited Use Codes match Base AU values
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')

      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_2)
      await expect(asset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST)
      await expect(asset4UseCode).toHaveText(USE_CODES.RESEARCH)
    })

    await test.step('10. Test downstream propagation from Base AU to Complete AU', async () => {
      // Navigate back to Base AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: baseAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Change IJW000001 from "Equating 2" to "Equating 3"
      const asset1Row = nextGenAssemblyUnitAuthoringPage.assetTableRow('IJW000001')
      const kebab1Button = asset1Row.getByRole('button', { name: 'Dropdown button IJW000001' })
      await kebab1Button.click()

      const editUseCode1 = page.getByRole('menuitem', { name: 'Edit Use Code' })
      await editUseCode1.click()

      const useCodeSelect1 = page.getByLabel('Use Code')
      await useCodeSelect1.selectOption(USE_CODES.EQUATING_3)

      const saveButton1 = page.getByRole('button', { name: 'Save' })
      await saveButton1.click()

      // Navigate to Complete AU and verify automatic propagation
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: completeAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify propagation to Complete AU
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')

      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_3)
      await expect(asset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST) // unchanged
      await expect(asset4UseCode).toHaveText(USE_CODES.RESEARCH) // unchanged
    })

    await test.step('11. Complete AU Independent Updates - no upstream impact', async () => {
      // While still in Complete AU, bulk update IJW000002 and IJW000004 to "Formative"
      const asset2Checkbox = page.getByLabel('IJW000002-checkbox')
      const asset4Checkbox = page.getByLabel('IJW000004-checkbox')
      await asset2Checkbox.check()
      await asset4Checkbox.check()

      // Open bulk actions dropdown
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
      const bulkUpdateButton = nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.bulkUpdate
      await expect(bulkUpdateButton).toBeEnabled()
      await bulkUpdateButton.click()

      // Select Use Code field and value
      const metadataField = page.getByLabel('Metadata Field')
      await metadataField.selectOption('Use Code')

      const useCodeValue = page.getByLabel('Use Code Value')
      await useCodeValue.selectOption(USE_CODES.FORMATIVE)

      // Apply the bulk update
      const applyButton = page.getByRole('button', { name: 'Apply' })
      await applyButton.click()

      // Verify Complete AU values changed
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')
      await expect(asset2UseCode).toHaveText(USE_CODES.FORMATIVE)
      await expect(asset4UseCode).toHaveText(USE_CODES.FORMATIVE)

      // Navigate back to Base AU and verify values remain unchanged (no upstream impact)
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: baseAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify Base AU values unchanged
      const baseAsset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const baseAsset4UseCode = page.getByLabel('IJW000004-use-code-cell')
      await expect(baseAsset2UseCode).toHaveText(USE_CODES.OPERATIONAL_PRETEST)
      await expect(baseAsset4UseCode).toHaveText(USE_CODES.RESEARCH)
    })

    await test.step('12. Form-Level Use Code Editing', async () => {
      // Navigate directly to the pre-generated form
      await formPage.goToPageWith({
        subject: SUBJECT_KEY,
        id: testForm.id
      })
      await waitForResponseFrom(page, endpoints.FORM_READ_BY_ID)

      // Verify Use Code column matches Complete AU values
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      const asset4UseCode = page.getByLabel('IJW000004-use-code-cell')

      await expect(asset1UseCode).toHaveText(USE_CODES.EQUATING_3)
      await expect(asset2UseCode).toHaveText(USE_CODES.FORMATIVE)
      await expect(asset4UseCode).toHaveText(USE_CODES.FORMATIVE)

      // Edit IJW000001 from "Equating 3" to "Operational" within the form
      const asset1Row = formPage.assetTableRow('IJW000001')
      const kebab1Button = asset1Row.getByRole('button', { name: 'Dropdown button IJW000001' })
      await kebab1Button.click()

      const editUseCode1 = page.getByRole('menuitem', { name: 'Edit Use Code' })
      await editUseCode1.click()

      const useCodeSelect1 = page.getByLabel('Use Code')
      await useCodeSelect1.selectOption(USE_CODES.OPERATIONAL)

      const saveButton1 = page.getByRole('button', { name: 'Save' })
      await saveButton1.click()

      // Verify admin-level editability worked
      await expect(asset1UseCode).toHaveText(USE_CODES.OPERATIONAL)
    })

    await test.step('13. AU Consistency Verification after form edits', async () => {
      // Navigate back to Base AU and confirm original values maintained
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: baseAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify Base AU maintains original values after form edits
      const baseAsset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      await expect(baseAsset1UseCode).toHaveText(USE_CODES.EQUATING_3)

      // Navigate to Complete AU and verify it reflects form changes appropriately
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: completeAU1.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify Complete AU reflects form changes
      const completeAsset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      await expect(completeAsset1UseCode).toHaveText(USE_CODES.OPERATIONAL)
    })

    await test.step('15. CSV Export Validation', async () => {
      // While in Complete AU, export CSV
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.export.click()
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.exportMetadata.click()

      // Wait for download and verify Use Code column exists
      const downloadPromise = page.waitForEvent('download')
      const download = await downloadPromise
      const downloadPath = await download.path()

      // Read CSV content and verify Use Code column exists with correct values
      const csvContent = fs.readFileSync(downloadPath, 'utf8')
      expect(csvContent).toContain('Use Code')
      expect(csvContent).toContain(USE_CODES.OPERATIONAL)
      expect(csvContent).toContain(USE_CODES.FORMATIVE)
    })

    await test.step('16. Navigate to standalone Complete AU with no inheritance', async () => {
      // Navigate to the pre-generated standalone Complete AU
      await nextGenAssemblyUnitAuthoringPage.goToPageWith({
        subject: SUBJECT_NAME,
        id: completeAU2.id
      })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Verify both items show blank Use Code (no inheritance)
      const asset1UseCode = page.getByLabel('IJW000001-use-code-cell')
      const asset2UseCode = page.getByLabel('IJW000002-use-code-cell')
      await expect(asset1UseCode).toBeEmpty()
      await expect(asset2UseCode).toBeEmpty()
    })

    await test.step('17. Evaluation Metrics Integration', async () => {
      // Navigate to Course Settings and check Eval Metrics tab
      await courseSettingsPage.goToPageWith()

      await courseSettingsPage.evaluationMetricsTab.click()
      await waitForResponseFrom(page, endpoints.EVALUATION_METRICS_READ)

      // Check that Use Code appears beside each asset
      const useCodeColumns = page.getByRole('columnheader', { name: 'Use Code' })
      await expect(useCodeColumns).toBeVisible()

      // Verify values match AU assignments for assets in the metrics
      const metricsTable = page.getByRole('table', { name: 'evaluation-metrics-table' })
      await expect(metricsTable).toBeVisible()
    })

    await test.step('18. Comparison Report Testing', async () => {
      // Navigate to Reports section
      const reportsLink = page.getByRole('link', { name: 'Reports' })
      await reportsLink.click()

      // Generate comparison report between AU_BASE_001 vs AU_COMPLETE_001
      const comparisonReportButton = page.getByRole('button', { name: 'Generate Comparison Report' })
      await comparisonReportButton.click()

      // Select Base AU and Complete AU for comparison
      const baseAUSelect = page.getByLabel('Base Assembly Unit')
      await baseAUSelect.selectOption(BASE_AU_NAME_1)

      const compareAUSelect = page.getByLabel('Compare Assembly Unit')
      await compareAUSelect.selectOption(COMPLETE_AU_NAME_1)

      const generateButton = page.getByRole('button', { name: 'Generate' })
      await generateButton.click()

      // Wait for report generation
      await waitForResponseFrom(page, endpoints.COMPARISON_REPORT_GENERATE)

      // Verify "Item Use Code" column appears on both sides
      const leftUseCodeColumn = page.getByRole('columnheader', { name: 'Item Use Code (Base)' })
      const rightUseCodeColumn = page.getByRole('columnheader', { name: 'Item Use Code (Compare)' })
      await expect(leftUseCodeColumn).toBeVisible()
      await expect(rightUseCodeColumn).toBeVisible()

      // Validate historical progression tracking
      const progressionIndicators = page.getByLabel('use-code-progression-indicator')
      await expect(progressionIndicators.first()).toBeVisible()
    })

    await test.step('19. Form Scoring Report Verification', async () => {
      // Navigate back to Forms and generate scoring report
      await formListPage.goToPageWith({ subject: SUBJECT_KEY })
      await waitForResponseFrom(page, endpoints.FORMS_READ)

      // Find form tied to Complete AU
      const formLink = page.getByRole('link', { name: new RegExp(COMPLETE_AU_NAME_1) })
      await formLink.click()
      await waitForResponseFrom(formPage.page, endpoints.FORM_READ_BY_ID)

      // Generate scoring report
      const actionsDropdown = formPage.actionsDropdown
      await actionsDropdown.button.click()
      await actionsDropdown.items.scoringReport.click()

      // Wait for report generation
      const downloadPromise = page.waitForEvent('download')
      const download = await downloadPromise
      const downloadPath = await download.path()

      // Verify Use Codes match AU_COMPLETE_001 values at scoring time
      const reportContent = fs.readFileSync(downloadPath, 'utf8')
      expect(reportContent).toContain('Use Code')
      expect(reportContent).toContain(USE_CODES.OPERATIONAL)
      expect(reportContent).toContain(USE_CODES.FORMATIVE)
    })

    await test.step('20. Asset Editor and Association Modal Verification', async () => {
      // Navigate to Asset Editor for one of our test assets
      const assetEditorUrl = `/assets/${TEST_ASSET_IDS[0]}/edit`
      await page.goto(assetEditorUrl)
      await waitForResponseFrom(page, endpoints.ASSET_READ_BY_ID)

      // Verify Use Code appears as read-only in Asset Editor
      const useCodeField = page.getByLabel('Use Code (Read Only)')
      await expect(useCodeField).toBeVisible()
      await expect(useCodeField).toBeDisabled()

      // Open Association Modal
      const associationsButton = page.getByRole('button', { name: 'View Associations' })
      await associationsButton.click()

      // Confirm read-only display in Association Modal
      const modalUseCodeField = page.getByLabel('Use Code (Read Only)')
      await expect(modalUseCodeField).toBeVisible()
      await expect(modalUseCodeField).toBeDisabled()
    })

    await test.step('21. Metadata Export Verification (AP2026)', async () => {
      // Navigate back to Complete AU
      await nextGenAssemblyUnitListPage.goToPageWith({ subject: SUBJECT_NAME })
      await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)

      const completeAULink = page.getByRole('link', { name: COMPLETE_AU_NAME_1 })
      await completeAULink.click()
      await waitForResponseFrom(nextGenAssemblyUnitAuthoringPage.page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)

      // Export AP2026 metadata
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.export.click()

      const ap2026ExportOption = page.getByRole('menuitem', { name: 'AP2026 Metadata (.xml)' })
      await ap2026ExportOption.click()

      // Wait for download and verify Use Code is excluded from AP2026 metadata exports
      const downloadPromise = page.waitForEvent('download')
      const download = await downloadPromise
      const downloadPath = await download.path()

      const xmlContent = fs.readFileSync(downloadPath, 'utf8')
      expect(xmlContent).not.toContain('UseCode')
      expect(xmlContent).not.toContain('use-code')
    })
  })

  test.afterAll(async ({ dataController }) => {
    // Clean up test data
    try {
      if (testAssets?.length) {
        await dataController.cleanup(DataControllerModel.ASSET, testAssets.map(asset => asset.id))
      }
      if (testCollection?.id) {
        await dataController.cleanup(DataControllerModel.COLLECTION, [testCollection.id])
      }
      if (baseAU1?.id) {
        await dataController.cleanup(DataControllerModel.ASSEMBLY_UNIT, [baseAU1.id])
      }
      if (baseAU2?.id) {
        await dataController.cleanup(DataControllerModel.ASSEMBLY_UNIT, [baseAU2.id])
      }
      if (seqAU?.id) {
        await dataController.cleanup(DataControllerModel.ASSEMBLY_UNIT, [seqAU.id])
      }
      if (completeAU1?.id) {
        await dataController.cleanup(DataControllerModel.ASSEMBLY_UNIT, [completeAU1.id])
      }
      if (completeAU2?.id) {
        await dataController.cleanup(DataControllerModel.ASSEMBLY_UNIT, [completeAU2.id])
      }
      if (testForm?.id) {
        await dataController.cleanup(DataControllerModel.FORM, [testForm.id])
      }
    } catch (error) {
      console.warn('Cleanup failed:', error)
    }
  })
})
