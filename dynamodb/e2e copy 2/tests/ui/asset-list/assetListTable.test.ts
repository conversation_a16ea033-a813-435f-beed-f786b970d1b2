import { expect, test } from '@e2e/cb-test'

const example = (...args: Parameters<typeof test>) => {
  // Do nothing, just an example
  return args
}

example(`should show some examples of what can be done`, async ({ assetListPage }) => {
  const { table } = assetListPage

  // Has filtering examples getting a row by id
  const rowWithId = table.allRows.filter({
    has: table.cellLocator({ columnName: 'id' }, { hasText: 'someid' }),
  })

  // Using an existing Locator object to chain within another to get a specific
  // cell within that row
  const createdByCellWithinRow = rowWithId.locator(table.cellLocator({ columnName: 'Created By' }))

  await expect(createdByCellWithinRow).toHaveText('mmorales')
})
