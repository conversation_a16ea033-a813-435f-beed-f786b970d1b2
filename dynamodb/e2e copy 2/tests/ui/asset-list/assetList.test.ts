import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const subject = 'Precalculus'

test.beforeEach(async ({ assetListPage, dataController }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 5,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
  await assetListPage.goToPageWith({ subject })
})

test('should have show the correct subject', async ({ assetListPage }) => {
  await expect(assetListPage.mainLocator.getByText(`AP ${subject}`).first()).toBeVisible()
})

test('should have content', async ({ assetListPage }) => {
  await expect(assetListPage.table.allRows).not.toHaveCount(0)
  // Adding in this check too since a 'row' would technically be there and the
  // above assertion would still pass even if still loading
  await expect(assetListPage.table.allRows.first()).not.toBeEmpty()
})

test('should have asset selection checkboxes', async ({ assetListPage }) => {
  await expect(assetListPage.table.allCheckBoxes).not.toHaveCount(0)
})

test('should select all checkboxes when header checkbox clicked', async ({ assetListPage }) => {
  const checkboxCount = await assetListPage.table.allCheckBoxes.count()

  await assetListPage.table.allCheckBoxes.first().check()

  for (let i = 0; i < checkboxCount; i++) {
    await expect(assetListPage.table.allCheckBoxes.nth(i)).toBeChecked()
  }
})
