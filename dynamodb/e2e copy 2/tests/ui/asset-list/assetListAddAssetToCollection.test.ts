import { expect, test } from '@e2e/cb-test'
import type { E2EAsset, E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetListPage, dataController, currentUser }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.READY_FOR_USE],
    length: 1,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
  const { subject } = asset
  await assetListPage.goToPageWith({ subject })
})

test('Select an asset and add to a new collection', async ({
  assetListPage,
  collectionPage,
  currentUser,
  page,
}) => {
  // filter and select the first asset
  await assetListPage
    .getFilterByName('Created By')
    .selectOptionByText(currentUser.getTestUserData().username as string)
  await assetListPage.getFilterByName('Workflow Step').selectOptionByText('Ready for Assembly')
  await assetListPage.table.allCheckBoxes.nth(1).check()

  // click the add to a new collection button
  await assetListPage.actionsDropdown.button.click()
  await assetListPage.actionsDropdown.items.add.hover()
  await assetListPage.nestedActionsDropdown.items.addAssetToNewCollection.click()

  // fill out the input and click apply
  await assetListPage.newCollectionDialog.content.nameInput.fill(
    'E2E new collection created from asset list'
  )
  await assetListPage.newCollectionDialog.buttons.apply.click()
  // check that the asset was successfully added to the new collection
  const response = await waitForResponseFrom(assetListPage.page, endpoints.COLLECTIONS_ADD)
  await waitForResponseFrom(assetListPage.page, endpoints.COLLECTIONS_ADD_ASSETS)
  const body = await (response as Response).json()
  expect(body).toHaveLength(1)
  const collectionId = body[0].result.data.id
  await expect(
    assetListPage.toastDialogLocator.filter({ hasText: `View the new collection ${collectionId}` })
  ).toBeVisible()
  // go to the collection to confirm creation
  const baseUrl = determineBaseUrlFromEnv()
  await page.goto(`${baseUrl}/ap/african-american-studies/collections/${collectionId}`)
  await waitForResponseFrom(page, endpoints.COLLECTIONS_READ_ONE)
  await expect(
    collectionPage.mainLocator.getByRole('heading', {
      name: 'E2E new collection created from asset list',
    })
  ).toBeVisible()
  await collectionPage.page.close()
})

test('Select an asset and add to an existing collection', async ({
  assetListPage,
  currentUser,
  dataController,
  page,
}) => {
  // create a collection to add the asset to
  const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    assetLength: 1,
    collectionProps: { name: 'E2E Collection For Adding Asset From Asset List Page' },
    workflowSteps: [WorkflowStepID.READY_FOR_USE],
  })) as E2ECollection[]
  if (!collection) {
    throw new Error('Collection generation failed')
  }
  // filter and select the first asset
  await assetListPage
    .getFilterByName('Created By')
    .selectOptionByText(currentUser.getTestUserData().username as string)
  await assetListPage.getFilterByName('Workflow Step').selectOptionByText('Ready for Assembly')
  await assetListPage.table.allCheckBoxes.nth(1).check()

  // click the add to an existing collection button
  await assetListPage.actionsDropdown.button.click()
  await assetListPage.actionsDropdown.items.add.hover()
  await assetListPage.nestedActionsDropdown.items.addAssetToExistingCollection.click()

  // fill out the input and click apply
  await assetListPage.searchCollectionDialog.content.searchBar.fill(collection.id)
  await assetListPage.searchCollectionDialog.content.getSearchResultByIndex(0).click()
  await assetListPage.searchCollectionDialog.buttons.add.click()
  // check that the asset was successfully added to the new collection
  await waitForResponseFrom(assetListPage.page, endpoints.COLLECTIONS_ADD_ASSETS)
  await expect(
    assetListPage.toastDialogLocator.filter({ hasText: `View collection ${collection.id}` })
  ).toBeVisible()

  // Click toast to open link in a new tab
  const [newPage] = await Promise.all([
    page.context().waitForEvent('page'),
    assetListPage.toastDialogLocator.getByRole('link', { name: 'VIEW' }).click(),
  ])
  // Perform assertions on the new page
  await expect(
    newPage.getByRole('heading', {
      name: 'E2E Collection For Adding Asset From Asset List Page',
    })
  ).toBeVisible()
})
