import fs from 'fs'
import { parse } from 'csv-parse/sync'
import { expect, test } from '@e2e/cb-test'
import type { E2EAsset } from '@e2e/lib'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import type { Locator } from '@playwright/test'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const numAssets = 5

test.beforeEach(async ({ assetListPage, dataController, currentUser }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: numAssets,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
  const { subject } = asset
  await assetListPage.goToPageWith({ subject })
})

test('Verify user can export CSV from asset list', async ({ assetListPage, page }) => {
  await assetListPage.table.allCheckBoxes.first().check()
  await expect
    .poll(async () => {
      const { selected: selectedCount } = await assetListPage.getAssetCounts()
      return selectedCount
    })
    .toBe(numAssets)

  const idCellLocator = assetListPage.table.cellLocator({ columnName: 'Unique ID' })
  await expect(idCellLocator).toHaveCount(numAssets)
  const originalAssetOrder = await idCellLocator.allTextContents()
  console.log(originalAssetOrder)

  const { actionsDropdown, exportMetadataDialog } = assetListPage
  // Open the dropdown
  await actionsDropdown.button.click()
  // Open the Download submenu
  await actionsDropdown.getOption({ name: 'Download' }).hover()
  // Click the CSV export
  await page.getByRole('menuitem', { name: 'Metadata (.csv)', exact: true }).click()
  // Fill in the dialog fields
  await expect(exportMetadataDialog.content.includeRowsForAssetMetadataCheckBox).toBeChecked()
  await expect(exportMetadataDialog.content.includeRowsForItemMetadataCheckBox).toBeChecked()
  await expect(exportMetadataDialog.content.includeRowsForStimuliMetadataCheckBox).toBeChecked()

  await exportMetadataDialog.content.includeRowsForItemMetadataCheckBox.uncheck()
  await exportMetadataDialog.content.includeRowsForStimuliMetadataCheckBox.uncheck()
  // open the metadata version dropdown
  await exportMetadataDialog.content.metadataVersion.root.click()
  const options = await exportMetadataDialog.content.metadataVersion.getAllOptions()
  await (options[0] as Locator).click()

  await waitForResponseFrom(page, endpoints.GET_ASSET_METADATA)
  // Race condition occurs before clicking download , it will attempt to download the actual html page and fail
  // eslint-disable-next-line playwright/no-wait-for-timeout
  await page.waitForTimeout(2000)
  const downloadPath = await clickAndDownloadFile(
    page,
    exportMetadataDialog.buttons.export,
    'metadata'
  )

  const records = parse(fs.readFileSync(downloadPath, 'utf8'), {
    columns: true,
    skip_empty_lines: true,
    skip_records_with_empty_values: true,
  })

  // Extract all 'Asset ID' values from the parsed records
  const csvAssetOrder = records.map((record: { [x: string]: any }) => record['Asset ID'])

  // Log and compare the orders
  console.log('Original Asset Order:', originalAssetOrder)
  console.log('CSV Asset Order:', csvAssetOrder)

  // Assert that the two arrays are the same
  //BLOCKED BY https://github.com/collegeboard-software/icg-hummingbird/issues/970
  //expect(csvAssetOrder).toContain(originalAssetOrder)
})
