import { users } from '@e2e/../bin/data/users'
import { expect, test } from '@e2e/cb-test'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { getUsers } from '@e2e/util/userUtils'
import { SubjectIdentifier } from '@shared/config'
import { capitalCase } from 'case-anything'
import casual from 'casual'

const singleFilterToTest = 'Created By' // Also will be column name

test.beforeEach(async ({ assetListPage, dataController, currentUser }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    length: 3,
  })) as E2EAsset[]
  const otherUser = getUsers().find((user) => user.email !== currentUser.email)
  if (!otherUser) throw new Error('Uhhh, what?')
  await dataController.generate(DataControllerModel.ASSET, {
    user: otherUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    length: 2,
  })

  if (asset?.subject) {
    await assetListPage.goToPageWith({ subject: asset.subject })
  }
})

test(`should filter by ${singleFilterToTest}`, async ({
  assetListPage,
  page,
  dataController,
  topBar,
}) => {
  const assets = dataController.get(DataControllerModel.ASSET) as E2EAsset[]
  const filterValueToUse = assets[0]?.username || casual.random_element(users).username
  const expectedDisplayValue = capitalCase(filterValueToUse, { keepSpecialCharacters: false })

  const getAssociatedColumnContents = () =>
    assetListPage.table.cellLocator({ columnName: singleFilterToTest }).allTextContents()

  await expect(page.locator('td > .cb-loader-shimmer').first()).toBeHidden()

  await test.step('Check that at least 1 row exists which has a different value', async () => {
    await expect(assetListPage.table.allRows.nth(0)).not.toBeEmpty()
    const allContentsFromColumn = await getAssociatedColumnContents()
    expect(allContentsFromColumn).toEqual(
      expect.arrayContaining([expect.not.stringContaining(expectedDisplayValue)])
    )
  })

  await test.step('Select the desired value in the filter', async () => {
    const createdByFilter = assetListPage.getFilterByName(singleFilterToTest)
    await createdByFilter.selectOptionByText(filterValueToUse)
  })

  await test.step('Verify the desired value shows and only it shows in the column', async () => {
    await expect(page.locator('td > .cb-loader-shimmer').first()).toBeHidden()

    const allContentsFromColumn = await getAssociatedColumnContents()
    expect(allContentsFromColumn).not.toHaveLength(0)
    expect(allContentsFromColumn).not.toEqual(
      expect.arrayContaining([expect.not.stringContaining(expectedDisplayValue)])
    )
  })

  await test.step('Verify the Url keeps the asset filter parameters after searching', async () => {
    const beforeUrl = page.url()
    const genericAssetSearchContent = '000'
    await topBar.searchInput.fill(genericAssetSearchContent)
    await topBar.searchInput.press('Enter')
    const afterUrl = page.url()
    expect(afterUrl).toContain(beforeUrl)
  })

  await test.step('Verify the Clear Filter button clears all filters and reset the Url', async () => {
    const beforeUrl = page.url()
    const allContentsFromColumn = await getAssociatedColumnContents()
    await assetListPage.clearFilterButton.click()
    const afterUrl = page.url()
    const allContentsAfterCleared = await getAssociatedColumnContents()

    expect(allContentsAfterCleared).toEqual(
      expect.arrayContaining([expect.not.stringContaining(expectedDisplayValue)])
    )
    expect(beforeUrl).toContain(afterUrl)
    expect(afterUrl).not.toEqual(beforeUrl)
    expect(allContentsFromColumn).not.toEqual(allContentsAfterCleared)
    expect(allContentsAfterCleared.length).toBeGreaterThan(allContentsFromColumn.length)
  })
})
