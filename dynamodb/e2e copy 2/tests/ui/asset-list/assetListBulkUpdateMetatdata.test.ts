import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Bulk update metadata of assets', async ({ assetListPage, dataController, page }) => {
  const [bulkMetadataUpdateAsset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_REVIEW],
    length: 5,
  })) as E2EAsset[]
  if (bulkMetadataUpdateAsset) {
    await assetListPage.goToPageWith({ ...bulkMetadataUpdateAsset })
  }
  test.fail(!bulkMetadataUpdateAsset, 'Failed to create assets')

  await test.step('Check asset and open bulk metadata update dialog', async () => {
    // Count of 6, since one is the Select All
    await expect(assetListPage.table.allCheckBoxes).toHaveCount(6)
    await assetListPage.table.allCheckBoxes.first().check()
    await assetListPage.actionsDropdown.button.click()
    await expect(assetListPage.actionsDropdown.items.bulkUpdate).toBeVisible()
    await assetListPage.actionsDropdown.items.bulkUpdate.click()
    await expect(assetListPage.bulkUpdateDialog.contentContainer).toContainText('Update metadata')
    await expect(assetListPage.bulkUpdateDialog.contentContainer).toContainText(
      'How would you like to update these 5 assets?'
    )
  })

  await test.step('Bulk Update 5 assets Number of Stimuli value to Single required', async () => {
    const stimuliOption = 'Number of Stimuli'
    const content = await assetListPage.bulkUpdateDialog.content
    await content.metadataField.selectOption(stimuliOption)
    await content.metadataValue.click()
    await page.getByLabel('Number of Stimuli options').waitFor({ state: 'visible' })
    const optionsList = page.getByLabel('Number of Stimuli options')
    await optionsList.hover()
    await optionsList.getByLabel('Single - required').click()

    await assetListPage.bulkUpdateDialog.buttons.save.click()
    await assetListPage.mainLocator.getByRole('button', { name: 'Continue' }).click()

    await expect(
      assetListPage.toastLocator.filter({
        hasText: `5 assets have been updated to "Single - required"`,
      })
    ).toBeVisible()
  })

  await test.step('Bulk Update 5 assets for Assessment Type with multiple items selected', async () => {
    await assetListPage.actionsDropdown.button.click()
    await assetListPage.actionsDropdown.items.bulkUpdate.click()

    const stimuliOption = 'Assessment Type'
    const content = await assetListPage.bulkUpdateDialog.content
    await content.metadataField.selectOption(stimuliOption)
    await page.locator('.ant-select-selector').dblclick()
    const optionsList = page.getByRole('tree')
    await optionsList.hover()
    await optionsList.getByText('Formative').click()
    await optionsList.getByText('Summative').click()

    await assetListPage.bulkUpdateDialog.buttons.save.click()
    await assetListPage.mainLocator.getByRole('button', { name: 'Continue' }).click()

    await expect(
      assetListPage.toastLocator.filter({
        hasText: `5 assets have been updated to "Formative, Summative"`,
      })
    ).toBeVisible()
  })
})
