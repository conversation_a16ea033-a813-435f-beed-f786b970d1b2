import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const nonexistentContent = 'nonexistent content which should not match anything'

test.beforeEach(async ({ assetListPage, randomAsset: { subject }, topBar }) => {
  await assetListPage.goToPageWith({ subject })
  await topBar.searchInput.fill(nonexistentContent)
  await topBar.searchInput.press('Enter')
})
//E2E FIX ME
test.skip('search input should produce a menu with a search suggestion', async ({
  assetListPage,
  topBar,
  randomAsset,
  page,
  dataController,
}) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)
  await topBar.searchInput.clear()
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
  console.log(asset.id)
  const generatedAsset = asset

  // locator for the asset suggestion that should pop up
  const menuAssetSuggestion = topBar.root.getByRole('menuitem', {
    name: `${generatedAsset.id} ${generatedAsset.subject}`,
  })

  // expect the suggestion to show
  const firstAssetSearchByIdPromise = waitForResponseFrom(
    assetListPage.page,
    endpoints.ASSET_SEARCH_BY_ID,
    200,
    35000
  )

  const firstThreeCharactersOfId = generatedAsset.id.slice(0, 4)
  await topBar.searchInput.fill(firstThreeCharactersOfId)
  await firstAssetSearchByIdPromise
  await expect(menuAssetSuggestion).toBeVisible()

  // the suggestion should show again when searching by lowercase
  const secondAssetSearchByIdPromise = waitForResponseFrom(
    assetListPage.page,
    endpoints.ASSET_SEARCH_BY_ID
  )
  await topBar.searchInput.clear()
  await topBar.searchInput.fill(firstThreeCharactersOfId.toLowerCase())
  await secondAssetSearchByIdPromise
  await expect(menuAssetSuggestion).toBeVisible()
  await menuAssetSuggestion.click()
  expect(page.url()).toContain(`/assets/${randomAsset.id}`)
})

test('should find an asset by ID', async ({ assetListPage, topBar, randomAsset }) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)

  await topBar.searchInput.fill(randomAsset.id)
  await topBar.searchInput.press('Enter')

  await expect(assetListPage.table.allRows).toHaveCount(1)
  await expect(assetListPage.table.allRows).toHaveText(new RegExp(randomAsset.id))
})

test('should find an asset by a single search term - display name', async ({
  assetListPage,
  topBar,
  randomAsset,
}) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)

  await topBar.searchInput.fill(randomAsset.displayName)
  await topBar.searchInput.press('Enter')
  await expect(assetListPage.table.allRows).toHaveCount(1)
  // Still checks for id to ensure actually correct asset is found
  await expect(assetListPage.table.allRows).toHaveText(new RegExp(randomAsset.id))
})

// TODO: Refactor this once able to use a generated asset where can control/specify
// a piece of primary content, like directions, stimuli, or stem
test.fixme(
  'should find an asset by a single content search term',
  async ({ assetListPage, topBar, randomAsset }) => {
    test.fixme(
      process.env.ENV === 'local',
      'Will be easier to implement once able to generate data'
    )
    // Covers case insensitivity and partial matching
    const assetPartialContent = 'e2e testing'

    await expect(assetListPage.table.allRows).not.toHaveCount(1)

    await topBar.searchInput.fill(assetPartialContent)
    await topBar.searchInput.press('Enter')

    await expect(assetListPage.table.allRows).toHaveCount(1)
    await expect(assetListPage.table.allRows).toHaveText(new RegExp(randomAsset.id))
  }
)

// Plan to have a test as part of a large user flow from creating an asset with
// every possible input field filled, that will then search for it using every
// possible search term. Since it's an AND match, every search term would have
// to work, which I currently believe should cover the rest sufficiently

test('should give empty results from no matches', async ({ assetListPage, topBar }) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)

  await topBar.searchInput.fill(nonexistentContent)
  await topBar.searchInput.press('Enter')

  await expect(assetListPage.table.allRows).toHaveCount(0)
  await expect(
    assetListPage.mainLocator.getByRole('button', { name: 'No matching results' })
  ).toBeVisible()
})

test('asset list should preserve search terms on refresh', async ({ assetListPage, topBar }) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)
  const searchTerms = 'search'
  await topBar.searchInput.fill(searchTerms)
  await topBar.searchInput.press('Enter')
  const urlBeforeReload = assetListPage.page.url()
  await assetListPage.reloadThenWaitForPageLoadNetworkCall()
  expect(assetListPage.page.url()).toEqual(urlBeforeReload)
})

test('search bar should preserve search terms on refresh', async ({ assetListPage, topBar }) => {
  await expect(assetListPage.table.allRows).toHaveCount(0)
  const searchTerms = '00046'
  await topBar.searchInput.fill(searchTerms)
  await topBar.searchInput.press('Enter')
  await assetListPage.reloadThenWaitForPageLoadNetworkCall()
  const newSearchTerms = await topBar.searchInput.inputValue()
  expect(newSearchTerms).toEqual(searchTerms)
})

// TODO: May also want a case now ensuring search works from a different page
