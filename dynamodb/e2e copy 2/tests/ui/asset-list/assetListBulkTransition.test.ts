import { expect, test } from '@e2e/cb-test'
import { E2EAsset } from '@e2e/lib/E2EAsset'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config/app-config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('should be able to bulk transition assets to different workflow stages', async ({
  dataController,
  assetListPage,
  page,
  currentUser,
}) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    length: 10,
    workflowSteps: [WorkflowStepID.EXTERNAL_REVIEW, WorkflowStepID.SENT_TO_VAULT],
  })) as E2EAsset[]

  if (!asset?.subject) throw new Error('Asset generation failed for Bulk Transition test')

  await assetListPage.goToPageWith({ subject: asset.subject })
  await expect(assetListPage.table.allRows).toHaveCount(10)

  const transitionAssetsTest = [
    {
      numAssets: 2,
      transitionTo: 'Ready for Assembly',
      successfulTransitions: 2,
      unableToTransition: 0, // unable to transition because these assets are already in the transitionTo step
    },
    {
      numAssets: 5,
      transitionTo: 'Internal Content Review 1',
      successfulTransitions: 5,
      unableToTransition: 0,
    },
    {
      numAssets: 10,
      transitionTo: 'DC Independent Work',
      successfulTransitions: 8,
      unableToTransition: 2,
    },
  ]

  for (const {
    numAssets,
    transitionTo,
    successfulTransitions,
    unableToTransition,
  } of transitionAssetsTest) {
    const assetTableRows = [...(await assetListPage.table.allRows.all())].slice(0, numAssets)
    expect(assetTableRows).toHaveLength(numAssets)

    const assetAlreadyInTransitionToState = []

    for (const [index] of Object.entries(assetTableRows)) {
      const row = assetListPage.table.getRow({ index: Number(index) })
      const rowWorkflowStep = await row.cellLocator({ columnName: 'Workflow Step' }).textContent()
      const rowAssetId = await row.cellLocator({ columnName: 'Unique ID' }).textContent()

      if (rowWorkflowStep === transitionTo) assetAlreadyInTransitionToState.push(rowAssetId)

      await row.locator('input[type=checkbox]').click()
    }

    expect(assetAlreadyInTransitionToState).toHaveLength(unableToTransition)

    await assetListPage.actionsDropdown.button.click()
    await assetListPage.actionsDropdown.items.transition.click()
    await assetListPage.transitionDialog.content.toSelect.selectOption({
      name: transitionTo,
      exact: true,
    })
    await assetListPage.transitionDialog.buttons.apply.click()

    await waitForResponseFrom(page, endpoints.ASSETS_TRANSITION_WORKFLOW_MANY)

    await expect(
      assetListPage.toastLocator.filter({
        hasText: `${successfulTransitions} assets transitioned to "${transitionTo}"`,
      })
    ).toBeVisible()

    if (unableToTransition) {
      const pattern = `Unable to transition assets:.*(?:${assetAlreadyInTransitionToState.join(
        '|'
      )})`
      const regex = new RegExp(pattern)
      await expect(
        assetListPage.toastLocator.filter({
          hasText: regex,
        })
      ).toBeVisible()
    }

    await expect(async () => {
      const workflowVals = (
        await assetListPage.table.cellLocator({ columnName: 'Workflow Step' }).allTextContents()
      ).slice(0, numAssets)

      expect(workflowVals.filter((val) => val === transitionTo)).toHaveLength(
        successfulTransitions + unableToTransition
      )
    }).toPass()

    // uncheck all checked checkboxes
    await assetListPage.table.root.locator('input[type=checkbox]').nth(0).click({
      clickCount: 2,
      delay: 300,
    })
  }
})
