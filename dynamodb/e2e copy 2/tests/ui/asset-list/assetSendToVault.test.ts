import { expect, test } from '@e2e/cb-test'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetListPage, dataController, currentUser }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: currentUser.getTestUserData(),
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.READY_FOR_USE],
    length: 1,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error('Asset generation failed')
  }
  const { subject } = asset
  await assetListPage.goToPageWith({ subject })
})

test('Send a single asset to vault from the asset list page', async ({ assetListPage }) => {
  const readyToSendStatus = 'Ready for Assembly'
  await assetListPage.getFilterByName('Workflow Step').selectOptionByText(readyToSendStatus)
  await expect(assetListPage.table.cellLocator({ columnName: 'Workflow Step' }).first()).toHaveText(
    readyToSendStatus
  )

  await assetListPage.table.allCheckBoxes.nth(1).check()
  await assetListPage.actionsDropdown.button.click()
  await assetListPage.actionsDropdown.items.sendToVault.click()
  await expect(assetListPage.sendToVaultDialog.contentContainer).toContainText(
    'Are you sure you want to send 1 asset to vault? Only assets that are Ready for Assembly will be sent to the vault.'
  )
  await assetListPage.sendToVaultDialog.buttons.send.click()
  await expect(assetListPage.toastLocator).toContainText('1 asset was sent to vault successfully.')
})
