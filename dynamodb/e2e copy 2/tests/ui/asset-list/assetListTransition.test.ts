import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { ITEM_FINALIZATION, WorkflowStepID } from '@shared/types/model/workflow/types'

test('should fail for 1 and succeed for 1 asset when transitioning both to one of their workflow', async ({
  assetListPage,
  dataController,
}) => {
  const [asset1] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  const [asset2] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.ITEM_FINALIZATION],
    length: 1,
  })) as E2EAsset[]
  if (!asset1 || !asset2) {
    throw new Error("Couldn't create assets!")
  }
  await assetListPage.goToPageWith({ ...asset1 })

  await test.step('Check asset and open transition dialog', async () => {
    // Count of 3, since one is the Select All
    await expect(assetListPage.table.allCheckBoxes).toHaveCount(3)
    await assetListPage.table.allCheckBoxes.first().check()
    await assetListPage.actionsDropdown.button.click()
    await assetListPage.actionsDropdown.items.transition.click()
    await expect(assetListPage.transitionDialog.contentContainer).toContainText('Multiple selected')
  })

  await test.step('Transition single asset to Ready for Assembly', async () => {
    const secondAssetLabel = ITEM_FINALIZATION[0]?.label
    if (!secondAssetLabel) {
      throw new Error('Red flag! Label does not exist!')
    }
    await assetListPage.transitionDialog.content.toSelect.selectOption(secondAssetLabel)
    await assetListPage.transitionDialog.buttons.apply.click()
    await expect(
      assetListPage.toastLocator.filter({
        hasText: `1 asset transitioned to "${secondAssetLabel}"`,
      })
    ).toBeVisible()
    await expect(
      assetListPage.toastLocator.filter({
        hasText: `Unable to transition assets: ${asset2.id}`,
      })
    ).toBeVisible()
  })
})
