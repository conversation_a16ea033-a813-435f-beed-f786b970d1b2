import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection, E2EForm } from '@e2e/lib'
import { CollectionsPage, FormPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.setTimeout(2 * 60 * 1000)

test('Asset associations dialog should display correctly', async ({
  assetListPage,
  context,
  dataController,
  topBar,
  page,
}) => {
  const subject = 'african-american-studies'
  const baseUrl = determineBaseUrlFromEnv()
  const dialog = assetListPage.associationsDialog.root
  const entityTypes = ['Collections', 'Forms'] as const
  const entityTypePageMapping: Record<
    (typeof entityTypes)[number],
    typeof CollectionsPage | typeof FormPage
  > = {
    ['Collections']: CollectionsPage,
    ['Forms']: FormPage,
  }
  const checkAssociationTabs = async (entityType: (typeof entityTypes)[number]) => {
    // check for tab
    const entityTypeTabRegex = new RegExp(`${entityType} \\([0-9]\\)`)
    const entityTypeTab = dialog.getByRole('tab', { name: entityTypeTabRegex })
    await expect(entityTypeTab).toBeVisible()
    // check the content of the tab
    await entityTypeTab.click()
    const entityTypeTabPanel = dialog.getByRole('tabpanel', {
      name: entityTypeTabRegex,
    })
    const entityTypeTable = entityTypeTabPanel.getByRole('table')
    await expect(entityTypeTable).toBeVisible()
    const entityTypeSnapshotLinks = entityTypeTable.locator('tbody tr')
    await expect(entityTypeSnapshotLinks).toHaveCount(1)
    // check that the link works
    const entityTypeID = await entityTypeTable.locator('tbody tr td').first().innerText()
    const entityTypePagePromise = context.waitForEvent('page')
    await entityTypeSnapshotLinks.first().click()
    const newEntityTypePage = new entityTypePageMapping[entityType]({
      page: await entityTypePagePromise,
    })
    const newEntityTypePageID = newEntityTypePage.page.url().split('/').pop()
    expect(newEntityTypePageID).toBe(entityTypeID)
    await newEntityTypePage.page.close()
  }
  // create a collection, form, and asset for the tests
  const [form] = (await dataController.generate(DataControllerModel.FORM, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    assemblyUnitLength: 1,
    assetLength: 1,
    sequenceLength: 0,
    completeLength: 0,
    formProps: { name: 'associationsForm' },
    nextGenAssemblyUnitProps: {
      questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
      adminYear: '24',
      assemblyUnitType: AssemblyUnitType.BASE,
    },
  })) as E2EForm[]
  const assetId = form?.assetIDs?.[0]
  const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    collectionProps: { name: 'associationsCollection' },
    workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    assetIds: assetId ? [assetId] : undefined,
  })) as E2ECollection[]

  const assetListRoutes = [
    `${baseUrl}/ap/${subject}/assets`,
    `${baseUrl}/ap/${subject}/collections/${collection?.id}/add-assets`,
  ]

  // test each assetList route
  for (const route of assetListRoutes) {
    await test.step('Go to the assetList page of a specific route', async () => {
      if (assetId && collection && form) {
        await page.goto(route)
        await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY)
      }
      test.fail(
        !assetId || !collection || !form,
        'Failed to create resources for assetList associations dialog test'
      )
    })

    await test.step('Clicking the association column cell should open the association dialog', async () => {
      // add the 'Associations' column
      await assetListPage.tableColumnsDropdown.click()
      const associationsColumnOption = assetListPage.tableColumnsRegion.getByLabel(
        'Show Associations column',
        {
          exact: true,
        }
      )

      if (!(await associationsColumnOption.isChecked())) {
        await associationsColumnOption.setChecked(true)
        await assetListPage.updateButton.click()
      } else {
        await assetListPage.tableColumnsDropdown.click()
      }
      // open the associations modal
      const associationsCell = assetListPage.associationsButtons
      await associationsCell.nth(0).click()
      await expect(assetListPage.associationsDialog.root).toBeVisible()
    })

    await test.step('Check content and link on each association tab', async () => {
      for (const type of entityTypes) {
        await checkAssociationTabs(type)
      }
    })

    await test.step('Check that the close button works', async () => {
      await assetListPage.associationsDialog.buttons.remove.click()
      await expect(dialog).toBeHidden()
    })
  }
})
