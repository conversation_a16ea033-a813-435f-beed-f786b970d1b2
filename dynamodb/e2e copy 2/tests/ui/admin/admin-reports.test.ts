import { expect, test } from '@e2e/cb-test'
test.beforeEach(async ({ adminReportsPage }) => {
  await adminReportsPage.goToPageWith()
})

test('should have content in reports', async ({ adminReportsPage }) => {
  await expect(adminReportsPage.courseDropdown).toHaveCount(1)
})

test.skip('should generate report when course is selected and report generation is clicked', async ({
  adminReportsPage,
}) => {
  // TODO
})
