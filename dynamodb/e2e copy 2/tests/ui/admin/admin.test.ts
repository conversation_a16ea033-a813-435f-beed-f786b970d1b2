import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ adminPage }) => {
  await adminPage.goToPageWith()
})

test('should load "Users" and "Migrations" and "Reports" buttons', async ({ adminPage }) => {
  await expect(adminPage.usersButton).toBeVisible()
  await expect(adminPage.migrationsButton).toBeVisible()
  await expect(adminPage.reportsButton).toBeVisible()
})

test('should update the title when hovering over the "Users" and "Migrations" and "Reports" buttons', async ({
  adminPage,
}) => {
  // Users
  await expect(
    adminPage.mainLocator.getByRole('heading', { name: 'Create and Edit Users' })
  ).toBeHidden()
  await adminPage.usersButton.hover()
  await expect(
    adminPage.mainLocator.getByRole('heading', { name: 'Create and Edit Users' })
  ).toBeVisible()
  // Migrations
  await expect(
    adminPage.mainLocator.getByRole('heading', { name: 'Run Migrations Against The Database' })
  ).toBeHidden()
  await adminPage.migrationsButton.hover()
  await expect(
    adminPage.mainLocator.getByRole('heading', { name: 'Run Migrations Against The Database' })
  ).toBeVisible()
  // Reports
  await expect(
    adminPage.mainLocator.getByRole('heading', {
      name: 'Reports Generation with Controlled Access',
    })
  ).toBeHidden()
  await adminPage.reportsButton.hover()
  await expect(
    adminPage.mainLocator.getByRole('heading', {
      name: 'Reports Generation with Controlled Access',
    })
  ).toBeVisible()
})

test('should redirect to the users table page when the "Users" button is clicked', async ({
  page,
  adminPage,
  adminUsersPage,
}) => {
  const adminUserUrl = adminUsersPage.url()
  await expect(page).not.toHaveURL(adminUserUrl)
  await adminPage.usersButton.click()
  await expect(page).toHaveURL(adminUserUrl)
})

test('should redirect to the migrations page when the "Migrations" button is clicked', async ({
  page,
  adminPage,
  adminMigrationsPage,
}) => {
  const adminMigrationUrl = adminMigrationsPage.url()
  await expect(page).not.toHaveURL(adminMigrationUrl)
  await adminPage.migrationsButton.click()
  await expect(page).toHaveURL(adminMigrationUrl)
})

test('should redirect to the reports page when the "Reports" button is clicked', async ({
  page,
  adminPage,
  adminReportsPage,
}) => {
  const adminReportsUrl = adminReportsPage.url()
  await expect(page).not.toHaveURL(adminReportsUrl)
  await adminPage.reportsButton.click()
  await expect(page).toHaveURL(adminReportsUrl)
})
