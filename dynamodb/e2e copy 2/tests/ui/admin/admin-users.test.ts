import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ adminUsersPage }) => {
  await adminUsersPage.goToPageWith()
})

test('should have content in table', async ({ adminUsersPage }) => {
  await expect(adminUsersPage.tableRows).not.toHaveCount(0)
})

test('should update the table when interacting with the sidebar filter', async ({
  adminUsersPage,
}) => {
  await expect(adminUsersPage.tableRows).not.toHaveCount(0)
  await adminUsersPage.filterSection.inputs.name.fill('tst-kfarraday4')
  await adminUsersPage.filterSection.buttons.search.press('Enter')
  await expect(adminUsersPage.tableRows).toHaveCount(1)
  await adminUsersPage.filterSection.buttons.clear.press('Enter')
  await expect(adminUsersPage.tableRows).not.toHaveCount(0)
})

test.fixme(
  'should show a successful toast message when user creation is successful',
  async ({ page, adminUsersPage }) => {
    // TODO: remove this mock when user delete API is set up
    await page.route('**/usersCreate**', async (route) => {
      const json = [
        {
          result: {
            data: {
              hk: 'User#test',
              sk: 'User#01GVGDHSV4YG6RFMG7DAJ4TAM9',
              userId: '01GVGDHSV4YG6RFMG7DAJ4TAM9',
              username: 'test',
              firstName: 'test',
              lastName: 'test',
              emailAddress: 'test',
              allowedCourses: ['116', '117', '999', '13', '12', '30'],
              isActive: true,
              created: 1678809556836,
              updated: 1678809556836,
            },
          },
        },
      ]
      await route.fulfill({ json })
    })
    await expect(
      adminUsersPage.mainLocator.getByText('Successfully added user to the database')
    ).toBeHidden()
    await adminUsersPage.addUserButton.click()
    await adminUsersPage.addUserDialog.inputs.username.fill('test')
    await adminUsersPage.addUserDialog.inputs.firstname.fill('test')
    await adminUsersPage.addUserDialog.inputs.lastname.fill('test')
    await adminUsersPage.addUserDialog.inputs.emailAddress.fill('test')
    await adminUsersPage.addUserDialog.inputs.allowedCourses.EnglishLanguageAndComposition.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.EnglishLiteratureAndComposition.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.Psychology.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.AfricanAmericanStudies.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.Precalculus.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.ESL.click()
    await adminUsersPage.addUserDialog.buttons.apply.click()
    await expect(page.getByText('Successfully added user to the database')).toBeVisible()
  }
)

test.fixme(
  'should show a failure toast message when user creation failed',
  async ({ page, adminUsersPage }) => {
    // TODO: remove this mock when user delete API is set up
    await page.route('**/usersCreate**', async (route) => {
      await route.abort('failed')
    })
    await expect(
      adminUsersPage.mainLocator.getByText(/Unable to add to the database.*/)
    ).toBeHidden()
    await adminUsersPage.addUserButton.click()
    await adminUsersPage.addUserDialog.inputs.username.fill('test')
    await adminUsersPage.addUserDialog.inputs.firstname.fill('test')
    await adminUsersPage.addUserDialog.inputs.lastname.fill('test')
    await adminUsersPage.addUserDialog.inputs.emailAddress.fill('test')
    await adminUsersPage.addUserDialog.inputs.allowedCourses.EnglishLanguageAndComposition.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.EnglishLiteratureAndComposition.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.Psychology.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.AfricanAmericanStudies.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.Precalculus.click()
    await adminUsersPage.addUserDialog.inputs.allowedCourses.ESL.click()
    await adminUsersPage.addUserDialog.buttons.apply.click()
    await expect(page.getByText(/Unable to add to the database.*/)).toBeVisible()
  }
)

test('should redirect to the admin page when the "Back to Admin" button is clicked', async ({
  page,
  adminPage,
  adminUsersPage,
}) => {
  const adminUrl = adminPage.url()
  await expect(page).not.toHaveURL(adminUrl)
  await adminUsersPage.backButton.click()
  await expect(page).toHaveURL(adminUrl)
})
