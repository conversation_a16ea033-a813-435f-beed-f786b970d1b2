import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EForm } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Evaluation Metrics', async ({ page, courseSettingsPage, dataController, collectionPage }) => {
  let collectionID: string
  let metricsData: any

  await test.step('Create collection and navigate to course settings', async () => {
    const baseUrl = determineBaseUrlFromEnv()
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      assetProps: { workflowStateId: WorkflowStepID.READY_FOR_USE },
      collectionProps: { name: 'E2E Eval Metrics Collection' },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
    })) as E2EForm[]
    test.fail(!collection, 'Failed to create a collection')
    collectionID = collection!.id!

    if (collection) {
      const responsePromise = waitForResponseFrom(page, endpoints.EVALUATION_METRICS_READ_ONE)
      await page.goto(`${baseUrl}/ap/african-american-studies/course-settings`)
      await responsePromise
    }
  })

  await test.step('Configure evaluation metrics settings', async () => {
    await expect(courseSettingsPage.usersTab).toBeVisible({ timeout: 750 })
    await expect(courseSettingsPage.metadataTab).toBeVisible({ timeout: 750 })
    await expect(courseSettingsPage.evaluationMetricsTab).toBeVisible({ timeout: 750 })
    await expect(courseSettingsPage.unitsRegion).toBeVisible({ timeout: 750 })

    if (!(await courseSettingsPage.displayCheckbox.isChecked())) {
      await courseSettingsPage.displayCheckbox.click({ timeout: 500 })
    }

    if (!(await courseSettingsPage.fasCheckbox.isChecked())) {
      await courseSettingsPage.fasCheckbox.click()
    }

    await courseSettingsPage.minInput.fill('1')
    await courseSettingsPage.maxInput.fill('5')
    await courseSettingsPage.applyButton.click()
    await expect(courseSettingsPage.toastLocator).toBeVisible()
  })

  await test.step('Navigate to collection and retrieve evaluation metrics payload', async () => {
    await collectionPage.goToPageWith({
      subject: SubjectKey.AFRICAN_AMERICAN_STUDIES,

      id: collectionID,
    })
    const response = await page.waitForResponse(
      (resp) => resp.url().includes('readEvaluationMetricsCalculations') && resp.status() === 200
    )

    const payload = await response.json()
    metricsData = payload[0].result.data
    await courseSettingsPage.metricsTab.click()
  })

  await test.step('Validate Key Distribution Metrics', async () => {
    const {
      A: payloadA,
      B: payloadB,
      C: payloadC,
      D: payloadD,
    } = metricsData.keyDistributionMetrics

    const uiA = Number((await courseSettingsPage.metricsCardEntryA.textContent())?.trim())
    const uiB = Number((await courseSettingsPage.metricsCardEntryB.textContent())?.trim())
    const uiC = Number((await courseSettingsPage.metricsCardEntryC.textContent())?.trim())
    const uiD = Number((await courseSettingsPage.metricsCardEntryD.textContent())?.trim())

    expect(uiA).toBe(payloadA)
    expect(uiB).toBe(payloadB)
    expect(uiC).toBe(payloadC)
    expect(uiD).toBe(payloadD)
  })

  await test.step('Validate Question Type Metrics', async () => {
    const {
      'MCQ: Single-Select': payloadMCQ,
      'FRQ: Source-Based': payloadFRQSourceBased,
      'FRQ: Non Source-Based': payloadFRQNonSourceBased,
      total: payloadTotalQuestions,
    } = metricsData.questionTypeMetrics

    const uiMCQ = Number((await courseSettingsPage.metricsCardEntryLabelMCQ.textContent())?.trim())

    const uiFRQSourceBased = Number(
      (await courseSettingsPage.metricsCardEntryFRQSourceBased.textContent())?.trim()
    )
    const uiFRQNonSourceBased = Number(
      (await courseSettingsPage.metricsCardEntryFRQNonSourceBased.textContent())?.trim()
    )
    const uiTotalQuestions = Number(
      (await courseSettingsPage.getMetricsCardQuestionEntryTotal().textContent())?.trim()
    )

    expect(uiMCQ).toBe(payloadMCQ)
    expect(uiFRQSourceBased).toBe(payloadFRQSourceBased)
    expect(uiFRQNonSourceBased).toBe(payloadFRQNonSourceBased)
    expect(uiTotalQuestions).toBe(payloadTotalQuestions)
  })

  await test.step('Validate Word Count Metrics', async () => {
    const {
      mcq: payloadWordCountMCQ,
      frq: payloadWordCountFRQ,
      total: payloadWordCountTotal,
    } = metricsData.wordCountMetrics

    const uiWordCountMCQ = Number(
      (await courseSettingsPage.metricsCardEntryLabelMultipleChoice.textContent())?.trim()
    )
    const uiWordCountFRQ = Number(
      (await courseSettingsPage.metricsCardEntryFreeResponse.textContent())?.trim()
    )
    const uiWordCountTotal = Number(
      (await courseSettingsPage.getMetricsCardWordCountEntryTotal().textContent())?.trim()
    )

    expect(uiWordCountMCQ).toBe(payloadWordCountMCQ)
    expect(uiWordCountFRQ).toBe(payloadWordCountFRQ)
    expect(uiWordCountTotal).toBe(payloadWordCountTotal)
  })

  await test.step('Validate UI elements visibility for Evaluation Metrics cards and table row', async () => {
    // Validate table row for "1: Origins of the African Diaspora".
    await expect(courseSettingsPage.originsRow).toHaveCount(1)
    await expect(courseSettingsPage.getOriginsRowFirstCell()).toHaveText('1')
    await expect(courseSettingsPage.getOriginsRowSecondCellIcon()).toBeVisible()
    await expect(courseSettingsPage.getOriginsRowThirdCell()).toHaveText(
      '1: Origins of the African Diaspora'
    )
    await expect(courseSettingsPage.getOriginsRowFourthCell()).toHaveText('0')
    await expect(courseSettingsPage.getOriginsRowFifthCell()).toHaveText('1')
    await expect(courseSettingsPage.getOriginsRowSixthCell()).toHaveText('5')
    await expect(courseSettingsPage.getOriginsRowSeventhCellIcon()).toBeVisible()
  })
})
