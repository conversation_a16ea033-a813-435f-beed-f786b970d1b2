import { SubjectIdentifier, getCourseConfigFromFile } from '@bin/data/course-config'
import { expect, test } from '@e2e/cb-test'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

test.beforeEach(async ({ newAssetPage }) => {
  await newAssetPage.goToPageWith()
})

const precalcConfig = getCourseConfigFromFile(SubjectIdentifier.PRECALCULUS)
const unitedStatesHistory = getCourseConfigFromFile(SubjectIdentifier.UNITED_STATES_HISTORY)

const subjectsWithPrograms = [
  {
    subjectName: precalcConfig.name,
    programKey: precalcConfig.programKey,
  },
  {
    subjectName: unitedStatesHistory.name,
    programKey: unitedStatesHistory.programKey,
  },
]

const questionTypesToTest: {
  questionType: 'Multiple Choice Question' | 'Free Response Question'
  subQuestionType?: 'Booklet'
}[] = [
  { questionType: 'Multiple Choice Question' },
  { questionType: 'Free Response Question' },
  { questionType: 'Free Response Question', subQuestionType: 'Booklet' },
]
const quantity = 100

subjectsWithPrograms.forEach(({ subjectName, programKey }, i) => {
  questionTypesToTest.forEach(({ questionType, subQuestionType }) => {
    test(`should create an asset with subject ${subjectName} and question type ${questionType} with subtype ${subQuestionType}`, async ({
      newAssetPage,
      page,
      assetEditorPage,
    }) => {
      await expect(newAssetPage.mainLocator.getByText('Create a New Asset')).toBeVisible()
      await expect(newAssetPage.courseCombobox).toContainText('African American Studies')
      await page.getByRole('combobox', { name: 'Course' }).click()
      await newAssetPage.courseCombobox.selectOption(subjectName)
      await newAssetPage.getQuestionTypeRadio({ name: questionType }).check()
      if (subQuestionType) {
        await newAssetPage.getQuestionTypeRadio({ name: subQuestionType }).check()
      }
      if (questionType === 'Free Response Question') {
        await newAssetPage.getStimulusOptionRadio().check()
      } else {
        await expect(newAssetPage.getStimulusOptionRadio()).toBeHidden()
      }

      await newAssetPage.createButton.click()

      await waitForResponseFrom(page, endpoints.ASSET_CREATE_MANY)
      await page
        .getByRole('status', { name: 'Loading ...' })
        .waitFor({ state: 'detached', timeout: 30_000 })
      const expectedUrlWithSubject = new RegExp(
        assetEditorPage.url({
          program: programKey,
          subject: subjectName,
          id: '\\w+',
        })
      )

      await expect(page, 'Wrong subject!').toHaveURL(expectedUrlWithSubject)

      // Doing poll since is check doesn't wait - see https://playwright.dev/docs/test-assertions#polling
      await expect
        .poll(() => assetEditorPage.isForQuestionType(questionType, subQuestionType), {
          message: 'Created incorrect question type!',
        })
        .toBe(true)
    })
  })

  // We know questionTypesToTest isn't empty, and using the modulo this way ensures it's one of its indices
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const { questionType } = questionTypesToTest[i % questionTypesToTest.length]!
  test(`should create ${quantity} assets with subject ${subjectName} and question type ${questionType}`, async ({
    newAssetPage,
    page,
    assetListPage,
  }) => {
    await expect(newAssetPage.courseCombobox).toContainText('African American Studies')

    await newAssetPage.courseCombobox.selectOption(subjectName)
    await newAssetPage.getQuestionTypeRadio({ name: questionType }).check()
    if (questionType === 'Free Response Question') {
      await newAssetPage.getStimulusOptionRadio().check()
    }
    await newAssetPage.quantityInput.fill(quantity.toString())

    const responsePromise = page.waitForResponse(
      (response) => response.url().includes('read/assetsCreateMany') && response.status() === 200
    )
    await newAssetPage.createButton.click()
    await responsePromise
    const expectedUrlWithSubject = assetListPage.url({ subject: subjectName })
    await expect(page, 'Wrong subject!').toHaveURL(new RegExp(`${expectedUrlWithSubject}.*`))
  })
})
