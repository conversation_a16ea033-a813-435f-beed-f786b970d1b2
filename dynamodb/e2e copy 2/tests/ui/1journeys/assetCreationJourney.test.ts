import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import type { AssetEditorAccordion } from '@e2e/components/asset-editor'
import { AssetEditorOptionsList } from '@e2e/components/asset-editor/AssetEditorOptionsList'
import { AntTreeSelect, TextEditor } from '@e2e/components/common'
import { ComboBox } from '@e2e/components/common/ComboBox'
import type { E2EAsset } from '@e2e/lib'
import { PreviewPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import type { Dialog, Locator } from '@playwright/test'

type AssertPreviewType = ({ container }: { container: PreviewPage }) => Promise<void>
type CanInsertAndAssert = {
  insert(): Promise<void>
  assert(): Promise<void>
  assertPreview: AssertPreviewType
}

type InsertAndAssertSetupProps = {
  input: Locator | TextEditor | AssetEditorOptionsList | ComboBox | AntTreeSelect
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  insertion?: string | Function // For TextEditors
  insertionType?: 'text' | 'function'
  previewLocation?:
    | 'stimulus'
    | 'question'
    | 'answer'
    | 'key'
    | 'rationales'
    | 'metadata'
    | 'comments'
    | 'none'
} & (
  | {
      inputType: 'radio' // For simple radio Locators
      radioPreviewHandler: AssertPreviewType
    }
  | {
      inputType?: 'simpletext' | 'select' // For simple Locators
      radioPreviewHandler?: never // Isn't typescript interesting?
    }
)
const DEFAULT_PAGE_TITLE = 'Hummingbird'
let setLayout: string | null

/**
 * This is used for an issue where the previous tree hasn't closed yet, so it gets
 * the wrong text content for the new tree before the old one closes. Since we
 * don't have control to give the `tree` role a proper name to better scope by,
 * this was the best solution I had.
 */
let lastTreeFirstOption = 'nonexistent option'

const setUpInputInsertAndAssert = ({
  input,
  inputType,
  insertion,
  insertionType = 'text',
  previewLocation,
  radioPreviewHandler,
}: InsertAndAssertSetupProps): CanInsertAndAssert => {
  if (input instanceof TextEditor) {
    if (typeof insertion === 'function') {
      insertionType = 'function'
    }
    switch (insertionType) {
      case 'text':
        const text = (insertion as string) ?? casual.text
        return {
          async insert() {
            await input.root.fill(text)
          },
          async assert() {
            await expect.soft(input.root).toHaveText(text)
          },
          async assertPreview({ container }) {
            if (!setLayout) throw new Error('Layout was not set')
            switch (previewLocation) {
              case 'stimulus':
                // TODO: Once Dave Marini from exam player team fixes the accessibility issue, uncomment
                // const stimulusContainer =
                // setLayout === 'Stem Single Pane'
                //   ? container.contents.discreteQuestionContainer
                //   : container.contents.stimulusContainer
                const stimulusContainer = container.contents.root
                await expect.soft(stimulusContainer).toContainText(text)
                break
              case 'question':
                // TODO: Once Dave Marini from exam player team fixes the accessibility issue, uncomment
                // const questionContainerLocator =
                //   setLayout === 'Stem Single Pane'
                //     ? container.contents.discreteQuestionContainer
                //     : container.contents.questionContainer
                const questionContainerLocator = container.contents.root
                await expect.soft(questionContainerLocator).toContainText(text)
                break
              case 'answer':
                await expect.soft(container.contents.answerOptionsContainer).toContainText(text)
                break
              case 'rationales':
                await container.contents.infoPanel.keyRationalesTab.open()
                await expect
                  .soft(container.contents.infoPanel.keyRationalesTab.content.rationaleTextSection)
                  .toContainText(text)
                break
              case 'metadata':
                await container.contents.infoPanel.metadataTab.open()
                await expect
                  .soft(container.contents.infoPanel.metadataTab.content.metadataTextSection)
                  .toContainText(text)
                break
              // TODO: Refactor metadata and comments handling as per ticket #5215.
              // This includes revisiting how metadata types are distinguished and how comments are implemented.
              // Reference: https://github.com/collegeboard-software/icg-hummingbird/issues/5215
              case 'comments':
                await container.contents.infoPanel.commentsTab.open()
                await expect
                  .soft(container.contents.infoPanel.commentsTab.content.commentsTextSection)
                  .toContainText(text)
                break
              case 'none':
                // some fields in asset editor don't have preview locations
                console.log(`No preview location for this ${input}`)
                break
              default:
                throw new Error('This preview location is not implemented!')
            }
          },
        }
      default:
        throw new Error('Need to implement this case!')
    }
  } else if (input instanceof AssetEditorOptionsList) {
    return setUpRegionInsertAndAssert(() => {
      const open = () => input.showRationalesButton.click()
      const { A, B, C, D } = input.options
      return [
        {
          insert: open,
          assert: open,
          async assertPreview() {
            return
          },
        },
        setUpInputInsertAndAssert({
          input: B.radioButton,
          inputType: 'radio',
          async radioPreviewHandler({ container }) {
            await container.contents.infoPanel.keyRationalesTab.open()
            await expect
              .soft(container.contents.infoPanel.keyRationalesTab.content.keyTextSection)
              .toContainText('The Key is B')
          },
        }),
        setUpInputInsertAndAssert({ input: B.answerEditor, previewLocation: 'answer' }),
        setUpInputInsertAndAssert({ input: B.rationaleEditor, previewLocation: 'rationales' }),
        setUpInputInsertAndAssert({ input: A.answerEditor, previewLocation: 'answer' }),
        setUpInputInsertAndAssert({ input: A.rationaleEditor, previewLocation: 'rationales' }),
        setUpInputInsertAndAssert({ input: C.answerEditor, previewLocation: 'answer' }),
        setUpInputInsertAndAssert({ input: C.rationaleEditor, previewLocation: 'rationales' }),
        setUpInputInsertAndAssert({ input: D.answerEditor, previewLocation: 'answer' }),
        setUpInputInsertAndAssert({ input: D.rationaleEditor, previewLocation: 'rationales' }),
      ]
    })
  } else if (input instanceof ComboBox) {
    let expectedSelection: string | null | undefined = ''
    return {
      async insert() {
        expectedSelection = await input.selectFirstOption()
      },
      async assert() {
        const selectionText = await input.getCurrentSelection()
        expect(selectionText).toEqual(expectedSelection)
      },
      async assertPreview() {},
    }
  } else if (input instanceof AntTreeSelect) {
    const expectedSelections: Awaited<ReturnType<Locator['textContent']>>[] = []
    return {
      // eslint-disable @typescript-eslint/no-non-null-assertion
      async insert() {
        await input.comboBox.click()

        const [firstNode, secondNodeBeforeOpen] = await input.tree.nodeLocator.all()
        if (!firstNode) {
          throw new Error('First node not located')
        }
        secondNodeBeforeOpen && (await expect.soft(secondNodeBeforeOpen).toBeVisible())
        await expect.soft(firstNode).not.toHaveText(lastTreeFirstOption)
        const firstNodeText = await firstNode.textContent()
        // Probably not the best way to do this (would use expect), but just trying to
        // satisfy typescript (which expect doesn't) since I haven't seen it return null yet
        if (firstNodeText === null) {
          throw new Error('First node text was null!')
        }
        lastTreeFirstOption = firstNodeText
        expectedSelections.push(firstNodeText)
        // Asserting this won't be null since already checked if it's visible

        const secondNodeTextBeforeOpen =
          (await input.tree.nodeLocator.count()) > 1 && secondNodeBeforeOpen
            ? await secondNodeBeforeOpen.textContent()
            : undefined

        await firstNode.opener.click()
        const [, secondNodeAfterOpen] = await input.tree.nodeLocator.all()
        if (secondNodeAfterOpen) {
          secondNodeTextBeforeOpen &&
            (await expect.soft(secondNodeAfterOpen).not.toHaveText(secondNodeTextBeforeOpen))
          const secondNodeTextAfterOpen = await secondNodeAfterOpen.textContent()
          expectedSelections.push(secondNodeTextAfterOpen)
          await secondNodeAfterOpen.click()
        } else {
          await firstNode.click()
        }
      },
      async assert() {
        expect.soft(expectedSelections).not.toHaveLength(0)
        expect.soft(expectedSelections).not.toContain(null)
        for (const expected of expectedSelections) {
          // We'll already get the error from above if it's null
          expected && (await expect.soft(input.getCurrentSelectionLocator(expected)).toBeVisible())
        }
      },
      async assertPreview() {},
    }
  } else {
    switch (inputType) {
      case 'simpletext':
        const text = (insertion as string) ?? casual.text
        return {
          async insert() {
            await input.fill(text)
          },
          async assert() {
            await expect.soft(input).toHaveValue(text)
          },
          async assertPreview({ container }) {
            switch (previewLocation) {
              case 'question':
                await expect.soft(container.contents.questionContainer).toContainText(text)
                break
              case 'answer':
                throw new Error(
                  'The answer preview location does not apply to any simple text fields.'
                )
              case 'stimulus':
                await expect.soft(container.contents.stimulusContainer).toContainText(text)
                break
              case 'rationales':
                await expect
                  .soft(container.contents.infoPanel.keyRationalesTab.content.rationaleTextSection)
                  .toContainText(text)
                break
              case 'metadata':
                await container.contents.infoPanel.metadataTab.open()
                await expect
                  .soft(container.contents.infoPanel.metadataTab.content.metadataTextSection)
                  .toContainText(text)
                break
              case 'comments':
                await container.contents.infoPanel.commentsTab.open()
                await expect
                  .soft(container.contents.infoPanel.commentsTab.content.commentsTextSection)
                  .toContainText(text)
                break
              case 'none':
                // some fields in asset editor don't have preview locations
                console.log(`No preview location for this ${input}`)
                break
              default:
                throw new Error('This preview location is not implemented!')
            }
          },
        }
      case 'radio':
        return {
          async insert() {
            await expect.soft(input).not.toBeChecked()
            await input.check()
          },
          async assert() {
            await expect.soft(input).toBeChecked()
          },
          async assertPreview({ container }) {
            await radioPreviewHandler({ container })
          },
        }
      default:
        throw new Error('Need to implement this case!')
    }
  }
}

const setUpRegionInsertAndAssert = (
  cb: () => ReturnType<typeof setUpInputInsertAndAssert>[]
): CanInsertAndAssert => {
  const inputSetups = cb()
  const failedInserts: typeof inputSetups = []
  return {
    async insert() {
      for (const setup of inputSetups) {
        await expect
          .soft(
            (async () => {
              try {
                await setup.insert()
              } catch (e) {
                failedInserts.push(setup)
                throw e
              }
            })()
          )
          .resolves.not.toThrowError()
      }
    },
    async assert() {
      for (const setup of inputSetups) {
        // Skip assertions where insertion already failed/threw
        if (!failedInserts.includes(setup)) {
          await setup.assert()
        }
      }
    },
    async assertPreview({ container }) {
      for (const setup of inputSetups) {
        // Skip assertions where insertion already failed/threw
        if (!failedInserts.includes(setup)) {
          await setup.assertPreview({ container })
        }
      }
    },
  }
}

test.setTimeout(3 * 60 * 1000)

test("should follow a user's journey creating an asset", async ({
  homePage,
  topBar,
  sideMenu,
  newAssetPage,
  assetEditorPage,
  dataController,
  assetListPage,
  context,
  page,
}) => {
  let previewPage: PreviewPage

  await test.step('Navigate to new asset page', async () => {
    await homePage.goToPageWith()
    await expect(homePage.page).toHaveTitle(`Programs - ${DEFAULT_PAGE_TITLE}`)
    await topBar.burgerButton.click()
    await sideMenu.createAssetLink.click()
    await expect(newAssetPage.page).toHaveTitle(`New Asset - ${DEFAULT_PAGE_TITLE}`)
  })

  // TODO: Have a few (dynamic) tests for different question types
  const subjectName = 'African American Studies'
  const questionType = 'Multiple Choice Question'

  await test.step('Create an asset', async () => {
    await newAssetPage.courseCombobox.selectOption(subjectName)
    await newAssetPage.getQuestionTypeRadio({ name: questionType }).check()
    await newAssetPage.createButton.click()
    // Needed to add this wait to ensure getItemAccordion works as expected
    // during the item1/2Setup calls
    await assetEditorPage.page.waitForURL(assetEditorPage.url({ subject: subjectName, id: '*' }))
  })
  const metadataSetup = async (accordion: AssetEditorAccordion<object>) => {
    const selectComboBoxes = await accordion.content.metadataTab.getSelectComboBoxInputs()
    const treeSelects = await accordion.content.metadataTab.getAntTreeSelectComponents()
    // Ensure actually found some, otherwise we get a false positive
    expect.soft(treeSelects).not.toHaveLength(0)
    const textInputs = await accordion.content.metadataTab.getTextInputs()
    return setUpRegionInsertAndAssert(() => [
      ...selectComboBoxes.map((comboBox) => setUpInputInsertAndAssert({ input: comboBox })),
      ...treeSelects.map((treeSelect) => setUpInputInsertAndAssert({ input: treeSelect })),
      ...textInputs.map((textInput) =>
        setUpInputInsertAndAssert({ input: textInput, inputType: 'simpletext' })
      ),
    ])
  }
  /** Used to store the metadataSetup for entities */
  const metadataSetupMap = new Map<CanInsertAndAssert, CanInsertAndAssert>()
  const getMetadataSetup = (setup: CanInsertAndAssert) => {
    const metadataSetup = metadataSetupMap.get(setup)
    if (!metadataSetup) throw new Error('Failed to get metadata setup for metadata assertion.')
    return metadataSetup
  }

  const generalSetup = setUpRegionInsertAndAssert(() => {
    const { primaryInputs } = assetEditorPage.generalAccordion.content
    return [
      setUpInputInsertAndAssert({ input: primaryInputs.directions, previewLocation: 'question' }),
      setUpInputInsertAndAssert({
        input: primaryInputs.assetTitle,
        insertion: 'General Asset Title',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
    ]
  })

  const generalSetupAccnum = setUpRegionInsertAndAssert(() => {
    const { assetAccnum } = assetEditorPage.generalAccordion.content.primaryInputs
    return [
      setUpInputInsertAndAssert({
        input: assetAccnum,
        insertion: 'ACCNUM',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
    ]
  })

  const item1Setup = setUpRegionInsertAndAssert(() => {
    const { primaryInputs } = assetEditorPage.getItemAccordion({ number: 1 }).content
    return [
      setUpInputInsertAndAssert({
        input: primaryInputs.stem,
        previewLocation: 'question',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.itemTitle,
        insertion: 'Item 1 Title',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.itemAccnum,
        insertion: 'Item 1 Accnum',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
      setUpInputInsertAndAssert({ input: primaryInputs.optionsList }),
    ]
  })
  const item2Setup = setUpRegionInsertAndAssert(() => {
    const { primaryInputs } = assetEditorPage.getItemAccordion({ number: 2 }).content
    return [
      setUpInputInsertAndAssert({
        input: primaryInputs.stem,
        previewLocation: 'question',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.itemTitle,
        insertion: 'Item 2 Title',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.itemAccnum,
        insertion: 'Item 2 Accnum',
        inputType: 'simpletext',
        previewLocation: 'none',
      }),
      setUpInputInsertAndAssert({ input: primaryInputs.optionsList }),
    ]
  })

  const stimuliSetup = setUpRegionInsertAndAssert(() => {
    const { primaryInputs } = assetEditorPage.getStimulusAccordion({ index: 0 }).content
    return [
      setUpInputInsertAndAssert({
        input: primaryInputs.passageIntroduction,
        previewLocation: 'question',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.stimulusTitle,
        previewLocation: 'stimulus',
      }),
      setUpInputInsertAndAssert({
        input: primaryInputs.sourceAttribution,
        previewLocation: 'question',
      }),
      setUpInputInsertAndAssert({ input: primaryInputs.creditLine, previewLocation: 'question' }),
      setUpInputInsertAndAssert({ input: primaryInputs.caption, previewLocation: 'question' }),
    ]
  })

  // TODO: Refactor metadata and comments handling as per ticket #5215.
  // This includes revisiting how metadata types are distinguished and how comments are implemented.
  // Reference: https://github.com/collegeboard-software/icg-hummingbird/issues/5215
  const commentsSetup = setUpRegionInsertAndAssert(() => {
    const { commentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
    const { addCommentContainer, commentsThread } = commentsTab.content.commentContent.threads
    const newCommentText = 'asset creation journey e2e comment'
    return [
      {
        insert: async () => {
          await commentsTab.open()
          await expect(commentsTab.contentContainer).not.toContainText(newCommentText)
          await addCommentContainer.addCommentButton.click()
          await addCommentContainer.editableTextBox.textBox.fill(newCommentText)
          const responsePromise = waitForResponseFrom(assetEditorPage.page, endpoints.COMMENTS_ADD)
          await addCommentContainer.editableTextBox.saveButton.click()
          await responsePromise
        },
        assert: async () => {
          await expect(commentsThread.commentsContent.body.root).toContainText(newCommentText)
        },
      },
    ]
  })

  const waitAndSaveAsset = async (
    description: string,
    saveType: 'toolbar' | 'menu' = 'toolbar'
  ) => {
    await test.step(`Save the asset using the ${saveType}: ${description}`, async () => {
      // Generally, a fixed timeout like this is not good or recommended, as it
      // could like to issues around flakiness, which could get worse in slower
      // environments or if the app somehow gets slower. Waiting for events,
      // element visibility/actionability, or other true indicators is better,
      // However, in this case, this has an interesting delay before it's saveable
      // (contents will actually save). And while the Save button isn't enabled
      // until it would work, if you do more than one thing before saving it will be
      // enabled after the first. The delay could be perhaps due to debouncing
      // timeouts, which seem to be around 500. And while there may be a way to detect
      // or change code like disabling the save, this seemed like a safe timeout that
      // wouldn't be significant, and if flakiness does still arise we could tackle
      // a new approach then.
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await assetEditorPage.page.waitForTimeout(1000)
      if (saveType === 'toolbar') {
        await assetEditorPage.toolbar.saveButton.click()
      } else {
        await assetEditorPage.menu.clickThroughMenu('File', 'Save all')
      }
    })
  }

  await test.step('Add content to every field', async () => {
    const { generalAccordion } = assetEditorPage
    await generalAccordion.open()
    await generalSetup.insert()
    const generalMetadataSetup = await metadataSetup(generalAccordion)
    await generalMetadataSetup.insert()
    metadataSetupMap.set(generalSetup, generalMetadataSetup)
    const assetAccnumRoot = generalAccordion.content.primaryInputs.assetAccnum
    const item1Accordion = assetEditorPage.getItemAccordion({ number: 1 })
    await item1Accordion.open()
    await item1Setup.insert()
    const item1MetadataSetup = await metadataSetup(item1Accordion)
    await item1MetadataSetup.insert()
    metadataSetupMap.set(item1Setup, item1MetadataSetup)
    // With only one item the general accnum auto-populates to match the item's accnum
    // and is readonly
    await expect(assetAccnumRoot).toHaveText('Item 1 Accnum')
    await expect(assetAccnumRoot).toHaveAttribute('readonly')
  })

  await test.step('Check asset ID in browser tab', async () => {
    const url = assetEditorPage.page.url()
    const id = url.split('/').pop() || 'url not found'

    console.log('Extracted ID:', id)
    await expect(assetEditorPage.page).toHaveTitle(id + ` - ${DEFAULT_PAGE_TITLE}`)
  })

  // This step happens here before saving for ease so the accordion
  // no longer has the "DRAFT" name version in its title
  await test.step('add new item', async () => {
    await assetEditorPage.addAnItemButton.click()
    // When second item is added the general accnum is cleared
    await expect(assetEditorPage.generalAccordion.content.primaryInputs.assetAccnum).toHaveText('')
  })

  await waitAndSaveAsset('Saving Item 1 content and newly added item')

  await test.step('Verify metadata viewer item and general information', async () => {
    await assetEditorPage.menu.clickThroughMenu('View', 'Metadata')

    const metadataDictionary = {
      'Number of Stimuli:': 'Single - required',
      'Set Type': 'A Text: historical primary',
      'Question Sub-type': 'MCQ: Single-Select',
      'Year Of Exam': '2024',
      Form: 'Operational',
      Security: 'Teacher Use Only',
      'Question Set': 'Yes',
      Section: 'Section I',
      'Assessment Type': 'Formative',
      Formative: 'AP Topic Question',
    }

    // Iterate through the dictionary and perform assertions
    for (const [metadataType, expectedValue] of Object.entries(metadataDictionary)) {
      const metadataRow = assetEditorPage.metadataDialog.content.metadataRow(metadataType)
      await expect.soft(metadataRow).toContainText(expectedValue)
    }

    // Verify first item metadata
    await assetEditorPage.metadataDialog.buttons.item.first().click()

    const itemDictionary = {
      'Question Difficulty': 'Emerging',
      'Exam Alignment': 'High',
      'Use Code': 'Equating',
      Unit: '1: Origins of the African Diaspora',
      Topic: '1.1: What Is African American Studies?',
      'Skill Category':
        'Skill Category 1: Applying Disciplinary Knowledge - Explain course concepts, developments, patterns, and processes (e.g., cultural, historical, political, social).',
      Skill: '1.A: Identify and explain course concepts, developments, and processes',
    }

    // Iterate through the dictionary and perform assertions
    for (const [key, expectedValue] of Object.entries(itemDictionary)) {
      const itemRow = assetEditorPage.metadataDialog.content.itemRow(key)
      await expect.soft(itemRow).toContainText(expectedValue)
    }

    await assetEditorPage.metadataDialog.buttons.close.click()
  })

  await test.step('Refresh and ensure content saved', async () => {
    const responsePromise = waitForResponseFrom(page, endpoints.ASSETS_READ_ONE, 200, 20000)
    await assetEditorPage.page.reload()
    await responsePromise

    await assetEditorPage.generalAccordion.open()
    await generalSetup.assert()
    const generalMetadataSetup = getMetadataSetup(generalSetup)
    await generalMetadataSetup.assert()
    await generalSetupAccnum.insert()

    await assetEditorPage.getItemAccordion({ number: 1 }).open()
    await item1Setup.assert()
    const item1MetadataSetup = getMetadataSetup(item1Setup)
    await item1MetadataSetup.assert()
  })

  await test.step('add content for item 2', async () => {
    const item2Accordion = assetEditorPage.getItemAccordion({ number: 2 })
    await item2Accordion.open()
    await item2Setup.insert()
    const item2MetadataSetup = await metadataSetup(item2Accordion)
    metadataSetupMap.set(item2Setup, item2MetadataSetup)
    await item2MetadataSetup.insert()
  })

  // save again so Previewer will be able grab Item 2 content
  await waitAndSaveAsset('Saving Item 2 content.')

  await test.step('Refresh and ensure content Item 2 saved', async () => {
    const responsePromise = waitForResponseFrom(page, endpoints.ASSETS_READ_ONE, 200, 20000)
    await assetEditorPage.page.reload()
    await responsePromise
    await assetEditorPage.getItemAccordion({ number: 2 }).open()
    await item2Setup.assert()
    const item2MetadataSetup = getMetadataSetup(item2Setup)
    await item2MetadataSetup.assert()
    setLayout =
      await assetEditorPage.generalAccordion.content.metadataTab.content.layout.getLayoutValue()
  })

  await test.step('Open and verify preview', async () => {
    const pagePromise = context.waitForEvent('page')
    await assetEditorPage.menu.clickThroughMenu('View', 'Preview', 'New Tab')
    previewPage = new PreviewPage({ page: await pagePromise })

    await previewPage.page.waitForLoadState()
    const assetIdParam = previewPage.page.url().split('=')[1]
    await expect(previewPage.page).toHaveTitle(`${assetIdParam} Preview - ${DEFAULT_PAGE_TITLE}`)

    await previewPage.contents.showInfoButton.click()
    //FIX ME E2E
    //await item1Setup.assertPreview({ container: previewPage })

    await expect(async () => {
      const lastId = previewPage.contents.currentItemId
      const lastIdText = (await lastId.textContent())!
      await previewPage.contents.nextButton.click()
      const nextId = previewPage.contents.currentItemId
      await expect(nextId).not.toHaveText(lastIdText)
    }).toPass({ timeout: 10000 })
    //need to wait for aria-selected for keys/rationale tab to reset to false
    await expect
      .poll(() =>
        previewPage.contents.infoPanel.keyRationalesTab.root.getAttribute('aria-selected')
      )
      .toBe('false')
    await item2Setup.assertPreview({ container: previewPage })
  })

  await test.step('add a stimuli', async () => {
    await assetEditorPage.getStimulusAddButton({ type: 'stimulus' }).click()
    await stimuliSetup.insert()
    const stimuliMetadataSetup = await metadataSetup(
      assetEditorPage.getStimulusAccordion({ index: 0 })
    )
    metadataSetupMap.set(stimuliSetup, stimuliMetadataSetup)
    await stimuliMetadataSetup.insert()
  })

  // TODO: Refactor metadata and comments handling as per ticket #5215.
  // This includes revisiting how metadata types are distinguished and how comments are implemented.
  // Reference: https://github.com/collegeboard-software/icg-hummingbird/issues/5215
  await test.step('add a comment', async () => {
    await commentsSetup.insert()
    await commentsSetup.assert()
  })

  await waitAndSaveAsset('Save using menu', 'menu')

  await test.step('Refresh and ensure content saved for stimulus and item 2', async () => {
    const responsePromise = waitForResponseFrom(page, endpoints.ASSETS_READ_ONE, 200, 20000)
    await assetEditorPage.page.reload()
    await responsePromise
    await assetEditorPage.getStimulusAccordion({ index: 0 }).open()
    await stimuliSetup.assert()
    const stimuliMetadataSetup = getMetadataSetup(stimuliSetup)
    await stimuliMetadataSetup.assert()

    await assetEditorPage.generalAccordion.open()
    await generalSetup.assert()
    await generalSetupAccnum.assert()

    await assetEditorPage.getItemAccordion({ number: 2 }).open()
    await item2Setup.assert()
    const item2MetadataSetup = getMetadataSetup(item2Setup)
    await item2MetadataSetup.assert()
  })

  await test.step('Verify preview for general and stimuli', async () => {
    await generalSetup.assertPreview({ container: previewPage })
    await stimuliSetup.assertPreview({ container: previewPage })
  })

  await test.step('Search for and verify asset shows up in table', async () => {
    const assetId = (dataController.get(DataControllerModel.ASSET) as E2EAsset[])?.[0]?.id
    if (!assetId || typeof assetId !== 'string') {
      throw new Error("Couldn't search for asset since wasn't registered properly!")
    }

    // Honestly, shouldn't need this, since there wouldn't be unsaved changes...
    // however, it appears that the dialog comes up still. Perhaps a bug to fix,
    // and this should just be removed to catch in the future!
    const acceptDialog = async (dialog: Dialog) => {
      if (!dialog.message().includes('unsaved changes')) {
        await dialog.dismiss()
        throw new Error('Should not have gotten here!')
      }
      await dialog.accept()
    }
    assetEditorPage.page.on('dialog', acceptDialog)

    await topBar.searchInput.fill(assetId)
    await topBar.searchInput.press('Enter')

    assetEditorPage.page.off('dialog', acceptDialog)

    await expect(assetListPage.page).toHaveTitle(`Assets - ${DEFAULT_PAGE_TITLE}`)
    await expect(assetListPage.table.allRows).toHaveCount(1)
    await expect(assetListPage.table.allRows).toHaveText(new RegExp(assetId))
  })

  // Torn on whether this makes sense to be here, since this is more table usage
  // specific instead of creation. The benefit of having here is that a full asset
  // would have been created and filled out and thus ready for this. But using
  // the data generator to set up that test may be better/more flexible anyways
  await test.step('Filtering on everything still shows the asset', async () => {
    // TBD
  })

  expect(test.info().errors).toHaveLength(0)
})
