import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to insert prose and save', async ({ assetEditorPage, page }) => {
  const randomSentence = casual.sentence

  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  stem.root.click()

  await assetEditorPage.menu.clickThroughMenu('Insert', 'Prose')

  await assetEditorPage.proseContainer.fill(randomSentence)

  await assetEditorPage.toolbar.saveButton.click()
  await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
  await expect(assetEditorPage.toastLocator).toContainText(/Item .* saved successfully/)

  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()

  await expect(assetEditorPage.proseContainer).toHaveText(randomSentence)
})
