import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

let asset: E2EAsset

test.beforeEach(async ({ dataController }) => {
  ;[asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
})

test('Turn on Dev Feature Flags For Asset Editor Metadata Selection', async ({
  assetEditorPage,
  courseSettingsPage,
  page,
  adminFeaturesPage,
}) => {
  const baseUrl = determineBaseUrlFromEnv()

  await adminFeaturesPage.goToPageWith()
  await adminFeaturesPage.mainLocator
    .getByRole('switch', { name: 'Allow Metadata Switch In Asset' })
    .check()
  const responsePromise = waitForResponseFrom(page, endpoints.EVALUATION_METRICS_READ_ONE)
  await page.goto(`${baseUrl}/ap/precalculus/course-settings`)
  await responsePromise

  await courseSettingsPage.metadataTab.click()
  const toggle = courseSettingsPage.metadataVersionsToggle
  const isToggled = await toggle.getAttribute('aria-checked')
  if (isToggled !== 'true') {
    await toggle.click()
    await expect(courseSettingsPage.toastLocator).toContainText(
      'Successfully updated the course settings'
    )
  }

  const switchCount = await courseSettingsPage.metadataFrameworkTable.getByRole('switch').count()
  if (switchCount !== 2) {
    const linkText = await courseSettingsPage.openInSatchelLink.first().getAttribute('href')
    await courseSettingsPage.requestNewButton.click()
    await courseSettingsPage.getRequestNewMetadataTextbox().fill(linkText)
    await courseSettingsPage.getRequestNewMetadataRequestButton().click()

    await courseSettingsPage.getImportNewMetadataRadio().click()
    await courseSettingsPage.getImportNewMetadataImportButton().click()
    await courseSettingsPage.offSwitch.click()
  }

  await assetEditorPage.goToPageWith({ ...asset })
  await courseSettingsPage.metadataVersionDropdown.click()
  await courseSettingsPage.metadataVersionDropdownItem.last().click()
})
