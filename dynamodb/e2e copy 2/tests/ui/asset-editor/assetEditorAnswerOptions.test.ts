import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { AssetEditorPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType, SubType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const scrollIntoAnswerOptionsIntoViewAndClickOption = async (
  item: ReturnType<AssetEditorPage['getItemAccordion']>
) => {
  const optionA = item.content.primaryInputs.optionsList.options.A.radioButton
  const optionC = item.content.primaryInputs.optionsList.options.C.radioButton

  await optionA.check()
  await expect(optionA).toBeChecked()
  await optionC.check()
  await expect(optionA).not.toBeChecked()
  await expect(optionC).toBeChecked()
}

test.describe('AFAM asset tests', () => {
  test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
    const [asset] = await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      assetProps: { itemType: ItemType.MultipleChoiceQuestion, itemCount: 3 },
      workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
      length: 1,
    })

    if (asset) {
      await assetEditorPage.goToPageWith({ ...asset })
    }
  })

  test('Able to click answer choice and set one key', async ({ assetEditorPage, page }) => {
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    await scrollIntoAnswerOptionsIntoViewAndClickOption(itemOne)

    await assetEditorPage.toolbar.saveButton.click()
    await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
    await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await expect(itemOne.content.primaryInputs.optionsList.options.C.radioButton).toBeChecked()
  })

  test('Reorder item and make sure answer key is still correctly checked', async ({
    assetEditorPage,
  }) => {
    const itemOne = assetEditorPage.getItemAccordion({ index: 0 })
    const itemTwo = assetEditorPage.getItemAccordion({ index: 1 })

    await scrollIntoAnswerOptionsIntoViewAndClickOption(itemOne)

    const itemOneId = await itemOne.grabbableButton.textContent()
    if (!itemOneId) {
      // throw instead of expect since seems impossible but need to satisfy ts
      throw new Error('Item one has no text?')
    }
    await itemOne.grabbableButton.hover()
    await itemOne.grabbableButton.click()

    let itemOneBox
    let itemTwoBox
    do {
      itemOneBox = await itemOne.grabbableButton.boundingBox()
      itemTwoBox = await itemTwo.grabbableButton.boundingBox()
      // Making sure they're the same height ensures itemOne closed fully
      // after clicking to close it, so the resulting position isn't way too low
    } while (itemOneBox?.height !== itemTwoBox?.height)

    if (itemTwoBox) {
      await itemOne.grabbableButton.hover()
      await assetEditorPage.page.mouse.down()
      await assetEditorPage.page.mouse.move(
        itemTwoBox.x + 40,
        itemTwoBox.y + itemTwoBox.height + 15,
        {
          steps: 5,
        }
      )
      await assetEditorPage.page.mouse.up()
      await itemTwo.grabbableButton.hover()
    }

    // Item One should now be itemTwo
    await expect(itemTwo.grabbableButton).toHaveText(itemOneId)

    await itemTwo.grabbableButton.click()
    await expect(itemTwo.content.primaryInputs.optionsList.options.C.radioButton).toBeChecked()
  })
})

test.describe.skip('Eng lang asset tests', () => {
  // Using a bigger viewport helped fix something around playwright struggling with
  // the reorder I believe, it would just jump back and forth trying to scroll
  test.use({ viewport: { width: 1920, height: 1080 } })
  test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
    const [asset] = await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.ENGLISH_LANGUAGE_AND_COMPOSITION],
      assetProps: { itemType: ItemType.MultipleChoiceQuestion, itemCount: 3 },
      workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
      length: 1,
    })

    if (asset) {
      await assetEditorPage.goToPageWith({ ...asset })
    }
  })

  test('Add a 5th Answer Option, reorder and delete', async ({ assetEditorPage }) => {
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    const itemTwo = assetEditorPage.getItemAccordion({ number: 2 })

    // Collapse item 1 and expand item 2
    await itemOne.grabbableButton.click()
    await itemTwo.grabbableButton.click()

    await itemTwo.content.primaryInputs.stem.root.click()
    await assetEditorPage.page.mouse.wheel(0, 500)

    const { optionsList } = itemTwo.content.primaryInputs
    await optionsList.addAnOption.click()

    await expect(optionsList.options.E.radioButton).toBeVisible()
    await expect(optionsList.options.E.answerEditor.root).toBeVisible()

    // Reorder and Deletion Prep
    // Check an option and get text to ensure it moves
    await optionsList.options.B.radioButton.check()
    const answerOptionText =
      (await optionsList.options.D.answerEditor.root.textContent()) ?? 'Text not found'
    await optionsList.ensureRationaleVisibilityState('show')
    const rationaleText =
      (await optionsList.options.C.rationaleEditor.root.textContent()) ?? 'Text not found'

    const reloadAndPrep = async () => {
      // CKEditor was difficult to force it to rerender or accept changes to config,
      // and wasn't worth the time to keep fixing it since accessibility in our app
      // isn't critical and there's a workaround to just refresh, even if a bit longer
      await assetEditorPage.toolbar.saveButton.click()
      await assetEditorPage.page.reload()

      // Collapse item 1 and expand item 2 again
      await itemOne.grabbableButton.click()
      await itemTwo.grabbableButton.click()

      // Show rationales for verification
      await optionsList.ensureRationaleVisibilityState('show')
    }

    await test.step('Reorder the options', async () => {
      // Hide rationales again, just so the options can all be visible without scrolling
      await optionsList.ensureRationaleVisibilityState('hide')

      // Reorder the options
      await optionsList.reorderOption({
        moveThis: 'E',
        toThis: 'A',
      })

      await reloadAndPrep()

      // Verify the key, content, and rationales moved
      await expect(optionsList.options.C.radioButton).toBeChecked()
      await expect(optionsList.options.E.answerEditor.root).toHaveText(answerOptionText)
      await expect(optionsList.options.D.rationaleEditor.root).toHaveText(rationaleText)
    })

    await test.step('Delete an option', async () => {
      // Ensure delete button not available for keyed answer
      await expect(optionsList.options.C.deleteButton).toBeHidden()

      // Delete an option
      await optionsList.options.B.deleteButton.click()
      await optionsList.deletionModal.buttons.delete.click()

      await reloadAndPrep()

      // Verify the key, content, and rationales moved
      await expect(optionsList.options.B.radioButton).toBeChecked()
      await expect(optionsList.options.D.answerEditor.root).toHaveText(answerOptionText)
      await expect(optionsList.options.C.rationaleEditor.root).toHaveText(rationaleText)
    })
  })

  test('Hide and Show Rationales', async ({ assetEditorPage }) => {
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    const itemThree = assetEditorPage.getItemAccordion({ number: 3 })

    await itemOne.grabbableButton.click() //collapse item 1
    await itemThree.grabbableButton.click()

    const itemThreeOptionsList = itemThree.content.primaryInputs.optionsList
    await itemThreeOptionsList.showRationalesButton.click()
    const letters = ['A', 'B', 'C', 'D'] as const
    const rationales = letters.map(
      (letter) => itemThreeOptionsList.options[letter].rationaleEditor.root
    )

    for (const rationale of rationales) {
      await rationale.fill('This is a rationale explanation.')
    }

    await assetEditorPage.saveWithTimeout()
    await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()

    await itemThree.grabbableButton.click()
    await itemThreeOptionsList.showRationalesButton.click()

    for (const rationale of rationales) {
      await expect(rationale).toHaveText('This is a rationale explanation.')
    }

    // hide rationales
    await itemThreeOptionsList.hideRationalesButton.click()
    for (const rationale of rationales) {
      await expect(rationale).toBeHidden()
    }
  })
})

test.describe.skip('Comp sci principles asset tests', () => {
  test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
    const [asset] = await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.COMPUTER_SCIENCE_PRINCIPLES],
      assetProps: { itemType: ItemType.MultipleChoiceQuestion, itemCount: 3, isMultiSelect: true },
      workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
      length: 1,
    })

    if (asset) {
      await assetEditorPage.goToPageWith({ ...asset })
    }
  })

  test('Select multiple keys and reorder', async ({ assetEditorPage }) => {
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    const { optionsList } = itemOne.content.primaryInputs

    const optionA = optionsList.options.A.checkboxButton
    const optionB = optionsList.options.B.checkboxButton
    const optionC = optionsList.options.C.checkboxButton
    const optionD = optionsList.options.D.checkboxButton
    await optionB.check()
    await expect(optionB).toBeChecked()
    await optionC.check()
    await optionA.uncheck()
    await optionD.uncheck()
    await expect(optionA).not.toBeChecked()
    await expect(optionB).toBeChecked()
    await expect(optionC).toBeChecked()
    await expect(optionD).not.toBeChecked()

    // Reorder the options
    await optionsList.reorderOption({
      moveThis: 'D',
      toThis: 'A',
    })

    await assetEditorPage.toolbar.saveButton.click()
    await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)
    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await expect(optionA).not.toBeChecked()
    await expect(optionB).not.toBeChecked()
    await expect(optionC).toBeChecked()
    await expect(optionD).toBeChecked()
  })
})

test.describe.skip('FRQ choice asset tests', () => {
  test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
    const [asset] = await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.EUROPEAN_HISTORY],
      assetProps: {
        itemType: ItemType.FreeResponseQuestion,
        itemCount: 1,
        subType: SubType.CHOICE_QUESTION,
      },
      workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
      length: 1,
    })

    if (asset) {
      await assetEditorPage.goToPageWith({ ...asset })
    }
  })

  test('Write content on first and second option, when rearrenged expect content to have been switched', async ({
    assetEditorPage,
  }) => {
    const itemOneOptionsList = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
      .optionsList
    const firstOption = itemOneOptionsList.options[0].answerEditor.root
    const secondOption = itemOneOptionsList.options[1].answerEditor.root

    const firstRandomWord = casual.word
    const secondRandomWord = casual.word

    await firstOption.fill(firstRandomWord)
    await secondOption.fill(secondRandomWord)

    await itemOneOptionsList.reorderOption({
      moveThis: 0,
      toThis: 1,
    })

    await expect(firstOption).toHaveText(secondRandomWord)
    await expect(secondOption).toHaveText(firstRandomWord)
  })
})
