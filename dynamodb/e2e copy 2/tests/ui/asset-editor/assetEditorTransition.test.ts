import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import {
  SENDING_TO_VAULT,
  WORKFLOW_STEPS,
  WorkflowStep,
  WorkflowStepID,
  WorkflowStepReadable,
} from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_REVIEW],
    length: 1,
  })) as E2EAsset[]
  if (!asset) {
    throw new Error("Couldn't create an asset!")
  }
  await assetEditorPage.goToPageWith({ ...asset })
})

test.setTimeout(2 * 60 * 1000)

test('should be able to transition through all workflows in asset editor', async ({
  assetEditorPage,
  page,
}) => {
  const directionsInput = assetEditorPage.generalAccordion.content.primaryInputs.directions.root
  const workflows = WORKFLOW_STEPS.filter((step) => !SENDING_TO_VAULT.includes(step)).map(
    ({ label }) => label
  )
  const startingWorkflow = workflows.shift()
  if (!startingWorkflow) throw new Error('Config broken, could not get starting workflow')

  await workflows.reduce(async (promiseChain, workflow) => {
    const previousWorkflow = await promiseChain
    await expect(directionsInput).toHaveAttribute('contenteditable', 'true')
    await assetEditorPage.menu.clickThroughMenu('File', 'Transition')
    await expect(assetEditorPage.transitionDialog.content.fromLocator).toContainText(
      previousWorkflow
    )
    await page.waitForTimeout(2000)
    await expect.soft(assetEditorPage.transitionDialog.content.nextStepRadioButton).toBeVisible()
    await assetEditorPage.transitionDialog.buttons.apply.click()
    await page.waitForTimeout(2000)
    await expect(
      assetEditorPage.toastLocator.filter({ hasText: `1 asset transitioned to "${workflow}"` })
    ).toBeVisible()
    //FIX ME E2E
    // await expect(assetEditorPage.saveStatus).toContainText(workflow)
    return workflow
  }, Promise.resolve(startingWorkflow))

  // .not.toBeEditable() seems to be broken
  await expect(directionsInput).toHaveAttribute('contenteditable', 'false')
})

test('should handle workflow changes correctly', async ({ assetEditorPage }) => {
  const softExpect = expect.configure({ soft: true })

  const directionsInput = assetEditorPage.generalAccordion.content.primaryInputs.directions.root
  const checkIfReadOnly = (yesOrNot = true) =>
    softExpect(directionsInput).toHaveAttribute('contenteditable', yesOrNot ? 'false' : 'true')

  // Will check current workflow is disabled at multiple spots
  const verifyCurrentWorkflowDisabled = async () => {
    const { step } = await assetEditorPage.transitionDialog.getCurrentWorkflow()
    await softExpect(
      assetEditorPage.transitionDialog.content.toSelect.getOptionByIndexOrName({
        name: step,
        exact: true,
      })
    ).toBeDisabled()
    return step
  }

  const data = [
    {
      msg: 'to Ready for Assembly',
      radioVisible: true,
      label: WorkflowStepReadable[WorkflowStep.ReadyForAssembly],
      readOnly: true,
    },
    {
      msg: 'back from Ready for Assembly',
      radioVisible: false,
      label: WorkflowStepReadable[WorkflowStep.CopyEditReview],
      readOnly: false,
    },
    {
      msg: 'to current workflow fails',
      radioVisible: true,
      readOnly: false,
    },
    {
      msg: 'back even further to Internal Content Review 1',
      radioVisible: true,
      label: WorkflowStepReadable[WorkflowStep.InternalContentReview1],
      readOnly: false,
    },
    {
      msg: 'back to Ready for Assembly',
      radioVisible: true,
      label: WorkflowStepReadable[WorkflowStep.ReadyForAssembly],
      readOnly: true,
    },
  ]
  for (const { msg, radioVisible, label, readOnly } of data) {
    await test.step(`Transition asset ${msg}`, async () => {
      await assetEditorPage.menu.clickThroughMenu('File', 'Transition')
      await assetEditorPage.transitionDialog.content.toSelect.root.click()
      await verifyCurrentWorkflowDisabled()

      await softExpect(assetEditorPage.transitionDialog.content.nextStepRadioButton)[
        radioVisible ? 'toBeVisible' : 'toBeHidden'
      ]()

      label
        ? await assetEditorPage.transitionDialog.content.toSelect
            .getOptionByIndexOrName({
              name: label,
              exact: true,
            })
            .click()
        : await assetEditorPage.transitionDialog.content.fromLocator.click()
      await assetEditorPage.transitionDialog.buttons.apply.click()
      await expect(
        assetEditorPage.toastLocator.filter({
          hasText: label ? `1 asset transitioned to "${label}"` : `Unable to transition assets: `,
        })
      ).toBeVisible()

      await checkIfReadOnly(readOnly)
    })
  }
})
