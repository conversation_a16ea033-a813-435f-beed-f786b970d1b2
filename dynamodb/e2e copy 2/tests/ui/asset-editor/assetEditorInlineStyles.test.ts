import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { waitForAssetStateBeforeSave } from '@e2e/util/customWaitForTimeout'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('Verify ability to apply inline styles', async ({ assetEditorPage, page }) => {
  const { primaryInputs } = assetEditorPage.generalAccordion.content

  const inlineStyles = [
    {
      input: primaryInputs.directions,
      dropdownButtonName: 'Instructions',
      styleOptionName: 'Booklet Reference',
      styleLocator: 'span.dap_booklet_reference',
    },
  ]

  for (const { styleOptionName, styleLocator, input, dropdownButtonName } of inlineStyles) {
    const randomSentence = casual.sentence
    await input.root.fill(randomSentence)
    await input.root.selectText()
    await assetEditorPage.toolbar.page.getByRole('button', { name: dropdownButtonName }).click()
    await assetEditorPage.toolbar.page.getByRole('option', { name: styleOptionName }).click()
    await expect(input.root.locator(styleLocator)).toBeVisible()

    await waitForAssetStateBeforeSave(page, 1000)
    const saveButton = assetEditorPage.toolbar.saveButton
    await saveButton.click()

    await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
    await expect(assetEditorPage.toastLocator).toHaveText(/.* saved successfully/)

    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await expect(input.root.locator(styleLocator)).toBeVisible()
  }
})
