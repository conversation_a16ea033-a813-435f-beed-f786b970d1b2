import { expect, test } from '@e2e/cb-test'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

const newCommentText = 'Test adding a new comment'
const editCommentText = 'Test editing a new comment'
const replyCommentText = 'Test replying to a comment'

test('Comment Lifecycle', async ({ assetEditorPage, page }) => {
  const { commentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
  const { addCommentContainer, commentsThread } = commentsTab.content.commentContent.threads
  const { header, body, footer } = commentsThread.commentsContent
  await commentsTab.open()

  await test.step('Add a comment', async () => {
    await expect(commentsTab.contentContainer).not.toContainText(newCommentText)
    await addCommentContainer.addCommentButton.click()
    await addCommentContainer.editableTextBox.textBox.fill(newCommentText)
    await addCommentContainer.editableTextBox.saveButton.click()
    await waitForResponseFrom(assetEditorPage.page, 'commentsAdd')
    await expect(commentsThread.commentsContent.body.root.getByText(newCommentText)).toBeVisible()
  })

  await test.step('Edit a comment', async () => {
    await expect(async () => {
      await header.menuButton.click({ timeout: 1000 })
      await expect(header.menuEditButton).toBeVisible()
    }).toPass({
      timeout: 10_000,
    })
    await header.menuEditButton.click()
    await body.editableTextBox.textBox.fill(editCommentText)
    // TODO - Adding explicit timeout for quick fix for it not actually
    // saving the changes if saving too quickly after filling text. Look
    // into a better solution later. Note: Only affects editing.
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(250)
    await body.editableTextBox.saveButton.click()
    await waitForResponseFrom(assetEditorPage.page, 'commentsEdit')
    await expect(body.root.getByText(editCommentText)).toBeVisible()
  })

  await test.step('Reply to a comment', async () => {
    await footer.replyButton.click()
    await footer.editableTextBox.textBox.fill(replyCommentText)
    await footer.editableTextBox.saveButton.click()
    await waitForResponseFrom(assetEditorPage.page, 'commentsAdd')
    await expect(body.root.getByText(replyCommentText)).toBeVisible()
  })

  await test.step('Delete button should be hidden on a parent comment with replies', async () => {
    await expect(async () => {
      await header.menuButton.nth(0).click({ timeout: 1000 })
      await expect(header.menuEditButton).toBeVisible()
      await expect(header.menuDeleteButton).toBeHidden()
    }).toPass({
      timeout: 10_000,
    })
  })

  await test.step('Reload the page and open the comments tab', async () => {
    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await commentsTab.open()
    await expect(commentsTab.content.commentContent.root).toBeVisible()
  })

  await test.step('Delete a reply comment', async () => {
    await expect(async () => {
      await header.menuButton.nth(1).click({ timeout: 1000 })
      await expect(header.menuDeleteButton).toBeVisible()
    }).toPass({
      timeout: 10_000,
    })
    await header.menuDeleteButton.click()
    await expect(header.deleteCommentDialog.root).toBeVisible()
    await header.deleteCommentDialog.buttons.remove.click()
    await waitForResponseFrom(assetEditorPage.page, 'commentsRemove')
    await expect(commentsTab.content.commentContent.root).toBeVisible()
    // check that the reply is gone
    await expect(body.root.getByText(replyCommentText)).toBeHidden()
  })

  await test.step('Delete a parent comment', async () => {
    await expect(async () => {
      await header.menuButton.click({ timeout: 1000 })
      await expect(header.menuDeleteButton).toBeVisible()
    }).toPass({
      timeout: 10_000,
    })
    await header.menuDeleteButton.click()
    await expect(header.deleteCommentDialog.root).toBeVisible()
    await header.deleteCommentDialog.buttons.remove.click()
    await waitForResponseFrom(assetEditorPage.page, 'commentsRemove')
    await expect(commentsTab.content.commentContent.root).toBeVisible()
    // check that the reply is gone
    await expect(body.root.getByText(editCommentText)).toBeHidden()
  })

  await test.step('General Section Comments Disabled', async () => {
    await assetEditorPage.generalAccordion.open()
    const { commentsTab } = assetEditorPage.generalAccordion.content

    await commentsTab.open()

    const { addCommentContainer } = commentsTab.content.commentContent.threads

    await expect(addCommentContainer.addCommentButton).toBeHidden()
  })
})
