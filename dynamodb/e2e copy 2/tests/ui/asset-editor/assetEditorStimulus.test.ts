import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import type { AssetEditorPage } from '@e2e/pages'
import { DataController, DataControllerModel } from '@e2e/util/dataUtils'
import type { Page } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { StimulusFormatId, StimulusFormatIdReadable } from '@shared/types/model'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const verifySourceDocumentLabelInputCharacterLimit = async ({
  assetEditorPage,
  dataController,
  type,
}: {
  assetEditorPage: AssetEditorPage
  dataController: DataController
  page: Page
  type: StimulusFormatId.SOURCE | StimulusFormatId.DOCUMENT
}) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: {
      itemType: ItemType.FreeResponseQuestion,
      stimulusType: type,
    },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
    await assetEditorPage
      .getStimulusAddButton({
        type: StimulusFormatIdReadable[type].toLocaleLowerCase() as 'source' | 'document',
      })
      .click()
  }
  const { sourceDocumentLabel, sourceAttribution, stimulusTitle, stimulus } =
    assetEditorPage.getStimulusAccordion({
      index: 0,
      type: StimulusFormatIdReadable[type],
    }).content.primaryInputs
  const text1 = 'aabb'
  await test.step('Verify char limit', async () => {
    await sourceDocumentLabel.root.fill(text1)
    const innerText = await sourceDocumentLabel.root.inputValue()
    expect(innerText.length).toEqual(2)
    expect(innerText).toEqual('aa')
  })

  await test.step('Verify all labels appear for source type stimulus', async () => {
    await expect(sourceDocumentLabel.root).toBeVisible()
    await expect(sourceAttribution.root).toBeVisible()
    await expect(stimulusTitle.root).toBeVisible()
    await expect(stimulus.root).toBeVisible()
  })
}

test('Verify Source and Document Label input character limit', async ({
  assetEditorPage,
  dataController,
  page,
}) => {
  await verifySourceDocumentLabelInputCharacterLimit({
    assetEditorPage,
    dataController,
    page,
    type: StimulusFormatId.SOURCE,
  })
  await verifySourceDocumentLabelInputCharacterLimit({
    assetEditorPage,
    dataController,
    page,
    type: StimulusFormatId.DOCUMENT,
  })
})
