import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to insert footnote and save', async ({ assetEditorPage, page }) => {
  const randomMarkNumber = casual.integer()
  const randomMarkNumberString = randomMarkNumber.toString()
  const randomSentence = casual.sentence

  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const { footnotesDialog } = assetEditorPage

  await stem.root.click()

  await assetEditorPage.menu.clickThroughMenu('Insert', 'Footnote')

  await footnotesDialog.content.markInput.fill(randomMarkNumberString)
  await footnotesDialog.content.footnoteInput.fill(randomSentence)
  await assetEditorPage.footnotesDialog.buttons.apply.click()

  await assetEditorPage.toolbar.saveButton.click()
  await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)

  await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()

  await expect(stem.root.getByRole('link', { name: `Footnote` })).toContainText(
    randomMarkNumberString
  )

  await stem.root
    .getByRole('link', {
      name: `Footnote ${randomMarkNumberString}`,
    })
    //using a regular click here fails to open up the footnote modal, the pointer events is set to none, so the parent element is detected for the click instead
    // eslint-disable-next-line playwright/no-force-option
    .click({ force: true })

  await expect(footnotesDialog.content.markInput).toHaveValue(randomMarkNumberString)
  await expect(footnotesDialog.content.footnoteInput).toHaveText(randomSentence)
})
