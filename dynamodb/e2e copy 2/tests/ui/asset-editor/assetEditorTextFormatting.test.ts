import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController }) => {
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify text block formatting options from the menu including alignment, indentation, and list styles', async ({
  assetEditorPage,
}) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const stemTextbox = stem.root.locator('.dap_body')
  await stem.root.click()

  const textAlignments = ['Center', 'Right']
  const textAlignCSS = ['center', 'right']
  for (let i = 0; i < textAlignments.length; i++) {
    await assetEditorPage.menu.clickThroughMenu('Format', 'Align & Indent', textAlignments[i])
    await expect(stemTextbox).toHaveCSS('text-align', textAlignCSS[i])
  }

  const indents = ['Increase indent', 'Increase Indent', 'Decrease Indent']
  const marginLeftCSS = ['11px', '22px', '11px']
  for (let i = 0; i < indents.length; i++) {
    await assetEditorPage.menu.clickThroughMenu('Format', 'Align & Indent', indents[i])
    await expect(stemTextbox).toHaveCSS('margin-left', marginLeftCSS[i])
  }

  const orderedList = stem.root.locator('ol')
  const listTypes = [
    'Numbered List',
    'Lower-Latin List',
    'Upper-Latin List',
    'Upper-Roman List',
    'Lower-Roman List',
  ]
  const listStyleTypes = ['decimal', 'lower-latin', 'upper-latin', 'upper-roman', 'lower-roman']
  for (let i = 0; i < listTypes.length; i++) {
    await assetEditorPage.menu.clickThroughMenu('Format', 'Bullets & Numbering', listTypes[i])
    await expect(orderedList).toHaveCSS('list-style-type', listStyleTypes[i])
    await expect(orderedList).toBeVisible()
  }

  const unorderedList = stem.root.locator('ul')
  const bulletTypes = [/Bulleted List/, 'Non-bulleted List']
  const bulletStyleTypes = ['disc', 'none']

  await stem.root.fill(`${casual.sentence}\n${casual.sentence}`)

  for (let i = 0; i < bulletTypes.length; i++) {
    await stem.root.selectText()
    await assetEditorPage.menu.clickThroughMenu('Format', 'Bullets & Numbering', bulletTypes[i])
    await expect(unorderedList).toHaveCSS('list-style-type', bulletStyleTypes[i])
    await expect(unorderedList).toBeVisible()
    await assetEditorPage.toolbar.bulletedListButton.click() // reset to normal text w/o <ul>
  }
})

test('Verify ability to apply different numbered list styles and text alignments to a text block', async ({
  assetEditorPage,
}) => {
  const responsePromise = waitForResponseFrom(assetEditorPage.page, endpoints.METADATA_READ_ONE)
  await responsePromise

  const courseFrameworkAlignmentTextbox = assetEditorPage.getItemAccordion({ number: 1 }).content
    .metadataTab.content.courseFrameworkAlignment
  await expect(courseFrameworkAlignmentTextbox).toBeVisible()

  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const randomSentence = casual.sentence
  await stem.root.click()
  await stem.root.fill(randomSentence)
  await stem.root.selectText()

  const orderedList = stem.root.locator('ol')
  const listButtons = [
    { button: assetEditorPage.toolbar.decimalListButton, style: 'decimal' },
    {
      button: assetEditorPage.toolbar.decimalLeadingZeroListButton,
      style: 'decimal-leading-zero',
    },
    { button: assetEditorPage.toolbar.lowerLatinListButton, style: 'lower-latin' },
    { button: assetEditorPage.toolbar.upperLatinListButton, style: 'upper-latin' },
    { button: assetEditorPage.toolbar.upperRomanListButton, style: 'upper-roman' },
    { button: assetEditorPage.toolbar.lowerRomanListButton, style: 'lower-roman' },
  ]

  const textAlignments = [
    assetEditorPage.toolbar.alignCenterButton,
    assetEditorPage.toolbar.alignRightButton,
  ]
  const textAlignCSS = ['center', 'right']

  for (let i = 0; i < listButtons.length; i++) {
    await assetEditorPage.toolbar.numberedListButton.click()
    await listButtons[i].button.click()
    await expect(orderedList).toHaveCSS('list-style-type', listButtons[i].style)
    await expect(orderedList).toBeVisible()

    // Add alignment functionality after each list type
    for (let j = 0; j < textAlignments.length; j++) {
      await textAlignments[j].click()
      const liChildren = await orderedList.locator('li').getByRole('paragraph').all()
      expect(liChildren).not.toHaveLength(0)
      for (let k = 0; k < liChildren.length; k++) {
        await expect(liChildren[k]).toHaveCSS('text-align', textAlignCSS[j])
      }
    }
  }
  await test.step('Verify we can reverse list style', async () => {
    await assetEditorPage.toolbar.numberedListButton.click()
    await assetEditorPage.toolbar.listPropertiesButton.click()
    await assetEditorPage.toolbar.reversedButton.click()
    await expect(orderedList).toHaveAttribute('reversed')
  })
})

test('Verify ability to clear all formattings', async ({ assetEditorPage }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const stemTextbox = stem.root.locator('.dap_body')
  await stem.root.click()

  await assetEditorPage.menu.clickThroughMenu('Format', 'Align & Indent', 'Right')
  await assetEditorPage.menu.clickThroughMenu('Format', 'Align & Indent', 'Increase Indent')
  await assetEditorPage.menu.clickThroughMenu('Format', 'Align & Indent', 'Apply Hanging Indent')

  await assetEditorPage.menu.clickThroughMenu('Format', 'Clear Formatting')
  await expect(stemTextbox).toHaveCSS('text-align', 'left')
  await expect(stemTextbox).toHaveCSS('margin-left', '0px')
  await expect(stemTextbox).toHaveCSS('padding-left', '0px')
})
