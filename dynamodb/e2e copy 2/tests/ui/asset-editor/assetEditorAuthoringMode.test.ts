import { expect, test } from '@e2e/cb-test'
import casual from 'casual'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('Highlighting mode tests', async ({ assetEditorPage }) => {
  const { modeDropdown, highlightDropdown, defaultDropdown } = assetEditorPage
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  await test.step('fill stem with content and verify that initially no highlight css is applied', async () => {
    const text = casual.text
    stem.root.fill(text)
    const paragraph = stem.root.getByText(text)
    await expect(paragraph).toHaveCSS('background-color', 'rgba(0, 0, 0, 0)')
  })
  await test.step('click on the highlight option from the dropdown, and see if it highlights', async () => {
    const text = 'test edit works'
    stem.root.fill(text)
    await modeDropdown.click()
    await highlightDropdown.click()
    const paragraph = stem.root.getByText(text)
    await expect(paragraph).toHaveCSS('background-color', 'rgb(255, 243, 173)')
  })
  await test.step('click on the default dropdown, and see if it does not highlight', async () => {
    const text = 'test edit works when no highlight'
    stem.root.fill(text)
    await modeDropdown.click()
    await defaultDropdown.click()
    const paragraph = stem.root.getByText(text)
    await expect(paragraph).toHaveCSS('background-color', 'rgba(0, 0, 0, 0)')
  })

  await test.step('fill stem with content and verify that initially no highlight css is applied', async () => {
    const text = casual.text
    stem.root.fill(text)
    const paragraph = stem.root.getByText(text)
    await expect(paragraph).toHaveCSS('background-color', 'rgba(0, 0, 0, 0)')
  })
})
test('Html Viewer mode tests', async ({ assetEditorPage }) => {
  const { modeDropdown, htmlViewDropdown } = assetEditorPage
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const text = '!!!!!!!!!!!!!'

  await test.step('write some text and confirm html is not being applied', async () => {
    await stem.root.fill(text)
    const elementWithText = stem.root.getByText(text)
    await assetEditorPage.page.waitForTimeout(250)
    await expect(elementWithText).toBeVisible()
    await expect(elementWithText).not.toHaveClass(/language-html/)
  })

  await test.step('click on the html view mode option from the dropdown, and see if it returns the proper class', async () => {
    await modeDropdown.click()
    await htmlViewDropdown.click()
    await assetEditorPage.page.waitForSelector('code.language-html')
    const codeElements = assetEditorPage.page.locator('code.language-html')
    const elementWithText = codeElements.filter({ hasText: text })
    await expect(elementWithText).toBeVisible()
    await expect(elementWithText).toHaveClass(/language-html/)
  })
})
