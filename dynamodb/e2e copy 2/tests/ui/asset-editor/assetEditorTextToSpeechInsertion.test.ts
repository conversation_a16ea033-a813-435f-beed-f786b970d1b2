import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to add text to speech tags', async ({ assetEditorPage }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const { textToSpeechDialog } = assetEditorPage

  const ssmlTaggingText = 'This is text for SSML tagging.'
  const ssmlInputText = 'substitution text'

  await stem.root.fill(ssmlTaggingText)
  await stem.root.selectText()

  await assetEditorPage.menu.clickThroughMenu('Tools', 'Text to Speech (SSML)')

  await textToSpeechDialog.content.ssmlSelect.selectOption('substitution')
  await textToSpeechDialog.content.ssmlSubstitutionInput.fill(ssmlInputText)
  await textToSpeechDialog.buttons.apply.click()

  const ssmlTag = stem.root.getByText(ssmlTaggingText)
  const labelText = await ssmlTag.getAttribute('ssml-label')
  expect(labelText).toBe('Substitution')
  const subAliasText = await ssmlTag.getAttribute('data-ssml-sub-alias')
  expect(subAliasText).toBe(ssmlInputText)
})
