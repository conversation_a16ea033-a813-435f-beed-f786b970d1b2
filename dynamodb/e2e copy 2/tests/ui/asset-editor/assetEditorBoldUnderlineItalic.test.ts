import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
//E2E FIX ME
test.skip('Verify ability to insert B/U/I text and save', async ({
  assetEditorPage,
  dataController,
  page,
}) => {
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const text1 = casual.sentence
  await stem.root.selectText()
  await page.keyboard.press('Backspace')
  await stem.root.fill(text1)

  await test.step('Verify ability to bold, italic, underline to text; And save button can successfully save the formattings', async () => {
    await stem.root.selectText()
    await assetEditorPage.toolbar.boldButton.click()
    await assetEditorPage.toolbar.italicButton.click()
    await assetEditorPage.toolbar.underlineButton.click()
    await expect(page.getByText(text1)).toHaveCSS('font-weight', '900')
    await expect(page.getByText(text1)).toHaveCSS('font-style', 'italic')
    await expect(page.getByText(text1)).toHaveCSS('text-decoration-style', 'solid')

    await assetEditorPage.toolbar.saveButton.click()
    await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
    await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)
    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await expect(page.getByText(text1)).toHaveCSS('font-weight', '900')
    await expect(page.getByText(text1)).toHaveCSS('font-style', 'italic')
    await expect(page.getByText(text1)).toHaveCSS('text-decoration-style', 'solid')
  })
  await test.step('Verify ability to use shortcuts to apply Bold, Italic, Underline to text', async () => {
    await stem.root.selectText()
    await assetEditorPage.menu.clickThroughMenu('Format', 'Clear Formatting')
    await stem.root.selectText()

    await page.keyboard.press('Meta+KeyB')
    await page.keyboard.press('Meta+KeyI')
    await page.keyboard.press('Meta+KeyU')
    await page.keyboard.press('Control+KeyB')
    await page.keyboard.press('Control+KeyI')
    await page.keyboard.press('Control+KeyU')
    await expect(page.getByText(text1)).toHaveCSS('font-weight', '900')
    await expect(page.getByText(text1)).toHaveCSS('font-style', 'italic')
    await expect(page.getByText(text1)).toHaveCSS('text-decoration-style', 'solid')

    // Currently changing the text formats won't enable the save button in subsequent test.steps
    // Address this issue in another ticket
    await expect(async () => {
      await assetEditorPage.toolbar.saveButton.click({ timeout: 3000 })
      await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
      await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)
    }).rejects.toThrow()

    await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
    await expect(page.getByText(text1)).toHaveCSS('font-weight', '900')
    await expect(page.getByText(text1)).toHaveCSS('font-style', 'italic')
    await expect(page.getByText(text1)).toHaveCSS('text-decoration-style', 'solid')
  })
})
