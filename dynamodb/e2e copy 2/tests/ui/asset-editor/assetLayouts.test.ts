import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import type { TextEditor } from '@e2e/components/common'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { PreviewPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })

    // I don't recall the reason for doing this first
    await assetEditorPage.getStimulusAddButton({ type: 'stimulus' }).click()
  }
})

test('Basic layout ordering in preview', async ({ assetEditorPage, context }) => {
  const { generalAccordion } = assetEditorPage
  const { directions } = generalAccordion.content.primaryInputs
  const { layout: layoutCombobox } = generalAccordion.content.metadataTab.content
  const stimulusAccordion = assetEditorPage.getStimulusAccordion({ index: 0 })
  const { passageIntroduction, stimulusTitle, stimulus, sourceAttribution, creditLine, caption } =
    stimulusAccordion.content.primaryInputs
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  // Define layout ordering, see https://miro.com/app/board/uXjVNvKKMmo=/
  type LAYOUT_ORDER = {
    layoutName: string
    left?: (TextEditor & { tempTextForTest?: string })[]
    right: (TextEditor & { tempTextForTest?: string })[]
  }
  const stimulusArray = [
    passageIntroduction,
    stimulusTitle,
    stimulus,
    creditLine,
    sourceAttribution,
    caption,
  ]
  // Adding prettier-ignore so the arrays can stay multiline. This feels nice for
  // this specific case, as they represent the vertical ordering more visually
  // prettier-ignore
  const stemSinglePane: LAYOUT_ORDER = {
    layoutName: 'Stem Single Pane',
    right: [
      directions,
      ...stimulusArray,
      stem,
    ]
  }
  // prettier-ignore
  const stemRightPane: LAYOUT_ORDER = {
    layoutName: 'Stem Right Pane',
    left: [
      ...stimulusArray,
    ],
    right: [
      directions,
      stem,
    ]
  }
  // prettier-ignore
  const stemLeftPane: LAYOUT_ORDER = {
    layoutName: 'Stem Left Pane',
    left: [
      directions,
      ...stimulusArray,
      stem,
    ],
    right: []
  }
  // prettier-ignore
  const dirLeftStemRightPane: LAYOUT_ORDER = {
    layoutName: 'Directions Left Pane, Stem Right Pane',
    left: [
      directions,
      ...stimulusArray,
    ],
    right: [
      stem
    ]
  }

  // Generated assets start with text, but need to add stimuli
  for (const editor of stimulusArray) {
    await editor.root.fill(casual.text)
  }

  // The need for a pause before saving is noted in the creation journey test
  // eslint-disable-next-line playwright/no-wait-for-timeout
  await assetEditorPage.page.waitForTimeout(1000)

  // Save and open preview
  await assetEditorPage.toolbar.saveButton.click()
  const pagePromise = context.waitForEvent('page')
  await assetEditorPage.menu.clickThroughMenu('View', 'Preview', 'New Tab')
  const previewPage = new PreviewPage({ page: await pagePromise })
  await previewPage.page.waitForLoadState()

  // Get text for all editors
  await generalAccordion.open()
  for (const editor of stemSinglePane.right) {
    await expect(editor.root, 'Not all editors are visible').toBeVisible()
    const text = await editor.root.textContent()
    if (!text) {
      throw new Error(`An editor has no text! ${JSON.stringify(editor)}?`)
    }
    editor.tempTextForTest = text
  }

  // Loop through layouts and their arrays, making sure positioning is right by text
  // Note that it may be best to make sure first layout tested is not any default
  const layouts = [stemSinglePane, stemRightPane, stemLeftPane, dirLeftStemRightPane]
  const { root: previewerContainer } = previewPage.contents

  for (const layout of layouts) {
    await layoutCombobox.selectOption(layout.layoutName)
    if (layout.left) {
      for (let i = 0; i < layout.left.length; i++) {
        const below = i === 0 ? '' : `:below(:text("${layout.left[i - 1]?.tempTextForTest}"))`
        const above =
          i === layout.left.length - 1
            ? ''
            : `:above(:text("${layout.left[i + 1]?.tempTextForTest}"))`
        // TODO: Once Dave Marini from exam player team fixes the accessibility issue, uncomment
        // const locationLocator = stimulusContainer.locator(
        const locationLocator = previewerContainer.locator(
          `:text("${layout.left[i]?.tempTextForTest}")${below}${above}`
        )

        const errorMessage = `Couldn't find the ${layout.left[i]?.name} text in its expected previewer spot for ${layout.layoutName}`
        await expect(locationLocator, errorMessage).toBeVisible()
      }
    }
    for (let i = 0; i < layout.right.length; i++) {
      const below = i === 0 ? '' : `:below(:text("${layout.right[i - 1]?.tempTextForTest}"))`

      const above =
        i === layout.right.length - 1
          ? ''
          : `:above(:text("${layout.right[i + 1]?.tempTextForTest}"))`
      // TODO: Once Dave Marini from exam player team fixes the accessibility issue, uncomment
      // const locationLocator = (
      //   layout.layoutName === 'Stem Single Pane' ? discreteQuestionContainer : questionContainer
      // ).locator(`:text("${layout.right[i]?.tempTextForTest}")${below}${above}`)
      const locationLocator = previewerContainer.locator(
        `:text("${layout.right[i]?.tempTextForTest}")${below}${above}`
      )
      await expect(locationLocator).toBeVisible()
    }
    // Not sure if checking for the answers is needed here, as they're always in
    // the same spot, and their ordering and such should be tested elsewhere
  }
})
