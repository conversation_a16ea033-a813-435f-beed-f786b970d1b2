import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})
test('Verify ability to add aria heading attribute to text', async ({ assetEditorPage }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  await stem.root.selectText()
  await assetEditorPage.menu.clickThroughMenu('Tools', 'ARIA Heading')
  await stem.root.click()
  await assetEditorPage.ariaHeading.editButton.click()
  await assetEditorPage.ariaHeading.clickAriaLevel(3)
  await assetEditorPage.ariaHeading.saveButton.click()
  await expect(stem.root.getByRole('heading')).toHaveAttribute('aria-level', '3')
  await stem.root.click()
  await assetEditorPage.ariaHeading.deleteButton.click()
  await expect(stem.root.getByRole('heading')).toBeHidden()
})

test('Screen reader attribute tests', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  await test.step('Verify ability to add screen reader attribute to text', async () => {
    await stem.root.selectText()
    await assetEditorPage.menu.clickThroughMenu('Tools', 'Off-screen (sr-only) Text')
    await assetEditorPage.screenReader.screenReaderContentTextBox.fill('screen read test')
    await assetEditorPage.screenReader.saveButton.click()
  })

  await test.step('Verify ability to edit screen reader text', async () => {
    await assetEditorPage.screenReader.editButton.click()
    await expect(assetEditorPage.screenReader.screenReaderContentTextBox).toHaveValue(
      'screen read test'
    )
    await assetEditorPage.screenReader.screenReaderContentTextBox.fill('updated screen read test')
    await assetEditorPage.screenReader.saveButton.click()
    await expect(stem.root.locator('.dap_body > span')).toHaveAttribute(
      'sr',
      'updated screen read test'
    )
  })

  await test.step('Verify ability to remove screen reader attribute', async () => {
    await stem.root.selectText()
    await assetEditorPage.screenReader.removeScreenReaderAttributeButton.click()
    await expect(stem.root.locator('.dap_body > span')).toBeHidden()
  })
})
test('Language attribute tests', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  await test.step('Verify ability to add language attribute to text', async () => {
    await stem.root.selectText()
    await assetEditorPage.menu.clickThroughMenu('Tools', 'Language')
    await assetEditorPage.languageAttribute.languageSelectionDropdown.fill('Japanese')
    await page.getByRole('button', { name: 'Japanese' }).click()
    await assetEditorPage.languageAttribute.saveButton.click()
    await expect(stem.root.locator('.dap_body > span')).toHaveAttribute('lang', 'jpn')
  })

  await test.step('Verify ability to edit language selection attribute', async () => {
    await assetEditorPage.languageAttribute.langSpan.click()
    await assetEditorPage.languageAttribute.languageSelectionDropdown.fill('Danish')
    await page.getByRole('button', { name: 'Danish' }).click()
    await assetEditorPage.languageAttribute.saveButton.click()
    await expect(stem.root.locator('.dap_body > span')).toHaveAttribute('lang', 'dan')
  })

  await test.step('Verify ability to remove language attribute', async () => {
    await stem.root.selectText()
    await assetEditorPage.languageAttribute.langSpan.click()
    await assetEditorPage.languageAttribute.deleteButton.click()
    await assetEditorPage.langRemovalDialog.buttons.remove.click()
    await expect(stem.root.locator('.dap_body > span')).toBeHidden()
  })

  await test.step.skip('Verify ability to add language attribute via toolbar', async () => {
    await stem.root.selectText()
    await assetEditorPage.languageAttribute.languageToolbarButton.click()
    await expect(assetEditorPage.languageAttribute.deleteButton).toBeDisabled()
    await expect(assetEditorPage.languageAttribute.saveButton).toBeDisabled()
    await assetEditorPage.languageAttribute.languageSelectionDropdown.click()
    await page.getByLabel('Czech').click()
    await assetEditorPage.languageAttribute.saveButton.click()
    await expect(stem.root.locator('.dap_body > span')).toHaveAttribute('lang', 'ces')
  })
})
