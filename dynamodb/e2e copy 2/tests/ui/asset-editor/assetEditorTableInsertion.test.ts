import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to insert table and save', async ({ assetEditorPage, page }) => {
  // Generate four random table numbers and convert them to strings
  const randomTableNumbers = Array.from({ length: 4 }, () => casual.integer().toString())

  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  await stem.root.click()

  await assetEditorPage.toolbar.insertTableButton.click()
  await assetEditorPage.toolbar.chooseTableSize(2, 2)
  // Fill the table cells with the random numbers
  for (let i = 0; i < randomTableNumbers.length; i++) {
    await stem.root
      .getByRole('textbox')
      .nth(i)
      .fill(randomTableNumbers[i] as string)
  }

  await assetEditorPage.saveWithTimeout(500)

  await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()

  // Verify the contents of each cell after save
  for (let i = 0; i < randomTableNumbers.length; i++) {
    const cellContent = stem.root.getByRole('textbox').nth(i)
    await expect(cellContent).toContainText(randomTableNumbers[i] as string)
  }
})
