import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('isActive Lifecycle', async ({ assetEditorPage }) => {
  const { archiveDialog, restoreDialog } = assetEditorPage

  await test.step('Archive the asset ', async () => {
    await assetEditorPage.menu.clickThroughMenu('File', 'Archive')
    archiveDialog.buttons.archive.click()
    await assetEditorPage.page.waitForSelector('.archived-label', { state: 'visible' })
    expect(await assetEditorPage.isArchived()).toBe(true)
  })

  await test.step('Restore the asset ', async () => {
    await assetEditorPage.menu.clickThroughMenu('File', 'Restore')
    restoreDialog.buttons.restore.click()
    await assetEditorPage.page.waitForSelector('.archived-label', { state: 'hidden' })
    expect(await assetEditorPage.isArchived()).toBe(false)
  })
})
