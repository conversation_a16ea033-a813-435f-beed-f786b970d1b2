import { expect, test } from '@e2e/cb-test'
import casual from 'casual'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('Verify ability to add spacing to asset editor', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  await test.step('Verify ability to line break to text', async () => {
    await stem.root.click()
    await assetEditorPage.menu.clickThroughMenu('Insert', 'Line Break')
    const innerHTML = await stem.root.locator('p.dap_body').innerHTML()
    expect(innerHTML).toContain('<br>')
  })
  await test.step('Verify ability to click enter on text', async () => {
    await stem.root.selectText()
    await page.keyboard.press('Backspace')
    const text1 = casual.sentence
    await stem.root.fill(text1)
    await page.keyboard.press('Enter')
    const innerHTML = await stem.root.locator('p.dap_body').first().innerHTML()
    expect(innerHTML).toContain(text1)
  })

  await test.step('Verify ability to add non-breaking space to text', async () => {
    await stem.root.selectText()
    await page.keyboard.press('Backspace')
    await assetEditorPage.menu.clickThroughMenu('Insert', 'Non-Breaking Space')
    await expect(stem.root.locator('.dap_body > span')).toHaveClass(/(\bnbsp\b)/)
  })
})
