import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]

  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to insert gloss and save', async ({ assetEditorPage, page }) => {
  const { glossDialog } = assetEditorPage
  const randomWord = casual.word
  const directionsInput = assetEditorPage.generalAccordion.content.primaryInputs.directions.root
  await directionsInput.fill(randomWord)
  await directionsInput.selectText()
  await page.waitForTimeout(300)
  await assetEditorPage.menu.clickThroughMenu('Insert', 'Gloss')
  const wordExplanation = casual.word
  await glossDialog.content.wordExplanation.fill(wordExplanation)
  await page.waitForTimeout(300)
  await assetEditorPage.glossDialog.buttons.apply.click()
  await page.waitForTimeout(300)
  await assetEditorPage.toolbar.saveButton.click()

  await expect(assetEditorPage.toastLocator).toContainText(/Item .* saved successfully/)
  const glossedText = directionsInput.getByText(randomWord)

  // Changes can be observed immediately.
  await expectGlossAttributes()
  // Changes can be observed after a reload.
  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
  await expectGlossAttributes()

  async function expectGlossAttributes() {
    await expect(glossedText).toHaveAttribute(
      'data-cb-title',
      `<p class="dap_body">${wordExplanation}</p>`
    )
    await expect(glossedText).toHaveAttribute('aria-roledescription', 'Define term')
    await expect(glossedText).toHaveAttribute('data-cbtrack-comp', 'apricot-vanilla:tooltip')
  }
})
