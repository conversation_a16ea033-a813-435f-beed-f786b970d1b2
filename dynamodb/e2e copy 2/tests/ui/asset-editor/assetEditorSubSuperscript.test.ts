import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Verify ability to insert sub/superscript', async ({
  assetEditorPage,
  dataController,
  page,
}) => {
  const itemType = ItemType.MultipleChoiceQuestion
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]

  if (!asset) throw new Error('Failed to generate asset')

  await assetEditorPage.goToPageWith({ ...asset })
  await assetEditorPage.waitUntilAssetEditorStateSettles()

  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs

  const scriptTypes = [
    { button: assetEditorPage.toolbar.superscriptButton, tag: 'sup', text: casual.sentence },
    { button: assetEditorPage.toolbar.subscriptButton, tag: 'sub', text: casual.sentence },
  ]
  await stem.root.selectText()

  await page.keyboard.press('Backspace')
  for (const scriptType of scriptTypes) {
    await test.step(`Verify ability to add ${scriptType.tag}script`, async () => {
      await scriptType.button.click()
      await stem.root.pressSequentially(scriptType.text, { delay: 200 })
      await scriptType.button.click() // Turn off the current script type
      await stem.root.press('Enter') // Move the text cursor to a new line

      const paragraphIndex = scriptType.tag === 'sup' ? 0 : 1
      const paragraphHTML = await stem.root.locator('p.dap_body').nth(paragraphIndex).innerHTML()
      expect(paragraphHTML).toContain(`<${scriptType.tag}>${scriptType.text}</${scriptType.tag}>`)
    })
  }

  await stem.root.blur() // ensures changes in input are detected in state
  await assetEditorPage.toolbar.saveButton.click()
  await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
  await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
  await waitForResponseFrom(page, endpoints.ITEM_SET_VERSION)
  await stem.root.locator('p.dap_body').first().waitFor()
  const paragraphs = await stem.root.locator('p.dap_body').all()
  const innerHTMLs = await Promise.all(paragraphs.map((p) => p.innerHTML()))
  const reloadedInnerHTML = innerHTMLs.join('')

  for (const scriptType of scriptTypes) {
    await test.step(`Verify ${scriptType.tag}script is preserved after reload`, async () => {
      expect(reloadedInnerHTML).toContain(
        `<${scriptType.tag}>${scriptType.text}</${scriptType.tag}>`
      )
    })
  }
})
