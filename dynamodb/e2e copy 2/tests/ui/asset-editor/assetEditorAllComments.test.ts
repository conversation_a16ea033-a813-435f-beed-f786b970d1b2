import fs from 'fs'
import { parse } from 'csv-parse/sync'
import { expect, test } from '@e2e/cb-test'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

const newCommentText = 'Test adding a new comment'
let assetId = ''

const getDate = function () {
  const currentDate = new Date()
  const day = currentDate.getDate()
  const month = currentDate.toLocaleString('en-US', { month: 'short' }).substring(0, 3)
  const year = currentDate.getFullYear()
  return `${currentDate.toDateString().substr(0, 3)}_${month} ${day
    .toString()
    .padStart(2, '0')} ${year}`
}

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  assetId = randomAsset.id
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('All Comments', async ({ assetEditorPage, page }) => {
  const { commentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
  const { addCommentContainer, commentsThread } = commentsTab.content.commentContent.threads
  await commentsTab.open()

  // add a comment
  await expect(commentsTab.contentContainer).not.toContainText(newCommentText)
  await addCommentContainer.addCommentButton.click()
  await addCommentContainer.editableTextBox.textBox.fill(newCommentText)
  await addCommentContainer.editableTextBox.saveButton.click()
  await waitForResponseFrom(assetEditorPage.page, 'commentsAdd')
  await expect(commentsThread.commentsContent.body.root.getByText(newCommentText)).toBeVisible()

  // view all comments
  const { downloadCommentsButton, downloadCSVButton } = assetEditorPage
  await assetEditorPage.menu.clickThroughMenu('View', 'All Comments')

  await test.step('Download All Comments', async () => {
    const comments = await assetEditorPage.allCommentsContainer.allTextContents()
    await expect(comments[0]).toContain(newCommentText)
    await downloadCommentsButton.click()
    await expect(assetEditorPage.downloadCommentsDialog.root).toBeVisible()
    const downloadPath = await clickAndDownloadFile(
      page,
      downloadCSVButton,
      assetId + '-COMMENTS-' + getDate() + '.csv'
    )

    const downloadedComments = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })

    await expect(downloadedComments[0].content).toContain(newCommentText)
  })

  await test.step('Download External Comments Only', async () => {
    const comments = await assetEditorPage.allCommentsContainer.allTextContents()

    await expect(comments[0]).toContain(newCommentText)
    await downloadCommentsButton.click()
    await expect(assetEditorPage.downloadCommentsDialog.root).toBeVisible()
    await assetEditorPage.externalUsersOnlyButton.check()

    const downloadPath = await clickAndDownloadFile(
      page,
      downloadCSVButton,
      assetId + '-COMMENTS-' + getDate() + '.csv'
    )

    const downloadedComments = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })

    await expect(downloadedComments.length).toEqual(0)
  })
})
