import casual from 'casual'
import { expect, test } from '@e2e/cb-test'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

type EditingTools = 'Copy' | 'Paste' | 'Cut' | 'Undo' | 'Redo'
;['Menu', 'Toolbar'].forEach((toolLocation) => {
  test(`Standard Text Editing - ${toolLocation}`, async ({ assetEditorPage }) => {
    const directionsInput = assetEditorPage.generalAccordion.content.primaryInputs.directions.root
    const { menu, toolbar } = assetEditorPage

    const clickTool = async (toolName: EditingTools) => {
      if (toolLocation === 'Menu') {
        return menu.clickThroughMenu('Edit', toolName)
      }
      if (toolLocation === 'Toolbar') {
        const lowerToolName = toolName.toLowerCase() as Lowercase<EditingTools>
        const toolProp = `${lowerToolName}Button` as `${typeof lowerToolName}Button`
        return toolbar[toolProp].click()
      }
    }

    const initialText = casual.text
    const doubledText = initialText.repeat(2)
    await directionsInput.fill(initialText)

    await directionsInput.selectText()
    // Normally, explicit timeouts are not desired or recommended, but here the test
    // seems to move too fast for the app to handle correctly, and there's really
    // nothing implicit that can be waited on, so had to add this small blip
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await assetEditorPage.page.waitForTimeout(250)
    await clickTool('Copy')

    await directionsInput.press('ArrowRight')
    await clickTool('Paste')

    await expect(directionsInput).toHaveText(doubledText)

    const textToAddToAvoidFalsePositiveAfterCopy = 'a bit more'
    const finalText = doubledText + textToAddToAvoidFalsePositiveAfterCopy
    await directionsInput.fill(finalText)

    await directionsInput.selectText()
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await assetEditorPage.page.waitForTimeout(250)
    await clickTool('Cut')
    await expect(directionsInput).toHaveText('')

    await clickTool('Undo')
    await expect(directionsInput).toHaveText(finalText)

    await clickTool('Redo')
    await expect(directionsInput).toHaveText('')

    await clickTool('Paste')
    await expect(directionsInput).toHaveText(finalText)
  })
})

test('nbsps are detected when feature flag is turned on', async ({
  assetEditorPage,
  adminFeaturesPage,
  randomAsset,
}) => {
  await adminFeaturesPage.goToPageWith()
  await adminFeaturesPage.nbspSwitch.click()
  await assetEditorPage.goToPageWith({ ...randomAsset })
  const stemInput = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs.stem.root
  await stemInput.fill('hi\u00A0  bye')
  await expect(assetEditorPage.nbspCount).toBeVisible()
})
