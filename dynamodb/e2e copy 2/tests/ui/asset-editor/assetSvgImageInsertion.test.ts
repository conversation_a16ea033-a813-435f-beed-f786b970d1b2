import path from 'path'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController, page }) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.PRECALCULUS],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test('Verify ability to insert SVG image and save', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const element = stem.root

  const imgElement = element.locator('img')
  stem.root.click()

  const fileChooserPromise = page.waitForEvent('filechooser')
  await assetEditorPage.menu.clickThroughMenu('Insert', 'Graphic')

  const fileChooser = await fileChooserPromise

  // Locally this responds with a 302 vs the normal 200
  const graphicResponsePromise = assetEditorPage.page.waitForResponse('**/graphic/itemSet/**')

  await fileChooser.setFiles(path.join(__dirname, '..', '..', '..', 'uploads', 'hummingbird.svg'))
  await expect(imgElement).toBeVisible()

  await graphicResponsePromise
  await assetEditorPage.saveWithTimeout(1000)

  await expect(assetEditorPage.toastLocator).toContainText(/Item .* saved successfully/)

  await expect(imgElement).toHaveAttribute('src', /.*\/hummingbird\.svg/)
  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()

  await expect(imgElement).toHaveAttribute('src', /.*\/hummingbird\.svg/)
})
