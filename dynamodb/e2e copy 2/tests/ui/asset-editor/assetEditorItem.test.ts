import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Asset Item Drag with Metadata Check', async ({ assetEditorPage, dataController }) => {
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]

  if (asset) {
    // Go to page of generated asset
    await assetEditorPage.goToPageWith({ ...asset })
    const rationaleText = 'Ipsum Lipsum'

    // Assign the first asset item that should be auto generated
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    await itemOne.content.metadataTab.root.click()
    // Select the course framework alignment box and fill content with new text
    await itemOne.content.metadataTab.content.courseFrameworkAlignment.click()
    await itemOne.content.metadataTab.content.courseFrameworkAlignment.fill(rationaleText)
    await itemOne.grabbableButton.hover()
    // Minimize the asset item accordion by clicking on the grabbable button for
    // better tracking of dragging event in playwright UI
    await itemOne.grabbableButton.click()
    await itemOne.grabbableButton.hover()

    // Assume the possibility of only one asset item being generated
    // so add an additional item
    await assetEditorPage.addAnItemButton.click()
    // Save to remove the draft wording in asset item id and timeout
    await assetEditorPage.toolbar.saveButton.click()
    await assetEditorPage.page.waitForTimeout(4000)

    // Assign the second asset item
    const itemTwo = assetEditorPage.getItemAccordion({ number: 2 })
    // Generate the x and y coordinate values of the second asset items by the grabbable button
    const itemTwoBox = await itemTwo.grabbableButton.boundingBox()

    if (itemTwoBox) {
      // Simulate a drag and drop event through mouse actions
      await itemOne.grabbableButton.hover()
      await assetEditorPage.page.mouse.down()
      await assetEditorPage.page.mouse.move(
        itemTwoBox.x + 40,
        itemTwoBox.y + itemTwoBox.height + 15,
        {
          steps: 5,
        }
      )
      await assetEditorPage.page.mouse.up()
      await itemTwo.grabbableButton.hover()
    }
    await expect(itemOne.content.metadataTab.contentContainer).toContainText(rationaleText)
    await expect(itemTwo.content.metadataTab.contentContainer).not.toContainText(rationaleText)
  }
})
