import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('should load the correct asset', async ({ assetEditorPage, randomAsset }) => {
  await expect(assetEditorPage.mainLocator.getByText(randomAsset.displayName).first()).toBeVisible()
})

// This is not a durable way to test this going forward into a world where metadata is floatier.
test.skip('should load satchel metadata container if it is a satchel course', async ({
  assetEditorPage,
  dataController,
}) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
    await expect(assetEditorPage.mainLocator.getByText('Content').first()).toBeVisible()
  }
  test.fail(!asset, 'Failed to create assets')
})

test('should be able to interact with Top Bar in Asset Editor', async ({
  assetEditorPage,
  randomAsset,
  topBar,
}) => {
  const { accountDropdown, searchInput, searchInputMenu } = topBar

  async function openAccountDropdown() {
    await expect(accountDropdown.root).toBeHidden()
    await topBar.accountDropdownButton.click()
    await expect(accountDropdown.root).toBeVisible()
    await expect(accountDropdown.roleDisplay).toHaveText('Admin')
  }

  await waitForResponseFrom(assetEditorPage.page, endpoints.ITEM_SET_VERSION)
  await openAccountDropdown()

  await topBar.accountDropdown.adminMenuLink.click()
  await expect(assetEditorPage.page).toHaveURL(/admin/)

  await assetEditorPage.page.goBack()
  await waitForResponseFrom(assetEditorPage.page, endpoints.ITEM_SET_VERSION)
  await openAccountDropdown()

  await topBar.accountDropdown.debugLink.click()
  await expect(assetEditorPage.page).toHaveURL(/debug/)

  await assetEditorPage.page.goBack()
  await waitForResponseFrom(assetEditorPage.page, endpoints.ITEM_SET_VERSION)

  await expect(searchInput).toBeVisible()
  await searchInput.pressSequentially(randomAsset.id)
  await waitForResponseFrom(assetEditorPage.page, endpoints.ASSET_SEARCH_BY_ID)
  await expect(searchInputMenu).toBeVisible()
  const searchMenuItems = await searchInputMenu.getByRole('menuitem').all()
  expect(searchMenuItems).not.toHaveLength(0)

  for (const menuItem of searchMenuItems) {
    await expect(menuItem).toContainText(randomAsset.id)
  }
})
