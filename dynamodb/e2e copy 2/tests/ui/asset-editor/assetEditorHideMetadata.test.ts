import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Hide Info Panel Check', async ({ assetEditorPage, dataController }) => {
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]

  if (asset) {
    // Textbox text
    const text = 'I am the Wan'

    // Go to page of generated asset
    await assetEditorPage.goToPageWith({ ...asset })

    // Assign the first stimulus that shoudl have been created
    await assetEditorPage.getStimulusAddButton({ type: 'stimulus' }).click()
    const stimulusOne = assetEditorPage.getStimulusAccordion({
      index: 0,
    })
    // Fill the metadata textbox with some text
    await stimulusOne.content.metadataTab.root.click()
    await stimulusOne.content.metadataTab.content.courseFrameworkAlignment.click()
    await stimulusOne.content.metadataTab.content.courseFrameworkAlignment.fill(text)

    // Click the dropdown button and select hide metadata
    await stimulusOne.dropdownButton.click()
    await assetEditorPage.page.getByRole('menuitem', { name: 'Hide Info Panel' }).click()

    // Check that the metadata tab content is hidden
    await expect(stimulusOne.content.metadataTab.contentContainer).toBeHidden()
    await expect(stimulusOne.contentRegion).toBeVisible()

    // Perform the same steps but for asset item
    const itemOne = assetEditorPage.getItemAccordion({ number: 1 })
    await itemOne.content.metadataTab.root.click()
    await itemOne.content.metadataTab.content.courseFrameworkAlignment.click()
    await itemOne.content.metadataTab.content.courseFrameworkAlignment.fill(text)

    // Click the dropdown button and select hide metadata
    await itemOne.dropdownButton.click()
    await assetEditorPage.page.getByRole('menuitem', { name: 'Hide Info Panel' }).click()

    await expect(itemOne.content.metadataTab.contentContainer).toBeHidden()
    await expect(itemOne.contentRegion).toBeVisible()
  }
})
