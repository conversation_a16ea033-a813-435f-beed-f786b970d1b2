import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Passage Reference Tag and Item Passage Reference', async ({
  assetEditorPage,
  dataController,
}) => {
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]
  if (asset) {
    const text = 'AAAA BBBB CCCC'

    await assetEditorPage.goToPageWith({ ...asset })
    await assetEditorPage.getStimulusAddButton({ type: 'stimulus' }).click()
    await assetEditorPage.stimulusTextBox.click()
    await assetEditorPage.stimulusTextBox.fill(text)

    await expect(assetEditorPage.stimulusTextBox).toHaveText(text)

    await assetEditorPage.menu.clickThroughMenu('Insert', 'Passage Reference')
    await expect(assetEditorPage.stimulusTextBox).toContainText('Ref:1')

    const refTagsStart = assetEditorPage.page.locator('.focus_start')
    const refTagsStartBox = await refTagsStart.boundingBox()
    const textA = assetEditorPage.page.getByText(text)
    await textA.hover()
    const stimulusTextBox = await assetEditorPage.stimulusTextBox.boundingBox()

    if (refTagsStartBox && stimulusTextBox) {
      // Simulate a drag and drop event through mouse actions of reference tag
      await refTagsStart.hover()
      await assetEditorPage.page.mouse.down()
      await textA.hover()
      await textA.hover()
      // This timeout allows for visual observation of drag and drop in playwright UI
      await assetEditorPage.page.waitForTimeout(4000)
      await assetEditorPage.page.mouse.up()
      await assetEditorPage.stimulusTextBox.hover()

      // Check to make sure the full text is not available because it is separated by
      // the first ref tag
      await expect(assetEditorPage.stimulusTextBox).not.toHaveText(text)
    }
  }
  await test.step('Add Item Passage Reference', async () => {
    const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
    await stem.root.click()
    await assetEditorPage.menu.clickThroughMenu('Insert', 'Item Passage Reference')
    await assetEditorPage.insertPassageReferenceDialog.content.selectReferenceButton.click()
    await assetEditorPage.insertPassageReferenceDialog.content.selectReferenceMenu.first().click()
    await assetEditorPage.insertPassageReferenceDialog.buttons.apply.click()
    await stem.root.locator('passage-ref-container').click()
    await expect(
      assetEditorPage.insertPassageReferenceDialog.content.selectReferenceButtonRef
    ).toBeVisible()
  })
})
