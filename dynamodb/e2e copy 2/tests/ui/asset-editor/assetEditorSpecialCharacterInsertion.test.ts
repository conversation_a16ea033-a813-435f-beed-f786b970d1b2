import { expect, test } from '@e2e/cb-test'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('Verify ability to insert special character', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  await stem.root.click()
  await assetEditorPage.toolbar.insertSpecialCharacterButton.click()
  await assetEditorPage.toolbar.specialCharacterDropdownCategories.button.click()
  await assetEditorPage.toolbar.specialCharacterDropdownCategories.items.common.click()
  await page.getByRole('button', { name: '¶' }).click()
  await expect(stem.root).toContainText('¶')
  await assetEditorPage.toolbar.saveButton.click()
  await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
  await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)
  await assetEditorPage.reloadThenWaitForPageLoadNetworkCall()
  await expect(stem.root).toContainText('¶')
})
