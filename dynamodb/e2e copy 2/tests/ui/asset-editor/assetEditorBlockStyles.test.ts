import casual from 'casual'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAsset } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { ItemType } from '@shared/schema'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.beforeEach(async ({ assetEditorPage, dataController }) => {
  // Generate an asset
  const [asset] = (await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    length: 1,
    workflowSteps: [WorkflowStepID.INTERNAL_AUTHORING],
  })) as E2EAsset[]
  if (asset) {
    await assetEditorPage.goToPageWith({ ...asset })
  }
})

test.skip('Verify ability to apply block styles', async ({ assetEditorPage, page }) => {
  const { stem } = assetEditorPage.getItemAccordion({ number: 1 }).content.primaryInputs
  const randomSentence = casual.sentence
  await stem.root.click()
  stem.root.fill(randomSentence)
  stem.root.selectText()

  const blockStyles = [
    { buttonName: 'Body', optionName: 'Copyright Line', locator: 'p.dap_copyright' },
    {
      buttonName: 'Copyright Line',
      optionName: 'Figure Caption Label',
      locator: 'p.dap_figurecaptionlabel',
    },
    {
      buttonName: 'Figure Caption Label',
      optionName: 'Figure Caption Body',
      locator: 'p.dap_caption',
    },
    { buttonName: 'Figure Caption Body', optionName: 'Figure Title', locator: 'p.dap_figuretitle' },
    { buttonName: 'Figure Title', optionName: 'Instructions', locator: 'p.dap_instructions' },
    { buttonName: 'Instructions', optionName: 'Passage Intro', locator: 'p.dap_passageintro' },
    { buttonName: 'Passage Intro', optionName: 'Passage Title', locator: 'p.dap_passagetitle' },
    {
      buttonName: 'Passage Title',
      optionName: 'Phrase-level Emphasis',
      locator: 'p.dap_phraselevelemphasis',
    },
    {
      buttonName: 'Phrase-level Emphasis',
      optionName: 'Publication Title',
      locator: 'p.dap_publicationtitle',
    },
    { buttonName: 'Publication Title', optionName: 'Set Headline', locator: 'p.dap_setheadline' },
    {
      buttonName: 'Set Headline',
      optionName: 'Source Attribution',
      locator: 'p.dap_sourceattribution',
    },
    { buttonName: 'Source Attribution', optionName: 'Source Label', locator: 'p.dap_sourcelabel' },
    {
      buttonName: 'Source Label',
      optionName: 'Booklet Instructions',
      locator: 'p.dap_booklet_instructions',
    },
  ]

  let buttonName = 'Body'

  for (const { optionName, locator } of blockStyles) {
    await assetEditorPage.toolbar.page.getByRole('button', { name: buttonName }).click()
    await assetEditorPage.toolbar.page.getByRole('option', { name: optionName }).click()
    await expect(stem.root.locator(locator)).toBeVisible()

    buttonName = optionName
  }

  await assetEditorPage.toolbar.saveButton.click()
  await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
  await expect(assetEditorPage.toastLocator).toHaveText(/Item .* saved successfully/)

  await page.reload()
  await expect(stem.root.locator('p.dap_sourcelabel')).toBeVisible()
})
