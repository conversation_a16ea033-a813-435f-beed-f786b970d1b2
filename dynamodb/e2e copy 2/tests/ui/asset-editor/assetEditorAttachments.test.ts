import fs from 'fs'
import path from 'path'
import { ulid } from 'ulid'
import { expect, test } from '@e2e/cb-test'
import { clickAndDownloadFile, verifyPdfContent } from '@e2e/util/clickAndDownloadFile'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

const pdfName = 'samplePdf.pdf'
const notAcceptedFileType = 'notAcceptedFileType.css'
const hummingbirdJpg = 'hummingbird.jpg'
const excelFile = 'excelTest.xlsx'
const textFileName = `${ulid()}.txt`
const bytesPerMb = 1024 ** 2

const uploadToast = 'Attachment added successfully. Finishing up virus scanning.'

test.beforeEach(async ({ assetEditorPage, randomAsset }) => {
  await assetEditorPage.goToPageWith({ ...randomAsset })
})

test('Asset Attachments lifecycle', async ({ assetEditorPage, page }) => {
  const { attachmentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
  const { attachmentsContent } = attachmentsTab.content
  await attachmentsTab.open()

  await test.step('Upload an attachment and verify it saved', async () => {
    const { attachmentsContent } = attachmentsTab.content
    await attachmentsContent.uploadButton.click()
    await attachmentsContent.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', pdfName)
    )

    await expect(attachmentsContent.uploadDialog.previewArea).toContainText(pdfName)
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_STAGE)

    await attachmentsContent.uploadDialog.dialog.buttons.upload.click()
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_ADD)
    await expect(attachmentsContent.attachmentInfo.statusMessage).toContainText('Uploading')
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_READ)
    await expect(assetEditorPage.toastLocator).toContainText(uploadToast)
    await expect(attachmentsContent.attachmentInfo.statusMessage).toContainText(
      'Scanning for viruses'
    )
    const attachmentName = await attachmentsContent.attachmentInfo.name.innerText()
    expect(attachmentName).toContain(pdfName)
  })

  await test.step('Download an attachment successfully', async () => {
    await attachmentsTab.open()
    await attachmentsContent.actions.downloadButton.click()
    const downloadPath = await clickAndDownloadFile(
      page,
      attachmentsContent.downloadAttachmentDialog.buttons.confirm,
      pdfName
    )
    await verifyPdfContent(downloadPath, 'Dummy PDF file')
  })

  await test.step('Upload another attachment and ensure the order is descending', async () => {
    await attachmentsContent.uploadButton.click()
    await attachmentsContent.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', hummingbirdJpg)
    )

    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_STAGE)

    await attachmentsContent.uploadDialog.dialog.buttons.upload.click()
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_ADD)
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_READ)

    const expectedFileOrder = [hummingbirdJpg, pdfName]

    const fileOrderBeforeReload = await attachmentsContent.attachmentInfo.name.allInnerTexts()
    expectedFileOrder.forEach((fileName, index) =>
      expect(fileOrderBeforeReload[index]).toContain(fileName)
    )

    await expect(attachmentsContent.refreshButton).toBeEnabled()
    await expect(async () => {
      attachmentsContent.refreshButton.click()
      const fileOrderAfterReload = await attachmentsContent.attachmentInfo.name.allInnerTexts()
      expectedFileOrder.forEach((fileName, index) =>
        expect(fileOrderAfterReload[index]).toContain(fileName)
      )
    }).toPass({ intervals: [1000], timeout: 30000 })
  })

  await test.step('Upload another attachment to check excel files work', async () => {
    await attachmentsContent.uploadButton.click()
    await attachmentsContent.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', excelFile)
    )

    const dialogText = await attachmentsContent.uploadDialog.dialog.contentContainer.allInnerTexts()
    expect(dialogText.toString()).toContain(
      'Uploading or downloading Microsoft files may carry risks'
    )
    await expect(attachmentsContent.uploadDialog.dialog.buttons.upload).toBeEnabled()
    await attachmentsContent.uploadDialog.dialog.buttons.upload.click()

    await expect(assetEditorPage.toastLocator.last()).toContainText(uploadToast)
    const expectedFileOrder = [excelFile, hummingbirdJpg, pdfName]

    const fileOrderBeforeReload = await attachmentsContent.attachmentInfo.name.allInnerTexts()
    expectedFileOrder.forEach((fileName, index) =>
      expect(fileOrderBeforeReload[index]).toContain(fileName)
    )

    await expect(async () => {
      await page.reload()
      await attachmentsTab.open()
      const fileOrderAfterReload = await attachmentsContent.attachmentInfo.name.allInnerTexts()
      expectedFileOrder.forEach((fileName, index) =>
        expect(fileOrderAfterReload[index]).toContain(fileName)
      )
    }).toPass({ intervals: [500, 1000] })
  })

  await test.step('Delete an attachment successfully', async () => {
    const secondAttachmentDeleteButton = attachmentsContent.actions.deleteButton.nth(1)
    await secondAttachmentDeleteButton.click()
    await attachmentsContent.deleteDialog.buttons.remove.click()
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_REMOVE)
    const responsePromise = waitForResponseFrom(page, 'itemSetVersionsVerbose')
    await page.reload()
    await responsePromise

    await attachmentsTab.open()
    const presentAttachments = await attachmentsContent.attachmentInfo.name.all()
    expect(presentAttachments.length).toBe(2)
  })
})

test('Asset Attachments Upload Not Accepted File Type', async ({ assetEditorPage, page }) => {
  const { attachmentsTab } = assetEditorPage.getItemAccordion({ number: 1 }).content
  const { attachmentsContent } = attachmentsTab.content
  await attachmentsTab.open()

  await test.step('Attempt to upload an attachment', async () => {
    await attachmentsContent.uploadButton.click()
    await attachmentsContent.uploadDialog.uploadFiles(
      path.join(__dirname, '../../../uploads', notAcceptedFileType)
    )

    // expect request to fail
    await waitForResponseFrom(assetEditorPage.page, endpoints.ATTACHMENTS_STAGE, 400)
    await expect(assetEditorPage.toastLocator).toContainText(
      'text/css is not an accepted file type.'
    )

    // reload and ensure the attachment is not present
    await page.reload()
    await attachmentsTab.open()
    await expect(attachmentsContent.root).toContainText('There are no attachments')
  })

  await test.step('Upload a large file that will fail', async () => {
    const textFilePath = path.join(__dirname, '..', '..', '..', 'uploads', textFileName)
    const fileDesc = fs.openSync(textFilePath, fs.constants.O_CREAT | fs.constants.O_WRONLY)

    if (fileDesc === -1) {
      throw 'Unable to find file'
    }

    if (fileDesc) {
      fs.writeFileSync(fileDesc, Buffer.alloc(51 * bytesPerMb))
    }

    if (!fs.fstatSync(fileDesc).size) throw 'Unable to attachment file.'

    await attachmentsContent.uploadButton.click()
    await attachmentsContent.uploadDialog.uploadFiles(textFilePath)
    await expect(attachmentsContent.uploadDialog.dialog.buttons.upload).toBeEnabled()
    await attachmentsContent.uploadDialog.dialog.buttons.upload.click()
    await expect(
      assetEditorPage.toastLocator.filter({ hasText: 'File size exceeds allowed 50MB.' }).first()
    ).toBeVisible()

    fs.unlinkSync(textFilePath)
  })
})
