import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2ECollection } from '@e2e/lib'
import { CollectionsPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Asset associations dialog should display correctly', async ({
  assetEditorPage,
  context,
  dataController,
}) => {
  await test.step('Go to page with a specific asset with an association', async () => {
    const [collection] = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      collectionProps: { name: 'associationsCollection' },
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW],
    })) as E2ECollection[]
    if (collection) {
      const assetId = collection.assetIDs[0]
      test.fail(!assetId, 'Failed to create assets in a collection')
      await assetEditorPage.goToPageWith({ id: assetId as string, subject: collection.subject })
    }
    test.fail(
      !collection,
      'Failed to create required resources for assetEditorAssociationsDialog test'
    )
  })

  await test.step('Clicking on the View > Associations option should display the associations dialog', async () => {
    await assetEditorPage.menu.clickThroughMenu('View', 'Associations')
    await expect(assetEditorPage.associationsDialog.root).toBeVisible()
  })

  await test.step('The association dialog should display 3 tabs: Assembly Units (#), Collections (#), and Forms (#)', async () => {
    const dialog = assetEditorPage.associationsDialog.root
    // check for both tabs
    const assemblyUnitsTab = dialog.getByRole('tab', { name: /Assembly Units \([0-9]\)/ })
    await expect(assemblyUnitsTab).toBeVisible()
    const collectionsTab = dialog.getByRole('tab', { name: /Collections \([0-9]\)/ })
    await expect(collectionsTab).toBeVisible()
    const formsTab = dialog.getByRole('tab', { name: /Forms \([0-9]\)/ })
    await expect(formsTab).toBeVisible()
    // check the content of assembly unit tab
    await assemblyUnitsTab.click()
    const assemblyUnitsTabPanel = dialog.getByRole('tabpanel', { name: /Assembly Units \([0-9]\)/ })
    await expect(assemblyUnitsTabPanel.getByText('No association to show')).toBeVisible()
    // check the content of form tab
    await formsTab.click()
    const formsTabPanel = dialog.getByRole('tabpanel', { name: /Forms \([0-9]\)/ })
    await expect(formsTabPanel.getByText('No association to show')).toBeVisible()
    // check the content of collection tab
    await collectionsTab.click()
    const collectionsTabPanel = dialog.getByRole('tabpanel', { name: /Collections \([0-9]\)/ })
    const table = collectionsTabPanel.getByRole('table')
    await expect(table).toBeVisible()
    const snapshotLinks = table.locator('tbody tr')
    await expect(snapshotLinks).toHaveCount(1)
    // check that the link works
    const id = await table.locator('tbody tr td').first().innerText()
    const pagePromise = context.waitForEvent('page')
    await snapshotLinks.first().click()
    const newCollectionPage = new CollectionsPage({ page: await pagePromise })
    const newCollectionPageID = newCollectionPage.page.url().split('/').pop()
    expect(newCollectionPageID).toBe(id)
    // check that the close button works
    await assetEditorPage.associationsDialog.buttons.remove.click()
    await expect(dialog).toBeHidden()
  })
})
