import { test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAssemblyUnit } from '@e2e/lib'
import { AddAssetsPage, NextGenAssemblyUnitAuthoringPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { dragAndDrop } from '@e2e/util/dragAndDrop'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { expect } from '@playwright/test'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const subjectName = 'African American Studies'
const adminYear = '25'
const courseCode = 'AAS'
const numAssets = 5

test.setTimeout(3 * 60 * 1000)

test('Verify propagation of adding assets in AU', async ({
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
  assemblyUnitAddAssetsPage,
  context,
}) => {
  // Generate assets and assembly units for testing
  await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.READY_FOR_USE],
    length: numAssets,
  })

  const assemblyUnits1 = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    assetLength: 2,
    sequenceLength: 1,
    completeLength: 1,
    nextGenAssemblyUnitProps: {
      questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
      adminYear: adminYear,
      assemblyUnitType: AssemblyUnitType.BASE,
    },
  })) as E2EAssemblyUnit[]

  const [
    { id: baseAUId, name: baseAUName },
    { id: sequenceAUId, name: sequenceAUName },
    { id: completeAUId, name: completeAUName },
  ] = assemblyUnits1

  await test.step('Go to page with specific subject', async () => {
    await nextGenAssemblyUnitListPage.goToPageWith({ subject: subjectName })
    await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_ALL_DESCENDANTS_BY_ID)
  })

  let assetNameOne: string
  let assetNameTwo: string
  let firstItemOfAssetOne: string
  let newPage: any
  let newAssemblyUnitAddAssetsPage: AddAssetsPage
  let newNextGenAssemblyUnitAuthoringPage: NextGenAssemblyUnitAuthoringPage

  await test.step('Add assets to a base AU', async () => {
    const listHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      `${adminYear}_${courseCode}`
    )
    await listHeadingItem.click()

    const listBaseHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(baseAUId)
    await listBaseHeadingItem.getByRole('button', { name: 'Dropdown' }).click()
    await listBaseHeadingItem.getByRole('menuitem', { name: 'Add Assets' }).click()
    await listBaseHeadingItem.getByRole('menuitem', { name: '...from asset library' }).click()

    await expect(assemblyUnitAddAssetsPage.getAddAssetsStatusBar(baseAUName)).toBeVisible()
    await assemblyUnitAddAssetsPage.table.allCheckBoxes.nth(1).check()
    await assemblyUnitAddAssetsPage.table.allCheckBoxes.nth(2).check()

    // Extract asset names
    const fullTextOne = await assemblyUnitAddAssetsPage.table.allRows.nth(0).textContent()
    assetNameOne = fullTextOne.split('AP')[0].trim()

    const fullTextTwo = await assemblyUnitAddAssetsPage.table.allRows.nth(1).textContent()
    assetNameTwo = fullTextTwo.split('AP')[0].trim()

    await assemblyUnitAddAssetsPage.getAddAssetsButton({ assetCount: 2 }).click()
    await expect(
      assemblyUnitAddAssetsPage.toastLocator.filter({ hasText: '2 assets added successfully.' })
    ).toBeVisible()
    await assemblyUnitAddAssetsPage.doneButton.click()

    // Verify assets added to base
    await expect(page.getByRole('row', { name: assetNameOne })).toBeVisible()
    await expect(page.getByRole('row', { name: assetNameTwo })).toBeVisible()
    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()

    // Verify assets propagated to sequence, and details has related base and complete
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(sequenceAUName)
    await page.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    await expect(page.getByRole('row', { name: assetNameOne })).toBeVisible()
    await expect(page.getByRole('row', { name: assetNameTwo })).toBeVisible()
    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(baseAUName)
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(completeAUName)

    // Verify assets propagated to complete, and details has related sequence and base
    await page.getByRole('link', { name: completeAUName, exact: true }).click()
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    await expect(page.getByRole('row', { name: assetNameOne })).toBeVisible()
    await expect(page.getByRole('row', { name: assetNameTwo })).toBeVisible()
    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(baseAUName)
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(sequenceAUName)

    // Navigate back to AU list view via breadcrumb
    await nextGenAssemblyUnitAuthoringPage.clickBreadcrumbLink('au')
    await listHeadingItem.click()
  })

  await test.step('Verify item deactivation propagation', async () => {
    // Open the base AU in a new tab
    ;[newPage] = await Promise.all([
      context.waitForEvent('page'),
      nextGenAssemblyUnitListPage
        .getAssemblyUnitListHeadingItem(baseAUId)
        .getByRole('link', { name: baseAUName, exact: true })
        .click(),
    ])

    newAssemblyUnitAddAssetsPage = new AddAssetsPage({
      page: newPage,
      structureType: 'Assembly Unit',
    })

    newNextGenAssemblyUnitAuthoringPage = new NextGenAssemblyUnitAuthoringPage({
      page: newPage,
    })

    const assetTableRowLocator = await newNextGenAssemblyUnitAuthoringPage.assetTableRow(
      assetNameOne
    )
    await expect(assetTableRowLocator).toBeVisible()

    const expandButtonLocator = await newNextGenAssemblyUnitAuthoringPage.expandRowButton(
      assetNameOne
    )
    await expandButtonLocator.click()

    // Get the first item name from the asset name
    firstItemOfAssetOne = getFirstItemNameFromAsset(assetNameOne)

    // Deactivate the item in the base AU
    await newNextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(
      firstItemOfAssetOne,
      'Deactivate'
    )

    await expect(
      newAssemblyUnitAddAssetsPage.toastLocator.filter({ hasText: 'Item successfully deactivated' })
    ).toBeVisible()

    const deactivatedIconLocator = await newNextGenAssemblyUnitAuthoringPage.deactivateItemIcon(
      firstItemOfAssetOne
    )

    // Verify deactivation in base AU
    await expect(deactivatedIconLocator).toBeVisible()

    // Verify propagation to sequence AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(newNextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(
      sequenceAUName
    )
    await newPage.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)
    await expandButtonLocator.click()
    await expect(deactivatedIconLocator).toBeVisible()

    // Verify propagation to complete AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(newNextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(
      completeAUName
    )
    await newPage.getByRole('link', { name: completeAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)
    await expandButtonLocator.click()
    await expect(deactivatedIconLocator).toBeVisible()
  })

  await test.step('Verify item reactivation propagation', async () => {
    // Reactivate the item in complete AU
    await newNextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(
      firstItemOfAssetOne,
      'Reactivate'
    )

    await expect(
      newAssemblyUnitAddAssetsPage.toastLocator.filter({ hasText: 'Item successfully reactivated' })
    ).toBeVisible()

    const deactivatedIconLocator = await newNextGenAssemblyUnitAuthoringPage.deactivateItemIcon(
      firstItemOfAssetOne
    )
    await expect(deactivatedIconLocator).toBeHidden()

    // Verify propagation in sequence AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)
    await newNextGenAssemblyUnitAuthoringPage.expandRowButton(assetNameOne).click()

    // Sequence should still show deactivated (no downward propagation)
    await expect(deactivatedIconLocator).toBeVisible()

    // Verify propagation in base AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: baseAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)
    await newNextGenAssemblyUnitAuthoringPage.expandRowButton(assetNameOne).click()

    // Base should still show deactivated (no downward propagation)
    await expect(deactivatedIconLocator).toBeVisible()
  })

  await test.step('Verify asset removal propagation', async () => {
    // Remove asset from the base AU
    await newNextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(assetNameOne, 'Remove')

    await expect(
      newAssemblyUnitAddAssetsPage.toastLocator.filter({
        hasText: `Asset ${assetNameOne} was successfully removed`,
      })
    ).toBeVisible()

    const assetTableRowLocator = await newNextGenAssemblyUnitAuthoringPage.assetTableRow(
      assetNameOne
    )
    await expect(assetTableRowLocator).toBeHidden()

    // Verify propagation to sequence AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)

    // Asset should still be present in sequence (no upward propagation) (double check with team, seeing that it is not present in sequence)
    await expect(assetTableRowLocator).toBeHidden()

    // Verify propagation to complete AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: completeAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)

    // Asset should be removed from complete (downward propagation)
    await expect(assetTableRowLocator).toBeHidden()
  })

  await test.step('Verify asset and item reordering propagation', async () => {
    // Navigate to sequence AU
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)

    // ----- ASSET REORDERING VERIFICATION -----

    // Get initial order of assets in sequence AU
    await newNextGenAssemblyUnitAuthoringPage.expandAllButton.click()
    const initialFirstAsset = await newNextGenAssemblyUnitAuthoringPage.allAssets
      .first()
      .innerText()
    const initialSecondAsset = await newNextGenAssemblyUnitAuthoringPage.allAssets
      .nth(1)
      .innerText()
    const initialThirdAsset = await newNextGenAssemblyUnitAuthoringPage.allAssets.nth(2).innerText()
    console.log('Initial asset order:', initialFirstAsset, initialSecondAsset, initialThirdAsset)

    // Use drag and drop to reorder assets
    await newNextGenAssemblyUnitAuthoringPage.reorderButton.click()
    await newNextGenAssemblyUnitAuthoringPage.editAssetOrder.click()

    // Add a small wait to ensure the UI is ready
    await newPage.waitForTimeout(3000)

    await newNextGenAssemblyUnitAuthoringPage.editAssetTextBox.first().fill('1000')

    await newPage.waitForTimeout(3000)
    // Apply the changes
    await newNextGenAssemblyUnitAuthoringPage.applyButton.click()
    await waitForResponseFrom(newPage, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
    await page.waitForTimeout(3000)

    // Get the new order after reordering
    const newFirstAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .first()
      .innerText()
    const newSecondAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .nth(1)
      .innerText()
    const newThirdAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .nth(2)
      .innerText()

    // - The second item becomes first
    // - The third item becomes second
    // - The first item becomes third
    expect(newFirstAsset).toMatch(initialSecondAsset)
    expect(newSecondAsset).toMatch(initialThirdAsset)
    expect(newThirdAsset).toMatch(initialFirstAsset)

    // Close the toast notification
    await newNextGenAssemblyUnitAuthoringPage.toastLocator
      .getByRole('button', { name: 'Close alert' })
      .click()

    // Navigate to complete AU and verify asset order propagation
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: completeAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)

    // Verify asset order in complete AU matches sequence AU
    // Target the nested table row by ID pattern
    const completeFirstAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .first()
      .innerText()
    const completeSecondAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .nth(1)
      .innerText()
    const completeThirdAsset = await newNextGenAssemblyUnitAuthoringPage.nestedTableLink
      .nth(2)
      .innerText()

    // Verify the complete AU has the same order as sequence AU
    expect(completeFirstAsset).toMatch(newFirstAsset)
    expect(completeSecondAsset).toMatch(newSecondAsset)
    expect(completeThirdAsset).toMatch(newThirdAsset)

    // ----- ITEM REORDERING VERIFICATION -----

    // Navigate back to sequence AU for item reordering
    await newNextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await newPage.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(newPage, endpoints.ASSETS_READ_MANY_BY_IDS)

    // After expanding the asset
    await newNextGenAssemblyUnitAuthoringPage.expandRowButton(newFirstAsset).click()
    await newPage.waitForTimeout(1000) // Wait for expansion animation

    // Get initial item IDs by targeting the specific spans containing the IDs
    const itemIdSpans = newPage.locator(
      'span[aria-label="id-column-value"] span.cb-ic-nectar-nested-table-icon'
    )
    const initialItemIds = await itemIdSpans.allInnerTexts()
    const firstItemId = initialItemIds[0]
    const secondItemId = initialItemIds[1]
    console.log('Initial item order:', firstItemId, secondItemId)

    // Enter reorder mode for items
    await newNextGenAssemblyUnitAuthoringPage.reorderButton.click()
    await newNextGenAssemblyUnitAuthoringPage.dragAndDrop.click()
    await newPage.waitForTimeout(1000) // Wait for reorder mode to activate
    await newNextGenAssemblyUnitAuthoringPage.expandRowButton(newFirstAsset).click()

    // Target the drag icons for dragging
    const firstItemDragIcon = newPage.locator(`#${firstItemId}-drag-icon`)
    const secondItemDragIcon = newPage.locator(`#${secondItemId}-drag-icon`)

    // Perform the drag and drop
    await dragAndDrop(newPage, firstItemDragIcon, secondItemDragIcon)

    // Apply the changes
    await newNextGenAssemblyUnitAuthoringPage.applyButton.click()
    await waitForResponseFrom(newPage, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
    await newPage.waitForTimeout(1000)
    await newNextGenAssemblyUnitAuthoringPage.expandRowButton(newFirstAsset).click()
    await newPage.waitForTimeout(2000)

    // Get the new order after reordering
    const newItemIdSpans = newPage.locator(
      'span[aria-label="id-column-value"] span.cb-ic-nectar-nested-table-icon'
    )
    const newItemIds = await newItemIdSpans.allInnerTexts()
    const newFirstItemId = newItemIds[0]
    const newSecondItemId = newItemIds[1]
    console.log('New item order:', newFirstItemId, newSecondItemId)

    // Verify the order has changed
    expect(newFirstItemId).toEqual(secondItemId)
    expect(newSecondItemId).toEqual(firstItemId)
  })

  await test.step('Test archive propagation', async () => {
    // Navigate back to list page
    await newNextGenAssemblyUnitAuthoringPage.clickBreadcrumbLink('au')
    await nextGenAssemblyUnitListPage.getCheckboxFilter('Archived').click()

    // Archive base AU
    const baseHierarchyList = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(baseAUId)
    await expect(baseHierarchyList).toBeVisible()
    await baseHierarchyList.getByRole('button', { name: 'Dropdown' }).click()
    await baseHierarchyList.getByRole('menuitem', { name: 'Archive' }).click()
    await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_ARCHIVE)

    // Verify archive chip is visible on base AU
    await expect(baseHierarchyList.getByText('Archived')).toBeVisible()

    // Verify propagation to sequence AU
    const sequenceHierarchyList = page.getByRole('button', {
      name: `Hierarchy list item heading ${sequenceAUId}`,
    })
    await expect(sequenceHierarchyList.getByText('Archived')).toBeVisible()
    await sequenceHierarchyList.getByTestId(`expand-collapse-icon-${sequenceAUId}`).click()

    // Verify propagation to complete AU
    const completeHierarchyList = page.getByRole('button', {
      name: `Hierarchy list item heading ${completeAUId}`,
    })
    await expect(completeHierarchyList.getByText('Archived')).toBeVisible()
  })
})

/**
 * Extracts the first item name from an asset name by incrementing the numeric suffix
 * @param assetName Asset name to get corresponding item name from
 * @returns First item name derived from asset name
 */
function getFirstItemNameFromAsset(assetName: string): string {
  const numberPart = assetName.match(/\d+$/)?.[0] // Extract the numeric part from the end

  if (numberPart) {
    const incrementedNumber = parseInt(numberPart, 10) + 1 // Increment the number
    const firstPartOfAssetName = assetName.slice(0, -numberPart.length)
    return firstPartOfAssetName + incrementedNumber.toString().padStart(numberPart.length, '0')
  }

  console.error('No numeric part found at the end of the asset name.')
  return assetName // Fallback to original asset name
}
