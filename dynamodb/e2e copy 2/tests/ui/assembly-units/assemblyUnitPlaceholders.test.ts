import { test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAssemblyUnit } from '@e2e/lib'
import { E2EForm } from '@e2e/lib/E2EForm'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { expect } from '@playwright/test'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
const SUBJECT_NAME = 'African American Studies'
const ADMIN_YEAR = '25'
const NUM_TEST_ASSETS = 5
const ASSETS_TO_GENERATE = 2
const PLACEHOLDERS_ABOVE = 2
const PLACEHOLDERS_BELOW = 1
const ITEMS_PER_PLACEHOLDER_ABOVE = 3
const ITEMS_PER_PLACEHOLDER_BELOW = 1
test.setTimeout(3 * 60 * 1000)
test('Verify asset placeholder propagation between base, sequence, and complete assembly units', async ({
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
  assemblyUnitAddAssetsPage,
  formPage,
}) => {
  // Generate test assets
  await dataController.generate(DataControllerModel.ASSET, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    assetProps: { itemType: ItemType.MultipleChoiceQuestion },
    workflowSteps: [WorkflowStepID.READY_FOR_USE],
    length: NUM_TEST_ASSETS,
  })
  // Generate assembly units for testing
  const assemblyUnits = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    assetLength: ASSETS_TO_GENERATE,
    sequenceLength: 1,
    completeLength: 1,
    nextGenAssemblyUnitProps: {
      questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
      adminYear: ADMIN_YEAR,
      assemblyUnitType: AssemblyUnitType.BASE,
    },
  })) as E2EAssemblyUnit[]
  // Extract assembly unit IDs and names
  const [
    { id: baseAUId, name: baseAUName },
    { id: sequenceAUId, name: sequenceAUName },
    { id: completeAUId, name: completeAUName },
  ] = assemblyUnits
  let firstAssetName: string, secondAssetName
  await test.step('Verify base assembly unit has initial assets', async () => {
    await nextGenAssemblyUnitAuthoringPage.goToPageWith({
      id: baseAUId,
      subject: SUBJECT_NAME,
    })
    firstAssetName = (
      await nextGenAssemblyUnitAuthoringPage.nestedTableLink.first().textContent()
    ).trim()
    secondAssetName = (
      await nextGenAssemblyUnitAuthoringPage.nestedTableLink.last().textContent()
    ).trim()
    await expect(nextGenAssemblyUnitAuthoringPage.assetTableRow(firstAssetName)).toBeVisible()
    await expect(nextGenAssemblyUnitAuthoringPage.assetTableRow(secondAssetName)).toBeVisible()
  })
  await test.step('Add placeholders above first asset', async () => {
    await nextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(
      firstAssetName,
      'Add placeholder above'
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.assetCount.fill(
      String(PLACEHOLDERS_ABOVE)
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.itemPerAssetCount.fill(
      String(ITEMS_PER_PLACEHOLDER_ABOVE)
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.buttons.apply.click()
    // Verify toast messages
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: 'All related sequence and complete AUs have been updated',
      })
    ).toBeVisible()
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: `Placeholder added to ${baseAUName}`,
      })
    ).toBeVisible()
  })
  await test.step('Add placeholders below first asset', async () => {
    await nextGenAssemblyUnitAuthoringPage.performActionOnDropdownItem(
      firstAssetName,
      'Add placeholder below'
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.assetCount.fill(
      String(PLACEHOLDERS_BELOW)
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.itemPerAssetCount.fill(
      String(ITEMS_PER_PLACEHOLDER_BELOW)
    )
    await nextGenAssemblyUnitListPage.placeholderInputDialog.buttons.apply.click()
    // Verify toast messages
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: 'All related sequence and complete AUs have been updated',
      })
    ).toBeVisible()
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: `Placeholder added to ${baseAUName}`,
      })
    ).toBeVisible()
  })
  await test.step('Verify placeholders were added correctly in base AU', async () => {
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    // Get row data using the POM
    const rowData = await nextGenAssemblyUnitAuthoringPage.getRowTypesWithDetails()
    // Skip the header row
    const contentRows = rowData.slice(1)
    // Verify placeholder positions
    const firstAssetIndex = contentRows.findIndex((row) => row.type === 'Asset')
    const secondAssetIndex = contentRows.findIndex(
      (row, index) => row.type === 'Asset' && index > firstAssetIndex
    )
    // Count placeholders before first asset
    const placeholdersBeforeFirstAsset = contentRows
      .slice(0, firstAssetIndex)
      .filter((row) => row.type === 'Placeholder').length
    // Count placeholders between assets
    const placeholdersBetweenAssets = contentRows
      .slice(firstAssetIndex + 1, secondAssetIndex)
      .filter((row) => row.type === 'Placeholder').length
    // Verify placeholders were added as specified
    expect(placeholdersBeforeFirstAsset).toBe(PLACEHOLDERS_ABOVE)
    expect(placeholdersBetweenAssets).toBe(PLACEHOLDERS_BELOW)
    // Verify overall structure
    expect(contentRows[0].type).toBe('Placeholder')
    expect(contentRows[1].type).toBe('Placeholder')
    expect(contentRows[2].type).toBe('Asset')
    expect(contentRows[3].type).toBe('Placeholder')
    expect(contentRows[4].type).toBe('Asset')
  })
  await test.step('Verify placeholder propagation to sequence AU', async () => {
    // Navigate to sequence AU
    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await page.getByRole('link', { name: sequenceAUName, exact: true }).click()
    await waitForResponseFrom(page, endpoints.READ_EVALUATION_METRICS_CALCULATIONS)
    // Get row data using the POM
    const rowTypes = await nextGenAssemblyUnitAuthoringPage.getRowTypes()
    const contentRows = rowTypes.slice(1) // Skip header
    // Verify same structure was propagated
    expect(contentRows[0].type).toBe('Placeholder')
    expect(contentRows[1].type).toBe('Placeholder')
    expect(contentRows[2].type).toBe('Asset')
    expect(contentRows[3].type).toBe('Placeholder')
    expect(contentRows[4].type).toBe('Asset')
    // Verify placeholder options are not available in sequence AU
    await nextGenAssemblyUnitAuthoringPage.dropdownButton(firstAssetName).click()
    await expect(page.getByRole('menuitem', { name: 'Add placeholder above' })).toBeHidden()
    await expect(page.getByRole('menuitem', { name: 'Add placeholder below' })).toBeHidden()
    // Close dropdown
    await page.mouse.click(0, 0)
  })
  await test.step('Verify placeholder propagation to complete AU', async () => {
    // Navigate to complete AU
    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await page.getByRole('link', { name: completeAUName, exact: true }).click()
    await waitForResponseFrom(page, endpoints.READ_EVALUATION_METRICS_CALCULATIONS)
    // Get row data using the POM
    const rowTypes = await nextGenAssemblyUnitAuthoringPage.getRowTypes()
    const contentRows = rowTypes.slice(1) // Skip header
    // Verify same structure was propagated
    expect(contentRows[0].type).toBe('Placeholder')
    expect(contentRows[1].type).toBe('Placeholder')
    expect(contentRows[2].type).toBe('Asset')
    expect(contentRows[3].type).toBe('Placeholder')
    expect(contentRows[4].type).toBe('Asset')
  })
  let replacementAssetName
  await test.step('Replace placeholder with asset in complete AU', async () => {
    // Find first placeholder row
    const placeholderRow = nextGenAssemblyUnitAuthoringPage.getPlaceholderRow()
    // Open dropdown menu
    await placeholderRow.locator(nextGenAssemblyUnitAuthoringPage.moreOptionsButton).click()
    // Navigate through menu options
    await page.getByRole('menuitem', { name: ' Replace ' }).hover()
    await page.waitForTimeout(300)
    const assetLibraryOption = page.getByRole('menuitem', {
      name: 'with an asset from the library',
    })
    await expect(assetLibraryOption).toBeVisible()
    await assetLibraryOption.click()
    // Verify asset library dialog
    await expect(assemblyUnitAddAssetsPage.statusBar).toContainText(
      `Select an asset to replace a placeholder within "${completeAUName}"`
    )
    // Select an asset
    await assemblyUnitAddAssetsPage.table.allCheckBoxes.nth(1).check()
    const fullText = await assemblyUnitAddAssetsPage.table.allRows.nth(0).textContent()
    replacementAssetName = fullText.split('AP')[0].trim()
    // Confirm replacement
    await assemblyUnitAddAssetsPage.replaceButton.click()
    // Verify toast message
    await expect(
      nextGenAssemblyUnitListPage.page.locator('div[role="alert"].cb-toast-success')
    ).toContainText(`Placeholder replaced with asset ${replacementAssetName}`)
  })

  await test.step('Verify placeholder was replaced with asset', async () => {
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    // Get row data using the POM
    const rowTypes = await nextGenAssemblyUnitAuthoringPage.getRowTypes()
    const contentRows = rowTypes.slice(1) // Skip header
    // Count remaining placeholders
    const remainingPlaceholders = contentRows.filter((row) => row.type === 'Placeholder').length
    expect(remainingPlaceholders).toBe(2) // One less than the original 3
  })
  await test.step('Verify form can add AU with placeholders with warning', async () => {
    // Create a test form
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      formProps: { name: 'Form Asset Placeholder Test' },
      assemblyUnitLength: 0,
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assetLength: 1,
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW, WorkflowStepID.EXTERNAL_AUTHORING],
      assetProps: {
        workflowStateId: WorkflowStepID.INTERNAL_REVIEW,
      },
    })) as E2EForm[]
    test.fail(!form, 'Failed to create a form')
    // Navigate to form
    await formPage.goToPageWith({ ...form! })
    await formPage.sectionOneTab.click()
    // Add assembly unit with placeholders
    await formPage.moreActionsButton.first().click()
    await formPage.moreActionsAddMenuItem.hover()
    await formPage.moreActionsAddAssemblyUnitsMenuItem.first().click()
    console.log(completeAUId)
    await formPage.addAssemblyUnitsToFormSectionModalInput.fill(completeAUName)
    await page.getByText(new RegExp(`^${completeAUName}${completeAUId}`)).click()
    await formPage.addAssemblyUnitToFormSectionModalDoneButton.first().click()
    // Verify warning about placeholders
    await expect(formPage.addToFormWarningDialog).toContainText('Are you sure?')
    await expect(formPage.addToFormWarningDialog).toContainText(
      'The following Assembly Units contain placeholders:'
    )

    await expect(page.getByRole('link', { name: completeAUName })).toBeVisible()
    const href = page.getByRole('link', { name: completeAUName })
    const expectedHref = `/ap/african-american-studies/au/${completeAUId}`
    await await expect(href).toHaveAttribute('href', expectedHref)

    // Confirm anyway
    await formPage.addToFormWarningContinueButton.click()
    // Wait for assets to load
    await waitForResponseFrom(page, endpoints.ASSETS_READ_MANY_BY_IDS)
    // Verify AU was added to form
    await expect(formPage.assemblyUnitButton(completeAUName)).toBeVisible()
  })
})
