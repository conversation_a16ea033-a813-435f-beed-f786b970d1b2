import casual from 'casual'
import { test } from '@e2e/cb-test'
import type { NextGenAssemblyUnitListPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { Locator, Page, expect } from '@playwright/test'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { makeAssemblyUnitName } from '@shared/data-generation/utils'
import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
  QuestionTypeNames,
} from '@shared/dynamo-hummingbird/tables/split-models'
import {
  INTERNAL_ITEM_REVIEW_STEPS,
  ITEM_FINALIZATION,
  WorkflowStepID,
} from '@shared/types/model/workflow/types'

const subjectName = 'African American Studies'
const adminYear = casual.random_element(['24', '25', '26'])
const adminYearValue = `20${adminYear}`
const baseForm = casual.random_element(Object.values(BaseForm))
const questionType = QuestionType.MULTIPLE_CHOICE_QUESTION
const questionTypeValue = QuestionTypeNames[questionType]
const questionTypeNumber = casual.integer(0, 4)
const questionTypeNumberValue = `0${questionTypeNumber}`
const courseCode = 'AAS'
const baseAUName = makeAssemblyUnitName({
  assemblyUnitType: AssemblyUnitType.BASE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
})

const navigateToAfamPage = async (
  nextGenAssemblyUnitListPage: NextGenAssemblyUnitListPage,
  page: Page
) => {
  await nextGenAssemblyUnitListPage.goToPageWith({ subject: subjectName })
  await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)
}

const expectPointerEventEnabled = async (locator: Locator) => {
  await expect(locator).toHaveCSS('pointer-events', 'auto')
}

const expectPointerEventDisabled = async (locator: Locator) => {
  await expect(locator).toHaveCSS('pointer-events', 'none')
}

test('Verify Asset Bulk Actions in the Assembly Unit Editor Page', async ({
  assemblyUnitAddAssetsPage,
  currentUser,
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
}) => {
  await test.step('Create assets for insertion into new AU', async () => {
    await dataController.generate(DataControllerModel.ASSET, {
      user: currentUser.getTestUserData(),
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      assetProps: { itemType: ItemType.MultipleChoiceQuestion },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
      length: 5,
    })
  })

  await test.step('Go to page with specific subject', async () => {
    await navigateToAfamPage(nextGenAssemblyUnitListPage, page)
  })

  //This is to ensure that the prevent duplicate AU message upon AU creation is suppressed
  await test.step('Set up network interception for duplicate assembly unit check', async () => {
    await page.route('**/assembly-units/assemblyUnitsQueryDuplicate?batch=1', (route) => {
      // Mock a successful response indicating the assembly unit is unique
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([{ result: { data: { assemblyUnitIsUnique: true } } }]),
      })
    })
  })

  await test.step('Create new BASE assembly unit and go to editor page', async () => {
    await nextGenAssemblyUnitListPage.createNewButton.click()
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.auTypeOption.selectOption('Base')
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.adminYearOption.selectOption(
      adminYearValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.baseFormOption.selectOption(
      baseForm
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeOption.selectOption(
      questionTypeValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeNumberOption.selectOption(
      questionTypeNumberValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.buttons.apply.click()

    // input the placeholder values
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.assetCount.fill('1')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.itemPerAssetCount.fill('2')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.buttons.apply.click()

    await expect(page.locator('.overlay-loader-text')).toContainText(
      'Creating and navigating to 1 Base assembly unit(s).'
    )
    // creating new base au leads to the authoring page
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_READ_BY_ID
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'A new Base AU has been created.',
      })
    ).toBeVisible()
    const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(
      baseAUName
    )
    await expect(assemblyUnitTitle).toBeVisible()
  })

  await test.step('Add assets to a base AU', async () => {
    await assemblyUnitAddAssetsPage.doThenWaitForPageLoadNetworkCall(async () => {
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.addAssets.click()
      await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.fromAllAssets.click()
    })

    await expect(assemblyUnitAddAssetsPage.getAddAssetsStatusBar(baseAUName)).toBeVisible()
    await assemblyUnitAddAssetsPage
      .getFilterByName('Created By')
      .selectOptionByText(currentUser.getTestUserData().username as string)
    await assemblyUnitAddAssetsPage
      .getFilterByName('Workflow Step')
      .selectOptionByText('Ready for Assembly')
    await assemblyUnitAddAssetsPage.table.allCheckBoxes.first().check()
    await assemblyUnitAddAssetsPage.getAddAssetsButton({ assetCount: 5 }).click()
    await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_ADD_ASSETS)
    await assemblyUnitAddAssetsPage.doneButton.click()
    // expect the toast
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: '5 assets added successfully.',
      })
    ).toBeVisible()
  })

  await test.step('Transition assets to Internal Reviewer', async () => {
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSETS_READ_MANY_BY_IDS
    )
    const {
      addAssets,
      generateAU,
      transition,
      sendToVault,
      archive,
      export: download,
    } = nextGenAssemblyUnitAuthoringPage.actionsDropdown.items
    // verify that buttons are in the appropriate state
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await expectPointerEventEnabled(addAssets)
    await expectPointerEventEnabled(generateAU)
    await expectPointerEventEnabled(download)
    await expectPointerEventEnabled(archive)
    await expectPointerEventDisabled(transition)
    await expectPointerEventDisabled(sendToVault)
    // check all checkboxes
    const table = nextGenAssemblyUnitAuthoringPage.page.getByRole('table', { name: 'nested-table' })
    const allCheckBoxes = table.locator('input[type=checkbox]')
    await expect(allCheckBoxes).toHaveCount(6)
    await allCheckBoxes.first().check()

    // verify that buttons are in the appropriate state after checkbox click
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await expectPointerEventDisabled(addAssets)
    await expectPointerEventDisabled(generateAU)
    await expectPointerEventEnabled(download)
    await expectPointerEventDisabled(archive)
    await expectPointerEventEnabled(transition)
    await expectPointerEventEnabled(sendToVault)

    // click transition button
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.transition.click()
    await expect(nextGenAssemblyUnitAuthoringPage.transitionDialog.contentContainer).toContainText(
      'Multiple selected'
    )

    const internalReviewStep = INTERNAL_ITEM_REVIEW_STEPS[0]?.label
    if (!internalReviewStep) {
      throw new Error('Red flag! Label does not exist!')
    }
    await nextGenAssemblyUnitAuthoringPage.transitionDialog.content.toSelect.selectOption(
      internalReviewStep
    )
    await nextGenAssemblyUnitAuthoringPage.transitionDialog.buttons.apply.click()
    // confirm the number of assets transitioned
    const response = await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSETS_TRANSITION_WORKFLOW_MANY
    )
    const body = await (response as Response).json()
    const successLength = body[0].result.data.success.length
    expect(successLength).toBe(5)
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: `5 assets transitioned to "${internalReviewStep}"`,
      })
    ).toBeVisible()
  })

  await test.step('Send Internal Review assets to the vault and it should fail', async () => {
    // check all checkboxes
    const table = nextGenAssemblyUnitAuthoringPage.page.getByRole('table', { name: 'nested-table' })
    const allCheckBoxes = table.locator('input[type=checkbox]')
    await expect(allCheckBoxes).toHaveCount(6)
    await allCheckBoxes.first().check()

    // click send to vault button and confirm
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.sendToVault.click()
    await expect(nextGenAssemblyUnitAuthoringPage.sendToVaultDialog.contentContainer).toContainText(
      'Only assets that are Ready for Assembly will be sent to the vault. To include the manifest, all items must be “Ready for Assembly” or “Sent to Vault”.'
    )
    await nextGenAssemblyUnitAuthoringPage.sendToVaultDialog.buttons.send.click()
    // confirm the number of assets sent to vault
    const response = await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.SEND_TO_VAULT
    )
    const body = await (response as Response).json()
    const failed = body[0].result.data.failed as string[]
    expect(failed).toHaveLength(5)
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: `Unable to send assets ${failed.join(', ')} to vault`,
      })
    ).toBeVisible()
    await nextGenAssemblyUnitAuthoringPage.page.keyboard.press('Escape')
  })

  await test.step('Transition assets to Ready for Assembly', async () => {
    // check all checkboxes
    const table = nextGenAssemblyUnitAuthoringPage.page.getByRole('table', { name: 'nested-table' })
    const allCheckBoxes = table.locator('input[type=checkbox]')
    await expect(allCheckBoxes).toHaveCount(6)
    await allCheckBoxes.first().check()

    // click transition button
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.transition.click()
    await expect(nextGenAssemblyUnitAuthoringPage.transitionDialog.contentContainer).toContainText(
      'Multiple selected'
    )

    // transition back to ready for assembly so they can be send to vault
    const readyForAssemblyUnitStep = ITEM_FINALIZATION.at(-1)?.label
    if (!readyForAssemblyUnitStep) {
      throw new Error('Red flag! Label does not exist!')
    }
    await nextGenAssemblyUnitAuthoringPage.transitionDialog.content.toSelect.selectOption(
      readyForAssemblyUnitStep
    )
    await nextGenAssemblyUnitAuthoringPage.transitionDialog.buttons.apply.click()
    // confirm the number of assets transitioned
    const response = await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSETS_TRANSITION_WORKFLOW_MANY
    )
    const body = await (response as Response).json()
    const successLength = body[0].result.data.success.length
    expect(successLength).toBe(5)
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: `5 assets transitioned to "${readyForAssemblyUnitStep}"`,
      })
    ).toBeVisible()
  })

  await test.step('Send Ready for Assembly assets to the vault', async () => {
    // check all checkboxes
    const table = nextGenAssemblyUnitAuthoringPage.page.getByRole('table', { name: 'nested-table' })
    const allCheckBoxes = table.locator('input[type=checkbox]')
    await expect(allCheckBoxes).toHaveCount(6)
    await allCheckBoxes.first().check()

    // click send to vault button and confirm
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.sendToVault.click()
    await expect(nextGenAssemblyUnitAuthoringPage.sendToVaultDialog.contentContainer).toContainText(
      'Only assets that are Ready for Assembly will be sent to the vault. To include the manifest, all items must be “Ready for Assembly” or “Sent to Vault”.'
    )
    await nextGenAssemblyUnitAuthoringPage.sendToVaultDialog.buttons.send.click()
    // confirm the number of assets sent to vault
    const response = await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.SEND_TO_VAULT
    )
    const body = await (response as Response).json()
    const success = body[0].result.data.succeeded as string[]
    expect(success).toHaveLength(5)
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: `Successfully sent assets ${success.join(', ')} to vault`,
      })
    ).toBeVisible()
  })
})
