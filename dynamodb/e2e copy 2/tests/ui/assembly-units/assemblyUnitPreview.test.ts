import { test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAssemblyUnit } from '@e2e/lib'
import { PreviewPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { expect } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'

const subjectName = 'African American Studies'
const adminYear = '25'
const assetLength = 2

test('Verify previewing an AU', async ({
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
}) => {
  const [baseAssemblyUnit] = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    assetLength,
    assetProps: { itemCount: 1 },
    useExistingAssets: false,
    sequenceLength: 0,
    completeLength: 0,
    nextGenAssemblyUnitProps: {
      questionType: QuestionType.MULTIPLE_CHOICE_QUESTION,
      adminYear: adminYear,
      assemblyUnitType: AssemblyUnitType.BASE,
    },
  })) as E2EAssemblyUnit[]

  test.fail(!baseAssemblyUnit, 'Failed to create a base assembly unit')

  await nextGenAssemblyUnitAuthoringPage.goToPageWith({
    id: baseAssemblyUnit!.id,
    subject: subjectName,
  })

  await test.step('Preview the assembly unit', async () => {
    const uniqueId = page.locator('.cb-ic-nectar-nested-table-link')

    await expect(uniqueId).toHaveCount(assetLength)

    //unique ids of au (their assets)
    //need to remove white space
    const originalAssetOrder = (await uniqueId.allTextContents()).map((text) => text.trim())
    console.log(originalAssetOrder)

    const previewPagePromise = page.context().waitForEvent('page')
    await nextGenAssemblyUnitAuthoringPage.previewButtonGeneric.click()
    const previewPage = await previewPagePromise
    const auPreviewPage = new PreviewPage({ page: previewPage })

    // wait for the preview page to be done rendering
    await expect(
      auPreviewPage.page.getByRole('region', { name: 'Question and Answer' })
    ).toBeVisible({ timeout: 30000 })

    const assetDropdown = auPreviewPage.contents.assetDropdown
    const optionsLocator = auPreviewPage.contents.selectOptionDropdown
    await expect(assetDropdown).toBeVisible()
    await assetDropdown.click()
    await expect(optionsLocator).toHaveCount(assetLength)
    const options = await optionsLocator.allTextContents()

    console.log('select dropdown options:', options)

    //verify dropdown selection
    const cleanedOptions = options.map((option) => option?.split(') ')[1])
    if (originalAssetOrder.length !== 0 && cleanedOptions.length !== 0) {
      for (let i = 0; i < originalAssetOrder.length; i++) {
        if (originalAssetOrder[i] !== cleanedOptions[i]) {
          throw new Error(
            `Mismatch found at position ${i}. Expected: ${originalAssetOrder[i]}, Found: ${cleanedOptions[i]}`
          )
        }
      }
      console.log('Verification successful: Asset order matches options.')
    } else {
      console.error('Asset order or options list is empty.')
    }

    await auPreviewPage.contents.showInfoButton.click()
    async function verifyAssetAndClickNext(assetId: string, isLastAsset: boolean) {
      try {
        await expect(auPreviewPage.contents.assetTitle.getByText(assetId)).toBeVisible()
        const assetIdPrefix = assetId.slice(0, 3) // Grab first 3 characters of assetId

        console.log('assetIdPrefix:', assetIdPrefix)
        const infoPanel = auPreviewPage.contents.infoPanel
        await expect(infoPanel).toBeVisible()

        const infoPanelDropdownOptions = infoPanel.dropdown.locator('option')

        const optionsText = await infoPanelDropdownOptions.allTextContents()

        expect(optionsText).not.toHaveLength(0)

        for (const optionText of optionsText) {
          expect(optionText).toContain(assetIdPrefix)
        }

        const nextButton = auPreviewPage.contents.nextButton
        await expect(nextButton).toBeVisible()

        if (!isLastAsset) {
          await nextButton.click()
        }
      } catch (error) {
        console.error(`Error verifying asset ${assetId} and clicking next:`, error)
        throw error
      }
    }

    for (const [index, assetId] of originalAssetOrder.entries()) {
      const isLastAsset = index === originalAssetOrder.length - 1
      await verifyAssetAndClickNext(assetId, isLastAsset)
    }
  })
})
