import casual from 'casual'
import { test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { NextGenAssemblyUnitAuthoringPage, NextGenAssemblyUnitListPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { Page, expect } from '@playwright/test'
import { ItemType, SubjectIdentifier } from '@shared/config'
import { makeAssemblyUnitName } from '@shared/data-generation/utils'
import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
  QuestionTypeNames,
} from '@shared/dynamo-hummingbird/tables/split-models'
import type { AssemblyUnit } from '@shared/schema/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

const subjectName = 'African American Studies'
const adminYear = casual.random_element(['24', '25', '26'])
const adminYearValue = `20${adminYear}`
const baseForm = casual.random_element(Object.values(BaseForm))
const questionType = QuestionType.MULTIPLE_CHOICE_QUESTION
const questionTypeValue = QuestionTypeNames[questionType]
const questionTypeNumber = casual.integer(0, 4)
const questionTypeNumberValue = `0${questionTypeNumber}`
const courseCode = 'AAS'
const pretestingNumber = 0
const pretestingNumberValue = `${pretestingNumber}`
const baseAUName = makeAssemblyUnitName({
  assemblyUnitType: AssemblyUnitType.BASE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
})
const sequenceAUName = makeAssemblyUnitName({
  sequence: 0,
  assemblyUnitType: AssemblyUnitType.SEQUENCE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
  parentAssemblyUnitName: baseAUName,
})
const completeAUName = makeAssemblyUnitName({
  sequence: pretestingNumber,
  assemblyUnitType: AssemblyUnitType.COMPLETE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
  parentAssemblyUnitName: sequenceAUName,
})

const navigateToAfamPage = async (
  nextGenAssemblyUnitListPage: NextGenAssemblyUnitListPage,
  page: Page
) => {
  await nextGenAssemblyUnitListPage.goToPageWith({ subject: subjectName })
  await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)
}

const navigateToEditorPage = async (
  id: string,
  name: string,
  nextGenAssemblyUnitAuthoringPage: NextGenAssemblyUnitAuthoringPage,
  page: Page
) => {
  await nextGenAssemblyUnitAuthoringPage.goToPageWith({ id, subject: subjectName })
  await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
  const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(name)
  await expect(assemblyUnitTitle).toBeVisible()
}

const getAssemblyUnitAddResponse = async (responsePromise: Promise<unknown>) => {
  const response = (await responsePromise) as Response
  const body = await response.json()
  return body[0].result.data[0] as AssemblyUnit
}

test.describe.configure({ mode: 'serial' })
test.setTimeout(2 * 60 * 1000)

test.skip('Verify BASE, SEQUENCE, and COMPLETE assembly unit creation', async ({
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
}) => {
  const numAssets = 5
  let baseAUId: string | undefined
  let sequenceAUId: string | undefined
  let completeAUId: string | undefined

  await test.step('Create assets for insertion into new AU', async () => {
    await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      assetProps: { itemType: ItemType.MultipleChoiceQuestion },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
      length: numAssets,
    })
  })

  await test.step('Go to page with specific subject', async () => {
    await navigateToAfamPage(nextGenAssemblyUnitListPage, page)
  })

  await test.step('Create new BASE assembly unit', async () => {
    await nextGenAssemblyUnitListPage.createNewButton.click()
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.auTypeOption.selectOption('Base')
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.adminYearOption.selectOption(
      adminYearValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.baseFormOption.selectOption(
      baseForm
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeOption.selectOption(
      questionTypeValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeNumberOption.selectOption(
      questionTypeNumberValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.buttons.apply.click()

    // input the placeholder values
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.assetCount.fill('1')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.itemPerAssetCount.fill('2')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.buttons.apply.click()

    await expect(page.locator('.overlay-loader-text')).toContainText(
      'Creating and navigating to 1 Base assembly unit(s).'
    )
    // creating new base au leads to the authoring page
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_READ_BY_ID
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'A new Base AU has been created.',
      })
    ).toBeVisible()
    const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(
      baseAUName
    )
    await expect(assemblyUnitTitle).toBeVisible()
    const baseAUUrl = page.url().replace(/http:\/\/[^/]+/, '')
    baseAUId = baseAUUrl?.split('/').at(-1)
  })

  await test.step('Go back to the Assembly Unit list page', async () => {
    await navigateToAfamPage(nextGenAssemblyUnitListPage, page)
  })

  await test.step('Expand the appropriate year and check for BASE assembly unit name', async () => {
    // verify that au name in the hierarchy list
    const listHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      `${adminYear}_${courseCode}`
    )
    await listHeadingItem.click()
    const listBaseHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      baseAUId as string
    )
    await expect(listBaseHeadingItem).toBeVisible()
    // check the elements in the heading item
    const assemblyUnitTitle = listBaseHeadingItem.getByRole('link', {
      name: baseAUName,
      exact: true,
    })
    await expect(assemblyUnitTitle).toBeVisible()
  })

  await test.step('Create new SEQUENCE assembly unit', async () => {
    const listBaseHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      baseAUId as string
    )
    await expect(listBaseHeadingItem).toBeVisible()
    // click dropdown and the generate AU button
    await listBaseHeadingItem.getByRole('button', { name: 'Dropdown' }).click()
    await listBaseHeadingItem.getByRole('menuitem', { name: 'Generate assembly units' }).click()
    // just need to fill the quantity since the rest are prefilled
    await nextGenAssemblyUnitListPage.newAssemblyUnitDialog.quantityInput.fill('1')
    const responsePromise = waitForResponseFrom(
      nextGenAssemblyUnitListPage.page,
      endpoints.ASSEMBLY_UNIT_ADD
    )
    await nextGenAssemblyUnitListPage.newAssemblyUnitDialog.buttons.apply.click()
    await expect(page.locator('.overlay-loader-text')).toContainText(
      'Creating  1 Sequence assembly unit(s).'
    )
    const newAssemblyUnit = await getAssemblyUnitAddResponse(responsePromise)
    sequenceAUId = newAssemblyUnit.id
    expect(newAssemblyUnit.name).toBe(sequenceAUName)
    await waitForResponseFrom(
      nextGenAssemblyUnitListPage.page,
      endpoints.ASSEMBLY_UNIT_READ_ALL_DESCENDANTS_BY_ID
    )
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: 'A new Sequence AU has been created.',
      })
    ).toBeVisible()
    // verify that au name in the hierarchy list
    const listSequenceHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      sequenceAUId as string
    )
    await expect(listSequenceHeadingItem).toBeVisible()
    // check the elements in the heading item
    const assemblyUnitTitle = listSequenceHeadingItem.getByRole('link', {
      name: sequenceAUName,
      exact: true,
    })
    await expect(assemblyUnitTitle).toBeVisible()
  })

  await test.step('Create new COMPLETE assembly unit', async () => {
    const listSequenceHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      sequenceAUId as string
    )
    await expect(listSequenceHeadingItem).toBeVisible()
    // click dropdown and the generate AU button
    await listSequenceHeadingItem.getByRole('button', { name: 'Dropdown' }).click()
    await listSequenceHeadingItem.getByRole('menuitem', { name: 'Generate assembly units' }).click()
    // just need to fill the pretesting number and quantity since the rest are prefilled
    await nextGenAssemblyUnitListPage.newAssemblyUnitDialog.pretestingNumberInput.fill(
      pretestingNumberValue
    )
    await nextGenAssemblyUnitListPage.newAssemblyUnitDialog.quantityInput.fill('1')
    const responsePromise = waitForResponseFrom(
      nextGenAssemblyUnitListPage.page,
      endpoints.ASSEMBLY_UNIT_ADD
    )
    await nextGenAssemblyUnitListPage.newAssemblyUnitDialog.buttons.apply.click()
    await expect(page.locator('.overlay-loader-text')).toContainText(
      'Creating  1 Complete assembly unit(s).'
    )
    const newAssemblyUnit = await getAssemblyUnitAddResponse(responsePromise)
    completeAUId = newAssemblyUnit.id
    expect(newAssemblyUnit.name).toBe(completeAUName)
    await waitForResponseFrom(
      nextGenAssemblyUnitListPage.page,
      endpoints.ASSEMBLY_UNIT_READ_ALL_DESCENDANTS_BY_ID
    )
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: 'A new Complete AU has been created.',
      })
    ).toBeVisible()
    // verify that au name in the hierarchy list
    const listCompleteHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      completeAUId as string
    )
    await expect(listCompleteHeadingItem).toBeVisible()
    // check the elements in the heading item
    const assemblyUnitTitle = listCompleteHeadingItem.getByRole('link', {
      name: completeAUName,
      exact: true,
    })

    await expect(assemblyUnitTitle).toBeVisible()
  })

  await test.step('Edit the AU name', async () => {
    const listCompleteHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      completeAUId as string
    )
    await listCompleteHeadingItem.getByRole('button', { name: 'Dropdown' }).click()
    await listCompleteHeadingItem.getByRole('menuitem', { name: 'Edit AU Name' }).click()
    await nextGenAssemblyUnitListPage.renameAssemblyUnitDialog.content.editAUNameTextBox.fill(
      'A New AU Name'
    )
    await nextGenAssemblyUnitListPage.renameAssemblyUnitDialog.buttons.apply.click()
    await waitForResponseFrom(nextGenAssemblyUnitListPage.page, endpoints.ASSEMBLY_UNIT_RENAME)
    await expect(
      nextGenAssemblyUnitListPage.toastLocator.filter({
        hasText: 'AU name updated',
      })
    ).toBeVisible()
    const assemblyUnitTitleEditedName = page.getByRole('link', {
      name: 'A NEW AU NAME',
      exact: true,
    })
    await expect(assemblyUnitTitleEditedName).toBeVisible()
  })
  await test.step('Verify available options listed in Base, Sequence and Complete', async () => {
    // AU data init
    const defaultOptions = ['Preview', 'Download', 'Archive']
    const archivedOptions = ['Preview', 'Download']
    const auData = [
      {
        auId: baseAUId,
        defaultOptions: [...defaultOptions, 'Add assets', 'Generate assembly units'],
        archivedOptions: [...archivedOptions, 'Restore'],
      },
      {
        auId: sequenceAUId,
        defaultOptions: [...defaultOptions, 'Generate assembly units'],
        archivedOptions: [...archivedOptions],
      },
      {
        auId: completeAUId,
        defaultOptions: [...defaultOptions, 'Add assets', 'Edit AU Name', 'Form Associations'],
        archivedOptions: [...archivedOptions, 'Form Associations'],
      },
    ]
    // Using for-loop to check through the Base, Sequence and Complete AU dropdown menu options
    async function verifyMenuOptions(auData: any, isArchived: boolean) {
      for (const { auId, defaultOptions, archivedOptions } of auData) {
        const hierarchyList = page.getByRole('button', {
          name: `Hierarchy list item heading ${auId}`,
        })
        // Verify the dropdown menu options are visible based on different types of AU
        await hierarchyList.getByRole('button', { name: 'Dropdown' }).click()
        if (isArchived) {
          await expect(hierarchyList.getByText('Archived')).toBeVisible()
          for (const option of archivedOptions) {
            await expect(hierarchyList.getByRole('menuitem', { name: option })).toBeVisible()
          }
          // Complete AUs do not have a visible expand/collapse icon
          if (auId !== completeAUId) {
            await hierarchyList.getByTestId(`expand-collapse-icon-${auId}`).click()
          }
        } else {
          for (const option of defaultOptions) {
            await expect(hierarchyList.getByRole('menuitem', { name: option })).toBeVisible()
          }
        }
        // dismiss the dropdown
        await hierarchyList.getByRole('button', { name: 'Dropdown' }).click()
      }
    }
    // Verify the menu options under the Archived state
    const baseHierarchyList = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(baseAUId)
    await expect(baseHierarchyList).toBeVisible()
    await verifyMenuOptions(auData, false)
    await baseHierarchyList.getByRole('button', { name: 'Dropdown' }).click()
    await baseHierarchyList.getByRole('menuitem', { name: 'Archive' }).click()
    await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_ARCHIVE)
    await nextGenAssemblyUnitListPage.getCheckboxFilter('Archived').click()
    await baseHierarchyList.click()
    await verifyMenuOptions(auData, true)
  })

  await test.step('Test active and archive filters', async () => {
    const base = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(baseAUId)
    const sequence = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(sequenceAUId)
    const complete = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(completeAUId)
    const activeFilterCheckbox = nextGenAssemblyUnitListPage.getCheckboxFilter('Active')
    const archivedFilterCheckbox = nextGenAssemblyUnitListPage.getCheckboxFilter('Archived')

    // Check the active filter
    await archivedFilterCheckbox.click()
    await expect(base).toBeHidden()
    await expect(sequence).toBeHidden()
    await expect(complete).toBeHidden()

    // Check the archived filter
    await activeFilterCheckbox.click()
    await archivedFilterCheckbox.click()
    await expect(base).toBeVisible()
    await expect(sequence).toBeVisible()
    await expect(complete).toBeVisible()

    // Check if no filters are applied
    await archivedFilterCheckbox.click()
    const allHierarchyHeadings =
      await nextGenAssemblyUnitListPage.getAllAssemblyUnitListHeadingItems()
    for (const heading of allHierarchyHeadings) {
      await expect(heading).toBeHidden()
    }
  })

  await test.step('Verify that the Base AU can see Sequence and Complete AUs', async () => {
    // navigate to base AU editor page
    await navigateToEditorPage(
      baseAUId as string,
      baseAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(sequenceAUName)
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText('A NEW AU NAME')
  })

  await test.step('Verify that the Sequence AU can see Base and Complete AUs', async () => {
    // navigate to sequence AU editor page
    await navigateToEditorPage(
      sequenceAUId as string,
      sequenceAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(baseAUName)
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText('A NEW AU NAME')
  })

  await test.step('Verify that the Complete AU can see Base and Sequence AUs', async () => {
    // navigate to complete AU editor page
    await navigateToEditorPage(
      completeAUId as string,
      'A NEW AU NAME',
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(baseAUName)
    await expect(nextGenAssemblyUnitAuthoringPage.relationshipTable).toContainText(sequenceAUName)
  })
})
