import { expect, test } from '@e2e/cb-test'
import { NextGenAssemblyUnitAuthoringPage } from '@e2e/pages/NextGenAssemblyUnitAuthoringPage'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectKey } from '@shared/config'

test.skip('Verify Assembly Unit Collapse and Expand and Reordering Assets', async ({
  nextGenAssemblyUnitListPage,
  page,
}) => {
  const responsePromise = waitForResponseFrom(
    page,
    endpoints.ASSEMBLY_UNIT_READ_ALL_DESCENDANTS_BY_ID
  )
  await nextGenAssemblyUnitListPage.goToPageWith({ subject: SubjectKey.AFRICAN_AMERICAN_STUDIES })
  await nextGenAssemblyUnitListPage.expandAllButton.click()

  const responseBody = await (await responsePromise).json()
  const baseAssemblyUnit = responseBody[0].result?.data.assemblyUnitTree
  const baseAUAdded = nextGenAssemblyUnitListPage.assemblyUnitTableRow(baseAssemblyUnit.name)

  const [newPage] = await Promise.all([
    page.context().waitForEvent('page'),
    await baseAUAdded
      .getByRole('link', { name: baseAssemblyUnit.name, exact: true })
      .first()
      .click(),
  ])

  const newAUPage = new NextGenAssemblyUnitAuthoringPage({ page: newPage })

  await test.step('Verify Assembly Unit Collapses and Expands Assets', async () => {
    // test caret expand
    expect(await newAUPage.allItems.count()).toEqual(0)
    await newAUPage.expandButton.click()
    await newAUPage.allItems.first().waitFor({ state: 'visible' })
    expect(await newAUPage.allItems.count()).not.toEqual(0)

    await newAUPage.expandButton.click()
    await newAUPage.allItems.first().waitFor({ state: 'hidden' })

    // test expand all button
    expect(await newAUPage.allItems.count()).toEqual(0)
    await newAUPage.expandAllButton.click()
    await newAUPage.allItems.first().waitFor({ state: 'visible' })
    expect(await newAUPage.allItems.count()).not.toEqual(0)
    await newAUPage.collapseAllButton.click()
    await newAUPage.allItems.first().waitFor({ state: 'hidden' })
    const itemsNotVisible = newAUPage.allItems.first()
    await expect(itemsNotVisible).toBeHidden()
  })
})
