import { pascalCase } from 'case-anything'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EAssemblyUnit, E2ECollection } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { ItemType } from '@shared/data-generation/mock'
import { AssemblyUnitType, QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import { ItemType as ItemTypeEnum } from '@shared/schema'

const adminYear = '25'
const questionType = QuestionType.MULTIPLE_CHOICE_QUESTION
const courseCode = 'AAS'
let assemblyUnitId = ''

test('Verify Adding To An Assembly Unit', async ({
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
  dataController,
}) => {
  await test.step('Generate assembly unit and navigate to page', async () => {
    const assemblyUnit = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      assetLength: 5,
      sequenceLength: 0,
      completeLength: 0,
      nextGenAssemblyUnitProps: {
        questionType,
        adminYear,
        assemblyUnitType: AssemblyUnitType.BASE,
      },
    })) as E2EAssemblyUnit[]
    test.fail(!assemblyUnit?.[0] || !assemblyUnit?.[0]?.id, 'Failed to create an assembly unit')
    const id = assemblyUnit?.[0]?.id as string
    assemblyUnitId = id
    await nextGenAssemblyUnitAuthoringPage.goToPageWith({
      subject: SubjectKey.AFRICAN_AMERICAN_STUDIES,
      id,
    })
  })

  await test.step('Verify Adding Assets From a Collection', async () => {
    const oppositeQuestionType = {
      [QuestionType.MULTIPLE_CHOICE_QUESTION]: ItemTypeEnum.FreeResponseQuestion,
      [QuestionType.FREE_RESPONSE_QUESTION]: ItemTypeEnum.MultipleChoiceQuestion,
      [QuestionType.PROJECT]: ItemTypeEnum.MultipleChoiceQuestion,
    }
    const assetCount = 5
    // generate collection with assets with appropriate question type
    const [collectionWithAppropriateQuestionType] = (await dataController.generate(
      DataControllerModel.COLLECTION,
      {
        user: userE2E,
        courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
        length: 1,
        assetLength: assetCount,
        assetProps: { itemType: pascalCase(questionType) as ItemType },
        collectionProps: { name: 'Add From Collection Test' },
      }
    )) as E2ECollection[]
    test.fail(!collectionWithAppropriateQuestionType, 'Failed to create a collection')

    // generate collection with assets with the wrong question type
    const [collectionWithWrongQuestionType] = (await dataController.generate(
      DataControllerModel.COLLECTION,
      {
        user: userE2E,
        courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
        length: 1,
        assetLength: assetCount,
        assetProps: {
          itemType: oppositeQuestionType[questionType],
        },
        collectionProps: { name: 'Add From Collection Test' },
      }
    )) as E2ECollection[]
    test.fail(!collectionWithWrongQuestionType, 'Failed to create a collection')

    // select 'from a collection' option
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.addAssets.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.fromACollection.click()

    // search for collection, select result, and press add
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.content.searchBar.fill(
      collectionWithAppropriateQuestionType!.id
    )
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.content
      .getSearchResultByIndex(0)
      .click()
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.buttons.add.click()
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSETS_READ_MANY_BY_IDS
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastDialogLocator.filter({
        hasText: `Successfully saved ${assetCount} assets from ${collectionWithAppropriateQuestionType?.name}`,
      })
    ).toBeVisible()
    // wait for assets to be visible
    await expect(
      nextGenAssemblyUnitAuthoringPage.assetTableRow(
        collectionWithAppropriateQuestionType!.assetIDs[0]!
      )
    ).toBeVisible()
    const allAssets = await nextGenAssemblyUnitAuthoringPage.allAssets.allInnerTexts()
    const lastFiveAssets = allAssets.slice(-5)
    expect(lastFiveAssets).toEqual(collectionWithAppropriateQuestionType!.assetIDs)

    // verify 'Undo' option on the toast
    await nextGenAssemblyUnitAuthoringPage.toastDialogLocator
      .getByRole('link', { name: 'UNDO' })
      .click()
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_UNDO_ADD_ASSETS_FROM_COLLECTION
    )
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSETS_READ_MANY_BY_IDS
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: `Successfully removed ${assetCount} assets from assembly unit`,
      })
    ).toBeVisible()
    // wait for assets to be removed
    await expect(
      nextGenAssemblyUnitAuthoringPage.assetTableRow(
        collectionWithAppropriateQuestionType!.assetIDs[0]!
      )
    ).toBeHidden()
    const allAssetAfterUndo = await nextGenAssemblyUnitAuthoringPage.allAssets.allInnerTexts()
    collectionWithAppropriateQuestionType?.assetIDs?.forEach((id) => {
      expect(allAssetAfterUndo).not.toContain(id)
    })

    // select 'from a collection' option
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.addAssets.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.fromACollection.click()

    // search for collection, select result, and press add
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.content.searchBar.fill(
      collectionWithWrongQuestionType!.id
    )
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.content
      .getSearchResultByIndex(0)
      .click()
    await nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.buttons.add.click()
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_ADD_ASSETS_FROM_COLLECTION
    )

    // verify that 0 assets were added
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_READ_BY_ID
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastDialogLocator.filter({
        hasText: `Successfully saved 0 assets from ${collectionWithWrongQuestionType?.name}`,
      })
    ).toBeVisible()
    const allAssetsAfterSecondAdd = await nextGenAssemblyUnitAuthoringPage.allAssets.allInnerTexts()
    collectionWithWrongQuestionType?.assetIDs?.forEach((id) => {
      expect(allAssetsAfterSecondAdd).not.toContain(id)
    })
  })

  await test.step('Navigate to the Assembly Unit list page', async () => {
    await nextGenAssemblyUnitListPage.goToPageWith({ subject: SubjectKey.AFRICAN_AMERICAN_STUDIES })
    await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)
  })

  await test.step('Verify that "from a collection" option menu takes user to authoring page', async () => {
    const listHeadingItem = nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(
      `${adminYear}_${courseCode}`
    )
    await listHeadingItem.click()
    const listBaseHeadingItem =
      nextGenAssemblyUnitListPage.getAssemblyUnitListHeadingItem(assemblyUnitId)
    // click dropdown and select 'from a collection' button
    await listBaseHeadingItem.getByRole('button', { name: 'Dropdown' }).click()
    await listBaseHeadingItem.getByRole('menuitem', { name: 'Add assets' }).click()
    await listBaseHeadingItem.getByRole('menuitem', { name: '...from a collection' }).click()

    // verify that 'Add from a collection' dialog is automatically opened and visible
    await expect(nextGenAssemblyUnitAuthoringPage.addFromCollectionsDialog.root).toBeVisible()
    expect(nextGenAssemblyUnitAuthoringPage.page.url()).toContain(assemblyUnitId)
  })
})
