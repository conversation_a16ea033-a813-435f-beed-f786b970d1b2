import casual from 'casual'
import { test } from '@e2e/cb-test'
import type { NextGenAssemblyUnitAuthoringPage, NextGenAssemblyUnitListPage } from '@e2e/pages'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { Page, expect } from '@playwright/test'
import { makeAssemblyUnitName } from '@shared/data-generation/utils'
import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
  QuestionTypeNames,
} from '@shared/dynamo-hummingbird/tables/split-models'
import type { AssemblyUnit } from '@shared/schema/split-models'

const subjectName = 'African American Studies'
const adminYear = casual.random_element(['24', '25', '26'])
const adminYearValue = `20${adminYear}`
const baseForm = BaseForm.F0
const newBaseForm = BaseForm.F6
const questionType = QuestionType.MULTIPLE_CHOICE_QUESTION
const questionTypeValue = QuestionTypeNames[questionType]
const questionTypeNumber = 0
const newQuestionTypeNumber = 4
const questionTypeNumberValue = `0${questionTypeNumber}`
const newQuestionTypeNumberValue = `0${newQuestionTypeNumber}`
const courseCode = 'AAS'
const pretestingNumber = 0
const pretestingNumberValue = `${pretestingNumber}`
const baseAUName = makeAssemblyUnitName({
  assemblyUnitType: AssemblyUnitType.BASE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
})
const newBaseAUName = makeAssemblyUnitName({
  assemblyUnitType: AssemblyUnitType.BASE,
  adminYear,
  baseForm: newBaseForm,
  courseCode,
  questionType,
  questionTypeNumber: newQuestionTypeNumber,
})
const sequenceAUName = makeAssemblyUnitName({
  sequence: 0,
  assemblyUnitType: AssemblyUnitType.SEQUENCE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
  parentAssemblyUnitName: baseAUName,
})
const newSequenceAUName = makeAssemblyUnitName({
  sequence: 0,
  assemblyUnitType: AssemblyUnitType.SEQUENCE,
  adminYear,
  baseForm: newBaseForm,
  courseCode,
  questionType,
  questionTypeNumber: newQuestionTypeNumber,
  parentAssemblyUnitName: newBaseAUName,
})
const completeAUName = makeAssemblyUnitName({
  sequence: pretestingNumber,
  assemblyUnitType: AssemblyUnitType.COMPLETE,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  questionTypeNumber,
  parentAssemblyUnitName: sequenceAUName,
})
const newCompleteAUName = makeAssemblyUnitName({
  sequence: pretestingNumber,
  assemblyUnitType: AssemblyUnitType.COMPLETE,
  adminYear,
  baseForm: newBaseForm,
  courseCode,
  questionType,
  questionTypeNumber: newQuestionTypeNumber,
  parentAssemblyUnitName: newSequenceAUName,
})

const navigateToAfamPage = async (
  nextGenAssemblyUnitListPage: NextGenAssemblyUnitListPage,
  page: Page
) => {
  await nextGenAssemblyUnitListPage.goToPageWith({ subject: subjectName })
  await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ASSEMBLY_UNIT_TYPE)
}

const navigateToEditorPage = async (
  id: string,
  name: string,
  nextGenAssemblyUnitAuthoringPage: NextGenAssemblyUnitAuthoringPage,
  page: Page
) => {
  await nextGenAssemblyUnitAuthoringPage.goToPageWith({ id, subject: subjectName })
  await waitForResponseFrom(page, endpoints.ASSEMBLY_UNIT_READ_BY_ID)
  const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(name)
  await expect(assemblyUnitTitle).toBeVisible()
}

const getAssemblyUnitAddResponse = async (responsePromise: Promise<unknown>) => {
  const response = (await responsePromise) as Response
  const body = await response.json()
  return body[0].result.data[0] as AssemblyUnit
}

test.describe.configure({ mode: 'serial' })

test('Verify base assembly unit attribute update', async ({
  nextGenAssemblyUnitListPage,
  nextGenAssemblyUnitAuthoringPage,
  page,
}) => {
  let baseAUId: string | undefined
  let sequenceAUId: string | undefined
  let completeAUId: string | undefined

  await test.step('Go to page with specific subject', async () => {
    await navigateToAfamPage(nextGenAssemblyUnitListPage, page)
  })

  //This is to ensure that the prevent duplicate AU message upon AU creation is suppressed
  await test.step('Set up network interception for duplicate assembly unit check', async () => {
    await page.route('**/assembly-units/assemblyUnitsQueryDuplicate?batch=1', (route) => {
      // Mock a successful response indicating the assembly unit is unique
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([{ result: { data: { assemblyUnitIsUnique: true } } }]),
      })
    })
  })

  await test.step('Create new BASE assembly unit', async () => {
    await nextGenAssemblyUnitListPage.createNewButton.click()
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.auTypeOption.selectOption('Base')
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.adminYearOption.selectOption(
      adminYearValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.baseFormOption.selectOption(
      baseForm
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeOption.selectOption(
      questionTypeValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.questionTypeNumberOption.selectOption(
      questionTypeNumberValue
    )
    await nextGenAssemblyUnitListPage.newBaseAssemblyUnitDialog.buttons.apply.click()

    // input the placeholder values
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.assetCount.fill('1')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.content.itemPerAssetCount.fill('2')
    await nextGenAssemblyUnitListPage.placeholderInputDialog.buttons.apply.click()

    await expect(page.locator('.overlay-loader-text')).toContainText(
      'Creating and navigating to 1 Base assembly unit(s).'
    )
    // creating new base au leads to the authoring page
    await waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_READ_BY_ID
    )
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'A new Base AU has been created.',
      })
    ).toBeVisible()
    const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(
      baseAUName
    )
    await expect(assemblyUnitTitle).toBeVisible()
    const baseAUUrl = page.url().replace(/http:\/\/[^/]+/, '')
    baseAUId = baseAUUrl?.split('/').at(-1)
  })

  await test.step('Create new SEQUENCE assembly unit', async () => {
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.generateAU.click()
    // just need to fill the quantity since the rest are prefilled
    await nextGenAssemblyUnitAuthoringPage.newAssemblyUnitDialog.quantityInput.fill('1')
    const responsePromise = waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_ADD
    )
    await nextGenAssemblyUnitAuthoringPage.newAssemblyUnitDialog.buttons.apply.click()
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'A new Sequence AU has been created.',
      })
    ).toBeVisible()
    const newAssemblyUnit = await getAssemblyUnitAddResponse(responsePromise)
    sequenceAUId = newAssemblyUnit.id
    expect(newAssemblyUnit.name).toBe(sequenceAUName)
  })

  await test.step('Navigate to SEQUENCE assembly unit', async () => {
    // navigate to sequence AU editor page
    await navigateToEditorPage(
      sequenceAUId as string,
      sequenceAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )
  })

  await test.step('Create new COMPLETE assembly unit', async () => {
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.button.click()
    await nextGenAssemblyUnitAuthoringPage.actionsDropdown.items.generateAU.click()
    // just need to fill the pretesting number and quantity since the rest are prefilled
    await nextGenAssemblyUnitAuthoringPage.newAssemblyUnitDialog.pretestingNumberInput.fill(
      pretestingNumberValue
    )
    await nextGenAssemblyUnitAuthoringPage.newAssemblyUnitDialog.quantityInput.fill('1')
    const responsePromise = waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNIT_ADD
    )
    await nextGenAssemblyUnitAuthoringPage.newAssemblyUnitDialog.buttons.apply.click()
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'A new Complete AU has been created.',
      })
    ).toBeVisible()
    const newAssemblyUnit = await getAssemblyUnitAddResponse(responsePromise)
    completeAUId = newAssemblyUnit.id
    expect(newAssemblyUnit.name).toBe(completeAUName)
  })

  await test.step('Update BASE assembly unit attributes', async () => {
    // navigate to base AU editor page
    await navigateToEditorPage(
      baseAUId as string,
      baseAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()

    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(baseForm)
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(
      questionTypeNumberValue
    )

    await nextGenAssemblyUnitAuthoringPage.editAUAttributesButton.click()
    await nextGenAssemblyUnitAuthoringPage.editAssemblyUnitAttributesDialog.baseFormOption.selectOption(
      newBaseForm
    )
    await nextGenAssemblyUnitAuthoringPage.editAssemblyUnitAttributesDialog.questionTypeNumberOption.selectOption(
      newQuestionTypeNumberValue
    )
    const responsePromise = waitForResponseFrom(
      nextGenAssemblyUnitAuthoringPage.page,
      endpoints.ASSEMBLY_UNITS_UPDATE_ATTRIBUTES
    )
    await nextGenAssemblyUnitAuthoringPage.editAssemblyUnitAttributesDialog.buttons.apply.click()
    await expect(
      nextGenAssemblyUnitAuthoringPage.toastLocator.filter({
        hasText: 'Assembly unit attributes have been updated.',
      })
    ).toBeVisible()
    await getAssemblyUnitAddResponse(responsePromise)

    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(newBaseForm)
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(
      newQuestionTypeNumberValue
    )
    const assemblyUnitTitle = await nextGenAssemblyUnitAuthoringPage.getAssemblyUnitTitle(
      newBaseAUName
    )
    await expect(assemblyUnitTitle).toBeVisible()
  })

  await test.step('Verify BASE assembly unit attributes update on the SEQUENCE assembly unit', async () => {
    // navigate to sequence AU editor page
    await navigateToEditorPage(
      sequenceAUId as string,
      newSequenceAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(newBaseForm)
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(
      newQuestionTypeNumberValue
    )
  })
  await test.step('Verify BASE assembly unit attributes update on the COMPLETE assembly unit', async () => {
    // navigate to complete AU editor page
    await navigateToEditorPage(
      completeAUId as string,
      newCompleteAUName,
      nextGenAssemblyUnitAuthoringPage,
      page
    )

    await nextGenAssemblyUnitAuthoringPage.detailsTab.click()
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(newBaseForm)
    await expect(nextGenAssemblyUnitAuthoringPage.attributesTable).toContainText(
      newQuestionTypeNumberValue
    )
  })
})
