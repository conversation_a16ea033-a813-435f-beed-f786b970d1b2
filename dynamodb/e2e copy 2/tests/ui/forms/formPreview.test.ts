import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EAssemblyUnit, E2EForm } from '@e2e/lib'
import { FormPage, PreviewPage } from '@e2e/pages'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { Locator } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

async function addAssemblyUnitByNameOrId({
  formPage,
  nameOrId,
  toastLocator,
  toastLocatorCloseButton,
}: {
  formPage: FormPage
  nameOrId: string
  toastLocator: Locator
  toastLocatorCloseButton: Locator
}) {
  await formPage.moreActionsButton.first().click()
  await formPage.moreActionsAddMenuItem.hover()
  await formPage.moreActionsAddAssemblyUnitsMenuItem.first().click()
  await formPage.addAssemblyUnitsToFormSectionModalInput.fill(nameOrId)
  await formPage.addAssemblyUnitToFormSectionModalAddButton.first().click()
  await formPage.addAssemblyUnitToFormSectionModalDoneButton.first().click()
  await waitForResponseFrom(formPage.page, endpoints.FORMS_ADD_ASSEMBLY_UNITS)
  await waitForResponseFrom(formPage.page, endpoints.FORMS_READ_ONE)
  await expect(toastLocator).toBeVisible()
  await toastLocatorCloseButton.click()
}

async function removeAllAssemblyUnits({
  formPage,
  toastLocator,
  toastLocatorCloseButton,
}: {
  formPage: FormPage
  toastLocator: Locator
  toastLocatorCloseButton: Locator
}) {
  const assemblyUnitCount = await formPage.page
    .getByRole('button', { name: /Assembly Unit -/ })
    .count()
  for (let i = 0; i < assemblyUnitCount; i++) {
    const accordian = formPage.page.getByRole('button', { name: /Assembly Unit -/ }).first()
    const dropdown = accordian.locator('#panel-dropdown')
    await dropdown.click()
    await formPage.removeSelector.click()
    await formPage.removeAssemblyUnitDialog.buttons.delete.click()
    await waitForResponseFrom(formPage.page, endpoints.FORMS_REMOVE_ASSEMBLY_UNITS)
    await waitForResponseFrom(formPage.page, endpoints.FORMS_READ_ONE)
    await expect(toastLocator).toBeVisible()
    await toastLocatorCloseButton.click()
    await expect(formPage.assemblyUnitRow).toHaveCount(assemblyUnitCount - 1 - i)
  }
}

test.setTimeout(4 * 60 * 1000)

test('Form Preview', async ({ formPage, dataController, assetListPage }) => {
  let assemblyUnitComplete: E2EAssemblyUnit | undefined
  const assetLength = 3
  const assemblyUnitLength = 1
  const toastLocator = assetListPage.toastLocator.getByText('has been added to form section')
  const toastLocatorCloseButton = assetListPage.toastLocator.getByRole('button', {
    name: 'Close alert',
  })
  const removalToastLocator = assetListPage.toastLocator.getByText(
    'have been removed from form section'
  )

  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: { name: 'E2E Form Preview' },
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assemblyUnitLength,
      assetLength,
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW, WorkflowStepID.EXTERNAL_AUTHORING],
    })) as E2EForm[]
    if (form) {
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Assets in Assembly units have appropriate number of assets on preview page', async () => {
    const previewPagePromise = formPage.page.context().waitForEvent('page')
    await formPage.previewFormButton.click()
    const newPage = await previewPagePromise
    const formPreviewPage = new PreviewPage({ page: newPage })

    await expect(newPage.getByRole('heading', { name: 'Exam Directions' })).toContainText(
      'Exam Directions',
      { timeout: 30000 }
    )
    await expect(newPage.getByRole('heading', { name: 'Section Directions' })).toContainText(
      'Section Directions'
    )
    await expect(formPreviewPage.contents.formPreviewNext).toBeVisible()

    await formPreviewPage.contents.showInfoButton.click()

    // Testing 'Next' in previewer
    const nextButton = formPreviewPage.contents.formPreviewNext
    for (let i = 0; i < assetLength * assemblyUnitLength; i++) {
      const questionCounter = (i % assetLength) + 1

      if (i === assetLength) {
        // at preview review page between sections
        await expect(
          formPreviewPage.page.getByRole('region', { name: 'Review Page' })
        ).toBeVisible()
        await nextButton.click()
        // directions pop up after moving to the next section
        await formPreviewPage.page.getByLabel('Close Directions').click()
      }
      const questionCounterLocator = formPreviewPage.mainLocator.getByRole('button', {
        name: `Question Navigator Question ${questionCounter} of ${assetLength}`,
      })
      await expect(questionCounterLocator).toBeVisible()
      await nextButton.click()
    }

    // Testing 'Back' in previewer
    const backButton = formPreviewPage.contents.formPreviewBack
    for (let i = assetLength; i > 0; i--) {
      await backButton.click()
      const questionCounterLocator = formPreviewPage.mainLocator.getByRole('button', {
        name: `Question Navigator Question ${i} of ${assetLength}`,
      })
      await expect(questionCounterLocator).toBeVisible()
    }

    // close the previewer
    await formPreviewPage.page.close()
  })

  await test.step('Generate an assembly unit to add to both sections', async () => {
    const assemblyUnits = (await dataController.generate(DataControllerModel.ASSEMBLY_UNIT, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      assetLength: 1,
      nextGenAssemblyUnitProps: { adminYear: '26' },
      sequenceLength: 1,
      completeLength: 1,
    })) as E2EAssemblyUnit[]
    assemblyUnitComplete = assemblyUnits.at(-1)
  })

  await test.step('Add assembly units to form section 1 and 2', async () => {
    await formPage.sectionOneTab.click()
    // add assembly unit through the 'Add assembly units' button
    if (assemblyUnitComplete) {
      await addAssemblyUnitByNameOrId({
        formPage,
        nameOrId: assemblyUnitComplete.id,
        toastLocator,
        toastLocatorCloseButton,
      })
    }

    await formPage.sectionTwoTab.click()
    // add assembly unit through the 'Add assembly units' button
    if (assemblyUnitComplete) {
      await addAssemblyUnitByNameOrId({
        formPage,
        nameOrId: assemblyUnitComplete.id,
        toastLocator,
        toastLocatorCloseButton,
      })
    }

    await formPage.sectionOneTab.click()
    // confirm that the assembly unit is no longer in section 1
    if (assemblyUnitComplete) {
      const assemblyUnitAccordions = await formPage.page
        .getByRole('button', { name: /Assembly Unit -/ })
        .all()
      for (const accordion of assemblyUnitAccordions) {
        await expect(accordion).not.toContainText(assemblyUnitComplete.id)
      }
    }
  })

  await test.step('Remove all assembly units from form section 1', async () => {
    await formPage.sectionOneTab.click()
    await removeAllAssemblyUnits({
      formPage,
      toastLocator: removalToastLocator,
      toastLocatorCloseButton,
    })
  })

  await test.step('Remove all assembly units from form section 2', async () => {
    await formPage.sectionTwoTab.click()
    await removeAllAssemblyUnits({
      formPage,
      toastLocator: removalToastLocator,
      toastLocatorCloseButton,
    })
  })

  await test.step('Open exam previewer and confirm that the no asset error message is shown', async () => {
    const previewPagePromise = formPage.page.context().waitForEvent('page')
    await formPage.previewFormButton.click()
    const newPage = await previewPagePromise
    const newFormsPage = new FormPage({ page: newPage })
    await expect(newFormsPage.previewNoAssetWarning).toBeVisible({ timeout: 30000 })
    await newFormsPage.page.close()
  })
})
