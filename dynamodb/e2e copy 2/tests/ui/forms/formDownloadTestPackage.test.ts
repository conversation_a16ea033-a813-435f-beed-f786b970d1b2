import fs from 'fs'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EForm } from '@e2e/lib'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStep, WorkflowStepID } from '@shared/types/model/workflow/types'

test('Test Package JSON successfully downloads', async ({ formPage, page, dataController }) => {
  let formId: string
  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      formProps: { name: 'Test Package E2E' },
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assemblyUnitLength: 2,
      assetLength: 1,
      assetProps: {
        workflowStateId: WorkflowStepID.SENT_TO_VAULT,
      },
      directionProps: {
        workflowStepId: WorkflowStep.SentToVault,
      },
    })) as E2EForm[]
    if (form) {
      formId = form.id
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Test Package JSON can be downloaded', async () => {
    const {
      moreActionsDropdown,
      downloadTestPackageDialog,
      moreActionsDownloadMenuItem,
      moreActionsDownloadTestPackageMenuItem,
    } = formPage
    await moreActionsDropdown.button.click()
    // Open the Download submenu
    await moreActionsDownloadMenuItem.hover()
    // Click the test package export
    await moreActionsDownloadTestPackageMenuItem.click()
    const downloadPath = await clickAndDownloadFile(
      page,
      downloadTestPackageDialog.buttons.download,
      'test-package.json'
    )

    const testPackageJson = JSON.parse(fs.readFileSync(downloadPath, 'utf8'))

    expect(testPackageJson).toBeDefined()
    expect(testPackageJson.id).toEqual(formId)
    expect(testPackageJson.test.instructions).not.toBe('')
    expect(testPackageJson.test.sections.length).toEqual(2)
  })
})

test('Test Package JSON should not fail due to missing Vault IDs', async ({
  formPage,
  page,
  dataController,
}) => {
  let formId: string
  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      formProps: { name: 'Test Package E2E Missing Vault IDs' },
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assemblyUnitLength: 1,
      assetLength: 1,
      assetProps: {
        excludeVaultId: true,
      },
    })) as E2EForm[]

    if (form) {
      formId = form.id
      await formPage.goToPageWith({ ...form })
    }

    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Test Package JSON should still be able to download even when asset is missing vault IDs', async () => {
    const {
      moreActionsDropdown,
      downloadTestPackageDialog,
      moreActionsDownloadMenuItem,
      moreActionsDownloadTestPackageMenuItem,
    } = formPage
    await moreActionsDropdown.button.click()

    // Open the Download submenu
    await moreActionsDownloadMenuItem.hover()
    // Click the test package export
    await moreActionsDownloadTestPackageMenuItem.click()

    const downloadPath = await clickAndDownloadFile(
      page,
      downloadTestPackageDialog.buttons.download,
      `${formId}-test-package.json`
    )

    const testPackageJson = JSON.parse(fs.readFileSync(downloadPath, 'utf8'))

    expect(testPackageJson).toBeDefined()
    expect(testPackageJson.id).toEqual(formId)
    expect(testPackageJson.test.instructions).not.toBe('')
  })
})
