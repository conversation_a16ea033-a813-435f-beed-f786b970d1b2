import fs from 'fs'
import { parse } from 'csv-parse/sync'
import { expect } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EForm } from '@e2e/lib'
import { FormPage } from '@e2e/pages'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataController, DataControllerModel } from '@e2e/util/dataUtils'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import type { Page } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import { FormAssemblyUnit } from '@shared/schema/split-models/forms'
export type DownloadType = 'metadata' | 'formScoring'
export interface DownloadConfig {
  type: DownloadType
  performDownload: (formPage: FormPage) => Promise<void>
  fileNameSuffix: string
}
export const downloadConfigs: DownloadConfig[] = [
  {
    type: 'metadata',
    performDownload: async (formPage: FormPage) => {
      await formPage.moreActionsDownloadMenuItem.hover()
      await formPage.moreActionsDownloadMetadataMenuItem.click()
    },
    fileNameSuffix: 'metadata',
  },
  {
    type: 'formScoring',
    performDownload: async (formPage: FormPage) => {
      await formPage.moreActionsDownloadMenuItem.hover()
      await formPage.moreActionsDownloadFormScoringCSV.click()
    },
    fileNameSuffix: 'metadata',
  },
]
export async function runFormExportTest(
  page: Page,
  formPage: FormPage,
  dataController: DataController,
  config: DownloadConfig
) {
  // Step 1: Go to page with specific subject and form

  const [form] = (await dataController.generate(DataControllerModel.FORM, {
    user: userE2E,
    courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    length: 1,
    formProps: { name: 'Form PDF Download E2E' },
    nextGenAssemblyUnitProps: { adminYear: '26' },
    assemblyUnitLength: 2,
    assetLength: 3,
  })) as E2EForm[]
  if (form) {
    // Step 2: Assert order of assets and export CSV

    // Initiate the response listener before navigation
    const responsePromise = waitForResponseFrom(page, endpoints.FORMS_READ_ONE, 200, 7500)

    await formPage.goToPageWith({ ...form })

    // Await the response after navigation
    const response = await responsePromise
    const body = await response.json()
    if (!Array.isArray(body) || body.length === 0) {
      throw new Error('Response body is empty or not an array')
    }

    const resultData = body[0].result.data
    const sections = resultData.sections
    if (!Array.isArray(sections)) {
      throw new Error('Sections data is undefined')
    }
    const sectionSequenceNumbering: string[][] = Array.from({ length: sections.length }, () => [])
    const itemIds = sections.flatMap((section, index) => {
      if (section.assemblyUnits.length) {
        return section.assemblyUnits.flatMap((assemblyUnitId: string) => {
          const assemblyUnit = resultData.assemblyUnits[assemblyUnitId] as FormAssemblyUnit
          if (!assemblyUnit) {
            throw new Error(`AssemblyUnit with ID ${assemblyUnitId} not found`)
          }
          const deactivatedItemIds = assemblyUnit.deactivatedItems.reduce<Record<string, boolean>>(
            (acc, item) => {
              acc[item] = true
              return acc
            },
            {}
          )
          const customItemOrder: Record<string, string[]> = assemblyUnit.customItemOrder
          const items = assemblyUnit.assetIds.flatMap((assetId) => customItemOrder[assetId] || [])
          const processedItems = items.flatMap((itemId) => {
            if (deactivatedItemIds[itemId]) {
              return []
            } else {
              const lastSeq = Number(
                sectionSequenceNumbering[index]?.[sectionSequenceNumbering[index]?.length - 1] || 0
              )
              sectionSequenceNumbering[index]!.push(String(lastSeq + 1))
              return itemId
            }
          })
          return processedItems
        })
      }
      return []
    })
    const expectedItemSequencing = sectionSequenceNumbering.flat()
    const { moreActionsDropdown, exportMetadataDialog } = formPage
    await moreActionsDropdown.button.click()
    await config.performDownload(formPage)
    await page.waitForTimeout(2000)
    const downloadFileName = `Form PDF Download E2E-${config.fileNameSuffix}`
    const downloadPath = await clickAndDownloadFile(
      page,
      exportMetadataDialog.buttons.export,
      downloadFileName
    )
    const records: Array<Record<string, unknown>> = parse(fs.readFileSync(downloadPath, 'utf8'), {
      columns: true,
      skip_empty_lines: true,
      skip_records_with_empty_values: true,
    })
    const csvItemOrder = records.flatMap((record) => (record['Item ID'] ? [record['Item ID']] : []))
    const csvSequenceNumberOrder = records
      .map(
        (record) =>
          Object.entries(record).find(([key, value]) => key.match(/Sequence Number/i) && value)?.[1]
      )
      .filter(Boolean)
    console.log('Original Item Order:', itemIds)
    console.log('CSV Item Order:', csvItemOrder)
    expect(csvItemOrder).toEqual(itemIds)
    console.log('Original item sequencing:', expectedItemSequencing)
    console.log('CSV item sequencing:', csvSequenceNumberOrder)
    expect(csvSequenceNumberOrder).toEqual(expectedItemSequencing)
    fs.unlinkSync(downloadPath)
  }
}
