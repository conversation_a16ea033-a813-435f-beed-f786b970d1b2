import { test } from '@e2e/cb-test'
import { downloadConfigs, runFormExportTest } from './utils'
test(`Export form scoring CSV respects order and hides deactivated items`, async ({
  formPage,
  page,
  dataController,
}) => {
  const scoringConfig = downloadConfigs.find((config) => config.type === 'formScoring')
  if (scoringConfig) {
    await runFormExportTest(page, formPage, dataController, scoringConfig)
  } else {
    test.fail('Form scoring config not found')
  }
})
