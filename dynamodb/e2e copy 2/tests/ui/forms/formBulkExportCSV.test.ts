import fs from 'fs'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EForm } from '@e2e/lib/E2EForm'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils/DataController'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'

test('Form Bulk Exports - Export of CSV', async ({
  formPage,
  page,
  formListPage,
  dataController,
}) => {
  const subFormCode = 'subFormCode'
  const adminCode = 'adminCode'
  const systemFormCode = 'systemFormCode'

  await test.step('Create form with sub form code, admin code, and system form code', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: {
        name: 'Form Bulk Export - Export CSV E2E',
        subFormCode,
        adminCode,
        systemFormCode,
      },
      assemblyUnitLength: 0,
      assetLength: 0,
    })) as E2EForm[]

    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Go to form landing page', async () => {
    await formListPage.goToPageWith({ subject: 'precalculus' })
    await waitForResponseFrom(formPage.page, endpoints.FORMS_READ)
  })

  await test.step('Verify download of form Export CSV', async () => {
    await formListPage.formBulkExportDropdown.click()

    const downloadPath = await clickAndDownloadFile(
      page,
      formListPage.exportFormCodesReportButton,
      `${SubjectKey.PRECALCULUS}-form-csv-export`
    )
    const csvData = fs.readFileSync(downloadPath, 'utf8')

    expect(csvData).toContain(subFormCode)
    expect(csvData).toContain(adminCode)
    expect(csvData).toContain(systemFormCode)
  })
})
