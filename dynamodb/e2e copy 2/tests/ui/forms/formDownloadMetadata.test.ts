import { test } from '@e2e/cb-test'
import { downloadConfigs, runFormExportTest } from './utils'
test(`Export metadata CSV respects order and hides deactivated items`, async ({
  formPage,
  page,
  dataController,
}) => {
  const metadataConfig = downloadConfigs.find((config) => config.type === 'metadata')
  if (metadataConfig) {
    await runFormExportTest(page, formPage, dataController, metadataConfig)
  } else {
    test.fail('Metadata config not found')
  }
})
