import fs from 'fs'
import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EForm } from '@e2e/lib/E2EForm'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils/DataController'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test.setTimeout(2 * 60 * 1000)

test('Form CSV Export of Comparison Report', async ({
  formPage,
  page,
  formListPage,
  dataController,
  assetEditorPage,
}) => {
  const useCode = 'Operational'

  await test.step('Create form with OPERATIONAL status', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: { name: 'Form Bulk Export Comparison Report E2E' },
      assemblyUnitLength: 2,
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assetLength: 1,
      workflowSteps: [WorkflowStepID.LEADERSHIP_REVIEW, WorkflowStepID.EXTERNAL_AUTHORING],
      assetProps: {
        workflowStateId: WorkflowStepID.INTERNAL_REVIEW,
      },
    })) as E2EForm[]

    test.fail(!form, 'Failed to create a form')

    await formPage.goToPageWith({ ...form! })
    await formPage.sectionOneTab.click()

    // Click on the first link in the table within the Assembly Unit region
    await page
      .getByRole('region', { name: /Assembly Unit -/ })
      .getByRole('table')
      .getByRole('row')
      .getByRole('link')
      .first()
      .click()

    // Update status to Operational and save
    await assetEditorPage.useCodeValueContainer.click()
    await page.getByLabel(useCode, { exact: true }).click()
    await assetEditorPage.toolbar.saveButton.click()
    await waitForResponseFrom(page, endpoints.ITEM_SET_UPDATE_ONE)
  })

  await test.step('Go to form landing page', async () => {
    await formListPage.goToPageWith({ subject: 'precalculus' })
    await waitForResponseFrom(formPage.page, endpoints.FORMS_READ)
  })

  await test.step('Verify download of form comparison report', async () => {
    await formListPage.formBulkExportDropdown.click()
    await page.waitForTimeout(2000)

    const downloadPath = await clickAndDownloadFile(
      page,
      formListPage.comparisonReportButton,
      `${SubjectKey.PRECALCULUS}_Form_Compare_Report`
    )
    const csvData = fs.readFileSync(downloadPath, 'utf8')

    expect(csvData).toContain(useCode)
  })
})
