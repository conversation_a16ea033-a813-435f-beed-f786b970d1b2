import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EForm } from '@e2e/lib'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'
import { WorkflowStepID } from '@shared/types/model/workflow/types'

test('Negative Test - Send form with assets not sent to vault', async ({
  formPage,
  dataController,
}) => {
  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: { name: 'E2E Form Vault Fail' },
      assemblyUnitLength: 0,
      assetLength: 0,
      sectionDirectionLength: 0,
      directionProps: { workflowStateId: WorkflowStepID.READY_FOR_USE },
      workflowSteps: [WorkflowStepID.READY_FOR_USE],
    })) as E2EForm[]
    if (form) {
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Attempt to send to the vault', async () => {
    const {
      moreActionsButton,
      moreActionsSendTestManifestMenuItem,
      sendTestManifestToVaultDialog,
      failedToSendTestManifestToVaultDialog,
    } = formPage

    await moreActionsButton.click()
    await moreActionsSendTestManifestMenuItem.click()
    await sendTestManifestToVaultDialog.buttons.send.click()
    await expect(failedToSendTestManifestToVaultDialog.root).toBeVisible()
    await expect(failedToSendTestManifestToVaultDialog.contentContainer).toContainText(
      'Unable to send/export due to assets not in Sent to Vault status'
    )
    // check that header chip updated
    await expect(formPage.mainLocator.getByText('Failed send to Vault')).toBeVisible()
  })
})

test('Negative Test - Send form with no form codes', async ({ formPage, dataController }) => {
  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: { name: 'E2E Form Vault Fail' },
      assemblyUnitLength: 0,
      assetLength: 0,
      examDirectionLength: 0,
      sectionDirectionLength: 0,
    })) as E2EForm[]
    if (form) {
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Attempt to send to the vault', async () => {
    const {
      moreActionsButton,
      moreActionsSendTestManifestMenuItem,
      sendTestManifestToVaultDialog,
      failedToSendTestManifestToVaultDialog,
    } = formPage

    await moreActionsButton.click()
    await moreActionsSendTestManifestMenuItem.click()
    await sendTestManifestToVaultDialog.buttons.send.click()
    await expect(failedToSendTestManifestToVaultDialog.root).toBeVisible()
    await expect(failedToSendTestManifestToVaultDialog.contentContainer).toContainText(
      'Cannot send Test Manifest to vault without Sub Form Code, Admin Code, and System Form Code.'
    )
    // check that header chip updated
    await expect(formPage.mainLocator.getByText('Failed send to Vault')).toBeVisible()
  })
})

test('Send Form to Vault', async ({ formPage, dataController }) => {
  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: {
        name: 'E2E Form Vault',
        subFormCode: 'subFormCode',
        adminCode: 'adminCode',
        systemFormCode: 'systemFormCode',
      },
      assemblyUnitLength: 1,
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assetLength: 1,
      assetProps: { workflowStateId: WorkflowStepID.SENT_TO_VAULT },
      directionProps: { workflowStateId: WorkflowStepID.SENT_TO_VAULT },
      workflowSteps: [WorkflowStepID.SENT_TO_VAULT],
    })) as E2EForm[]
    if (form) {
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Attempt to send to the vault', async () => {
    const {
      moreActionsButton,
      moreActionsSendTestManifestMenuItem,
      sendTestManifestToVaultDialog,
      successfullySentToVaultDialog,
    } = formPage

    await moreActionsButton.click()
    await moreActionsSendTestManifestMenuItem.click()
    await sendTestManifestToVaultDialog.buttons.send.click()
    await expect(successfullySentToVaultDialog.root).toBeVisible()
    await successfullySentToVaultDialog.root.getByRole('button', { name: 'OK' }).click()
    // check that header chip updated
    await expect(formPage.mainLocator.getByText('Pending send to Vault')).toBeVisible()
  })
})
