import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import type { E2EForm } from '@e2e/lib'
import { clickAndDownloadFile, verifyPdfContent } from '@e2e/util/clickAndDownloadFile'
import { DataControllerModel } from '@e2e/util/dataUtils'
import { SubjectIdentifier } from '@shared/config'

test('Test successful Form PDF downloads', async ({
  formPage,
  page,
  dataController,
  currentUser,
}) => {
  let formId: string

  await test.step('Go to page with specific subject and form', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
      length: 1,
      formProps: { name: 'Form PDF Download E2E' },
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assemblyUnitLength: 2,
      assetLength: 1,
    })) as E2EForm[]
    if (form) {
      formId = form.id
      await formPage.goToPageWith({ ...form })
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Download form PDF', async () => {
    const {
      moreActionsDropdown,
      pdfDownloadDialog,
      moreActionsDownloadMenuItem,
      moreActionsDownloadPDFMenuItem,
    } = formPage
    await moreActionsDropdown.button.click()
    // Open the Download submenu
    await moreActionsDownloadMenuItem.hover()
    // Click the test package export
    await moreActionsDownloadPDFMenuItem.click()
    const downloadPath = await clickAndDownloadFile(
      page,
      pdfDownloadDialog.buttons.download,
      `form-${formId}.pdf`
    )

    await expect(formPage.toastLocator).toHaveText(
      new RegExp(`Download of PDF for form ${formId} complete!`)
    )

    const generatedDate = new Date().toLocaleDateString()
    await verifyPdfContent(
      downloadPath,
      `Form ${formId}\nGenerated ${generatedDate}\nGenerated By: ${currentUser.email}`
    )
  })
})
