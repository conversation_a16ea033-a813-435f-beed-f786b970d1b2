import { expect, test } from '@e2e/cb-test'
import { clickAndDownloadFile } from '@e2e/util/clickAndDownloadFile'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

test('Form Bulk Exports - All Scoring Reports ZIP', async ({ formPage, page, formListPage }) => {
  const subject = 'precalculus'
  await test.step('Go to form landing page', async () => {
    await formListPage.goToPageWith({ subject })
    await waitForResponseFrom(formPage.page, endpoints.FORMS_READ)
  })

  await test.step('Verify download of All Scoring Reports ZIP', async () => {
    await formListPage.formBulkExportDropdown.click()

    const downloadPath = await clickAndDownloadFile(
      page,
      formListPage.exportAllScoringReportsButton,
      `${subject}-scoring-reports.zip`
    )

    expect(downloadPath).toContain('.zip')
  })
})
