import { expect, test } from '@e2e/cb-test'
import { userE2E } from '@e2e/global.setup'
import { E2EForm } from '@e2e/lib/E2EForm'
import { DataControllerModel } from '@e2e/util/dataUtils/DataController'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { SubjectIdentifier } from '@shared/config'
import { FormStep } from '@shared/types/model/workflow/forms/steps'

test('Form Stages', async ({ formPage, page, dataController }) => {
  await test.step('Create form with sub form code, admin code, and system form code', async () => {
    const [form] = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [SubjectIdentifier.PRECALCULUS],
      length: 1,
      formProps: {
        name: 'Form Stages - E2E',
        workflowSteps: { [FormStep.FORM_BUILDING]: true },
      },
      assemblyUnitLength: 0,
      assetLength: 0,
    })) as E2EForm[]
    if (form) {
      await formPage.goToPageWith({ ...form })
      await waitForResponseFrom(page, endpoints.FORMS_READ_ONE)
    }
    test.fail(!form, 'Failed to create a form')
  })

  await test.step('Select Multiple Form Stages From Dropdown', async () => {
    await formPage.formStageDropdownIcon.click()
    await formPage.getFormStageOptionCheckbox('Form Certified').click()
    await formPage.getFormStageOptionCheckbox('GPER Review').click()
    await formPage.formStageDropdownIcon.click()

    // Verify that the pills for the selected stages are visible
    await expect(formPage.getFormStagePill('GPER Review')).toBeVisible()
    await expect(formPage.getFormStagePill('Form Certified')).toBeVisible()

    // Save the form stage changes
    await formPage.formCodeSaveButton.click()

    await page.reload()
    await waitForResponseFrom(page, endpoints.FORMS_READ_ONE)

    // After reload, verify additional stage
    await expect(formPage.getFormStagePill('GPER Review')).toBeVisible()
    await expect(formPage.getFormStagePill('Form Certified')).toBeVisible()
  })
})
