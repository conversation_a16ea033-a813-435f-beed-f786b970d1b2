import { MSLoginPage, ProgramsPage, ZScalerLoginPage } from '@e2e/pages'

import type { BrowserContext } from '@playwright/test'
import { determineBaseUrlFromEnv } from '@e2e/util/envUtils'
import { getUsers } from '@e2e/util/userUtils'
import { performSharedAuthSetup } from '@e2e/setup/auth-setup/performSharedAuthSetup'
import { test } from '@e2e/cb-test'

type StorageStateType = Awaited<ReturnType<BrowserContext['storageState']>>

test(`A manual exploration`, async ({ page, contextSupplier }) => {
  // Since doing manual testing, want to make test not time out, we'll tell to finish
  test.setTimeout(0)

  // We'll use the finishPromise to await at the end to know when to finish
  // First function exposed to hub page, clicking button will finish
  const finishPromise = new Promise((resolve) => {
    page.exposeFunction('finishExploration', resolve)
  })

  // This will handle opening the desired environment with the given user,
  // logging in if needed
  const storageStateByUser: { [key: string]: StorageStateType } = {}
  await page.exposeFunction('openEnvWithUser', async ({ userInput = '01', envInput = 'uat' }) => {
    const lowerUserInput = userInput.toLocaleLowerCase()
    const user = getUsers().find((user) => {
      return (
        user.email.toLowerCase().includes(lowerUserInput) ||
        user.roles.some((role) => role.toLocaleLowerCase().includes(lowerUserInput))
      )
    })
    if (!user) {
      return 'Could not find user'
    }
    const existingStorageState = storageStateByUser[user.email]
    const originalENV = process.env.ENV
    process.env.ENV = envInput
    const newContext = await contextSupplier.supplyContext({
      user,
      options: {
        baseURL: determineBaseUrlFromEnv(envInput),
        storageState: existingStorageState,
      },
    })
    const page = await newContext.newPage()
    if (existingStorageState) {
      const homePage = new ProgramsPage({ page })
      await homePage.goToPageWith()
      return 'Successfully opened new page in new context already logged in'
    } else {
      await performSharedAuthSetup({
        msLoginPage: new MSLoginPage(page),
        zscalerLoginPage: new ZScalerLoginPage(page),
        homePage: new ProgramsPage({ page: page }),
        user,
      })
      process.env.ENV = originalENV
      storageStateByUser[user.email] = await newContext.storageState()
      return 'Successfully opened and logged in user in new context and page'
    }
  })

  page.on('dialog', () => {
    // Do nothing, this just prevents autodismissal in the tool's main page
    // so that we have to manually dismiss our dialog when displayed
  })

  // Create the hub page. Allows specifying user and env, opening a page,
  // and finally finishing when done, which will close all open contexts
  await page.setContent(`
    <script>
      async function envOpenHandler() {
        const resultSpan = document.querySelector('#result')
        resultSpan.textContent = " Processing..."
        resultSpan.style.color = 'black'
        const userInput = document.querySelector('#user-input').value
        const envInput = document.querySelector('#env-input').value
        let result = ''
        if (!userInput || !envInput) {
          result = 'Please provide a user and env value!'
        } else {
          if (!/local|pr[0-9]+/.test(envInput)) {
            alert("!!-Note-!!\\n\\nPlease be mindful of other people's data they may be working with")
          }
          try {
            result = await openEnvWithUser({ userInput, envInput })
          } catch (e) {
            result = e.message
          }
        }
        resultSpan.textContent = " Result: " + result
        // If someone color blind is going to start using, should change colors
        // Maybe see about using css classes instead as well
        resultSpan.style.color = result.toLowerCase().includes('success') ? 'green' : 'red'
      }
    </script>
    <h1>Manual Exploration and Testing</h1>
    <div>Use this tool to open any env (except prod and staging) with any test user!</div>
    <br>
    <div>
      <label for="user-input">User: </label>
      <input list="user-options" id="user-input" type="text" placeholder="role like admin, number like 01, or other user matcher, can be substring"></input>
      <datalist id="user-options">
        ${getUsers()
          .map(
            ({ email, roles }) =>
              `<option value="${email}">${roles.join(', ') || 'NO ROLE'}</option>`
          )
          .join('\n')}
      </datalist>
    </div>
    <div>
      <label for="env-input">Env: </label>
      <input id="env-input" type="text" placeholder="env to open like uat, must be exact!"></input>
    </div>
    <br>
    <div>
      <button onclick="envOpenHandler()">Open</button>
      <span id="result"></span>
    </div>
    <br>
    <div>
      <button onclick="finishExploration()">Finish</button>
      <span style="color: orange; weight: bold">Note: This will close everything opened by this!</span>
    </div>
  `)

  // Wait until Finish is clicked to indicate when the test should be over, and
  // everything should be cleaned up
  await finishPromise
})
