import { test } from '@e2e/cb-test'
import type { PermissionGroup } from '@shared/rbac'
import { getUsers } from '@e2e/util/userUtils'

type UserAllowedSetting = 'permitted' | 'visible' | 'own' | 'all' | 'active' | string | undefined
type UserAllowedSettings = UserAllowedSetting[]
type TestDefinitions = {
  [permissionWithinGroup: string]: (userAllowedSettings: UserAllowedSettings) => void
}

export function wrapTests(permissionGroup: PermissionGroup, testDefinitions: TestDefinitions) {
  getUsers().forEach((user) => {
    const userRoleString = user.roles.join(',')
    test.describe(`Permissions tests for user ${user.email} with roles ${userRoleString}`, () => {
      test.use({ currentUser: user })
      test.describe(`- ${permissionGroup} permission group`, () => {
        Object.entries(testDefinitions).forEach(([permission, callback]) => {
          test.describe(`- ${permission} permission`, () => {
            const userAllowedSettings = user.permissions
              .filter((perm) => perm.includes(`${permissionGroup}.${permission}`))
              .map((perm) => {
                const permParts = perm.split('.')
                if (!permParts[2]) {
                  throw new Error('Need to update to handle perm with only 2 parts!')
                }
                return permParts[2]
              })
            callback(userAllowedSettings)
          })
        })
      })
    })
  })
}
