import { expect, test } from '@e2e/cb-test'
import { wrapTests } from '../permissionsTestStructure'
import { endpoints } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'

wrapTests('assets', {
  create: (userAllowedSettings) => {
    test.beforeEach(async ({ newAssetPage }) => {
      await newAssetPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test(`attempt to create asset with perm settings: ${settingsString}`, async ({
      newAssetPage,
      assetEditorPage,
    }) => {
      if (userAllowedSettings.includes('permitted')) {
        await expect(newAssetPage.createButton).toBeVisible()
        await newAssetPage.createButton.click()

        await waitForResponseFrom(newAssetPage.page, endpoints.ASSET_CREATE_MANY)
        await expect(newAssetPage.page).toHaveURL(
          new RegExp(assetEditorPage.url({ id: '.*', subject: '.*' }))
        )
      } else {
        // Having to deal with multiple Error toasts
        await expect(newAssetPage.createButton).toBeHidden()
        await expect(
          newAssetPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
        // Updated tests since the new asset page route is now blocked to roles that do not have create new asset permission

        // Should potentially look at this later - I found admin even though creation
        // is success, error toasts still came up. So will this actually fail when expected?
      }
    })
  },
})
