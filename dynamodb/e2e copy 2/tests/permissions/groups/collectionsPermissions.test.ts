import { expect, test } from '@e2e/cb-test'
import { wrapTests } from '../permissionsTestStructure'

// NOTE: These tests are designed to verify permissions, not individual functionality.
// Each functionality is individually tested separately.
// The focus of the permissions test is on whether access is granted or denied.
wrapTests('collections', {
  view: (userAllowedSettings) => {
    test.beforeEach(async ({ collectionsPage }) => {
      await collectionsPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view collections: ${settingsString}`, async ({ collectionsPage }) => {
      if (userAllowedSettings.includes('collections.view.active')) {
        await expect(collectionsPage.newCollectionButton).toBeVisible()
      } else {
        await expect(
          collectionsPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  create: (userAllowedSettings) => {
    test.beforeEach(async ({ collectionsPage }) => {
      await collectionsPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to create a collection: ${settingsString}`, async ({ collectionsPage }) => {
      if (userAllowedSettings.includes('collections.create.permitted')) {
        await expect(collectionsPage.newCollectionButton).toBeVisible()
        await collectionsPage.newCollectionButton.click()
      } else {
        await expect(
          collectionsPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  viewComments: (userAllowedSettings) => {
    test.beforeEach(async ({ collectionPage }) => {
      await collectionPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view comments at collection level: ${settingsString}`, async ({
      collectionPage,
    }) => {
      if (userAllowedSettings.includes('collectionComment.view.own')) {
        await expect(collectionPage.showCommentsButton).toBeVisible()
      } else {
        await expect(collectionPage.showCommentsButton).toBeHidden()
      }
    })
  },
  canEditCollection: (userAllowedSettings) => {
    test.beforeEach(async ({ collectionPage }) => {
      await collectionPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to edit a collection (rename, add assets,transition, archive): ${settingsString}`, async ({
      collectionPage,
    }) => {
      if (
        userAllowedSettings.includes('collections.view.active') &&
        !userAllowedSettings.includes('collections.edit.active')
      ) {
        const { moreActionsDropdown } = collectionPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('aria-disabled', 'true')
        await expect(moreActionsDropdown.items.addAssets).toHaveAttribute('aria-disabled', 'true')
        await expect(moreActionsDropdown.items.transition).toHaveAttribute('aria-disabled', 'true')
        await expect(moreActionsDropdown.items.sendToVault).toHaveAttribute('aria-disabled', 'true')
        await expect(moreActionsDropdown.items.exportMetadata).toHaveAttribute(
          'aria-disabled',
          'true'
        )
        await expect(moreActionsDropdown.items.exportPdf).toHaveAttribute('aria-disabled', 'true')
        await expect(moreActionsDropdown.items.archive).toHaveAttribute('aria-disabled', 'true')
      } else if (userAllowedSettings.includes('collections.edit.active')) {
        const { moreActionsDropdown } = collectionPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.addAssets).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.transition).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.sendToVault).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.exportMetadata).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.exportPdf).toHaveAttribute('role', 'menuitem')
        await expect(moreActionsDropdown.items.archive).toHaveAttribute('role', 'menuitem')
      } else {
        // collection page will not be reachable
      }
    })
  },
})
