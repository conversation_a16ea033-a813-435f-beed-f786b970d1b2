import { expect, test } from '@e2e/cb-test'
import { wrapTests } from '../permissionsTestStructure'

// NOTE: These tests are designed to verify permissions, not individual functionality.
// Each functionality is individually tested separately.
// The focus of the permissions test is on whether access is granted or denied.
wrapTests('forms', {
  view: (userAllowedSettings) => {
    test.beforeEach(async ({ formListPage }) => {
      await formListPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view forms: ${settingsString}`, async ({ formListPage }) => {
      if (userAllowedSettings.includes('forms.view.active')) {
        await expect(formListPage.newFormButton).toBeVisible()
      } else {
        await expect(
          formListPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  create: (userAllowedSettings) => {
    test.beforeEach(async ({ formListPage }) => {
      await formListPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to create an form: ${settingsString}`, async ({ formListPage }) => {
      if (userAllowedSettings.includes('forms.create.permitted')) {
        await expect(formListPage.newFormButton).toBeVisible()
      } else {
        await expect(
          formListPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  viewComments: (userAllowedSettings) => {
    test.beforeEach(async ({ formPage }) => {
      await formPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view comments at forms level: ${settingsString}`, async ({
      formPage,
    }) => {
      if (userAllowedSettings.includes('formComment.view.own')) {
        await expect(formPage.showCommentsButton).toBeVisible()
      } else {
        await expect(formPage.showCommentsButton).toBeHidden()
      }
    })
  },
  canEditFormUnit: (userAllowedSettings) => {
    test.beforeEach(async ({ formPage }) => {
      await formPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to rename an form: ${settingsString}`, async ({ formPage }) => {
      if (
        userAllowedSettings.includes('forms.rename.visible') &&
        !userAllowedSettings.includes('forms.edit.active')
      ) {
        const { moreActionsDropdown } = formPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('aria-disabled', 'true')
      } else if (userAllowedSettings.includes('forms.edit.active')) {
        const { moreActionsDropdown } = formPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('role', 'menuitem')
      } else {
        // form page will not be reachable
      }
    })
  },
})
