import { expect, test } from '@e2e/cb-test'
import { wrapTests } from '../permissionsTestStructure'

// NOTE: These tests are designed to verify permissions, not individual functionality.
// Each functionality is individually tested separately.
// The focus of the permissions test is on whether access is granted or denied.
wrapTests('assemblyUnits', {
  view: (userAllowedSettings) => {
    test.beforeEach(async ({ assemblyUnitListPage }) => {
      await assemblyUnitListPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view assembly units: ${settingsString}`, async ({
      assemblyUnitListPage,
    }) => {
      if (userAllowedSettings.includes('assemblyUnits.view.active')) {
        await expect(assemblyUnitListPage.newAssemblyUnitButton).toBeVisible()
      } else {
        await expect(
          assemblyUnitListPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  create: (userAllowedSettings) => {
    test.beforeEach(async ({ assemblyUnitListPage }) => {
      await assemblyUnitListPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to create an assembly unit: ${settingsString}`, async ({
      assemblyUnitListPage,
    }) => {
      if (userAllowedSettings.includes('assemblyUnits.create.permitted')) {
        await expect(assemblyUnitListPage.newAssemblyUnitButton).toBeVisible()
      } else {
        await expect(
          assemblyUnitListPage.page.getByText('You are not authorized to view this page')
        ).toBeVisible()
      }
    })
  },
  viewComments: (userAllowedSettings) => {
    test.beforeEach(async ({ assemblyUnitPage }) => {
      await assemblyUnitPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to view comments at assembly units level: ${settingsString}`, async ({
      assemblyUnitPage,
    }) => {
      if (userAllowedSettings.includes('assemblyUnitComment.view.own')) {
        await expect(assemblyUnitPage.showCommentsButton).toBeVisible()
      } else {
        await expect(assemblyUnitPage.showCommentsButton).toBeHidden()
      }
    })
  },
  canEditAssemblyUnit: (userAllowedSettings) => {
    test.beforeEach(async ({ assemblyUnitPage }) => {
      await assemblyUnitPage.goToPageWith()
    })

    const settingsString = userAllowedSettings.join(',')
    test.skip(`attempt to rename an assembly unit: ${settingsString}`, async ({
      assemblyUnitPage,
    }) => {
      if (
        userAllowedSettings.includes('assemblyUnits.rename.visible') &&
        !userAllowedSettings.includes('assemblyUnits.edit.active')
      ) {
        const { moreActionsDropdown } = assemblyUnitPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('aria-disabled', 'true')
      } else if (userAllowedSettings.includes('assemblyUnits.edit.active')) {
        const { moreActionsDropdown } = assemblyUnitPage
        await moreActionsDropdown.button.click()
        await expect(moreActionsDropdown.items.rename).toHaveAttribute('role', 'menuitem')
      } else {
        // assembly unit page will not be reachable
      }
    })
  },
})
