import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AdminUsersAddUserDialog extends BaseComponent {
  readonly inputs: {
    username: Locator
    firstname: Locator
    lastname: Locator
    emailAddress: Locator
    allowedCourses: {
      EnglishLanguageAndComposition: Locator
      EnglishLiteratureAndComposition: Locator
      Psychology: Locator
      AfricanAmericanStudies: Locator
      Precalculus: Locator
      ESL: Locator
    }
    isActive: Locator
  }
  readonly buttons: {
    cancel: Locator
    apply: Locator
  }

  constructor({ scope, root = scope.locator('.ui-dialog') }: TypicalComponentConstructorParams) {
    super({ root })

    this.inputs = {
      username: this.root.getByRole('textbox', { name: 'Username' }),
      firstname: this.root.getByRole('textbox', { name: 'First Name' }),
      lastname: this.root.getByRole('textbox', { name: 'Last Name' }),
      emailAddress: this.root.getByRole('textbox', { name: 'Email Address' }),
      allowedCourses: {
        EnglishLanguageAndComposition: this.root.getByRole('checkbox', {
          name: 'English Language and Composition',
        }),
        EnglishLiteratureAndComposition: this.root.getByRole('checkbox', {
          name: 'English Literature and Composition',
        }),
        Psychology: this.root.getByRole('checkbox', { name: 'Psychology' }),
        AfricanAmericanStudies: this.root.getByRole('checkbox', {
          name: 'African American Studies',
        }),
        Precalculus: this.root.getByRole('checkbox', { name: 'Precalculus' }),
        ESL: this.root.getByRole('checkbox', { name: 'ESL' }),
      },
      isActive: this.root.getByRole('switch', { name: 'Active' }),
    }
    this.buttons = {
      cancel: this.root.getByRole('button', { name: 'Cancel' }),
      apply: this.root.getByRole('button', { name: 'Apply' }),
    }
  }
}
