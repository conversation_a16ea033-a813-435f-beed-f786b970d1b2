import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AdminUsersFilters extends BaseComponent {
  readonly inputs: {
    name: Locator
    userType: Locator
    active: Locator
  }
  readonly buttons: {
    clear: Locator
    search: Locator
  }

  constructor({
    scope,
    root = scope.locator('#user-table-sidebar'),
  }: TypicalComponentConstructorParams) {
    super({ root })

    this.inputs = {
      name: this.root.getByRole('textbox', { name: 'Name' }),
      userType: this.root.getByRole('combobox', { name: 'User type' }),
      active: this.root.getByRole('combobox', { name: 'Is active' }),
    }
    this.buttons = {
      clear: this.root.getByRole('button', { name: 'Clear' }),
      search: this.root.getByRole('button', { name: 'Search' }),
    }
  }
}
