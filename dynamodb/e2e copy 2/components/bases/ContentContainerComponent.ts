import type { Locator } from '@playwright/test'
import {
  BaseComponent,
  BaseComponentConstructorParams,
  TypicalComponentConstructorParams,
} from './BaseComponent'

/**
 * Callback to set up the content which is a returned object that will
 * be assigned to the `content` property. Can utilize the containingLocator
 * locator for proper chaining anything inside.
 */
type ContentSetupHandler<ContentType extends object> = (
  contentContainer: Locator,
  createComponent: BaseComponent['createComponent']
) => ContentType

type TypicalContentSpecificParams<ContentType extends object> = {
  /**
   * Callback to set up the content which is a returned object that will
   * be assigned to the `content` property. Can utilize the containingLocator
   * locator for proper chaining anything inside.
   */
  setupContent: ContentSetupHandler<ContentType>
}

type ContentSpecificParams<ContentType extends object> =
  TypicalContentSpecificParams<ContentType> & {
    /**
     * Parameter for specifying the contentContainer Locator for chaining locators
     * or other methods within. Subclasses should specify this param themselves
     */
    contentContainer: Locator
  }

type ContentContainerComponentConstructorParams<ContentType extends object> =
  BaseComponentConstructorParams & ContentSpecificParams<ContentType>

export type TypicalContentContainerComponentConstructorParams<ContentType extends object> =
  TypicalComponentConstructorParams & TypicalContentSpecificParams<ContentType>

export class ContentContainerComponent<ContentType extends object> extends BaseComponent {
  /** Object containing the content within this component set up during construction */
  content: ContentType

  /** The Locator for the container of the content within this component */
  contentContainer: Locator

  constructor({
    setupContent,
    contentContainer,
    ...args
  }: ContentContainerComponentConstructorParams<ContentType>) {
    super({ ...args })

    this.contentContainer = contentContainer

    this.content = setupContent(
      contentContainer,
      this.createComponent.bind({
        ...this,
        root: contentContainer,
      })
    )
  }
}
