import type { Locator, Page } from '@playwright/test'
import type { Class, SetOptional } from 'type-fest'
import type { DestructuredParams } from '@e2e/types'

export type BaseComponentConstructorParams = {
  /**
   * The 'root' Locator for the component, which should be the outermost one
   * this component is representing
   */
  root: Locator
}

export type TypicalComponentConstructorParams = SetOptional<
  BaseComponentConstructorParams,
  'root'
> & {
  /**
   * Basically the container of this component, the "scope" to locate the
   * `root` within. Current expectation is that it's just needed in the
   * constructor, for this purpose.
   */
  scope: Page | Locator
}

export type TypicalNamedComponentConstructorParams = TypicalComponentConstructorParams & {
  /** Accessible name (heading/label) of this component's root */
  name: string
}

export type StandaloneComponentConstructorParams = {
  page: Page
}

// Does component make sense? I imagine like Locator, it might be allowed to
// reference a singular or multiple depending on the scenario
// Also, are these only expected to be used as containing type of components,
// and any 'leaf' or bottom level locators would just remain Locators, in
// which case perhaps even something like Container makes more sense?
export class BaseComponent {
  /**
   * The 'root' Locator for the component, which should be the outermost one
   * this component is representing
   */
  readonly root: Locator
  readonly page: Page

  constructor({ root }: BaseComponentConstructorParams) {
    this.root = root
    this.page = root.page()
  }

  /**
   * Create a component within the `scope` of this component's root, optionally
   * specifying the new component's class
   *
   * @param ComponentClass - Class of the component to create, `BaseComponent` if not specified
   * @param options - Any options to pass into the constructor; note that `scope` is automatically set to this component's root
   */
  createComponent<C extends Class<BaseComponent>>(
    ComponentClass: C,
    options?: SetOptional<DestructuredParams<C>, 'scope'>
  ): InstanceType<C>
  createComponent(options: BaseComponentConstructorParams): BaseComponent
  createComponent<C extends Class<BaseComponent>>(
    ComponentClassOrOptions: C | BaseComponentConstructorParams,
    options?: SetOptional<DestructuredParams<C>, 'scope'>
  ) {
    if (typeof ComponentClassOrOptions === 'function') {
      if (
        !(
          ComponentClassOrOptions.prototype instanceof BaseComponent ||
          ComponentClassOrOptions === BaseComponent
        )
      ) {
        throw new Error('createComponent can only create instances of BaseComponent')
      }
      return new ComponentClassOrOptions({ scope: this.root, ...options })
    }
    return new BaseComponent({ ...ComponentClassOrOptions })
  }
}
