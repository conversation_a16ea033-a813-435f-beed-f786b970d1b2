import type { Locator } from '@playwright/test'
import { BaseComponent, StandaloneComponentConstructorParams } from '../bases/BaseComponent'

export class BaseDialog extends BaseComponent {
  readonly cancelButton: Locator
  readonly proceedButton: Locator
  readonly closeButton: Locator

  constructor(
    { page }: StandaloneComponentConstructorParams,
    cancelButtonLabel: string,
    proceedButtonLabel: string,
    name?: string
  ) {
    super({ root: page.getByRole('dialog', { name: name }) })

    this.cancelButton = this.root.getByRole('button', { name: cancelButtonLabel })
    this.closeButton = this.root.getByRole('button', { name: 'Close Modal' })
    this.proceedButton = this.root.getByRole('button', { name: proceedButtonLabel })
  }
}
