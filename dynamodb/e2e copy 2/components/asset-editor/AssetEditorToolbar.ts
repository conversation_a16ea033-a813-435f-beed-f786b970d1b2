import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'
import { Dropdown } from '../common'

export class AssetEditorToolbar extends BaseComponent {
  saveButton: Locator

  undoButton: Locator
  redoButton: Locator

  cutButton: Locator
  copyButton: Locator
  pasteButton: Locator
  boldButton: Locator
  italicButton: Locator
  underlineButton: Locator
  insertTableButton: Locator
  insertSpecialCharacterButton: Locator
  insertSpecialCharacterMenu: Locator
  specialCharacterDropdownCategories
  insertCodeBlockDropdownArrow: Locator
  insertCodeBlockDropdownMenu: Locator
  numberedListButton: Locator
  decimalListButton: Locator
  decimalLeadingZeroListButton: Locator
  lowerRomanListButton: Locator
  upperRomanListButton: Locator
  lowerLatinListButton: Locator
  upperLatinListButton: Locator
  bulletedListButton: Locator
  listPropertiesButton: Locator
  reversedButton: Locator
  superscriptButton: Locator
  subscriptButton: Locator
  alignLeftButton: Locator
  alignCenterButton: Locator
  alignRightButton: Locator

  constructor({
    scope,
    root = scope.getByRole('toolbar', { name: 'Editor toolbar' }),
  }: TypicalComponentConstructorParams) {
    super({ root })

    // Would love to get this inside the actual toolbar at some point instead
    this.saveButton = scope.locator('.toolbar-container').getByRole('button', { name: 'Save' })

    this.undoButton = this.root.getByRole('button', { name: 'Undo' })
    this.redoButton = this.root.getByRole('button', { name: 'Redo' })

    this.cutButton = this.root.getByRole('button', { name: 'Cut' })
    this.copyButton = this.root.getByRole('button', { name: 'Copy' })
    this.pasteButton = this.root.getByRole('button', { name: 'Paste' })
    this.boldButton = this.root.getByRole('button', { name: 'Bold' })
    this.italicButton = this.root.getByRole('button', { name: 'Italic' })
    this.underlineButton = this.root.getByRole('button', { name: 'Underline' })
    this.superscriptButton = this.root.getByRole('button', { name: 'Superscript' })
    this.subscriptButton = this.root.getByRole('button', { name: 'Subscript' })

    this.alignLeftButton = this.root.getByRole('button', { name: 'Align Left' })
    this.alignCenterButton = this.root.getByRole('button', { name: 'Align Center' })
    this.alignRightButton = this.root.getByRole('button', { name: 'Align Right' })
    this.insertTableButton = this.root.getByRole('button', { name: 'Insert table' })
    this.numberedListButton = this.root.getByRole('button', { name: 'Numbered List' }).nth(1)
    this.decimalListButton = this.root.getByRole('button', {
      name: 'Toggle the decimal list style',
    })
    this.decimalLeadingZeroListButton = this.root.getByRole('button', {
      name: /Toggle the decimal with/,
    })
    this.lowerRomanListButton = this.root.getByRole('button', {
      name: /Toggle the lower–roman list/,
    })
    this.upperRomanListButton = this.root.getByRole('button', {
      name: /Toggle the upper–roman list/,
    })
    this.lowerLatinListButton = this.root.getByRole('button', {
      name: /Toggle the lower–latin list/,
    })
    this.upperLatinListButton = this.root.getByRole('button', {
      name: /Toggle the upper–latin list/,
    })
    this.bulletedListButton = this.root
      .getByRole('button', {
        name: 'Bulleted List',
      })
      .first()
    this.listPropertiesButton = this.root.getByRole('button', { name: 'List properties' })
    this.reversedButton = this.root.getByRole('button', { name: 'Reversed order' })
    this.insertSpecialCharacterButton = this.root.getByRole('button', {
      name: 'Special characters',
    })
    this.insertSpecialCharacterMenu = this.root.getByRole('generic', {
      name: 'Special characters Character categories',
    })

    this.specialCharacterDropdownCategories = new Dropdown({
      name: 'Character categories',
      scope: this.root,
      itemNames: {
        common: 'Common',
        greek: 'Greek',
        latin: 'Latin',
        text: 'Text',
        currency: 'Currency',
      },
      itemRole: 'menuitemradio',
    })

    this.insertCodeBlockDropdownArrow = this.root
      .getByRole('button', { name: 'Insert code block' })
      .nth(1)
    this.insertCodeBlockDropdownMenu = this.root.getByRole('menu', { name: 'Insert code block' })
  }
  async selectInsertCodeBlockDropdownCodeType(codeType: string): Promise<void> {
    this.insertCodeBlockDropdownMenu.getByRole('menuitemradio', { name: codeType }).click()
  }
  async chooseTableSize(num1: number, num2: number): Promise<void> {
    const buttonName = `${num1} × ${num2}`
    await this.page.getByRole('button', { name: buttonName }).click()
  }
}
