import { dragElement, ensureWithinViewport } from '@e2e/util/locatorUtils'
import { Locator, expect } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'
import { Dialog } from '../common'
import { AssetEditorOption } from './AssetEditorOption'

export class AssetEditorOptionsList extends BaseComponent {
  readonly showRationalesButton: Locator
  readonly hideRationalesButton: Locator
  readonly showOrHideRationalesButton: Locator
  readonly addAnOption: Locator
  readonly deletionModal

  readonly options

  constructor({
    scope,
    root = scope.getByRole('group', { name: 'Options' }),
  }: TypicalComponentConstructorParams) {
    super({ root })

    this.showRationalesButton = scope.getByRole('button', { name: 'Show Rationales' })
    this.hideRationalesButton = scope.getByRole('button', { name: 'Hide Rationales' })

    this.showOrHideRationalesButton = scope.getByRole('button', { name: /(Show|Hide) Rationales/ })

    this.options = {
      A: this.createComponent(AssetEditorOption, { letter: 'A' }),
      B: this.createComponent(AssetEditorOption, { letter: 'B' }),
      C: this.createComponent(AssetEditorOption, { letter: 'C' }),
      D: this.createComponent(AssetEditorOption, { letter: 'D' }),
      E: this.createComponent(AssetEditorOption, { letter: 'E' }),
      0: this.createComponent(AssetEditorOption, { index: 0 }),
      1: this.createComponent(AssetEditorOption, { index: 1 }),
      2: this.createComponent(AssetEditorOption, { index: 2 }),
      3: this.createComponent(AssetEditorOption, { index: 3 }),
      4: this.createComponent(AssetEditorOption, { index: 4 }),
    }

    this.addAnOption = scope.getByRole('button', { name: 'Add an option' })

    this.deletionModal = new Dialog({
      name: 'Are you sure?',
      scope: this.root,
      buttonNames: {
        delete: 'Delete',
        cancel: 'Cancel',
      },
      setupContent: () => ({}),
    })
  }

  async reorderOption({
    moveThis,
    toThis,
  }: {
    moveThis: keyof AssetEditorOptionsList['options'] | AssetEditorOption
    toThis: keyof AssetEditorOptionsList['options'] | AssetEditorOption
  }) {
    const movingOption = moveThis instanceof AssetEditorOption ? moveThis : this.options[moveThis]
    const targetOption = toThis instanceof AssetEditorOption ? toThis : this.options[toThis]

    // By ensuring the list is fully visible, we can avoid issues
    // from autoscrolling while trying to drag and drop
    // This includes ensure it can fit within the viewport, as well as an
    // unideal solution to just include a cushion for the rest of the page
    // outside of the scrollable area, including the header and empty space
    await ensureWithinViewport(this.root, { extraCushionHeight: 600 })

    // Drag and drop movingOption to targetOption
    // await movingOption.grabbableButton.dragTo(targetOption.grabbableButton)
    await dragElement({
      locator: movingOption.grabbableButton,
      destination: targetOption.grabbableButton,
    })
  }

  async ensureRationaleVisibilityState(expectedState: 'show' | 'hide') {
    // While generally assertions should not be in a page component object, this is
    // being used to ensure one of the buttons is visible before checking its state
    await expect(this.showOrHideRationalesButton).toBeVisible()
    if (await this[`${expectedState}RationalesButton`].isVisible()) {
      // If the rationale visibility is in the opposite state (based
      // on which is visible), click the button to change the state
      await this[`${expectedState}RationalesButton`].click()
    }
  }
}
