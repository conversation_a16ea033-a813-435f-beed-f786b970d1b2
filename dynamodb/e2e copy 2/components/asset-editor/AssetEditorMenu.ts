import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AssetEditorMenu extends BaseComponent {
  // Switch role to `menubar` (and `menu` below`) if we ever are able to correct roles
  constructor({ scope, root = scope.getByRole('list') }: TypicalComponentConstructorParams) {
    super({ root })
  }

  async clickThroughMenu(...optionsToClick: (string | RegExp)[]) {
    await optionsToClick.reduce(async (containingListLocatorPromise, option) => {
      const containingListLocator = await containingListLocatorPromise
      await containingListLocator
        .getByRole('listitem')
        // Unfortunately, playwright doesn't recognize/care about disabled status
        // on a non-interactive element like an li, so had to use straight css
        .and(containingListLocator.locator(':not([aria-disabled="true"])'))
        .filter({ hasText: option })
        .click()
      return containingListLocator.getByRole('list', { name: option })
    }, Promise.resolve(this.root))
  }
}
