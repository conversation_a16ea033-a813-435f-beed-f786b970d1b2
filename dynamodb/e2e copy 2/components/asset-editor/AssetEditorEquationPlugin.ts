import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AssetEditorEquationPlugin extends BaseComponent {
  readonly squareRootButton: Locator
  readonly mathInput: Locator
  readonly superscriptButton: Locator
  readonly insertButton: Locator

  constructor({ scope }: TypicalComponentConstructorParams) {
    //playwright was unable to recognize the accessible name for this dialog locator of MathType
    super({ root: scope.getByRole('dialog') })

    this.squareRootButton = this.root.getByRole('button', { name: 'Square root' })
    this.mathInput = this.root.getByRole('textbox', { name: 'Math input' })
    this.superscriptButton = this.root.getByRole('button', { name: 'Superscript (Cmd+Up)' })
    this.insertButton = this.root.getByRole('button', { name: 'Insert', exact: true })
  }

  async insertSquareRootValue(value: string): Promise<void> {
    await this.squareRootButton.click()
    await this.mathInput.fill(value)
  }
}
