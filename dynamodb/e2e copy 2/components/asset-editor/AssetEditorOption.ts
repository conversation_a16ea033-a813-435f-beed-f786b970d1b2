import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'
import { TextEditor } from '../common'

export class AssetEditorOption extends BaseComponent {
  readonly radioButton: Locator
  readonly checkboxButton: Locator
  readonly answerEditor: TextEditor
  readonly rationaleEditor: TextEditor
  readonly grabbableButton: Locator
  readonly deleteButton: Locator

  constructor({
    letter,
    index,
    name = letter,
    scope,
    root,
  }: TypicalComponentConstructorParams & { name?: string; letter?: string; index?: number }) {
    const baseRoot = root ?? scope.getByRole('group', { name })
    super({ root: index !== undefined ? baseRoot.nth(index) : baseRoot })

    // Making these all generic (not using name or letter) because they seem
    // to still be unique within the scope of the option group
    this.radioButton = this.root.getByRole('radio')
    this.checkboxButton = this.root.getByRole('checkbox')
    this.answerEditor = this.createComponent(TextEditor, {
      name: `Answer `,
    })
    this.rationaleEditor = this.createComponent(TextEditor, {
      name: `Rationale `,
    })
    // TODO: Add Accnum editor

    this.grabbableButton = this.root.getByRole('button', { name: 'Grab' })
    this.deleteButton = this.root.getByRole('button', { name: 'Delete' })
  }
}
