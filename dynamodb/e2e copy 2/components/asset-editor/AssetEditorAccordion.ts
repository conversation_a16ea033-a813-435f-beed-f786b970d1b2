import type { DestructuredParams } from '@e2e/types'
import { extendObject } from '@e2e/util/miscUtils'
import type { Locator } from '@playwright/test'
import { Accordion, Tab } from '../common'
import { Attachments } from '../common/Attachments'
import { Comments } from '../common/Comments'

// Moving up here to make working with types easier
const setupTabs = (contentRegion: Locator) => ({
  metadataTab: new Tab({
    scope: contentRegion,
    name: 'Metadata',
    setupContent: (tabPanel) => ({
      courseFrameworkAlignment: tabPanel.getByRole('textbox', {
        name: 'Course Framework Alignment',
      }),
      courseContent: tabPanel.getByRole('combobox', { name: 'Course Content' }),
      courseSkills: tabPanel.getByRole('combobox', { name: 'Course Skills' }),

      // Temp implementation to be replaced by ComboBox component
      layout: extendObject(tabPanel.getByRole('combobox', { name: 'Layout' }), (layout) => ({
        async selectOption(option: string) {
          let listboxId = await layout.getAttribute('aria-controls')
          if (!listboxId) {
            await layout.click()
            listboxId = await layout.getAttribute('aria-controls')
          }
          const listbox = layout.page().locator(`#${listboxId}`)
          // exact is needed since some are substrings of others
          await listbox.getByRole('option', { name: option, exact: true }).click()
        },

        async isOpen() {
          const expandedValue = await layout.getAttribute('aria-expanded')
          return expandedValue === 'true'
        },

        async ensureIsOpen() {
          if (!this.isOpen()) {
            layout.click()
          }
        },

        async getLayoutValue() {
          return tabPanel.getByRole('group', { name: 'Layout Value Container' }).textContent()
        },
      })),
    }),
  }),
  commentsTab: new Tab({
    scope: contentRegion,
    name: 'Comments',
    setupContent: (tabPanel) => ({
      commentContent: new Comments({
        scope: tabPanel,
        setupContent: () => ({}),
      }),
    }),
  }),
  attachmentsTab: new Tab({
    scope: contentRegion,
    name: 'Attachments',
    setupContent: (tabPanel) => ({
      attachmentsContent: new Attachments({
        scope: tabPanel,
        setupContent: () => ({}),
      }),
    }),
  }),
})

export class AssetEditorAccordion<BaseContentType extends object> extends Accordion<
  BaseContentType & ReturnType<typeof setupTabs>
> {
  dropdownButton

  constructor({ setupContent, ...args }: DestructuredParams<typeof Accordion<BaseContentType>>) {
    super({
      ...args,
      setupContent: function (contentRegion, createComponent) {
        return {
          ...setupTabs(contentRegion),
          ...setupContent(contentRegion, createComponent),
        }
      },
    })

    // TODO - Give dropdown an accessible name, just moved this from another spot
    this.dropdownButton = this.root.locator('#panel-dropdown')
  }
}
