import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AssetEditorLanguageAttribute extends BaseComponent {
  saveButton: Locator
  editButton: Locator
  deleteButton: Locator
  languageSelectionDropdown: Locator
  languageToolbarButton: Locator
  langSpan: Locator

  constructor({ scope }: TypicalComponentConstructorParams) {
    super({
      root: scope.locator('.ck-lang-form'),
    })

    this.saveButton = this.root.getByRole('button', {
      name: 'Apply',
      includeHidden: false,
    })
    this.deleteButton = this.root.getByRole('button', {
      name: 'Remove',
      includeHidden: false,
    })
    this.langSpan = this.page.locator('.langSpan')

    this.languageSelectionDropdown = this.root.locator(
      'input.ck-input[placeholder="Type to search..."]'
    )
    this.languageToolbarButton = this.page.getByRole('button', {
      name: 'Language',
    })
  }
}
