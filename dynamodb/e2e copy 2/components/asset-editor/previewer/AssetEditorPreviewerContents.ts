import { Tab } from '@e2e/components/common'
import { extendObject } from '@e2e/util/miscUtils'
import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../../bases/BaseComponent'

export class AssetEditorPreviewerContents extends BaseComponent {
  readonly prevButton: Locator
  readonly nextButton: Locator
  readonly backButton: Locator
  readonly formPreviewPrev: Locator
  readonly formPreviewNext: Locator
  readonly formPreviewBack: Locator

  readonly currentItemId: Locator

  readonly showInfoButton: Locator
  readonly hideInfoButton: Locator

  readonly stimulusContainer: Locator
  readonly questionContainer: Locator
  readonly discreteQuestionContainer: Locator
  readonly answerOptionsContainer: Locator
  readonly infoPanel

  readonly assetDropdown: Locator
  readonly assetTitle: Locator
  readonly selectOptionDropdown: Locator

  // Expect the root/scope to be either the dialog or the page (main locator)
  // that contains these contents
  constructor({
    scope,
    root = scope,
  }: Omit<TypicalComponentConstructorParams, 'scope'> & { scope: Locator }) {
    super({ root })

    const navigationGroup = this.root.getByRole('group', { name: 'Navigation' })
    this.prevButton = navigationGroup.getByRole('button', { name: 'Prev' })
    this.backButton = navigationGroup.getByRole('button', { name: 'Back' })
    this.nextButton = navigationGroup.getByRole('button', { name: 'Next' })
    this.currentItemId = navigationGroup.getByLabel('Active Preview Item ID')
    this.formPreviewPrev = this.root.getByRole('button', { name: 'Prev' })
    this.formPreviewNext = this.root.getByRole('button', { name: 'Next' })
    this.formPreviewBack = this.root.getByRole('button', { name: 'Back' })

    const additionalOptionsGroup = this.root.getByRole('group', { name: 'Additional Options' })
    this.showInfoButton = additionalOptionsGroup.getByRole('button', { name: 'Show Info' })
    this.hideInfoButton = additionalOptionsGroup.getByRole('button', { name: 'Hide Info' })

    this.assetDropdown = navigationGroup.locator('svg')
    this.assetTitle = this.root.locator('.previewer-header-asset')
    this.selectOptionDropdown = this.root.locator('div[id^="react-select-2-option-"]')

    this.stimulusContainer = this.root.getByRole('region', {
      name: `Passage, image, or other source content`,
      exact: true,
    })
    this.questionContainer = this.root.getByRole('region', {
      name: `Question and Answer`,
      exact: true,
    })
    this.discreteQuestionContainer = this.root.getByRole('region', {
      name: /Question \d+/,
      exact: true,
    })
    this.answerOptionsContainer = this.root.getByRole('region', {
      name: /^Answer Options/,
      exact: true,
    })

    this.infoPanel = extendObject(this.root.locator('#info-panel'), (infoPanel) => ({
      dropdown: infoPanel.getByRole('combobox', { name: 'info panel dropdown' }),
      keyRationalesTab: new Tab({
        scope: infoPanel,
        name: 'Key(s)/Rationale',
        setupContent: (tabPanel) => ({
          keyTextSection: tabPanel.getByRole('region', { name: 'Answer Key' }),
          rationaleTextSection: tabPanel.getByRole('region', { name: 'Rationale Information' }),
        }),
      }),
      metadataTab: new Tab({
        scope: infoPanel,
        name: 'Metadata',
        setupContent: (tabPanel) => ({
          metadataTextSection: tabPanel.getByRole('region', { name: 'Metadata Information' }),
        }),
      }),
      commentsTab: new Tab({
        scope: infoPanel,
        name: 'Comments',
        setupContent: (tabPanel) => ({
          commentsTextSection: tabPanel.getByRole('region', { name: 'Comments Section' }),
        }),
      }),
    }))
  }
}
