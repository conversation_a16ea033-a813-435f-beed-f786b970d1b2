import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AssetEditorAriaHeading extends BaseComponent {
  saveButton: Locator
  editButton: Locator
  deleteButton: Locator
  ariaLevelDropdown: Locator

  constructor({ scope }: TypicalComponentConstructorParams) {
    super({
      root: scope.locator('.ck-aria-heading-view').or(scope.locator('.ck-aria-heading-edit')),
    })

    this.saveButton = this.root.getByRole('button', { name: 'Save' })
    this.editButton = this.root.getByRole('button', { name: 'Edit' })
    this.deleteButton = this.root.getByRole('button', { name: 'Delete' })
    this.ariaLevelDropdown = this.root.getByRole('button', { name: 'Aria Level' })
  }

  async clickAriaLevel(level: number): Promise<void> {
    await this.ariaLevelDropdown.click()
    await this.root.getByRole('button', { name: `Aria Level ${level}` }).click()
  }
}
