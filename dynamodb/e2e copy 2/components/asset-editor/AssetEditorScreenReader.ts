import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

export class AssetEditorScreenReaderAttribute extends BaseComponent {
  saveButton: Locator
  editButton: Locator
  deleteButton: Locator
  srAttribute: Locator
  screenReaderContentTextBox: Locator
  screenReaderForm: Locator
  removeScreenReaderAttributeButton: Locator

  constructor({ scope }: TypicalComponentConstructorParams) {
    super({
      root: scope.locator('.ck-lang-form'),
    })

    this.saveButton = this.root.getByRole('button', { name: 'save' })
    this.editButton = this.page.getByRole('button', { name: 'Edit Screen Reader Text' })
    this.screenReaderContentTextBox = this.page.getByRole('textbox', {
      name: 'Screen Reader Content',
    })
    this.removeScreenReaderAttributeButton = this.page.getByRole('button', {
      name: 'Remove Screen Reader Attribute',
    })
  }
}
