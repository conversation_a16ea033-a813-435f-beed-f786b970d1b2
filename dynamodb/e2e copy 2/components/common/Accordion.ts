import { Locator, expect } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

type AccordionAdditionalConstructorParams<ContentType extends object> = {
  /** Accessible name (heading/label) of this accordion */
  name: string

  /** Index to use for nth() on root if multiple matches expected */
  index?: number

  /**
   * Callback to set up the content which is a returned object that will
   * be assigned to the `content` property. Can utilize the contentRegion
   * locator for proper chaining anything inside.
   */
  setupContent: (
    contentRegion: Locator,
    createComponent: BaseComponent['createComponent']
  ) => ContentType
}

/**
 * Represents an accordion
 */
export class Accordion<ContentType extends object> extends BaseComponent {
  /** Locator for the content region or container itself */
  contentRegion: Locator

  /** Object containing the content set up during construction */
  content: ContentType

  constructor({
    name,
    scope,
    index,
    // If draggable, there's an additional button for that purpose as well,
    // which would not have the expanded attribute
    root = scope.getByRole('button', { name }).and(scope.locator('[aria-expanded]')),
    setupContent,
  }: TypicalComponentConstructorParams & AccordionAdditionalConstructorParams<ContentType>) {
    super({ root: index !== undefined ? root.nth(index) : root })

    const contentRegion = scope.getByRole('region', { name })
    this.contentRegion = index !== undefined ? contentRegion.nth(index) : contentRegion

    this.content = setupContent(
      this.contentRegion,
      this.createComponent.bind({
        ...this,
        root: this.contentRegion,
      })
    )
  }

  async isOpen() {
    let expandedString = null
    // Note that expect/assertions for specific cases should *never* happen
    // in a page object/component. This is a special exception to use the poll
    // utility, since the page/component doesn't expect a null/the attribute
    // to not exist, and cannot answer whether it's open this way until it exists,
    // which seems to be a race condition here.
    await expect
      .poll(async () => {
        expandedString = await this.root.getAttribute('aria-expanded')
        return expandedString
      })
      .not.toBeNull()
    return expandedString === 'true'
  }

  // Should this be more explicit, like ensureOpen?
  async open() {
    if (!(await this.isOpen())) {
      await this.root.click()
    }
  }
}
