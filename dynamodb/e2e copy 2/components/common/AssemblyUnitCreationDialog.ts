import type { Locator } from '@playwright/test'
import type { TypicalContentContainerComponentConstructorParams } from '../bases/ContentContainerComponent'
import { ComboBox } from './ComboBox'
import { Dialog, DialogAdditionalConstructorParams } from './Dialog'

export class AssemblyUnitCreationDialog<
  ButtonNamesType extends { [key: string]: string },
  ContentType extends object
> extends Dialog<ButtonNamesType, ContentType> {
  readonly quantityInput: Locator
  readonly pretestingNumberInput: Locator
  readonly auTypeOption: ComboBox
  readonly adminYearOption: ComboBox
  readonly baseFormOption: <PERSON>mboBox
  readonly questionTypeOption: <PERSON><PERSON><PERSON><PERSON>
  readonly questionTypeNumberOption: ComboBox
  readonly baseAUOption: ComboBox
  readonly sequenceAUOption: ComboBox
  readonly metadataVersionOption: ComboBox

  constructor({
    name,
    scope,
    root = scope.getByRole('dialog', { name }),
    buttonNames,
    exactNames = false,
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType> &
    DialogAdditionalConstructorParams<ButtonNamesType>) {
    super({ name, scope, root, buttonNames, exactNames, setupContent })
    this.quantityInput = this.root.getByRole('textbox', { name: 'Quantity of New AUs' })
    this.pretestingNumberInput = this.root.getByRole('textbox', {
      name: 'Starting Pretesting Number',
    })
    this.auTypeOption = new ComboBox({
      name: 'AU Type',
      scope: this.contentContainer,
    })
    this.adminYearOption = new ComboBox({
      name: 'Admin Year',
      scope: this.contentContainer,
    })
    this.baseFormOption = new ComboBox({
      name: 'Base Form',
      scope: this.contentContainer,
    })
    this.questionTypeOption = new ComboBox({
      name: 'AU Question Type',
      scope: this.contentContainer,
      exact: true,
    })
    this.questionTypeNumberOption = new ComboBox({
      name: 'AU Question Type Label',
      scope: this.contentContainer,
      exact: true,
    })
    this.baseAUOption = new ComboBox({
      name: 'Base AU',
      scope: this.contentContainer,
    })
    this.sequenceAUOption = new ComboBox({
      name: 'Sequence AU',
      scope: this.contentContainer,
    })
    this.metadataVersionOption = new ComboBox({
      name: 'Metadata Version',
      scope: this.contentContainer,
    })
  }
}
