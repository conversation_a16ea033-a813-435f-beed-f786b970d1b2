import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalNamedComponentConstructorParams } from '../bases/BaseComponent'

export class AssetContainingStructureCard extends BaseComponent {
  readonly openButton: Locator
  readonly previewButton: Locator
  // Saw in designs, so will need to implement when applicable
  // readonly moreDropdown

  constructor({
    scope,
    name,
    root = scope.getByRole('group', { name }),
  }: TypicalNamedComponentConstructorParams) {
    super({ root })

    this.openButton = this.root.getByRole('button', { name: 'Open' })
    this.previewButton = this.root.getByRole('button', { name: 'Preview' })
  }
}
