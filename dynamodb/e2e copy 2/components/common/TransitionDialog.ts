import type { TypicalComponentConstructorParams } from '../bases/BaseComponent'
import { ComboBox } from './ComboBox'
import { Dialog } from './Dialog'

export class TransitionDialog {
  constructor() {
    throw new Error('Please use static create method instead')
  }

  static create({ scope }: Pick<TypicalComponentConstructorParams, 'scope'>) {
    const workflowMatcher = /(?<stage>[\w ]+), (?<step>[\w ()]+)/

    const dialog = new Dialog({
      name: 'Transition',
      scope,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        fromLocator: contentContainer.locator('[data-test-id="current-workflow"]'),
        toSelect: new ComboBox({ name: 'To:', scope: contentContainer }),
        nextStepRadioButton: contentContainer.getByRole('radio', { name: 'Next step' }),
      }),
    })

    return Object.assign(dialog, {
      // getFromStatus: async () => {
      getCurrentWorkflow: async () => {
        const text = await dialog.content.fromLocator.innerText()
        const { stage, step } = text.match(workflowMatcher)?.groups || {}
        if (!stage || !step) {
          throw new Error("Couldn't get stage and step from workflow text!")
        }
        return { stage, step }
      },
    })
  }
}
