import { waitForCondition } from '@e2e/util/waitForCondition'
import { expect } from '@playwright/test'
import {
  ContentContainerComponent,
  TypicalContentContainerComponentConstructorParams,
} from '../bases/ContentContainerComponent'
import { AntTreeSelect } from './AntTreeSelect'
import { ComboBox } from './ComboBox'
import { TextEditor } from './TextEditor'

/**
 * Represents an Tab
 */
export class Tab<ContentType extends object> extends ContentContainerComponent<ContentType> {
  constructor({
    name,
    scope,
    root = scope.getByRole('tab', { name }),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType> & { name: string }) {
    super({ root, contentContainer: scope.getByRole('tabpanel', { name }), setupContent })
  }

  // Or should this be isSelected?
  async isOpen() {
    let isSelectedString = null
    // Note that expect/assertions for specific cases should *never* happen
    // in a page object/component. This is a special exception to use the poll
    // utility, since the page/component doesn't expect a null/the attribute
    // to not exist, and cannot answer whether it's open this way until it exists,
    // which seems to be a race condition here.
    await expect
      .poll(async () => {
        isSelectedString = await this.root.getAttribute('aria-selected')
        return isSelectedString
      })
      .not.toBeNull()
    return isSelectedString === 'true'
  }

  // Should this be more explicit, like ensureOpen?
  async open() {
    await waitForCondition(async () => {
      // Realized doing a click whether open or not won't deselect it for Tabs, so
      // just keeping this around as a way to ensure it actually opens
      await this.root.click()
      return this.isOpen()
    })
  }

  async getSelectComboBoxInputs() {
    await this.open()
    const comboBoxes = await this.contentContainer
      .getByRole('combobox')
      .and(this.contentContainer.locator(':not([aria-haspopup="tree"])'))
      .all()
    const comboBoxInstances = comboBoxes.map(
      (comboBox) => new ComboBox({ root: comboBox, scope: this.contentContainer })
    )
    return comboBoxInstances
  }

  async getAntTreeSelectComponents() {
    await this.open()
    const roots = await this.contentContainer.getByRole('group', { name: 'Tree Metadata' }).all()
    const treeSelectInstances = roots.map(
      (root) => new AntTreeSelect({ root, scope: this.contentContainer })
    )
    return treeSelectInstances
  }

  async getTextInputs() {
    await this.open()
    const textInputs = await this.contentContainer.getByRole('textbox').all()
    // ck editor text input vs others
    const textInputInstances = await Promise.all(
      textInputs.map(async (textInput) =>
        (await textInput.getAttribute('aria-label'))?.includes('Rich Text Editor')
          ? new TextEditor({ root: textInput, scope: this.contentContainer, name: '' })
          : textInput
      )
    )
    return textInputInstances
  }
}
