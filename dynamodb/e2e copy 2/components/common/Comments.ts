import type { Locator } from '@playwright/test'
import {
  ContentContainerComponent,
  TypicalContentContainerComponentConstructorParams,
} from '../bases/ContentContainerComponent'
import { Dialog } from './Dialog'

/**
 * Represents Reusable Comments Editable Text Box Component
 */
export class CommentsEditableTextBox<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly textBox: Locator
  readonly buttons: Locator
  readonly saveButton: Locator
  readonly cancelButton: Locator

  constructor({
    cancelLabel,
    saveLabel,
    scope,
    root = scope.getByTestId('comment-editable-text-box'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType> & {
    cancelLabel?: string
    saveLabel?: string
  }) {
    super({ root, setupContent, contentContainer: root })

    this.textBox = this.root.getByRole('textbox')
    this.buttons = this.root.getByTestId('comment-editable-text-box-button-container')
    this.saveButton = this.buttons.getByRole('button', { name: saveLabel || 'Add' })
    this.cancelButton = this.buttons.getByRole('button', {
      name: cancelLabel || 'Cancel',
    })
  }
}

/**
 * Represents Reusable Comments Content Header Component
 */
export class CommentsContentHeader<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly menuButton: Locator
  readonly menuEditButton: Locator
  readonly menuDeleteButton: Locator
  readonly deleteCommentDialog

  constructor({
    scope,
    root = scope.getByTestId('comment-content-header'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.menuButton = this.root.getByTestId('comment-content-header-menu-icon')
    this.menuEditButton = this.root.getByRole('button', { name: 'Edit' })
    this.menuDeleteButton = this.root.getByRole('button', { name: 'Delete' })
    this.deleteCommentDialog = new Dialog({
      name: 'Delete Comment',
      scope: this.root,
      buttonNames: {
        cancel: 'Cancel',
        remove: 'Delete',
      },
      setupContent,
    })
  }
}

/**
 * Represents Reusable Comments Content Body Component
 */
export class CommentsContentBody<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly editableTextBox: CommentsEditableTextBox<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comment-content-body'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.editableTextBox = new CommentsEditableTextBox({
      scope: root,
      setupContent,
      saveLabel: 'Save',
    })
  }
}

/**
 * Represents Reusable Comments Content Footer Component
 */
export class CommentsContentFooter<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly replyButton: Locator
  readonly editableTextBox: CommentsEditableTextBox<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comment-content-footer'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.replyButton = this.root.getByRole('button', { name: 'Reply' })
    this.editableTextBox = new CommentsEditableTextBox({
      scope: root,
      setupContent,
      saveLabel: 'Reply',
    })
  }
}

/**
 * Represents Reusable Comments Content Component
 */
export class CommentsContent<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly header: CommentsContentHeader<ContentType>
  readonly body: CommentsContentBody<ContentType>
  readonly footer: CommentsContentFooter<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comment-content'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.header = new CommentsContentHeader({
      scope: root,
      setupContent,
    })
    this.body = new CommentsContentBody({
      scope: root,
      setupContent,
    })
    this.footer = new CommentsContentFooter({
      scope: root,
      setupContent,
    })
  }
}

/**
 * Represents Reusable Comments Thread Component
 */
export class CommentsThread<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly commentsContent: CommentsContent<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comment-thread'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.commentsContent = new CommentsContent({
      scope: root,
      setupContent,
    })
  }
}

/**
 * Represents Reusable Comments Add Comment Component
 */
export class CommentsAddCommentContainer<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly addCommentButton: Locator
  readonly editableTextBox: CommentsEditableTextBox<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comment-add-comment'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.addCommentButton = this.root.getByRole('button', { name: 'Add a comment' })
    this.editableTextBox = new CommentsEditableTextBox({
      scope: root,
      setupContent,
    })
  }
}

/**
 * Represents Reusable Comments Threads Component
 */
export class CommentsThreads<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly addCommentContainer: CommentsAddCommentContainer<ContentType>
  readonly commentsThread: CommentsThread<ContentType>

  constructor({
    scope,
    root = scope.getByTestId('comments-threads-container'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.addCommentContainer = new CommentsAddCommentContainer({
      scope: root,
      setupContent,
    })
    this.commentsThread = new CommentsThread({
      scope: this.root.getByTestId('comment-threads'),
      setupContent,
    })
  }
}

/**
 * Represents Reusable Comments TopBar Component
 */
export class CommentsTopBar<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly topBarLabel: Locator
  readonly topBarMenu: Locator
  readonly topBarMenuCloseButton: Locator

  constructor({
    scope,
    root = scope.getByTestId('comment-top-bar'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.topBarLabel = this.root.getByTestId('comment-top-bar-label')
    this.topBarMenu = this.root.getByTestId('comment-top-bar-menu')
    this.topBarMenuCloseButton = this.topBarMenu.getByTestId('comment-top-bar-menu-close-button')
  }
}

/**
 * Represents Reusable Comments Components
 */
export class Comments<ContentType extends object> extends ContentContainerComponent<ContentType> {
  readonly topBar: CommentsTopBar<ContentType>
  readonly threads: CommentsThreads<ContentType>

  constructor({
    scope,
    root = scope.getByRole('region', { name: 'comments-container' }),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.topBar = new CommentsTopBar({
      scope: root,
      setupContent,
    })
    this.threads = new CommentsThreads({
      scope: root,
      setupContent,
    })
  }
}
