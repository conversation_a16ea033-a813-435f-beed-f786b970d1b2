import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

type TextEditorAdditionalConstructorParams = {
  /** Accessible name (heading/label) of this text editor */
  name: string
  /** Grab text editor by exact accessible name */
  exact?: boolean
}

/**
 * Represents a rich/wysiwyg text editor
 */
export class TextEditor extends BaseComponent {
  name

  constructor({
    name,
    exact = false,
    scope,
    root = scope.getByRole('textbox', { name, exact }),
  }: TypicalComponentConstructorParams & TextEditorAdditionalConstructorParams) {
    super({ root })

    this.name = name

    // Will likely want to either add a type, or restrictions, or something
    // around knowing what can be done with this editor
  }
}
