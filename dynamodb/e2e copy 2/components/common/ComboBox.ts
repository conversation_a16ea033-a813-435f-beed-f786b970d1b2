import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

type ComboBoxAdditionalConstructorParams = {
  /** Accessible name (heading/label) of this combo box */
  name?: string
  /** Grab the react-select combo box by exact accessible name */
  exact?: boolean
}

/**
 * Represents a react-select combo box
 */
export class ComboBox extends BaseComponent {
  constructor({
    name,
    exact = false,
    scope,
    root = scope.getByRole('combobox', { name, exact }),
  }: TypicalComponentConstructorParams & ComboBoxAdditionalConstructorParams) {
    super({ root })
  }

  async getAllOptions() {
    const options = await this.page.getByRole('option').all()
    return options
  }

  getOptionByIndexOrName({
    index,
    name,
    exact,
  }: {
    index?: number
    name?: string
    exact?: boolean
  }) {
    const definedIndex = index !== undefined
    if (name && definedIndex) {
      return this.page.getByRole('option', { name, exact }).nth(index)
    } else if (definedIndex) {
      return this.page.getByRole('option').nth(index)
    } else if (name) {
      return this.page.getByRole('option', { name, exact })
    }
    throw new Error('Neither an index or name were provided.')
  }

  async selectFirstOption() {
    await this.root.click()
    const firstOption = this.getOptionByIndexOrName({ index: 0 })
    const firstOptionText = await firstOption?.textContent()
    await firstOption?.click()
    return firstOptionText
  }

  async selectOption(option: string | Parameters<typeof this.getOptionByIndexOrName>[0]) {
    const optionToUse = typeof option === 'object' ? option : { name: option }
    await this.root.click()
    await this.getOptionByIndexOrName(optionToUse).click()
  }

  async getCurrentSelection() {
    // strangely the 'input' element does not hold the value but the selected text value can be grabbed from
    // the parent's sibling
    const selectionText = await this.root.evaluate(
      (node) => node.parentElement?.previousSibling?.textContent
    )
    return selectionText
  }
}
