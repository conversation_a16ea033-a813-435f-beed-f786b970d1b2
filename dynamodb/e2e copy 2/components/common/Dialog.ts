import type { Locator } from '@playwright/test'
import {
  ContentContainerComponent,
  TypicalContentContainerComponentConstructorParams,
} from '../bases/ContentContainerComponent'

export type DialogAdditionalConstructorParams<ButtonNamesType extends { [key: string]: string }> = {
  /** Accessible name (heading/label) of this dialog */
  name: string | RegExp

  /**
   * Object where the keys will be the resulting keys within `buttons`, and the
   * values should be the accessible name of the buttons used for the Locators
   */
  buttonNames: ButtonNamesType

  /**
   * Flag to determine whether the button names have to be exact matches or not
   */
  exactNames?: boolean
}

export class Dialog<
  ButtonNamesType extends { [key: string]: string },
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly closeButton: Locator

  /**
   * Object containing the Locators for the buttons of this dialog
   */
  buttons: {
    [Property in keyof ButtonNamesType]: Locator
  }

  constructor({
    name,
    scope,
    root = scope.getByRole('dialog', { name }),
    buttonNames,
    exactNames = false,
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType> &
    DialogAdditionalConstructorParams<ButtonNamesType>) {
    super({ root, setupContent, contentContainer: root })

    this.closeButton = this.root.getByRole('button', { name: 'Close Modal' })

    this.buttons = {} as (typeof this)['buttons']
    Object.entries(buttonNames).forEach(([key, name]: [keyof typeof buttonNames, string]) => {
      this.buttons[key] = this.root.getByRole('button', { name, exact: exactNames })
    })
  }
}
