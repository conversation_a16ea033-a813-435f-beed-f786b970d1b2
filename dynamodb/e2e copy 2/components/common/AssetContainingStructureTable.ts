import type { DestructuredParams } from '@e2e/types'
import type { NonNullableLocatorName } from '@e2e/types/typeUtils'
import { extendLocator } from '@e2e/util/miscUtils'
import type { Locator } from '@playwright/test'
import { Dropdown } from './Dropdown'
import { ContentTableRow, Table } from './Table'

interface AssetsContainingStructureTableRow<
  DropdownItemNames extends { [key: string]: NonNullableLocatorName }
> extends ContentTableRow {
  moreDropdown: Dropdown<DropdownItemNames>
}

export class AssetContainingStructureTable<
  DropdownItemNames extends { [key: string]: NonNullableLocatorName }
> extends Table {
  protected dropdownItemNames: DropdownItemNames

  constructor({
    dropdownItemNames,
    ...args
  }: DestructuredParams<typeof Table> & { dropdownItemNames: DropdownItemNames }) {
    super({ ...args })

    this.dropdownItemNames = dropdownItemNames
  }

  override getRow({
    filter,
    index,
  }: {
    filter?: Parameters<Locator['filter']>[0]
    index?: number
  }): AssetsContainingStructureTableRow<DropdownItemNames> {
    const baseTableRow = super.getRow({ filter, index })
    return extendLocator(baseTableRow, (rowLocator) => ({
      moreDropdown: new Dropdown({
        name: 'More',
        scope: rowLocator,
        itemNames: this.dropdownItemNames,
      }),
    }))
  }
}
