import type { NonNullableLocatorName } from '@e2e/types/typeUtils'
import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalNamedComponentConstructorParams } from '../bases/BaseComponent'

export type DropdownAdditionalConstructorParams<
  ItemNamesType extends { [key: string]: NonNullableLocatorName }
> = {
  /**
   * Object where the keys will be the resulting keys within `items`, and the
   * values should be the accessible name of the items used for the Locators
   */
  itemNames: ItemNamesType
}
export class Dropdown<
  ItemNamesType extends { [key: string]: NonNullableLocatorName }
> extends BaseComponent {
  /**
   * Convenient property for the dropdown button locator vs using .root
   */
  readonly button: Locator
  // Marking protected for now since only needed internally
  protected readonly menu: Locator
  /**
   * Object containing the Locators for the items of this dropdown
   */
  items: {
    [Property in keyof ItemNamesType]: Locator
  }
  itemRole: 'menuitem' | 'menuitemradio' // General role for all items

  // Useful for dynamic dropdowns like for filters, vs more static ones like menus
  getOption(options: NonNullable<Parameters<Locator['getByRole']>[1]>): Locator {
    const optionRoleLocator = this.menu.getByRole(this.itemRole, options)

    if (options.name) {
      // Commenting out `or` part until we run into a situation where we actually need it
      return optionRoleLocator //.or(this.menu.getByText(options.name, options))
    }
    // Maybe just wants to get all items
    return optionRoleLocator
  }
  close() {
    // Clicking outside the menu is intercepted. and overlay aria-hidden
    return this.menu.press('Escape')
  }

  constructor({
    name,
    scope,
    root = scope.getByRole('button', { name }),
    itemNames,
    itemRole = 'menuitem', // Default role
    menuRole = 'menu', // Default role
  }: TypicalNamedComponentConstructorParams & {
    itemNames: ItemNamesType
    itemRole?: 'menuitem' | 'menuitemradio'
    menuRole?: 'menu' | 'region'
  }) {
    super({ root })

    this.button = this.root
    this.itemRole = itemRole

    // The menu, at least for MUI, is not within the scope or even `main`
    this.menu = this.page.getByRole(menuRole, { name })
    this.items = {} as (typeof this)['items']
    Object.entries(itemNames).forEach(([key, itemName]: [string, NonNullableLocatorName]) => {
      this.items[key as keyof ItemNamesType] = this.getOption({ name: itemName })
    })
  }
}
