import { extendLocator } from '@e2e/util/miscUtils'
import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

type AntTreeSelectAdditionalConstructorParams = {
  /** Accessible name (heading/label) of the combo box */
  name?: string
  /** Grab the combo box by exact accessible name */
  exact?: boolean

  /** The combobox (role) locator that is used for this tree select */
  // comboBox: Locator
}

// With no control over the Ant tree components, just have to make do with what
// selectors are available

/**
 * Represents an ant-tree-select component
 */
export class AntTreeSelect extends BaseComponent {
  readonly comboBox: Locator
  readonly tree

  constructor({
    name = '',
    exact = false,
    scope,
    root = scope.getByRole('group', { name: `Tree Metadata - ${name}`, exact }),
  }: TypicalComponentConstructorParams & AntTreeSelectAdditionalConstructorParams) {
    super({ root })

    this.comboBox = root.getByRole('combobox', { name, exact })

    // Ant has aria-owns and aria-controls, but the tree has no matching ids
    // Doing a generic tree role selector should work, since only one visible at a time
    this.tree = extendLocator(this.page.getByRole('tree'), (tree) => ({
      nodeLocator: extendLocator(
        tree.locator('.ant-select-tree-treenode', {
          // There is a first initial treenode which is empty and doesn't
          // have a switcher, so seemed the best approach without being able to use role
          has: this.page.locator('.ant-select-tree-switcher'),
        }),
        (node) => ({
          opener: node.getByRole('img', { name: 'plus-square' }),
          closer: node.getByRole('img', { name: 'minus-square' }),
        })
      ),
    }))
  }

  getCurrentSelectionLocator(
    label: string,
    additionalOptions?: Parameters<Locator['getByRole']>[1]
  ) {
    return this.root
      .getByRole('group', {
        name: `Current Selection`,
        ...additionalOptions,
      })
      .filter({ hasText: label })
  }
}
