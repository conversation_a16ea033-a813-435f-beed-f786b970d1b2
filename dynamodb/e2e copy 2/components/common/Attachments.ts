import { readFileSync } from 'node:fs'
import path from 'node:path'
import type { FileChooser, Locator } from '@playwright/test'
import {
  ContentContainerComponent,
  TypicalContentContainerComponentConstructorParams,
} from '../bases/ContentContainerComponent'
import { Dialog } from './Dialog'

/**
 * Represents Reusable Attachments Info Component
 */
export class AttachmentInfo<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly name: Locator
  readonly tag: Locator
  readonly statusMessage: Locator

  constructor({
    scope,
    root = scope.getByTestId('attachment-container'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.name = this.root.getByTestId('attachment-name')
    this.tag = this.root.getByTestId('attachment-type-tag')
    this.statusMessage = this.root.getByTestId('attachment-status-message')
  }
}

/**
 * Represents reusable Attachment Actions Component
 */
export class AttachmentActions<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly previewButton: Locator
  readonly downloadButton: Locator
  readonly deleteButton: Locator

  constructor({
    scope,
    root = scope.getByTestId('attachment-actions'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.previewButton = this.root.getByLabel('Preview attachment')
    this.downloadButton = this.root.getByLabel('Download attachment')
    this.deleteButton = this.root.getByLabel('Delete attachment')
  }
}

export class AttachmentsUploadDialog<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly dropzone: Locator
  readonly selectFilesBtn: Locator
  readonly visibilityDropdown: Locator
  readonly previewArea: Locator
  readonly alerts: Locator
  readonly dialog

  constructor({
    scope,
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root: scope as Locator, setupContent, contentContainer: scope as Locator })
    this.dialog = new Dialog({
      name: 'Upload attachments',
      scope,
      root: scope as Locator,
      buttonNames: {
        cancel: 'Cancel',
        upload: 'Upload',
        replace: 'Replace',
      },
      setupContent,
    })

    this.dropzone = this.root.getByLabel('Drop Files')
    this.selectFilesBtn = this.root.getByRole('button', { name: 'Select Files' })
    this.visibilityDropdown = this.root.getByRole('combobox', { name: 'Visibility' })
    this.previewArea = this.root.getByTestId('attachment-preview')
    this.alerts = this.root.getByRole('alert')
  }

  async uploadFiles(...args: Parameters<FileChooser['setFiles']>) {
    const popup = this.page.waitForEvent('filechooser')
    await this.selectFilesBtn.click()
    const picker = await popup
    await picker.setFiles(...args)
  }

  // https://github.com/microsoft/playwright/issues/10667#issuecomment-998397241
  async dropFiles(target: string, mimeType: string) {
    const fileName = path.basename(target)
    const buffer = readFileSync(target).toString('base64')
    const dataTransfer = await this.page.evaluateHandle(
      async ({ bufferData, localFileName, localFileType }) => {
        const dt = new DataTransfer()
        const blobData = await fetch(bufferData).then((res) => res.blob())

        const file = new File([blobData], localFileName, { type: localFileType })
        dt.items.add(file)
        return dt
      },
      {
        bufferData: `data:application/octet-stream;base64,${buffer}`,
        localFileName: fileName,
        localFileType: mimeType,
      }
    )

    await this.dropzone.dispatchEvent('drop', { dataTransfer, target: this.dropzone })
  }
}

/**
 * Represents reusable Attachment Components
 */
export class Attachments<
  ContentType extends object
> extends ContentContainerComponent<ContentType> {
  readonly actions: AttachmentActions<ContentType>
  readonly attachmentInfo: AttachmentInfo<ContentType>
  readonly uploadButton: Locator
  readonly uploadDialog: AttachmentsUploadDialog<ContentType>
  readonly refreshButton: Locator
  readonly deleteDialog
  readonly downloadAttachmentDialog

  constructor({
    scope,
    root = scope.getByTestId('attachment-list-container'),
    setupContent,
  }: TypicalContentContainerComponentConstructorParams<ContentType>) {
    super({ root, setupContent, contentContainer: root })

    this.actions = new AttachmentActions({
      scope: root,
      setupContent,
    })

    this.attachmentInfo = new AttachmentInfo({
      scope: root,
      setupContent,
    })
    this.deleteDialog = new Dialog({
      name: 'Remove Attachment',
      scope: this.page.getByTestId('attachment-container'),
      buttonNames: {
        cancel: 'No',
        remove: 'Yes',
      },
      setupContent,
    })
    this.downloadAttachmentDialog = new Dialog({
      name: 'Download Attachment',
      scope: this.page.getByTestId('attachment-container'),
      buttonNames: {
        cancel: 'No',
        confirm: 'Download',
      },
      setupContent,
    })
    this.uploadButton = this.root.getByRole('button', { name: 'Upload' })
    this.uploadDialog = new AttachmentsUploadDialog({
      scope: this.page.getByRole('dialog', { name: 'Upload attachments' }),
      setupContent,
    })
    this.refreshButton = this.root.getByRole('button', { name: 'Refresh attachment list' })
  }
}
