import { extendLocator } from '@e2e/util/miscUtils'
import { Locator } from '@playwright/test'
import { BaseComponent, TypicalNamedComponentConstructorParams } from '../bases/BaseComponent'

export interface ContentTableRow extends Locator {
  root: Locator
  cellLocator: (
    params: { columnName: string },
    options?: Parameters<Locator['locator']>[1]
  ) => Locator
}

export class Table extends BaseComponent {
  readonly allRows: Locator

  constructor({
    scope,
    name,
    root = scope.getByRole('table', { name }),
  }: TypicalNamedComponentConstructorParams) {
    super({ root })

    // Header row is also a 'row', and 'rowgroup' refers to thead and tbody
    this.allRows = this.root.locator('tbody tr') //.getByRole('row')
  }

  cellLocator({ columnName }: { columnName: string }, options?: Parameters<Locator['locator']>[1]) {
    return this.root.locator(`cell=${columnName}`, options)
  }

  getRow({
    filter,
    index,
  }: {
    filter?: Parameters<Locator['filter']>[0]
    index?: number
  }): ContentTableRow {
    const filteredRows = this.allRows.filter(filter)
    const rowLocator = index !== undefined ? filteredRows.nth(index) : filteredRows

    return extendLocator(rowLocator, (rowLocator) => ({
      root: rowLocator,
      cellLocator: this.cellLocator,
    }))
  }
}
