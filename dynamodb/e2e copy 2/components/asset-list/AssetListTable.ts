import type { Locator } from '@playwright/test'
import type { TypicalComponentConstructorParams } from '../bases/BaseComponent'
import { Table } from '../common'

export class AssetListTable extends Table {
  readonly allCheckBoxes: Locator

  constructor({
    scope,
    // Once either the Apricot Table can have an aria-label or we switch
    // off of it, remove this line and just pass the name down instead
    root = scope.getByRole('table'),
  }: TypicalComponentConstructorParams) {
    super({ scope, name: 'To Replace', root })

    this.allCheckBoxes = this.root.locator('input[type=checkbox]')
  }
}
