import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../bases/BaseComponent'

type ComboboxComponentAdditionalConstructorParams = {
  /** Accessible name (label) of this combobox */
  name: string
}

/**
 * Represents a filter on the asset list page
 *
 * Because the filter is a combobox, the `root` of this is actually
 * the input with role combobox, as everything else is accessible from it
 */
export class AssetListFilter extends BaseComponent {
  constructor({
    name,
    scope,
    root = scope.getByRole('combobox', { name }),
  }: TypicalComponentConstructorParams & ComboboxComponentAdditionalConstructorParams) {
    super({ root })
  }

  // These could potentially be separated out into a ComboboxComponent
  async getListBox() {
    const controlsId = await this.root.getAttribute('aria-controls')
    return this.root.page().locator(`#${controlsId}`)
  }

  async getOptionByText(text: string) {
    // Unfortunately, with this third party Select component, they do not have
    // the `listitem` role
    const listBox = await this.getListBox()
    await listBox.type(text)
    return listBox.press('Enter')
  }

  async selectOptionByText(text: string, options?: Parameters<Locator['getByText']>[1]) {
    await this.root.click()
    // Unfortunately, with this third party Select component, they do not have
    // the `listitem` role
    const option = (await this.getListBox()).getByText(text, options)
    await option.click()
  }
}
