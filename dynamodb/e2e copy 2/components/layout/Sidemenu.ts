import type { Locator } from '@playwright/test'
import { BaseComponent, StandaloneComponentConstructorParams } from '../bases/BaseComponent'

export class SideMenu extends BaseComponent {
  /**
   * The overlay over the rest of the page when the side menu is open
   *
   * Mainly useful in the tests if desired to click to close the menu
   */
  readonly overlay: Locator

  readonly closeButton: Locator

  readonly homeLink: Locator
  readonly createAssetLink: Locator

  constructor({ page }: StandaloneComponentConstructorParams) {
    super({ root: page.locator('.bm-menu-wrap') })

    // Overlay is a sibling to this root
    this.overlay = page.locator('.bm-overlay')

    this.closeButton = this.root.getByRole('button', { name: 'Close Menu' })

    this.homeLink = this.root.getByRole('link', { name: 'Home' })
    this.createAssetLink = this.root.getByRole('link', { name: 'Create an Asset' })
  }

  async clickOutside() {
    await this.overlay.click()
  }

  getLinkBySubject(which: 'Assets' | 'Collections', subject: string) {
    // Would want to modify the DOM if possible to make this better
    return this.root.locator(`:text("${which}"):below(:text("${subject}"))`).first()
  }

  getAssetsLinkBySubject(subject: string) {
    return this.getLinkBySubject('Assets', subject)
  }

  getCollectionsLinkBySubject(subject: string) {
    return this.getLinkBySubject('Collections', subject)
  }
}
