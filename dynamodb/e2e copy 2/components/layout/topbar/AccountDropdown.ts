import type { Locator } from '@playwright/test'
import { BaseComponent, TypicalComponentConstructorParams } from '../../bases/BaseComponent'

export class AccountDropdown extends BaseComponent {
  readonly roleDisplay: Locator
  readonly adminMenuLink: Locator
  readonly debugLink: Locator
  readonly restartSessionButton: Locator

  constructor({
    scope,
    root = scope.getByRole('navigation', { name: 'user menu' }),
  }: TypicalComponentConstructorParams) {
    super({ root })

    // These need better selectors
    this.roleDisplay = this.root.getByRole('listitem').first()
    this.adminMenuLink = this.root.getByRole('listitem').getByRole('link', { name: 'Admin Menu' })
    this.debugLink = this.root.getByRole('listitem').getByRole('link', { name: 'Debug' })
    this.restartSessionButton = this.root.getByText('Restart Session')
  }
}
