import type { Locator } from '@playwright/test'
import { BaseComponent, StandaloneComponentConstructorParams } from '../bases/BaseComponent'
import { AccountDropdown } from './topbar/AccountDropdown'

export class TopBar extends BaseComponent {
  readonly homeLink: Locator

  readonly burgerButton: Locator

  /**
   * Search input that only shows up on certain pages
   */
  readonly searchInput: Locator
  readonly searchInputMenu: Locator

  readonly accountDropdown: AccountDropdown
  readonly accountDropdownButton: Locator

  constructor({ page }: StandaloneComponentConstructorParams) {
    super({ root: page.locator('.sticky-header') })

    this.homeLink = this.root.getByRole('link', { name: '<PERSON><PERSON><PERSON>' })

    this.burgerButton = this.root.locator('.cb-burger-menu-button')

    this.searchInput = this.root.getByRole('textbox', { name: 'Search' })
    this.searchInputMenu = this.root.getByRole('menu', { name: 'Search' })

    this.accountDropdownButton = this.root.locator('.icg-user-menu')
    this.accountDropdown = this.createComponent(AccountDropdown)
  }
}
