import type { PlaywrightTestConfig } from '@playwright/test'
import mainConfig from './playwright.config'

/**
 * See https://playwright.dev/docs/test-configuration.
 */
const config: PlaywrightTestConfig = {
  ...mainConfig,
  testDir: './automations',
  testMatch: /.*automation\.ts/,
  /* Maximum time one test can run for. */
  timeout: 60 * 60 * 1000,
  use: {
    ...mainConfig.use,
  },
}

export default config
