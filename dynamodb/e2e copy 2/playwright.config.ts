// import { devices } from '@playwright/test';
import dotenv from 'dotenv-defaults'
import { defineConfig } from '@playwright/test'
import type { ConsolidatedFixtures } from './fixtures/types'
import { determineBaseUrlFromEnv, getEnv } from './util/envUtils'
import { getMainUser } from './util/userUtils'

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
dotenv.config()

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig<ConsolidatedFixtures>({
  globalSetup: require.resolve('./global.setup'),
  testDir: './tests',
  /* Maximum time one test can run for. */
  // Will have to reevaluate this as we go, but 5 minutes is more than should be needed
  // for a test to pass, and a long time to wait for something indefinite to time out
  timeout: 60 * 1000,
  globalTimeout: process.env.CI ? 60 * 11 * 1000 : undefined,
  expect: {
    /**
     * Maximum time expect() should wait for the condition to be met.
     * For example in `await expect(locator).toHaveText();`
     */
    // Default is 5000, but the app currently can have some slowness
    // that I ran into causing wide flakiness, and 10 seconds also seems
    // reasonable, so setting to that, at least for now
    timeout: 10_000,
  },
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 3 : 0,
  /* 4 Workers on GitHub Actions */
  workers: process.env.CI ? 3 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: process.env.CI ? [['blob', { outputDir: 'blob-report' }]] : [['html']],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: determineBaseUrlFromEnv(),

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    // trace: 'on-first-retry',
    /* Collect trace when retrying the failed test. */
    trace: process.env.CI ? 'retain-on-first-failure' : 'on',

    /* Record a video of each test run */
    video: process.env.CI ? 'off' : 'on',

    screenshot: 'only-on-failure',

    /* Will have to revisit if we want to run against anything but Chrome */
    channel: 'chrome',
  },

  projects: [
    {
      name: 'setupChecks',
      testDir: './setup',
      testMatch: 'verifyCanConnect.setup.ts',
      use: {
        // Name doesn't make sense here, but reusing existing flag for now
        isAuthSetup: true,
        trace: 'off',
        video: 'off',
        screenshot: 'off',
      },
    },
    {
      name: 'mainUserAuthSetup',
      testDir: './setup',
      testMatch: 'mainUserLogin.setup.ts',
      dependencies: ['setupChecks'],
      use: {
        isAuthSetup: true,
      },
    },
    {
      name: 'multiUserAuthSetup',
      testDir: './setup',
      testMatch: 'multiUserLogin.setup.ts',
      dependencies: ['setupChecks'],
      use: {
        isAuthSetup: true,
      },
    },
    {
      name: 'ui',
      testMatch: 'ui/**/*.test.ts',
      dependencies: ['setupChecks'].concat(getEnv() === 'local' ? [] : ['mainUserAuthSetup']),
      use: {
        currentUser: getMainUser(),
      },
    },
    {
      name: 'permissions',
      testMatch: 'permissions/**/*.test.ts',
      dependencies: ['setupChecks'].concat(getEnv() === 'local' ? [] : ['multiUserAuthSetup']),
    },
    {
      name: 'manual',
      testMatch: 'manual/**/*.test.ts',
      use: {
        headless: false,
        isAuthSetup: true,
      },
    },
  ],

  /* Configure projects for major browsers */
  // projects: [
  // Chromium is apparently a restricted app at CB
  // {
  //   name: 'chromium',
  //   use: {
  //     ...devices['Desktop Chrome'],
  //   },
  // },

  // {
  //   name: 'firefox',
  //   use: {
  //     ...devices['Desktop Firefox'],
  //   },
  // },

  // {
  //   name: 'webkit',
  //   use: {
  //     ...devices['Desktop Safari'],
  //   },
  // },

  /* Test against mobile viewports. */
  // {
  //   name: 'Mobile Chrome',
  //   use: {
  //     ...devices['Pixel 5'],
  //   },
  // },
  // {
  //   name: 'Mobile Safari',
  //   use: {
  //     ...devices['iPhone 12'],
  //   },
  // },

  /* Test against branded browsers. */
  // {
  //   name: 'Microsoft Edge',
  //   use: {
  //     channel: 'msedge',
  //   },
  // },
  // {
  //   name: 'Google Chrome',
  //   use: {
  //     channel: 'chrome',
  //   },
  // },
  // ],

  /* Folder for test artifacts such as screenshots, videos, traces, etc. */
  // outputDir: 'test-results/',

  /* Run your local dev server before starting the tests */
  // webServer: {
  //   command: 'npm run start',
  //   port: 3000,
  // },
})
