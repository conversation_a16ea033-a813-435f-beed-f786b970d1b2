/* eslint-disable max-lines-per-function */
import { Dialog, Tab } from '@e2e/components/common'
import { Attachments } from '@e2e/components/common/Attachments'
import { Comments } from '@e2e/components/common/Comments'
import type { DestructuredParams } from '@e2e/types'
import { formatSubjectForUrl } from '@e2e/util/urlUtils'
import type { Locator } from '@playwright/test'
import { AssetContainingStructurePageBase } from './AssetContainingStructurePageBase'
import type { BasePage } from './BasePage'

// Need to refactor these
const moreActionsDropdownItemNames = {
  rename: 'Rename',
  addAssets: 'Add assets',
  transition: 'Transition',
  sendTestManifestToVault: 'Send Test Manifest',
  download: 'Download',
  archive: 'Archive',
}
const rowMoreDropdownItemNames = {
  bringToTop: 'Bring to top',
  sendToBottom: 'Send to Bottom',
  remove: 'Remove',
}

export class FormPage extends AssetContainingStructurePageBase<
  'Form',
  typeof moreActionsDropdownItemNames,
  typeof rowMoreDropdownItemNames
> {
  readonly formsTitle: Locator
  readonly applyButtonFormModal: Locator
  readonly addExamDirectionsButton: Locator
  readonly addSectionDirectionsButton: Locator
  readonly addAssemblyUnitsButton: Locator
  readonly addReplaceDirectionButton: Locator
  readonly previewFormButton: Locator
  readonly moreActionsButton: Locator
  readonly moreActionsRenameMenuItem: Locator
  readonly moreActionsAddAssemblyUnitsMenuItem: Locator
  readonly moreActionsAddButton: Locator
  readonly moreActionsAddReplaceDirectionMenuItem: Locator
  readonly moreActionsArchiveMenuItem: Locator
  readonly moreActionsDownloadMenuItem: Locator
  readonly moreActionsAddMenuItem: Locator
  readonly moreActionsDownloadPDFMenuItem: Locator
  readonly moreActionsDownloadTestPackageMenuItem: Locator
  readonly moreActionsDownloadMetadataMenuItem: Locator
  readonly moreActionsSendTestManifestMenuItem: Locator
  readonly moreActionsDownloadFormScoringCSV: Locator
  readonly formBadge: Locator
  readonly formRenameTextbox: Locator
  readonly detailsTab: Locator
  readonly examDirectionsTab: Locator
  readonly sectionOneTab: Locator
  readonly sectionTwoTab: Locator
  readonly detailsContainer: Locator
  readonly attachmentsContainer: Attachments<object>
  readonly addAssemblyUnitsToFormModalInput: Locator
  readonly addAssemblyUnitToFormModalAddButton: Locator
  readonly addAssemblyUnitToFormModalDoneButton: Locator
  readonly archiveFormArchiveButton: Locator
  readonly archivedBadge: Locator
  readonly removeDirectionDialog
  readonly removeAssemblyUnitDialog
  readonly tableColumnsDropdown: Locator
  readonly updateButton: Locator
  readonly resetButton: Locator
  readonly tableColumnsRegion: Locator
  readonly failedToExportDialog: Locator
  readonly failedToSendTestManifestToVaultDialog
  readonly successfullySentToVaultDialog
  readonly exportMetadataDialog
  readonly pdfDownloadDialog
  readonly downloadTestPackageDialog
  readonly sendTestManifestToVaultDialog
  readonly formsTab
  readonly removeSelector: Locator
  readonly assemblyUnitRow: Locator
  readonly showCommentsButton: Locator
  readonly downloadCommentsButton: Locator
  readonly downloadCommentsDialog
  readonly downloadCSVButton: Locator
  readonly formsOnlyButton: Locator
  readonly formsAndAssetsButton: Locator
  readonly previewNoAssetWarning: Locator
  readonly previewDuplicateAssemblyUnitsWarning: Locator
  readonly subFormCodeInput: Locator
  readonly systemFormCodeInput: Locator
  readonly adminCodeInput: Locator
  readonly formCodesSaveButton: Locator
  readonly formRenameMenuItem: Locator
  readonly addAssemblyUnitsToFormSectionModalInput: Locator
  readonly addAssemblyUnitToFormSectionModalAddButton: Locator
  readonly addAssemblyUnitToFormSectionModalDoneButton: Locator
  readonly exportFormDialog
  readonly formCodeSaveButton: Locator
  readonly formStageDropdown: Locator
  readonly formStageDropdownIcon: Locator
  readonly addToFormWarningDialog: Locator
  readonly addToFormWarningContinueButton: Locator

  readonly commentsTab: Comments<object>

  override url({ subject, id }: { subject: string; id?: string }): string {
    return `/ap/${formatSubjectForUrl(subject)}/forms${id ? `/${id}` : ''}`
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5437 - Determine what matcher(s), if any, are needed
  ]

  formName(formName: string): Locator {
    return this.page.getByLabel(`form ${formName}`)
  }
  openFormButton(formName: string): Locator {
    return this.page
      .getByRole('group', { name: `${formName} ` })
      .getByRole('button', { name: 'Open Form' })
  }
  columnHeader(columnHeaderName: string): Locator {
    return this.page
      .getByRole('tabpanel')
      .getByRole('columnheader', { name: columnHeaderName, exact: true })
      .first()
  }
  formBadgeAssetCount(formName: string, numberOfAUs: number): Locator {
    return this.page
      .getByRole('group', { name: `${formName} ` })
      .locator('div')
      .filter({ hasText: `${numberOfAUs} Assembly Unit` })
  }
  clickFormDropdown(tabName: string): Locator {
    return this.page.getByRole('tabpanel', { name: tabName }).locator('.cb-dropdown')
  }

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({
      page,
      structureType: 'Form',
      moreActionsDropdownItemNames,
      rowMoreDropdownItemNames,
    })

    this.formRenameMenuItem = page.getByRole('menuitem', {
      name: 'Form',
    })
    this.formsTitle = page.locator('.header-title')
    this.formRenameTextbox = page.getByLabel('Form Name')
    this.applyButtonFormModal = page.getByRole('button', { name: 'Apply' })
    this.addExamDirectionsButton = page.getByRole('button', { name: 'Add exam directions' })
    this.addSectionDirectionsButton = page.getByRole('button', { name: 'Add section directions' })
    this.addAssemblyUnitsButton = page.getByRole('button', { name: 'Add assembly units' })
    this.previewFormButton = page.getByRole('button', { name: 'Preview' })
    this.moreActionsButton = page.getByRole('button', { name: 'Actions' })
    this.moreActionsRenameMenuItem = page.getByRole('menuitem', { name: 'Rename' })
    this.moreActionsAddMenuItem = page.getByRole('menuitem', { name: 'Add' })
    this.moreActionsAddAssemblyUnitsMenuItem = page.getByRole('menuitem', {
      name: 'Assembly Units',
    })
    this.moreActionsAddButton = page.getByRole('menuitem', { name: 'Add' })
    this.moreActionsAddReplaceDirectionMenuItem = page.getByRole('menuitem', {
      name: 'Directions (add or replace)',
    })
    this.moreActionsArchiveMenuItem = page.getByRole('menuitem', { name: 'Archive' })
    this.moreActionsDownloadMenuItem = page.getByRole('menuitem', {
      name: 'Download',
    })
    this.moreActionsDownloadPDFMenuItem = page.getByRole('menuitem', {
      name: 'PDF (.pdf)',
    })
    this.moreActionsDownloadTestPackageMenuItem = page.getByRole('menuitem', {
      name: 'Test Package (.json)',
    })
    this.moreActionsDownloadMetadataMenuItem = page.getByRole('menuitem', {
      name: 'Metadata (.csv)',
    })
    this.moreActionsDownloadFormScoringCSV = page.getByRole('menuitem', {
      name: 'Scoring Report (.csv)',
    })
    this.moreActionsSendTestManifestMenuItem = page.getByRole('menuitem', {
      name: 'Send Test Manifest',
    })
    this.formBadge = page.locator('.form-badge')
    this.detailsTab = page.getByRole('tab', { name: 'Details' })
    this.examDirectionsTab = page.getByRole('tab', { name: 'Exam Directions' })
    this.sectionOneTab = page.getByRole('tab', { name: 'Section 1' })
    this.sectionTwoTab = page.getByRole('tab', { name: 'Section 2' })
    this.detailsContainer = page.getByRole('tabpanel')
    this.attachmentsContainer = new Attachments({
      scope: this.detailsContainer,
      setupContent: () => ({}),
    })
    this.addAssemblyUnitsToFormModalInput = page
      .getByRole('dialog', { name: 'Add Assembly Units to your form' })
      .locator('#searchBar')
    this.addAssemblyUnitToFormModalAddButton = page
      .getByRole('dialog', { name: 'Add assembly units to your form' })
      .getByText('Add +')
    this.addAssemblyUnitToFormModalDoneButton = page.getByRole('button', { name: 'Done' })

    this.addAssemblyUnitsToFormSectionModalInput = page
      .getByRole('dialog', { name: 'Add Assembly Units to Form' })
      .getByRole('textbox', { name: 'cb-ic-nectar-search-bar-input' })
    this.addAssemblyUnitToFormSectionModalAddButton = page
      .getByRole('dialog', { name: 'Add Assembly Units to Form' })
      .getByText('Add +')
    this.addAssemblyUnitToFormSectionModalDoneButton = page.getByRole('button', { name: 'Add' })
    this.archiveFormArchiveButton = page.getByRole('button', { name: 'Archive' })
    this.archivedBadge = page.locator('.archived-label')
    this.removeDirectionDialog = new Dialog({
      name: 'Remove Direction',
      scope: this.mainLocator,
      buttonNames: {
        delete: 'Delete',
        cancel: 'Cancel',
        remove: 'Remove Direction',
      },
      setupContent: () => ({}),
    })
    this.removeAssemblyUnitDialog = new Dialog({
      name: 'Remove Assembly Unit',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        remove: 'Remove Assembly Unit',
        delete: 'Delete',
      },
      setupContent: () => ({}),
    })

    this.formsTab = new Tab({
      scope: this.mainLocator,
      name: 'Exam Directions',
      setupContent: (tabPanel) => ({
        itemButton: tabPanel.locator('dropdown'),
        stimulusButton: tabPanel.getByRole('button', { name: 'Stimulus' }),
        generalButton: tabPanel.getByRole('button', { name: 'General' }),
        textSection: tabPanel,
      }),
    })
    this.tableColumnsDropdown = this.page.getByRole('button', { name: 'Table Columns' })
    this.updateButton = this.page
      .getByRole('region', { name: 'Table Columns' })
      .getByRole('button', { name: 'Update' })

    this.resetButton = this.page.getByRole('button', { name: 'Reset' })
    this.tableColumnsRegion = this.page.getByRole('region', { name: 'Table Columns' })
    this.removeSelector = page.getByRole('menuitem', { name: 'Remove' })
    this.assemblyUnitRow = page.getByRole('button', { name: 'Assembly Unit' })
    this.addToFormWarningDialog = this.page.getByRole('dialog', {
      name: 'Add to Form Warning Dialog',
    })
    this.addToFormWarningContinueButton = this.page.getByRole('button', { name: 'Continue' })

    // pdf download
    this.pdfDownloadDialog = new Dialog({
      name: 'Download PDF',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    // csv export
    this.exportMetadataDialog = new Dialog({
      name: 'Download CSV',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        export: 'Export',
      },

      setupContent: (contentContainer) => ({
        includeRowsForItemMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for item metadata',
        }),
        includeRowsForAssetMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for asset metadata',
        }),
        includeRowsForStimuliMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for stimuli metadata',
        }),
        includeRowsForDeactivatedItemsMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for deactivated items metadata',
        }),
      }),
    })

    //test package
    this.downloadTestPackageDialog = new Dialog({
      name: 'Download Test Package',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    this.exportFormDialog = new Dialog({
      name: 'Download CSV',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        export: 'Export',
      },
      setupContent: () => ({}),
    })

    this.failedToExportDialog = page.getByRole('dialog', { name: 'Failed to Export' })
    this.showCommentsButton = this.mainLocator.getByRole('button', { name: 'Show Comments' })

    this.commentsTab = new Comments({
      scope: this.mainLocator.getByRole('tabpanel'),
      setupContent: () => ({}),
    })
    this.downloadCommentsButton = page.getByRole('button', { name: 'Forms Comments button' })
    this.downloadCSVButton = page.getByRole('button', { name: 'Download' })
    this.formsOnlyButton = page.getByRole('radio', { name: 'Form Comments' })
    this.formsAndAssetsButton = page.getByRole('radio', {
      name: 'Form & Asset Comments',
    })

    const downloadCommentsDialogTitlename = 'Download Comments'
    this.downloadCommentsDialog = new Dialog({
      name: downloadCommentsDialogTitlename,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    this.previewNoAssetWarning = page.getByRole('heading', {
      level: 6,
      name: 'Failed to Preview: Unable to preview because there are no assets to preview.',
    })
    this.previewDuplicateAssemblyUnitsWarning = page.getByRole('heading', {
      level: 6,
      name: /Failed to Preview: Unable to preview due to duplicate assembly units:/,
    })
    this.subFormCodeInput = this.detailsContainer.getByRole('textbox', { name: 'Sub Form Code' })
    this.systemFormCodeInput = this.detailsContainer.getByRole('textbox', {
      name: 'System Form Code',
    })
    this.adminCodeInput = this.detailsContainer.getByRole('textbox', { name: 'Admin Code' })
    this.formCodesSaveButton = this.detailsContainer.getByRole('button', { name: 'Save' })
    this.formStageDropdown = this.page.getByTestId('form-stage-dropdown')
    this.formStageDropdownIcon = this.formStageDropdown.locator('svg').nth(1)
    this.formCodeSaveButton = this.page.getByTestId('form-code-save-button')

    this.sendTestManifestToVaultDialog = new Dialog({
      name: 'Send Test Manifest to vault',
      scope: this.mainLocator,
      buttonNames: {
        close: 'Close',
        send: 'Send',
      },
      setupContent: () => ({}),
    })

    this.failedToSendTestManifestToVaultDialog = new Dialog({
      name: 'Failed to Send',
      scope: this.mainLocator,
      buttonNames: {
        close: 'Close',
        retry: 'Retry',
      },
      setupContent: () => ({}),
    })

    this.successfullySentToVaultDialog = new Dialog({
      name: 'Successfully Sent to Vault',
      scope: this.mainLocator,
      buttonNames: {
        ok: 'OK',
      },
      setupContent: () => ({}),
    })
  }

  getFormStageOptionCheckbox(stageName: string): Locator {
    return this.page.getByRole('option', { name: stageName, exact: true }).getByRole('checkbox')
  }

  getFormStagePill(stageName: string): Locator {
    return this.page.getByTestId(`form-stage-pill-for-${stageName}`)
  }
  assemblyUnitButton(auName: string): Locator {
    return this.page.getByRole('button', { name: `Assembly Unit - ${auName}` })
  }
}
