import { kebabCase } from 'case-anything'
import { AssetContainingStructureCard, Dialog } from '@e2e/components/common'
import type { DestructuredParams } from '@e2e/types'
import { formatSubjectForUrl } from '@e2e/util/urlUtils'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

type StructureTypes = 'Collection' | 'Assembly Unit' | 'Form' | 'AU'
export abstract class AssetContainingStructureListPageBase<
  Structure extends StructureTypes
> extends BasePage {
  readonly structureType: Structure

  // Reassign props with 'Structure' to new name/property in subclass
  protected readonly newStructureDialog

  protected readonly newStructureButton: Locator

  readonly titleHeading: Locator

  override url({ subject }: { subject: string }): string {
    const type = this.structureType === 'AU' ? 'au' : `${kebabCase(this.structureType)}s`
    return `/ap/${formatSubjectForUrl(subject)}/${type}`
  }

  getCard({ name }: { name: string }) {
    return this.createComponent(AssetContainingStructureCard, {
      name: `${this.structureType} card: ${name}`,
    })
  }

  getAllCardsLocator() {
    // Little hacky but gets the desired result and is abstracted in this method
    return this.getCard({ name: '' }).root
  }

  constructor({
    page,
    structureType,
  }: DestructuredParams<typeof BasePage> & {
    structureType: Structure
  }) {
    super({ page })

    this.structureType = structureType

    this.newStructureDialog = new Dialog({
      name: `New ${structureType}`,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: () => ({
        nameInput: this.mainLocator.getByRole('textbox', { name: `${structureType} Name` }),
      }),
    })

    this.newStructureButton = this.mainLocator.getByRole('button', { name: `Create new` })
    this.titleHeading = this.mainLocator.getByRole('heading', {
      level: 1,
      name: `${structureType}s`,
    })
  }
}
