import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class AdminFeaturesPage extends BasePage {
  readonly nbspSwitch: Locator
  readonly addUserButton: Locator
  readonly exportCommentsCsv: Locator

  override url(): string {
    return '/admin/features'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // No network call waits used or needed right now for this page
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })

    this.nbspSwitch = this.mainLocator.getByRole('switch', {
      name: 'Count Non Breaking Spaces In Asset Editor',
    })

    this.exportCommentsCsv = this.mainLocator.getByRole('switch', {
      name: 'Export Comment Csv',
    })
  }

  getFeatureFlagSwitch({ name }: { name: string }) {
    return this.mainLocator.getByRole('switch', { name })
  }

  async setFeatureFlag({ name, checked }: { name: string; checked: boolean }) {
    const switchLocator = this.getFeatureFlagSwitch({ name })
    if (checked) {
      await switchLocator.check()
    } else {
      await switchLocator.uncheck()
    }
  }
}
