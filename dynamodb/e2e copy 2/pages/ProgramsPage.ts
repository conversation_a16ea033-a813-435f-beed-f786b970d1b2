import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class ProgramsPage extends BasePage {
  readonly allRows: Locator

  override url(): string {
    return '/programs'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // No network call waits currently utilized or needed, except for ones
    // specific to the navigation layout which is unrelated here
  ]

  constructor({ ...args }: DestructuredParams<typeof BasePage>) {
    super({ ...args })

    this.allRows = this.mainLocator.getByRole('row')
  }
}
