import { AdminUsersAddUserDialog, AdminUsersFilters } from '@e2e/components/admin'
import type { DestructuredParams } from '@e2e/types'
import { endpointGlobs } from '@e2e/util/urlUtils'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class AdminUsersPage extends BasePage {
  readonly root: Locator
  readonly tableRows: Locator
  readonly backButton: Locator
  readonly filterSection: AdminUsersFilters
  readonly addUserButton: Locator
  readonly addUserDialog: AdminUsersAddUserDialog

  override url(): string {
    return '/admin/users'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    endpointGlobs.USERS_READ_MANY,
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })

    this.tableRows = this.mainLocator.getByRole('table').getByRole('row')
    this.backButton = this.mainLocator.getByRole('link', { name: 'Back to Admin' })
    this.filterSection = this.createComponent(AdminUsersFilters)
    this.addUserButton = this.mainLocator.getByRole('button', { name: 'Add users' })
    this.addUserDialog = this.createComponent(AdminUsersAddUserDialog)
  }
}
