import { AssetListFilter, AssetListTable } from '@e2e/components/asset-list'
import type { DestructuredParams } from '@e2e/types'
import { endpointGlobs } from '@e2e/util/urlUtils'
import { BasePage } from './BasePage'

// Used as a base for the pages looking at a course's assets
export abstract class CourseAssetsBasePage extends BasePage {
  readonly table: AssetListTable

  async goToPage(pageNumber: number) {
    await this.mainLocator.getByRole('link', { name: `${pageNumber}`, exact: true }).click()
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    endpointGlobs.ASSETS_READ_MANY,
  ]

  constructor({ page, ...args }: DestructuredParams<typeof BasePage>) {
    super({ page, ...args })

    this.table = this.createComponent(AssetListTable)
  }

  getFilterByName(name: string) {
    return this.createComponent(AssetListFilter, { name })
  }
}
