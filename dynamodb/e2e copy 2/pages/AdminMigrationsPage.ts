import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

enum MigrationButtons {
  SyncMetadataNames = 'syncMetadataNames',
  UpdateLayouts = 'updateLayouts',
  TransitionAssetsToLeadershipReview = 'transitionAssetsToLeadershipReview',
  MigrateAssetAndItemIdsToNewFormat = 'migrateAssetAndItemIdsToNewFormat',
}

const migrationButtonNames = {
  [MigrationButtons.SyncMetadataNames]: 'sync_metadata_names',
  [MigrationButtons.UpdateLayouts]: 'update_layouts',
  [MigrationButtons.TransitionAssetsToLeadershipReview]: 'afam_rename',
  [MigrationButtons.MigrateAssetAndItemIdsToNewFormat]:
    'transition_precalc_assets_to_leadership_review',
}

export class AdminMigrationsPage extends BasePage {
  readonly root: Locator
  readonly backButton: Locator
  readonly migrationButtons: { [key in MigrationButtons]: Locator }

  override url(): string {
    return '/admin/migrations'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // No network call waits used or needed right now for this page
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })

    this.backButton = this.mainLocator.getByRole('link', { name: 'Back to Admin' })
    this.migrationButtons = Object.values(MigrationButtons).reduce(
      (obj, button) => ({
        ...obj,
        [button]: this.mainLocator.getByRole('button', {
          name: migrationButtonNames[button],
        }),
      }),
      {} as { [key in MigrationButtons]: Locator }
    )
  }
}
