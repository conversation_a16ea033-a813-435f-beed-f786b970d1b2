import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class NewAssetPage extends BasePage {
  readonly courseCombobox: Locator
  readonly createButton: Locator
  readonly quantityInput: Locator

  override url(): string {
    return '/new-asset'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // Not waiting for responses currently in any tests
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })

    this.courseCombobox = this.mainLocator.getByRole('combobox', { name: 'Course' })
    this.createButton = this.mainLocator.getByRole('button', { name: 'Create' })
    this.quantityInput = this.mainLocator.getByTestId('new-asset-quantity-input-stepper')
  }

  getQuestionTypeRadio(options: Parameters<Locator['getByRole']>[1]) {
    return this.mainLocator.getByRole('radio', options)
  }

  getStimulusOptionRadio() {
    return this.mainLocator.getByRole('radio', { name: 'No, keep as stimuli' })
  }
}
