import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class AdminPage extends BasePage {
  readonly usersButton: Locator
  readonly migrationsButton: Locator
  readonly featuresButton: Locator
  readonly reportsButton: Locator

  override url(): string {
    return '/admin'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // No network call waits used or needed right now for this page
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })

    this.usersButton = this.mainLocator.getByRole('link', { name: 'Users' })
    this.migrationsButton = this.mainLocator.getByRole('link', { name: 'Migrations' })
    this.featuresButton = this.mainLocator.getByRole('link', { name: 'Features' })
    this.reportsButton = this.mainLocator.getByRole('link', { name: 'Reports' })
  }
}
