import { kebabCase } from 'case-anything'
import { AssetContainingStructureTable, Dialog, Dropdown } from '@e2e/components/common'
import type { DestructuredParams } from '@e2e/types'
import type { NonNullableLocatorName } from '@e2e/types/typeUtils'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

// Just a convenient place to store common stuff, if anything is or becomes specific
// to one of the structures, feel free to split out or put there too
type StructureTypes = 'Collection' | 'Assembly Unit' | 'Form' | 'AU'
export abstract class AssetContainingStructurePageBase<
  Structure extends StructureTypes,
  MoreActionsDropdownItemsType extends { [key: string]: NonNullableLocatorName },
  RowMoreDropdownItemsType extends { [key: string]: NonNullableLocatorName }
> extends BasePage {
  readonly structureType: Structure

  readonly renameDialog
  readonly restoreDialog
  readonly archiveDialog

  readonly removeDialog

  readonly topHeading: Locator

  readonly previewButtonGeneric: Locator

  readonly moreActionsDropdown

  readonly table

  override url({ subject, id }: { subject: string; id: string }): string {
    const type = this.structureType === 'AU' ? 'au' : `${kebabCase(this.structureType)}s`
    return `/ap/${kebabCase(subject)}/${type}/${id}`
  }

  constructor({
    page,
    structureType,
    moreActionsDropdownItemNames,
    rowMoreDropdownItemNames,
  }: DestructuredParams<typeof BasePage> & {
    structureType: Structure
    moreActionsDropdownItemNames: MoreActionsDropdownItemsType
    rowMoreDropdownItemNames: RowMoreDropdownItemsType
  }) {
    super({ page })

    this.structureType = structureType

    this.renameDialog = new Dialog({
      name: `New Name`,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: () => ({
        nameInput: this.mainLocator.getByRole('textbox', { name: `${structureType} Name` }),
      }),
    })

    const archiveDialogTitleName = 'Archive assembly unit'
    this.archiveDialog = new Dialog({
      name: archiveDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        close: 'Close',
        archive: 'Archive',
      },
      // Due to the cancel button being called Close instead
      exactNames: true,
      setupContent: () => ({}),
    })

    const restoreDialogTitleName = 'Restore assembly unit'
    this.restoreDialog = new Dialog({
      name: restoreDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Close',
        restore: 'Restore',
      },
      exactNames: true,
      setupContent: () => ({}),
    })

    const removeDialogTitleName = 'Are you sure you wish to remove '
    this.removeDialog = new Dialog({
      name: removeDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        remove: 'Remove',
      },
      setupContent: () => ({}),
    })

    this.topHeading = this.mainLocator.getByRole('heading', { level: 1 })

    this.previewButtonGeneric = this.mainLocator.getByRole('button', { name: 'Preview' })
    this.moreActionsDropdown = new Dropdown({
      name: 'Actions',
      scope: this.mainLocator,
      itemNames: moreActionsDropdownItemNames,
    })

    this.table = new AssetContainingStructureTable({
      name: `${structureType} Content`,
      scope: this.mainLocator,
      dropdownItemNames: rowMoreDropdownItemNames,
    })
  }

  async getAssetCount() {
    const assetCountText = await this.page.getByLabel('nested-table-count').textContent()
    const { count } = assetCountText?.match(/(?<count>\d+) Assets/)?.groups || { count: '0' }
    return parseInt(count || '0')
  }
}
