import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { AssetContainingStructureListPageBase } from './AssetContainingStructureListPageBase'
import type { BasePage } from './BasePage'

export class CollectionListPage extends AssetContainingStructureListPageBase<'Collection'> {
  readonly newCollectionDialog

  readonly newCollectionButton: Locator

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5432 - Determine what matcher(s), if any, are needed
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page, structureType: 'Collection' })

    // We could also just switch it to newButton and newDialog, but wasn't sure
    // that would be clear enough
    this.newCollectionDialog = this.newStructureDialog

    this.newCollectionButton = this.newStructureButton
  }
}
