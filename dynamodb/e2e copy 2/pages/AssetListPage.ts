import { kebabCase } from 'case-anything'
import { expect } from '@e2e/cb-test'
import { Dialog, Dropdown, TransitionDialog } from '@e2e/components/common'
import { ComboBox } from '@e2e/components/common/ComboBox'
import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import type { BasePage } from './BasePage'
import { CourseAssetsBasePage } from './CourseAssetsBasePage'

export class AssetListPage extends CourseAssetsBasePage {
  readonly exportMetadataDialog
  readonly transitionDialog
  readonly bulkUpdateDialog
  readonly sendToVaultDialog
  readonly newCollectionDialog
  readonly searchCollectionDialog

  readonly actionsDropdown
  readonly nestedActionsDropdown

  /** TODO potential refactor start */
  readonly associationsButtons: Locator
  readonly associationsDialog
  readonly tableColumnsDropdown: Locator
  readonly updateButton: Locator
  readonly resetButton: Locator
  readonly tableColumnsRegion: Locator
  readonly associationLinkOnAssemblyyUnit: Locator
  readonly clearFilterButton: Locator
  /** TODO potential refactor end */

  override url({ subject }: { subject: string }): string {
    return `/ap/${kebabCase(subject)}/assets`
  }

  // TODO - Move or remove like others
  directionToAddToFormStatusBar(formName: string): Locator {
    return this.page.getByRole('status').filter({ hasText: `Add Directions to "${formName}"` })
  }

  // TODO - Move or remove like others
  async addDirectionsToForm(numDirections: number) {
    await this.page.getByRole('button', { name: `Add ${numDirections} directions to form` }).click()
  }

  // TODO - Remove "verify"/expect containing method, or refactor to return the
  // count and do the expect in the test, and move the refactored method
  async verifyDirectionCount(numExamDirections: number) {
    const element = this.page
      .getByRole('status')
      .filter({ hasText: `${numExamDirections} directions added to` })
    await expect(element).toBeVisible()
  }

  // TODO - Remove "verify"/expect containing method, or refactor to return the
  // count and do the expect in the test, and move the refactored method
  async verifyAssemblyUnitCountInFormSection(numAssemblyUnits: number) {
    const elements = await this.page.locator('#count-of-assets').all()
    const directionCountText = await elements[0]?.textContent()
    expect(directionCountText).toContain(`${numAssemblyUnits} Assembly Units`)
  }

  constructor({ page, ...args }: DestructuredParams<typeof BasePage>) {
    super({ page, ...args })

    this.exportMetadataDialog = new Dialog({
      name: 'Download metadata',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        export: 'Export',
      },
      setupContent: (contentContainer) => ({
        includeRowsForItemMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for item metadata',
        }),
        includeRowsForAssetMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for asset metadata',
        }),
        includeRowsForStimuliMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for stimuli metadata',
        }),
        metadataVersion: new ComboBox({
          name: 'Metadata version selection',
          scope: contentContainer,
        }),
      }),
    })

    this.transitionDialog = TransitionDialog.create({ scope: this.mainLocator })

    this.sendToVaultDialog = new Dialog({
      name: 'Send to Vault',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        send: 'Send',
      },
      setupContent: () => ({}),
    })

    this.newCollectionDialog = new Dialog({
      name: 'New Collection',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Close',
        apply: 'Apply',
      },
      setupContent: () => ({
        nameInput: this.mainLocator.getByRole('textbox', { name: 'Collection Name' }),
      }),
    })

    this.searchCollectionDialog = new Dialog({
      name: 'Add Assets to a Collection',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        add: 'Add',
      },
      exactNames: true,
      setupContent: (dialog) => ({
        searchBar: dialog.getByRole('textbox', { name: 'cb-ic-nectar-search-bar-input' }),
        getSearchResultByName: (name: string) =>
          dialog.getByRole('button', { name: `cb-ic-nectar-search-bar-suggestion-${name}` }),
        getSearchResultByIndex: (index: number) =>
          dialog.getByRole('button', { name: /cb-ic-nectar-search-bar-suggestion/ }).nth(index),
      }),
    })

    this.bulkUpdateDialog = new Dialog({
      name: 'Update metadata',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        save: 'Save',
      },
      setupContent: async (dialog) => {
        const metadataField = new ComboBox({ name: 'step selection', scope: dialog })
        const metadataValue = dialog.getByLabel('value container')
        return {
          metadataField,
          metadataValue,
        }
      },
    })

    this.actionsDropdown = new Dropdown({
      name: 'Actions',
      scope: this.mainLocator,
      itemNames: {
        add: 'Add',
        transition: 'Transition',
        bulkUpdate: 'Update Metadata',
        sendToVault: 'Send to vault',
        download: 'Download',
        archive: 'Archive',
      },
      menuRole: 'region',
    })

    this.nestedActionsDropdown = new Dropdown({
      name: 'Actions',
      scope: this.mainLocator,
      itemNames: {
        addAssetToNewCollection: '...to a new Collection',
        addAssetToExistingCollection: '...to an existing Collection',
      },
      menuRole: 'region',
    })

    /** TODO potential refactor start */
    this.tableColumnsDropdown = this.page.getByRole('button', { name: 'Table Columns' })
    this.updateButton = this.page.getByRole('button', { name: 'Update' })
    this.resetButton = this.page.getByRole('button', { name: 'Reset' })
    this.tableColumnsRegion = this.page.getByRole('region', { name: 'Table Columns' })
    this.clearFilterButton = page.getByRole('button', { name: 'Clear All' })
    this.associationsButtons = page.getByRole('button', { name: /\d+ Associations/ })

    this.associationLinkOnAssemblyyUnit = page
      .getByRole('button', { name: 'Assembly Unit' })
      .getByRole('button', { name: 'Associations' })

    this.associationsDialog = new Dialog({
      name: /Associations for [a-zA-Z0-9]+/,
      scope: this.mainLocator,
      buttonNames: {
        cancel: '',
        remove: 'Close',
      },
      exactNames: true,
      setupContent: () => ({}),
    })
    /** TODO potential refactor end */
  }

  async getAssetCounts() {
    const topSectionText = await this.page.locator('#assets-list-top-section').textContent()
    const regex = /(?<all>All )?(?:(?<selected>\d+) of )?(?<total>\d+) result/
    const { all, selected, total } = topSectionText?.match(regex)?.groups || {}
    const areAllSelected = !!all
    const totalNum = total && +total
    return {
      selected: areAllSelected ? totalNum : selected && +selected,
      total: totalNum,
      areAllSelected,
    }
  }
}
