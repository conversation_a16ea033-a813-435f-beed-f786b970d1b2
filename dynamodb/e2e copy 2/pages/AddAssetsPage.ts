import { kebabCase } from 'case-anything'
import type { DestructuredParams } from '@e2e/types'
import { formatSubjectForUrl } from '@e2e/util/urlUtils'
import type { Locator } from '@playwright/test'
import type { BasePage } from './BasePage'
import { CourseAssetsBasePage } from './CourseAssetsBasePage'

// Need to determine if Form is compatible, and even rename/refactor things as needed
type StructureTypes = 'Collection' | 'Assembly Unit' | 'Form'
export class AddAssetsPage extends CourseAssetsBasePage {
  readonly structureType: StructureTypes

  readonly doneButton: Locator
  readonly lastModifiedButton: Locator
  readonly statusBar: Locator
  readonly replaceButton: Locator

  override url({ subject, id }: { subject: string; id: string }): string {
    return `/ap/${formatSubjectForUrl(subject)}/${kebabCase(this.structureType)}s/${id}/add-assets`
  }

  constructor({
    page,
    structureType,
    ...args
  }: DestructuredParams<typeof BasePage> & { structureType: StructureTypes }) {
    super({ page, ...args })

    this.structureType = structureType

    this.doneButton = this.mainLocator.getByRole('button', { name: 'Done' })
    this.lastModifiedButton = this.mainLocator.getByRole('button', { name: 'Last Modified' })
    this.statusBar = this.page.getByTestId('add-assets-status-bar')
    this.replaceButton = this.page.getByRole('button', { name: 'Replace' })
  }

  getAddAssetsStatusBar(collectionOrAssemblyName: string): Locator {
    return this.mainLocator
      .getByRole('status')
      .filter({ hasText: `Add Assets to "${collectionOrAssemblyName}"` })
  }

  getAddAssetsButton({ assetCount }: { assetCount: number }) {
    const name = `Add ${assetCount} assets to ${this.structureType.toLowerCase()}`
    return this.mainLocator.getByRole('button', { name })
  }
}
