import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { AssetContainingStructureListPageBase } from './AssetContainingStructureListPageBase'
import type { BasePage } from './BasePage'

export class FormListPage extends AssetContainingStructureListPageBase<'Form'> {
  readonly newFormDialog

  readonly formsListCardTitle: Locator
  readonly formListCard: Locator
  readonly newFormButton: Locator
  readonly newFormTextBox: Locator
  readonly formBulkExportDropdown: Locator
  readonly exportFormCodesReportButton: Locator
  readonly exportAllScoringReportsButton: Locator
  readonly comparisonReportButton: Locator

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5436 - Determine what matcher(s), if any, are needed
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page, structureType: 'Form' })

    // We could also just switch it to newButton and newDialog, but wasn't sure
    // that would be clear enough
    this.newFormDialog = this.newStructureDialog

    this.newFormButton = this.newStructureButton

    this.formsListCardTitle = page.getByRole('link', { name: /Form.*/i })
    this.formListCard = page.getByRole('group', { name: /Form list card.*/i })
    this.newFormButton = page.getByRole('button', { name: 'Create new' })
    this.newFormTextBox = page.getByRole('textbox', { name: 'Form Name' })
    this.formBulkExportDropdown = page.getByRole('button', { name: 'Form Bulk Exports' })
    this.exportFormCodesReportButton = page.getByRole('menuitem', {
      name: 'Export Form Codes Report',
    })
    this.exportAllScoringReportsButton = page.getByRole('menuitem', {
      name: 'Export All Scoring Reports',
    })
    this.comparisonReportButton = page.getByRole('menuitem', { name: 'Comparison Report' })
  }
}
