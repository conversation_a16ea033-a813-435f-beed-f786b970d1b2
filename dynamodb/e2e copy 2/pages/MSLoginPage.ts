import type { Page } from '@playwright/test'

// I believe it may be useful to separate out the functionality for
// obtaining the user email and password down the line, but this is
// a simple approach for current usage
const TEST_EMAIL = process.env.TEST_EMAIL || 'email'
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password'

export class MSLoginPage {
  readonly page: Page

  constructor(page: Page) {
    this.page = page
  }

  async login({ email = TEST_EMAIL, password = TEST_PASSWORD } = {}) {
    // Being a third party MS login flow, we don't need to test it specifically
    // and only ever just need to 'login'. Being a special case and only needing
    // the locators here, it was decided to not make them properties
    await this.page.getByRole('textbox', { name: 'Email' }).fill(email)
    await this.page.getByRole('button', { name: 'Next' }).click()
    await this.page.getByRole('textbox', { name: 'password' }).fill(password)
    await this.page.getByRole('button', { name: 'Sign in' }).click()
    await this.page.getByRole('button', { name: 'Yes' }).click()
  }
}
