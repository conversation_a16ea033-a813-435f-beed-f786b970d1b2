import type { DestructuredParams } from '@e2e/types'
import { formatSubjectForUrl } from '@e2e/util/urlUtils'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class CollectionsPage extends BasePage {
  readonly collectionsListCardTitle: Locator
  readonly collectionListCard: Locator
  readonly newCollectionButton: Locator
  readonly collectionsHeader: Locator
  readonly createCollectionsButton: Locator
  readonly newCollectionsTextBox: Locator
  readonly applyButtonCollectionsModal: Locator
  readonly attachDocumentButton: Locator
  readonly addAssetsButton: Locator
  readonly previewCollectionButton: Locator
  readonly collectionBadge: Locator
  readonly assetDropdown: Locator
  readonly previewAssetTitle: Locator
  readonly selectOptionDropdown: Locator
  readonly collectionPreviewNext: Locator
  readonly collectionPreviewPrevious: Locator
  readonly collectionPreviewInfoPanel: Locator
  readonly collectionPreviewShowInfoButton: Locator
  readonly collectionPreviewDownloadButton: Locator
  readonly tableColumnsDropdown: Locator
  readonly updateButton: Locator
  readonly resetButton: Locator
  readonly tableColumnsRegion: Locator

  override url({ subject }: { subject: string }): string {
    return `/ap/${formatSubjectForUrl(subject)}/collections`
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5432 - Determine what matcher(s), if any, are needed
  ]

  columnHeader(name: string): Locator {
    return this.page.getByRole('cell', { name: name, exact: true })
  }

  collectionName(collectionName: string): Locator {
    return this.page.getByLabel(`collection ${collectionName}`)
  }
  openCollectionButton(collectionName: string): Locator {
    return this.page
      .getByRole('group', { name: `${collectionName} ` })
      .getByRole('button', { name: 'Open Collection' })
  }

  collectionBadgeAssetCount(collectionName: string, numberOfAssets: number): Locator {
    return this.page
      .getByRole('group', { name: `${collectionName} ` })
      .locator('div')
      .filter({ hasText: `${numberOfAssets} Assets` })
  }
  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })
    this.collectionsListCardTitle = page.getByRole('link', { name: /Collection.*/i })
    this.collectionListCard = page.getByRole('group', { name: /Collection list card.*/i })
    this.newCollectionButton = page.getByRole('button', { name: 'Create new' })
    this.newCollectionsTextBox = page.getByRole('textbox', { name: 'Collection Name' })
    this.collectionsHeader = page.getByRole('heading', { name: 'Collection Name' })
    this.createCollectionsButton = page.getByRole('button', { name: 'Preview collection' })
    this.applyButtonCollectionsModal = page.getByRole('button', { name: 'Apply' })
    this.attachDocumentButton = page.getByRole('button', { name: 'Attach document' })
    this.addAssetsButton = page.getByRole('button', { name: 'Add assets' })
    this.previewCollectionButton = page.getByRole('button', { name: 'Preview Collection' })
    this.collectionBadge = page.locator('.collection-badge')
    this.assetDropdown = page.getByRole('group', { name: 'Navigation' }).locator('svg')
    this.previewAssetTitle = page.locator('.previewer-header-asset')
    this.selectOptionDropdown = page.locator('div[id^="react-select-2-option-"]')
    this.collectionPreviewShowInfoButton = page.getByRole('button', { name: 'Show Info' })
    this.collectionPreviewNext = page.getByRole('button', { name: 'Next' })
    this.collectionPreviewPrevious = page.getByRole('button', { name: 'Prev' })
    this.collectionPreviewDownloadButton = page.getByRole('button', { name: 'Prev' })
    this.collectionPreviewInfoPanel = page.locator('#info-panel')
    this.tableColumnsDropdown = this.page.getByRole('button', { name: 'Table Columns' })
    this.updateButton = this.page.getByRole('button', { name: 'Update' })
    this.resetButton = this.page.getByRole('button', { name: 'Reset' })
    this.tableColumnsRegion = this.page.getByRole('region', { name: 'Table Columns' })
  }
}
