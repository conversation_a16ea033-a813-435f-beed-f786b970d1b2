import { AssetEditorPreviewerContents } from '@e2e/components/asset-editor'
import type { DestructuredParams } from '@e2e/types'
import { BasePage } from './BasePage'

export class PreviewPage extends BasePage {
  readonly contents: AssetEditorPreviewerContents

  override url({ assetId }: { assetId: string }): string {
    return `/preview-window?assetId=${assetId}`
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // Unused, no wait for responses with this page yet, kind of a weird case of a
    // page too and how to open, so for now leaving this blank, can add later if desired
  ]

  constructor({ page, ...args }: DestructuredParams<typeof BasePage>) {
    super({ page, ...args })

    this.contents = this.createComponent(AssetEditorPreviewerContents)
  }
}
