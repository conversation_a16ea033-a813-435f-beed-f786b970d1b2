import {
  AssemblyUnitCreationDialog,
  Dialog,
  Dropdown,
  TransitionDialog,
} from '@e2e/components/common'
import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { AssetContainingStructurePageBase } from './AssetContainingStructurePageBase'
import type { BasePage } from './BasePage'

const moreActionsDropdownItemNames = {
  rename: 'Rename',
  addAssets: 'Add assets',
  fromAllAssets: '...from asset library',
  fromACollection: '...from a collection',
  archive: 'Archive',
  restore: 'Restore',
  duplicate: 'Duplicate',
  export: 'Download',
  exportMetadata: 'Metadata (.csv)',
  downloadPdf: 'PDF (.pdf)',
  transition: 'Transition',
  sendToVault: 'Send to vault',
  generateAU: 'Generate assembly units',
}
const rowMoreDropdownItemNames = {
  viewItems: 'View items',
  bringToTop: 'Bring to top',
  sendToBottom: 'Send to Bottom',
  remove: 'Remove',
  // Note some are when viewing items vs assets
  deactivate: 'Deactivate',
  activate: /(?<!De)Activate/, // Also locates Deactivate if simple string
}

export class NextGenAssemblyUnitAuthoringPage extends AssetContainingStructurePageBase<
  'AU',
  typeof moreActionsDropdownItemNames,
  typeof rowMoreDropdownItemNames
> {
  readonly expandAllButton
  readonly collapseAllButton
  readonly addAssetsButton
  readonly allItems
  readonly expandButton
  readonly reorderButton
  readonly dragAndDrop
  readonly editAssetOrder
  readonly dragBoxes
  readonly allAssets
  readonly applyButton
  readonly editAssetTextBox
  readonly detailsTab
  readonly attributesTable
  readonly relationshipTable
  readonly addFromCollectionsDialog
  readonly actionsDropdown
  readonly transitionDialog
  readonly sendToVaultDialog
  readonly newAssemblyUnitDialog
  readonly editAssemblyUnitAttributesDialog
  readonly editAUAttributesButton
  readonly nestedTableRow: Locator
  readonly entityTypeColumnValue: Locator
  readonly moreOptionsButton: Locator
  readonly nestedTableLink: Locator
  readonly nestedTableCount: Locator

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5434 - Determine what matcher(s), if any, are needed
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    const structureType = 'AU'
    super({
      page,
      structureType,
      moreActionsDropdownItemNames,
      rowMoreDropdownItemNames,
    })

    this.allAssets = page.getByLabel('id-column-value').getByRole('link')
    this.allItems = page.getByLabel('nested-table-row').getByText('Item', { exact: true })
    this.editAssetTextBox = page.getByLabel('nested-table-row').getByLabel('reorder-input')
    this.expandButton = page.getByRole('button', { name: 'expand-button' }).first()
    this.dragAndDrop = page.getByRole('menuitem', {
      name: 'Drag and drop assets and items',
    })
    this.editAssetOrder = page.getByRole('menuitem', {
      name: 'Edit asset sequence',
    })
    this.applyButton = page.getByRole('button', { name: 'Apply' })
    this.dragBoxes = page.getByLabel('draggable-icon')
    this.reorderButton = page.getByRole('button', { name: 'Reorder mode' })
    this.expandAllButton = page.getByRole('button', { name: 'Expand All' })
    this.collapseAllButton = page.getByRole('button', { name: 'Collapse All' })
    this.addAssetsButton = page.getByRole('button', { name: 'Add Assets' })

    this.detailsTab = page.getByRole('tab', { name: 'Details' })
    this.attributesTable = page.getByRole('grid', { name: 'Attributes-table' })
    this.relationshipTable = page.getByRole('grid', { name: 'Relationships-table' })

    this.editAUAttributesButton = page.getByRole('button', { name: 'Edit Attributes' })
    this.nestedTableRow = page.locator('.cb-ic-nectar-nested-table-row')
    this.entityTypeColumnValue = page.locator('span[aria-label="entityType-column-value"]')
    this.moreOptionsButton = page.locator('.cb-ic-nectar-more-alt-button')
    this.nestedTableLink = page.locator('.cb-ic-nectar-nested-table-link')
    this.nestedTableCount = page.getByLabel('nested-table-count')
    this.addFromCollectionsDialog = new Dialog({
      name: 'Add Assets from a Collection',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        add: 'Add',
      },
      exactNames: true,
      setupContent: (dialog) => ({
        searchBar: dialog.getByRole('textbox', { name: 'cb-ic-nectar-search-bar-input' }),
        getSearchResultByName: (name: string) =>
          dialog.getByRole('button', { name: `cb-ic-nectar-search-bar-suggestion-${name}` }),
        getSearchResultByIndex: (index: number) =>
          dialog.getByRole('button', { name: /cb-ic-nectar-search-bar-suggestion/ }).nth(index),
      }),
    })

    this.actionsDropdown = new Dropdown({
      name: 'Actions',
      scope: this.mainLocator,
      itemNames: moreActionsDropdownItemNames,
      menuRole: 'region',
    })

    this.transitionDialog = TransitionDialog.create({ scope: this.mainLocator })

    this.sendToVaultDialog = new Dialog({
      name: 'Send to Vault',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        send: 'Send',
      },
      setupContent: () => ({}),
    })

    this.newAssemblyUnitDialog = new AssemblyUnitCreationDialog({
      name: `Create a new assembly unit`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Create',
      },
      setupContent: () => ({}),
    })

    this.editAssemblyUnitAttributesDialog = new AssemblyUnitCreationDialog({
      name: `Edit Attributes`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Save',
      },
      setupContent: () => ({}),
    })
  }

  async getAssemblyUnitTitle(name: string) {
    return this.mainLocator.getByRole('heading', { name, level: 1 })
  }

  async performActionOnDropdownItem(
    itemName: string,
    actionName:
      | 'Deactivate'
      | 'Reactivate'
      | 'Remove'
      | 'Add placeholder above'
      | 'Add placeholder below'
  ) {
    await this.page.getByRole('button', { name: `Dropdown button ${itemName}` }).click()
    // Use a regex to match the text regardless of extra spaces
    await this.page.getByRole('menuitem', { name: new RegExp(actionName.trim(), 'i') }).click()
  }
  async deactivateItemIcon(itemName: string) {
    return this.page.locator(`#${itemName}-deactivated-icon`)
  }

  expandRowButton(assetName: string): Locator {
    const assetRow = this.assetTableRow(assetName)
    return assetRow.getByRole('button', { name: 'expand-button' })
  }

  assetTableRow(assetName: string): Locator {
    return this.page.getByLabel(`nested-table-row-${assetName}-`)
  }
  dropdownButton(assetName: string): Locator {
    return this.page.getByRole('button', { name: `Dropdown button ${assetName}` })
  }
  async getRowTypes() {
    return this.page.evaluate(() => {
      return Array.from(document.querySelectorAll('.cb-ic-nectar-nested-table-row')).map((row) => {
        const entityTypeElement = row.querySelector('span[aria-label="entityType-column-value"]')
        const entityType = entityTypeElement ? entityTypeElement.textContent.trim() : ''
        return {
          type: entityType.includes('Placeholder') ? 'Placeholder' : entityType,
        }
      })
    })
  }
  async getRowTypesWithDetails() {
    return this.page.evaluate(() => {
      return Array.from(document.querySelectorAll('.cb-ic-nectar-nested-table-row')).map((row) => {
        const entityTypeElement = row.querySelector('span[aria-label="entityType-column-value"]')
        const entityType = entityTypeElement ? entityTypeElement.textContent.trim() : ''
        const idElement = row.querySelector('a.cb-ic-nectar-nested-table-link')
        const id = idElement ? idElement.textContent.trim().split(' ')[0] : ''
        const sequenceElement = row.querySelector('span:nth-child(3)')
        const sequence = sequenceElement ? sequenceElement.textContent.trim() : ''
        return {
          type: entityType.includes('Placeholder') ? 'Placeholder' : entityType,
          id: id,
          sequence: sequence,
        }
      })
    })
  }
  getPlaceholderRow(): Locator {
    return this.nestedTableRow
      .filter({
        has: this.entityTypeColumnValue.filter({
          hasText: 'Placeholder',
        }),
      })
      .first()
  }
}
