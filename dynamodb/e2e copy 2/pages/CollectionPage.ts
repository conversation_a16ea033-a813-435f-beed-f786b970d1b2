import { Dialog, Dropdown, TransitionDialog } from '@e2e/components/common'
import { Attachments } from '@e2e/components/common/Attachments'
import { ComboBox } from '@e2e/components/common/ComboBox'
import { Comments } from '@e2e/components/common/Comments'
import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { AssetContainingStructurePageBase } from './AssetContainingStructurePageBase'
import type { BasePage } from './BasePage'

const moreActionsDropdownItemNames = {
  rename: 'Rename',
  addAssets: 'Add assets',
  transition: 'Transition',
  bulkUpdate: 'Update Metadata',
  download: 'Download',
  sendAssets: 'Send assets',
  sendAssetsToAU: '...to an Assembly Unit',
  sendAssetsToVault: '...to the Vault',
  exportMetadata: 'Metadata (.csv)',
  exportPdf: 'PDF (.pdf)',
  archive: 'Archive',
}
const rowMoreDropdownItemNames = {
  bringToTop: 'Bring to top',
  sendToBottom: 'Send to Bottom',
  remove: 'Remove',
  addToCollecitons: 'Add to Collections',
}

export class CollectionPage extends AssetContainingStructurePageBase<
  'Collection',
  typeof moreActionsDropdownItemNames,
  typeof rowMoreDropdownItemNames
> {
  readonly exportMetadataDialog
  readonly exportPDFDialog
  readonly transitionDialog
  readonly sendToVaultDialog
  readonly bulkUpdateDialog
  readonly continueDialog

  readonly moreActions: Locator
  readonly addAssetsButton: Locator
  readonly showCommentsButton: Locator
  readonly addCommentButton: Locator
  readonly commentsTab: Comments<object>
  readonly downloadCommentsButton: Locator
  readonly downloadCommentsDialog
  readonly downloadCSVButton: Locator
  readonly collectionsOnlyButton: Locator
  readonly collectionsAndAssetsButton: Locator

  readonly detailsTab: Locator
  readonly assetsTab: Locator

  readonly detailsContainer: Locator
  readonly attachmentsContainer: Attachments<object>
  readonly actionsDropdown
  readonly nestedTableCount: Locator
  readonly tableColumnsDropdown: Locator
  readonly updateButton: Locator
  readonly resetButton: Locator
  readonly tableRows
  readonly addToAUDialog
  readonly allAssets
  readonly reorderButton
  readonly dragAndDrop
  readonly editAssetOrder
  readonly dragBoxes
  readonly applyButton
  readonly editAssetTextBox
  readonly allCheckboxes: Locator
  readonly getColumn
  readonly getItemLocatorsForAsset

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5433 - Determine what matcher(s), if any, are needed
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({
      page,
      structureType: 'Collection',
      moreActionsDropdownItemNames,
      rowMoreDropdownItemNames,
    })

    this.getColumn = function getColumn(column: string) {
      return this.mainLocator
        .getByLabel('nested-table', { exact: true })
        .filter({ hasText: `${column}` })
        .getByText(`${column}`)
    }

    this.getItemLocatorsForAsset = async (assetId: string) => {
      const assetRow = this.page
        .getByRole('table', { name: `nested-table` })
        .getByRole('row')
        .filter({ hasText: assetId })
      const itemRows = assetRow.getByRole('row', { name: 'nested-table-child-row' })
      const alreadyExpanded = (await itemRows.count()) > 0
      const expandButton = assetRow.getByRole('button', { name: 'expand-button' })
      if (!alreadyExpanded) {
        await expandButton.click()
      }
      return itemRows
    }

    const downloadCommentsDialogTitlename = 'Download Comments'
    this.downloadCommentsDialog = new Dialog({
      name: downloadCommentsDialogTitlename,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    this.exportMetadataDialog = new Dialog({
      name: 'Download metadata',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        export: 'Export',
      },
      setupContent: (contentContainer) => ({
        includeRowsForItemMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for item metadata',
        }),
        includeRowsForAssetMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for asset metadata',
        }),
        includeRowsForStimuliMetadataCheckBox: contentContainer.getByRole('checkbox', {
          name: 'Include rows for stimuli metadata',
        }),
        metadataVersion: new ComboBox({
          name: 'Metadata version selection',
          scope: contentContainer,
        }),
      }),
    })

    this.exportPDFDialog = new Dialog({
      name: 'Download PDF',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    this.transitionDialog = TransitionDialog.create({ scope: this.mainLocator })

    this.sendToVaultDialog = new Dialog({
      name: 'Send to vault',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        send: 'Send',
      },
      setupContent: () => ({}),
    })

    this.bulkUpdateDialog = new Dialog({
      name: 'Update metadata',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        save: 'Save',
      },
      setupContent: async (dialog) => {
        const metadataField = new ComboBox({ name: 'step selection', scope: dialog })
        const metadataValue = dialog.getByLabel('value container')
        return {
          metadataField,
          metadataValue,
        }
      },
    })

    this.continueDialog = new Dialog({
      name: 'confirmation dialog',
      scope: this.mainLocator,
      buttonNames: {
        goBack: 'Go Back',
        continue: 'Continue',
      },
      setupContent: () => ({}),
    })

    this.allAssets = page.getByLabel('id-column-value').getByRole('link')
    this.allCheckboxes = this.mainLocator
      .getByRole('table', { name: 'nested-table' })
      .locator('input[type=checkbox]')
    this.tableColumnsDropdown = this.page.getByRole('button', { name: 'Table Columns' })
    this.updateButton = this.page.getByRole('button', { name: 'Update' })
    this.resetButton = this.page.getByRole('button', { name: 'Reset' })
    this.editAssetTextBox = page.getByLabel('nested-table-row').getByLabel('reorder-input')
    this.addAssetsButton = this.mainLocator.getByRole('button', { name: 'Add assets' })
    this.showCommentsButton = this.mainLocator.getByRole('button', { name: 'Show Comments' })
    this.addCommentButton = this.mainLocator.getByRole('button', { name: 'Add Comment' })
    this.downloadCommentsButton = page.getByRole('button', { name: 'Collections Comments button' })
    this.downloadCSVButton = page.getByRole('button', { name: 'Download' })
    this.collectionsOnlyButton = page.getByRole('radio', { name: 'Collection Comments' })
    this.collectionsAndAssetsButton = page.getByRole('radio', {
      name: 'Collection & Asset Comments',
    })
    this.reorderButton = page.getByRole('button', { name: 'Reorder mode' })
    this.dragBoxes = page.getByLabel('draggable-icon')
    this.editAssetOrder = page.getByRole('menuitem', {
      name: 'Edit asset sequence',
    })
    this.applyButton = page.getByRole('button', { name: 'Apply' })

    this.dragAndDrop = page.getByRole('menuitem', {
      name: 'Drag and drop assets and items',
    })
    this.actionsDropdown = new Dropdown({
      name: 'Actions',
      scope: this.mainLocator,
      itemNames: moreActionsDropdownItemNames,
      menuRole: 'region',
    })

    this.commentsTab = new Comments({
      scope: this.mainLocator,
      setupContent: () => ({}),
    })

    this.detailsTab = page.getByRole('tab', { name: 'Details' })
    this.assetsTab = page.getByRole('tab', { name: 'Assets' })

    this.detailsContainer = page.getByRole('tabpanel')
    this.attachmentsContainer = new Attachments({
      scope: this.detailsContainer,
      setupContent: () => ({}),
    })

    // TODO: get by role once Nectar has been updated
    this.nestedTableCount = page.getByLabel('nested-table-count')
    this.tableRows = this.page.getByLabel('nested-table-row')
    this.addToAUDialog = new Dialog({
      name: 'Add Assets to an Assembly Unit',
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        add: 'Add',
      },
      exactNames: true,
      setupContent: (dialog) => ({
        searchBar: dialog.getByRole('textbox', { name: 'cb-ic-nectar-search-bar-input' }),
        getSearchResultByName: (name: string) =>
          dialog.getByRole('button', { name: `cb-ic-nectar-search-bar-suggestion-${name}` }),
        getSearchResultByIndex: (index: number) =>
          dialog.getByRole('button', { name: `cb-ic-nectar-search-bar-suggestion` }).nth(index),
      }),
    })
  }
}
