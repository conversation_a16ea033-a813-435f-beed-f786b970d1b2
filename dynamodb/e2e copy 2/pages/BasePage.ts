import type { Class, SetOptional } from 'type-fest'
import { BaseComponent, BaseComponentConstructorParams } from '@e2e/components/bases/BaseComponent'
import type { DestructuredParams } from '@e2e/types'
import { Locator, Page, expect } from '@playwright/test'

export type BasePageConstructorParams = { page: Page }

export type AdditionalGoToPageWithParams = {
  /** How long to wait in ms for the loading state to go away */
  loadingTimeout?: number

  pwOptions?: Parameters<Page['goto']>[1]
}

/**
 * Base class for a "Page Object" from the page object model. These are meant
 * to represent the page for the test writer to interact with like the user
 * would interact with the page. Useful for hiding implementation details,
 * like selectors, but should not include assertions as that is done by the
 * test/developer (and any helpers for that purpose)
 */
export abstract class BasePage {
  /**
   * The Playwright `page` fixture currently in use
   */
  readonly page: Page

  /**
   * The page's expected network calls' urlOrPredicate for waitForResponse when loading the page
   */
  protected abstract responseOnLoadMatcherArray: Parameters<Page['waitForResponse']>[0][]

  // Unfortunately had to repeat this from fixture types in order for it to
  // show when using the property
  /**
   * Locator we can use to locate other elements solely within the main content
   * of the page, rather than the outer layout/navigation pieces.
   *
   * Will probably be used in place of `page` for that purpose, except when
   * specifically working with the outer layout itself, which will be their
   * own fixtures
   */
  readonly mainLocator: Locator

  /**
   * Locator of toasts that can pop up on any page
   */
  readonly toastLocator: Locator

  /**
   * Locator of toasts that can perform actions i.e. Undo
   */
  readonly toastDialogLocator: Locator

  /**
   * Locator of the breadcrumb navigation
   */
  readonly breadcrumbLocator: Locator

  /**
   * Provides the url (possibly parameterized) that this page represents.
   * Usually just the path, unless specifying the full url is necessary.
   * @example
   * url({ assetId }) => `/assets/{assetId}`
   */
  abstract url({ ...args }: { [key: string]: string }): string

  constructor({ page }: BasePageConstructorParams) {
    this.page = page
    this.mainLocator = page.getByRole('main')

    this.toastLocator = page.getByRole('alert')
    this.toastDialogLocator = page.getByRole('alertdialog')
    this.breadcrumbLocator = page.getByRole('navigation', { name: 'breadcrumbs' })
  }

  async clickBreadcrumbLink(linkName: string) {
    await this.breadcrumbLocator.getByRole('link', { name: linkName }).click()
  }

  /**
   * Method for navigating directly to the page represented by this object,
   * along with any options specified
   *
   * @param options - Options to specify when going to the page
   *
   * @example
   * await basePage.goToPageWith()
   */
  async goToPageWith({
    loadingTimeout = 30_000,
    pwOptions = {},
    ...urlParams
  }: Parameters<this['url']>[0] & AdditionalGoToPageWithParams = {}) {
    await this.doThenWaitForPageLoadNetworkCall(async () => {
      await this.page.goto(this.url({ ...urlParams }), pwOptions)

      // From 3.1, the `load` state of the app happens while the loading spinner
      // is up and unfortunately sometimes the loading is slow or doesn't even ever
      // end, necesitating this behavior to handle that
      // If long-lasting or an optimization is desired, the list pages take the
      // the longest, but the content starts loading alongside the loader, and
      // so it could just wait for any content besides the loader to know if the
      // refresh is needed
      try {
        // Try waiting for slow load
        await this.page
          .getByRole('status', { name: 'Loading ...' })
          .waitFor({ state: 'detached', timeout: loadingTimeout })
      } catch (e: any) {
        if (e?.name !== 'TimeoutError') {
          throw e
        }
        // Refresh and try one more time
        await this.page.reload()
        await this.page
          .getByRole('status', { name: 'Loading ...' })
          .waitFor({ state: 'detached', timeout: loadingTimeout })
      }
    })
  }

  async reloadThenWaitForPageLoadNetworkCall() {
    return this.doThenWaitForPageLoadNetworkCall(async () => await this.page.reload())
  }

  async doThenWaitForPageLoadNetworkCall<T>(doAction: () => T): Promise<Awaited<T>> {
    const responsePromiseArray = this.responseOnLoadMatcherArray.map((urlOrPredicate) =>
      this.page.waitForResponse(urlOrPredicate)
    )
    const result = await doAction()
    await Promise.all(responsePromiseArray)
    return result
  }

  /**
   * Create a component within the `scope` of the mainLocator, optionally
   * specifying the new component's class
   *
   * @param ComponentClass - Class of the component to create, `BaseComponent` if not specified
   * @param options - Any options to pass into the constructor; note that `scope` is automatically set to the mainLocator
   */
  createComponent<C extends Class<BaseComponent>>(
    ComponentClass: C,
    options?: SetOptional<DestructuredParams<C>, 'scope'>
  ): InstanceType<C>
  createComponent(options: BaseComponentConstructorParams): BaseComponent
  createComponent<C extends Class<BaseComponent>>(
    ComponentClassOrOptions: C | BaseComponentConstructorParams,
    options?: SetOptional<DestructuredParams<C>, 'scope'>
  ) {
    if (typeof ComponentClassOrOptions === 'function') {
      if (
        !(
          ComponentClassOrOptions.prototype instanceof BaseComponent ||
          ComponentClassOrOptions === BaseComponent
        )
      ) {
        throw new Error('createComponent can only create instances of BaseComponent')
      }
      return new ComponentClassOrOptions({ scope: this.mainLocator, ...options })
    }
    return new BaseComponent({ ...ComponentClassOrOptions })
  }

  /**
   * Convenient utility method that allows waiting for a specific condition before proceeding
   *
   * @param conditionFunction Function to run that should return a truthy value if the condition is met
   * @param messageOrOptions Message or timing options. See [expect.poll](https://playwright.dev/docs/test-assertions#expectpoll) for details
   */
  async waitForCondition<ConditionReturnType>(
    conditionFunction: () => ConditionReturnType,
    messageOrOptions?: Parameters<(typeof expect)['poll']>[1]
  ) {
    let resultOfCondition: Awaited<ConditionReturnType>

    // expect generally shouldn't be used within page objects, except if
    // needed for waiting for a condition, so abstracted this out
    await expect
      .poll(async () => (resultOfCondition = await conditionFunction()), messageOrOptions)
      .toBeTruthy()

    // @ts-expect-error Doesn't think it's assigned, but it is by expect.poll
    return resultOfCondition
  }
}
