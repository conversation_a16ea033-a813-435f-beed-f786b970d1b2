import { kebabCase } from 'case-anything'
import { AssemblyUnitCreationDialog, Dialog } from '@e2e/components/common'
import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { AssetContainingStructureListPageBase } from './AssetContainingStructureListPageBase'
import type { BasePage } from './BasePage'

export class NextGenAssemblyUnitListPage extends AssetContainingStructureListPageBase<'AU'> {
  readonly createNewButton: Locator
  readonly expandAllButton: Locator
  readonly collapseAllButton: Locator
  readonly newAssemblyUnitDialog
  readonly newBaseAssemblyUnitDialog
  readonly renameAssemblyUnitDialog
  readonly placeholderInputDialog
  readonly assetNameLink: Locator

  override url({ subject }: { subject: string }): string {
    return `/ap/${kebabCase(subject)}/au`
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // TODO: #5435 - Determine what matcher(s), if any, are needed
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page, structureType: 'AU' })

    this.createNewButton = page.getByRole('button', { name: 'Create new' })
    this.newAssemblyUnitDialog = new AssemblyUnitCreationDialog({
      name: `Create a new assembly unit`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Create',
      },
      setupContent: () => ({}),
    })
    this.newBaseAssemblyUnitDialog = new AssemblyUnitCreationDialog({
      name: `Create a new assembly unit`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Continue',
      },
      setupContent: () => ({}),
    })
    this.expandAllButton = page.getByRole('button', { name: 'Expand All' })
    this.collapseAllButton = page.getByRole('button', { name: 'Collapse All' })

    this.renameAssemblyUnitDialog = new Dialog({
      name: `Edit AU Name`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        editAUNameTextBox: contentContainer.getByRole('textbox', { name: 'Assembly Unit Name' }),
      }),
    })
    this.assetNameLink = this.page.locator('.cb-ic-nectar-nested-table-link')
    this.placeholderInputDialog = new Dialog({
      name: `Add placeholder`,
      scope: page,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        assetCount: contentContainer.getByTestId('asset-placeholders-count'),
        itemPerAssetCount: contentContainer.getByTestId('item-placeholders-per-asset-count'),
      }),
    })
  }

  async getAllAssemblyUnitListHeadingItems() {
    return this.page.getByRole('button', { name: /Hierarchy list item heading/ }).all()
  }
  getAssemblyUnitListHeadingItem(name: string) {
    return this.page.getByRole('button', {
      name: `Hierarchy list item heading ${name}`,
      exact: true,
    })
  }
  getAssemblyUnitListContentItem(name: string) {
    return this.page.getByRole('button', {
      name: `Hierarchy list item content ${name}`,
      exact: true,
    })
  }
  getAssemblyUnitListItem(name: string) {
    return this.page.getByRole('link', { name, exact: true })
  }
  assemblyUnitTableRow(nameOfAssemblyUnit: string): Locator {
    return this.page.locator('.cb-ic-nectar-hierarchy-list-item-heading', {
      hasText: nameOfAssemblyUnit,
    })
  }
  verifyValidationError(nameOfValidationError: string): Locator {
    return this.page.locator('.cb-validation-error', {
      hasText: nameOfValidationError,
    })
  }
  verifyBadgeChip(nameOfBadgeChip: string): Locator {
    return this.page.locator('.cb-ic-nectar-chip', {
      hasText: nameOfBadgeChip,
    })
  }
  getCheckboxFilter(nameOfCheckboxFilter: string): Locator {
    return this.page.getByRole('checkbox', { name: nameOfCheckboxFilter, exact: true })
  }
}
