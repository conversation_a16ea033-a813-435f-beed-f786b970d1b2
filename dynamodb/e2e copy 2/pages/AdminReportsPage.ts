import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'

export class AdminReportsPage extends BasePage {
  readonly root: Locator
  readonly backButton: Locator
  readonly courseDropdown: Locator
  readonly generateReportButton: Locator

  override url(): string {
    return '/admin/reports'
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    // No network call waits used or needed right now for this page
  ]

  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })
    this.backButton = this.mainLocator.getByRole('link', { name: 'Back to Admin' })
    this.courseDropdown = this.mainLocator.getByRole('combobox', { name: 'Course' })
    this.generateReportButton = this.mainLocator.getByRole('button', {
      name: 'Click me to do the thing',
    })
  }
}
