import type { DestructuredParams } from '@e2e/types'
import type { Locator } from '@playwright/test'
import { BasePage } from './BasePage'
export class CourseSettingsPage extends BasePage {
  // Selectors for Evaluation Metrics configuration.
  readonly displayCheckbox: Locator
  readonly fasCheckbox: Locator
  readonly minInput: Locator
  readonly maxInput: Locator
  readonly applyButton: Locator
  // Tabs on the page.
  readonly usersTab: Locator
  readonly metadataTab: Locator
  readonly evaluationMetricsTab: Locator
  readonly metricsTab: Locator
  // Region for units, topics, learning objectives, etc.
  readonly unitsRegion: Locator
  // Evaluation Metrics card selectors.
  readonly metricsCardEntryLabelMCQ: Locator
  readonly metricsCardEntryFRQSourceBased: Locator
  readonly metricsCardEntryFRQNonSourceBased: Locator
  readonly metricsCardQuestionTotals: Locator
  readonly metricsCardEntryA: Locator
  readonly metricsCardEntryB: Locator
  readonly metricsCardEntryC: Locator
  readonly metricsCardEntryD: Locator
  readonly metricsCardEntryLabelMultipleChoice: Locator
  readonly metricsCardEntryFreeResponse: Locator
  readonly metricsCardWordCount: Locator
  // Selector for the specific table row containing "1: Origins of the African Diaspora".
  readonly originsRow: Locator

  readonly metadataVersionsToggle: Locator
  readonly metadataFrameworkTable: Locator
  readonly openInSatchelLink: Locator
  readonly requestNewButton: Locator
  readonly metadataVersionDropdown: Locator
  readonly metadataVersionDropdownItem: Locator
  readonly switchMetadataDialog: Locator
  readonly switchMetadataDialogSwitchButton: Locator
  readonly offSwitch: Locator
  readonly requestNewMetadataDialog: Locator
  readonly importNewMetadataDialog: Locator

  override url(): string {
    // The URL for the course settings page.
    return '/ap/african-american-studies/course-settings'
  }
  constructor({ page }: DestructuredParams<typeof BasePage>) {
    super({ page })
    // Define selectors using the main locator as a root.
    this.displayCheckbox = this.mainLocator.getByTestId(
      'display-checkbox-1: Origins of the African Diaspora'
    )
    this.fasCheckbox = this.mainLocator.getByTestId(
      'fas-checkbox-1: Origins of the African Diaspora'
    )
    this.minInput = this.mainLocator.getByTestId('min-input-1: Origins of the African Diaspora')
    this.maxInput = this.mainLocator.getByTestId('max-input-1: Origins of the African Diaspora')
    this.applyButton = this.mainLocator.getByTestId('evaluation-metrics-apply-button')
    // Tabs.
    this.usersTab = this.mainLocator.getByRole('tab', { name: 'Users' })
    this.metadataTab = this.mainLocator.getByRole('tab', { name: 'Metadata' })
    this.evaluationMetricsTab = this.mainLocator.getByRole('tab', { name: 'Evaluation Metrics' })
    this.metricsTab = this.mainLocator.getByRole('tab', { name: 'Metrics' })
    this.unitsRegion = this.mainLocator.getByRole('region', {
      name: 'Units, Topics, Learning Objectives, and Essential Knowledge - 1',
    })
    // Evaluation Metrics card selectors.
    this.metricsCardEntryLabelMCQ = this.mainLocator.getByTestId(
      'evaluation-metrics-card-entry-value-mcq:-single-select'
    )
    this.metricsCardEntryFRQSourceBased = this.mainLocator.getByTestId(
      'evaluation-metrics-card-entry-value-frq:-source-based'
    )
    this.metricsCardEntryFRQNonSourceBased = this.mainLocator.getByTestId(
      'evaluation-metrics-card-entry-value-frq:-non-source-based'
    )
    this.metricsCardQuestionTotals = this.mainLocator.getByTestId(
      'evaluation-metrics-card-question-totals'
    )
    this.metricsCardEntryA = this.mainLocator.getByTestId('evaluation-metrics-card-entry-value-a')
    this.metricsCardEntryB = this.mainLocator.getByTestId('evaluation-metrics-card-entry-value-b')
    this.metricsCardEntryC = this.mainLocator.getByTestId('evaluation-metrics-card-entry-value-c')
    this.metricsCardEntryD = this.mainLocator.getByTestId('evaluation-metrics-card-entry-value-d')
    this.metricsCardEntryLabelMultipleChoice = this.mainLocator.getByTestId(
      'evaluation-metrics-card-entry-value-multiple-choice-questions'
    )
    this.metricsCardEntryFreeResponse = this.mainLocator.getByTestId(
      'evaluation-metrics-card-entry-value-free-response-questions'
    )
    this.metricsCardWordCount = this.mainLocator.getByTestId('evaluation-metrics-card-word-count')

    // Table row selector: locates the row with the unique text.
    this.originsRow = this.mainLocator.locator(
      'tr.cb-ic-nectar-table-row:has(td.cb-ic-nectar-table-cell:text("1: Origins of the African Diaspora"))'
    )
    this.metadataVersionsToggle = this.mainLocator
      .getByTestId('metadata-versions-toggle')
      .getByRole('switch')
    this.metadataFrameworkTable = this.mainLocator.getByTestId('metadata-framework-table')
    this.openInSatchelLink = this.mainLocator.getByRole('link', { name: 'Open in Satchel' })
    this.requestNewButton = this.mainLocator.getByRole('button', { name: 'Request New' })
    this.metadataVersionDropdown = this.mainLocator.locator('.metadata-version-dropdown')
    this.metadataVersionDropdownItem = this.mainLocator.getByTestId(
      'metadata-version-dropdown-item'
    )
    this.switchMetadataDialog = this.page.getByRole('dialog', {
      name: 'Switch Metadata Versions',
    })
    this.switchMetadataDialogSwitchButton = this.switchMetadataDialog.getByRole('button', {
      name: 'Switch',
    })
    this.offSwitch = this.mainLocator.getByRole('switch', { name: 'Off' })
    this.requestNewMetadataDialog = this.mainLocator.getByRole('dialog', {
      name: 'Request New Metadata Framework',
    })
    this.importNewMetadataDialog = this.mainLocator.getByRole('dialog', {
      name: 'Import New Metadata Framework',
    })
  }
  /**
   * Returns a cell from the origins row by its zero-based index.
   *
   * Mapping:
   * - 0: Should contain row number
   * - 1: Should contain an icon
   * - 2: Should contain course name
   * - 3: Should contain total number
   * - 4: Should contain min
   * - 5: Should contain max
   * - 6: Should contain an icon for FAS required
   */
  getOriginsRowCell(cellIndex: number): Locator {
    return this.originsRow.locator('td.cb-ic-nectar-table-cell').nth(cellIndex)
  }
  getOriginsRowFirstCell(): Locator {
    return this.getOriginsRowCell(0)
  }
  getOriginsRowSecondCellIcon(): Locator {
    return this.getOriginsRowCell(1).locator('i.cb-minus')
  }
  getOriginsRowThirdCell(): Locator {
    return this.getOriginsRowCell(2)
  }
  getOriginsRowFourthCell(): Locator {
    return this.getOriginsRowCell(3)
  }
  getOriginsRowFifthCell(): Locator {
    return this.getOriginsRowCell(4)
  }
  getOriginsRowSixthCell(): Locator {
    return this.getOriginsRowCell(5)
  }
  getOriginsRowSeventhCellIcon(): Locator {
    return this.getOriginsRowCell(6).locator('i.cb-check-fill')
  }
  /**
   * Returns the entry total element within the metrics card question totals.
   */
  getMetricsCardQuestionEntryTotal(): Locator {
    return this.metricsCardQuestionTotals.getByTestId('evaluation-metrics-card-entry-value-total')
  }
  /**
   * Returns the entry total element within the metrics card word count.
   */
  getMetricsCardWordCountEntryTotal(): Locator {
    return this.metricsCardWordCount.getByTestId('evaluation-metrics-card-entry-value-total')
  }
  // Helper methods for the Request New Metadata dialog.
  getRequestNewMetadataTextbox(): Locator {
    return this.requestNewMetadataDialog.getByRole('textbox')
  }
  getRequestNewMetadataRequestButton(): Locator {
    return this.requestNewMetadataDialog.getByRole('button', { name: 'Request' })
  }
  // Helper methods for the Import New Metadata dialog.
  getImportNewMetadataRadio(): Locator {
    return this.importNewMetadataDialog.getByRole('radio').first()
  }
  getImportNewMetadataImportButton(): Locator {
    return this.importNewMetadataDialog.getByRole('button', { name: 'Import' })
  }
}
