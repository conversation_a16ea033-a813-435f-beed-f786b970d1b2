import {
  AssetEditorAccordion,
  AssetEditorAriaHeading,
  AssetEditorEquationPlugin,
  AssetEditorLanguageAttribute,
  AssetEditorMenu,
  AssetEditorOptionsList,
  AssetEditorScreenReaderAttribute,
  AssetEditorToolbar,
} from '@e2e/components/asset-editor'
import { Dialog, TextEditor, TransitionDialog } from '@e2e/components/common'
import type { DestructuredParams } from '@e2e/types'
import { endpointGlobs, endpoints, formatSubjectForUrl } from '@e2e/util/urlUtils'
import { waitForResponseFrom } from '@e2e/util/waitForResponseFrom'
import { type Locator, expect } from '@playwright/test'
import { BasePage } from './BasePage'

export class AssetEditorPage extends BasePage {
  readonly menu: AssetEditorMenu
  readonly toolbar: AssetEditorToolbar
  readonly equationPlugin: AssetEditorEquationPlugin
  readonly ariaHeading: AssetEditorAriaHeading
  readonly screenReader: AssetEditorScreenReaderAttribute
  readonly languageAttribute: AssetEditorLanguageAttribute
  readonly archiveDialog
  readonly restoreDialog
  readonly transitionDialog
  readonly generalAccordion
  readonly modeDropdown: Locator
  readonly defaultDropdown: Locator
  readonly htmlViewDropdown: Locator
  readonly highlightDropdown: Locator
  readonly highlightClass: Locator
  readonly stimulusAccordionButton: Locator
  readonly stimulusTextBox: Locator
  readonly addAnItemButton: Locator
  readonly archivedLabel: Locator
  readonly saveStatus: Locator
  readonly associationsButtons: Locator
  readonly footnotesDialog
  readonly langRemovalDialog
  readonly textToSpeechDialog
  readonly glossDialog
  readonly associationsDialog
  readonly nbspCount: Locator
  readonly proseContainer: Locator
  readonly poetryContainer: Locator
  readonly allCommentsContainer: Locator
  readonly downloadCommentsButton: Locator
  readonly downloadCommentsDialog
  readonly externalUsersOnlyButton: Locator
  readonly downloadCSVButton: Locator
  readonly useCodeValueContainer: Locator
  readonly insertPassageReferenceDialog
  readonly metadataDialog

  override url({
    program = 'ap',
    subject,
    id,
  }: {
    program?: string
    subject: string
    id: string
  }): string {
    return `/${formatSubjectForUrl(program)}/${formatSubjectForUrl(subject)}/assets/${id}`
  }

  protected responseOnLoadMatcherArray: BasePage['responseOnLoadMatcherArray'] = [
    endpointGlobs.ASSETS_READ_ONE,
    endpointGlobs.ITEM_SET_READ_ONE,
  ]

  constructor({ page, ...args }: DestructuredParams<typeof BasePage>) {
    super({ page, ...args })

    this.menu = this.createComponent(AssetEditorMenu)
    this.toolbar = this.createComponent(AssetEditorToolbar)
    this.equationPlugin = new AssetEditorEquationPlugin({ scope: this.page })
    this.ariaHeading = new AssetEditorAriaHeading({ scope: this.page })
    this.screenReader = new AssetEditorScreenReaderAttribute({ scope: this.page })
    this.languageAttribute = new AssetEditorLanguageAttribute({ scope: this.page })

    this.nbspCount = page.getByLabel('Non Breaking Space Count')

    const glossDialogTitleName = 'Gloss'
    this.glossDialog = new Dialog({
      name: glossDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        wordExplanation: contentContainer.getByRole('textbox', {
          name: 'Gloss',
        }),
      }),
    })

    const langRemovalDialogTitleName = 'Remove Language Tag'
    this.langRemovalDialog = new Dialog({
      name: langRemovalDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        remove: 'Remove',
      },
      setupContent: () => ({}),
    })

    const footnotesDialogTitleName = 'Footnote'
    this.footnotesDialog = new Dialog({
      name: footnotesDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        markInput: contentContainer.getByRole('textbox', { name: 'Mark' }),
        footnoteInput: contentContainer.getByRole('textbox', {
          name: 'footnote input',
        }),
      }),
    })

    const textToSpeechDialogTitleName = 'Text to speech (SSML)'
    this.textToSpeechDialog = new Dialog({
      name: textToSpeechDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        remove: 'Remove tag',
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        ssmlSelect: contentContainer.getByRole('combobox', { name: 'Tag as' }),
        ssmlSubstitutionInput: contentContainer.getByRole('textbox', { name: 'substitution' }),
      }),
    })

    const insertPassageReferenceDialogTitleName = 'Insert Passage Reference'
    this.insertPassageReferenceDialog = new Dialog({
      name: insertPassageReferenceDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        apply: 'Apply',
      },
      setupContent: (contentContainer) => ({
        selectReferenceButton: contentContainer.getByRole('button', { name: 'Select Reference' }),
        selectReferenceMenu: contentContainer.getByRole('menu', { name: 'Select Reference' }),
        selectReferenceButtonRef: contentContainer.getByRole('button', { name: 'ref-1' }),
      }),
    })

    const archiveDialogTitleName = 'Confirm Archive'
    this.archiveDialog = new Dialog({
      name: archiveDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        archive: 'Archive',
      },
      setupContent: () => ({}),
    })

    const restoreDialogTitleName = 'Confirm Restore'
    this.restoreDialog = new Dialog({
      name: restoreDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        restore: 'Restore',
      },
      setupContent: () => ({}),
    })

    const downloadCommentsDialogTitlename = 'Download Comments'
    this.downloadCommentsDialog = new Dialog({
      name: downloadCommentsDialogTitlename,
      scope: this.mainLocator,
      buttonNames: {
        cancel: 'Cancel',
        download: 'Download',
      },
      setupContent: () => ({}),
    })

    const metadataDialogTitleName = 'Metadata'
    this.metadataDialog = new Dialog({
      name: metadataDialogTitleName,
      scope: this.mainLocator,
      buttonNames: {
        generalInformation: 'General Information',
        item: 'Item',
        close: 'Close',
      },
      setupContent: (contentContainer) => {
        // Helper function to ensure the key ends with a colon
        const formatKey = (key: string) => {
          return key.endsWith(':') ? key : `${key}:`
        }

        return {
          metadataRow: (metadataType: string) => {
            const formattedMetadataType = formatKey(metadataType)
            return contentContainer.getByRole('row').filter({ hasText: formattedMetadataType })
          },
          itemRow: (itemType: string) => {
            const formattedItemType = formatKey(itemType)
            return contentContainer.getByRole('row').filter({ hasText: formattedItemType })
          },
        }
      },
    })
    this.transitionDialog = TransitionDialog.create({ scope: this.mainLocator })

    this.modeDropdown = page.getByRole('button', { name: 'Authoring' })
    this.highlightDropdown = page.getByRole('menuitem', {
      name: 'Authoring (Highlight paragraphs)',
    })
    this.htmlViewDropdown = page.getByRole('menuitem', {
      name: 'HTML (read only)',
    })
    this.defaultDropdown = page.getByRole('menuitem', {
      name: 'Authoring',
      exact: true,
    })

    this.addAnItemButton = page.getByRole('button', { name: '+ Add an item' })
    this.archivedLabel = page.getByText('Archived', { exact: true })
    this.saveStatus = this.mainLocator.locator('[data-test-id="save-status"]')
    this.stimulusTextBox = page.getByRole('textbox', { name: 'Stimulus', exact: true })
    this.proseContainer = page.getByRole('textbox', { name: 'prose container' })
    this.poetryContainer = page.getByRole('textbox', { name: 'poem container' })
    this.allCommentsContainer = page.getByRole('region', { name: 'Aggregate Comments Panel' })
    this.downloadCommentsButton = page.getByRole('button', { name: 'Assets Comments button' })
    this.downloadCSVButton = page.getByRole('button', { name: 'Download' })
    this.useCodeValueContainer = page.getByRole('group', { name: 'Use code value container' })
    this.externalUsersOnlyButton = page.getByRole('radio', { name: 'External Users only' })
    this.generalAccordion = new AssetEditorAccordion({
      name: 'General',
      scope: this.mainLocator,
      setupContent: (contentRegion, createComponent) => ({
        /** Primary inputs (basically those that don't fall under metadata) */
        primaryInputs: {
          directions: createComponent(TextEditor, {
            name: 'Directions',
          }),
          assetTitle: contentRegion.getByRole('textbox', { name: 'Asset Title' }),
          assetAccnum: contentRegion.getByRole('textbox', { name: 'Asset Accnum' }),
        },
      }),
    })

    this.associationsButtons = page.getByRole('button', { name: /[a-zA-Z0-9]+ Associations/ })
    this.associationsDialog = new Dialog({
      name: /Associations for [a-zA-Z0-9]+/,
      scope: this.mainLocator,
      buttonNames: {
        cancel: '',
        remove: 'Close',
      },
      exactNames: true,
      setupContent: () => ({}),
    })
  }

  getStimulusAccordion({
    id,
    index = 0,
    type = 'Stimulus',
  }: {
    id?: string
    index?: number
    type?: 'Stimulus' | 'Source' | 'Document'
  }) {
    let name: string | RegExp
    if (id) {
      name = `${type} ${id}`
    } else {
      name = type
    }
    return new AssetEditorAccordion({
      name,
      index,
      scope: this.mainLocator,
      setupContent: (contentRegion, createComponent) => {
        const stimulusContent = type === 'Stimulus' ? 'Stimulus' : `${type} Content`
        const documentAttribution =
          type === 'Document' ? 'Document Attribution' : 'Source Attribution'
        return {
          /** Primary inputs (basically those that don't fall under metadata) */
          primaryInputs: {
            passageIntroduction: createComponent(TextEditor, {
              name: 'Passage Introduction',
            }),
            stimulusTitle: createComponent(TextEditor, { name: `${type} Title`, exact: true }),
            stimulus: createComponent(TextEditor, { name: stimulusContent, exact: true }),
            sourceAttribution: createComponent(TextEditor, {
              name: documentAttribution,
            }),
            creditLine: createComponent(TextEditor, {
              name: 'Credit Line',
            }),
            caption: createComponent(TextEditor, {
              name: 'Caption',
            }),
            sourceDocumentLabel: createComponent(TextEditor, { name: `${type} Label` }),
          },
        }
      },
    })
  }

  async isArchived(): Promise<boolean> {
    return this.archivedLabel.isVisible()
  }

  getStimulusAddButton({ type }: { type: 'stimulus' | 'source' | 'document' }) {
    return this.page.getByRole('button', { name: `+ Add a ${type}` })
  }

  // Need to make sure the page has the correct url before using this!
  // At least while it uses the url to get the asset id
  getItemAccordion({ number, index }: { number?: string | number; index?: number }) {
    // This approach still isn't ideal and won't handle drafts, but will handle
    // flakiness at least. Except for the hopefully rare case where another asset
    // is created at the same time as the item and breaks up the id numbers
    const assetId = this.page.url().split('/').pop() as string // this would never be undefined
    const { letters = '', number: assetNumberStr = '000000' } =
      assetId.match(/(?<letters>[A-Z]{3})(?<number>\d{6})/)?.groups || {}
    let name = ''
    if (number) {
      const assetNumber = +assetNumberStr
      const itemNumber = (assetNumber + +number).toString().padStart(6, '0')
      const newAssetId = letters + itemNumber
      name = newAssetId
    } else if (index !== undefined) {
      name = letters
    } else {
      throw new Error('Need to provide number or index to getItemAccordion!')
    }

    const accordion = new AssetEditorAccordion({
      name,
      index,
      scope: this.mainLocator,
      setupContent: (contentRegion, createComponent) => {
        return {
          /** Primary inputs (basically those that don't fall under metadata) */
          primaryInputs: {
            stem: createComponent(TextEditor, {
              name: 'Stem',
            }),
            optionsList: createComponent(AssetEditorOptionsList),
            itemTitle: contentRegion.getByRole('textbox', { name: 'Item Title' }),
            itemAccnum: contentRegion.getByRole('textbox', { name: 'Item Accnum' }),
            bookletResponse: contentRegion.getByRole('textbox', { name: 'Response' }),
          },
        }
      },
    })

    return Object.assign(accordion, {
      grabbableButton: accordion.root.getByRole('button', { name }),
    })
  }

  async isForQuestionType(
    questionType: 'Multiple Choice Question' | 'Free Response Question',
    subQuestionType?: 'Booklet'
  ) {
    // Switch will allow more easily supporting values like MCQ as well if we
    // desire it in the future
    switch (questionType) {
      case 'Multiple Choice Question':
        // TODO - Use better instance property when available
        //
        return (await this.mainLocator.getByRole('radio', { name: 'A', exact: true }).count()) > 0
      // Turns out this doesn't pass visibility, since bounding box is zero. While using
      // count above will work for this specifically, it will become an issue for
      // actually clicking/checking the radios
      // Easy fix (taken from sr-only class actually) is to make width/height 1px instead of 0
      // return this.mainLocator.getByRole('checkbox', { name: 'A', exact: true }).isVisible()
      case 'Free Response Question':
        // if its booklet
        // TODO - Use better locator when availble
        if (subQuestionType === 'Booklet') {
          const { bookletResponse } = this.getItemAccordion({ index: 0 }).content.primaryInputs
          return bookletResponse.isVisible()
        } else {
          return this.mainLocator.getByText('Answer').isVisible()
        }
      default:
        throw new Error(`Not an supported question type! ${questionType}`)
    }
  }

  /** Click the save button with configurable timeout. This is a cludge - saving should wait for
   * all in-memory updates. If the timeout value needs to be higher than 100ms the app is
   * broken, not the test - NL */
  async saveWithTimeout(timeoutMs = 100) {
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await this.page.waitForTimeout(timeoutMs)
    this.toolbar.saveButton.click()
  }

  async waitUntilAssetEditorStateSettles(locatorTimeOut = 3000) {
    // waits for metadata to load and the metadata panel inputs to be visible
    // this indicates that the asset editor is ready for interaction
    const responsePromise = waitForResponseFrom(this.page, endpoints.METADATA_READ_ONE)
    await responsePromise

    const courseFrameworkAlignmentTextbox = this.getItemAccordion({ number: 1 }).content.metadataTab
      .content.courseFrameworkAlignment

    await expect(courseFrameworkAlignmentTextbox).toBeVisible({ timeout: locatorTimeOut })
  }
}
