import { mkdirSync, writeFileSync, appendFileSync } from 'fs'
import { AssetEditorPage } from '@e2e/pages'
import { test as automation } from '@playwright/test'
import applyMathAttributes from './apply-math-formula-attributes/apply-math-attributes'
import parseInput from './apply-math-formula-attributes/input/input'

const INPUT_CSV = `/${process.env.APPLY_MATH_ATTRIBUTES_INPUT_FILENAME || 'local-example.csv'}`

console.info(`Starting application of math attributes to math formulas`)
console.info(`Global info for this run: `)
console.info(`Env: ${process.env.ENV}`)
console.info(`Input file name: ${INPUT_CSV}`)

const input = parseInput(INPUT_CSV)

// Create output file
mkdirSync('./output', { recursive: true })
writeFileSync('./output/asset-report.csv', 'id,reason\n')

console.info(`Processing ${input.length} assets...`)
input.forEach((asset) => {
  automation(`Process asset ${asset.id}`, async ({ context }) => {
    const assetPage = new AssetEditorPage({ page: await context.newPage() })
    console.info(`Processing ${asset.id}...`)
    try {
      const { id, reason } = await applyMathAttributes(assetPage, asset)
      appendFileSync('./output/asset-report.csv', `${id},${reason}\n`)
    } catch (error) {
      console.error(error)
      appendFileSync('./output/asset-report.csv', `${asset.id},${(error as Error)?.message}\n`)
    }
  })
})
