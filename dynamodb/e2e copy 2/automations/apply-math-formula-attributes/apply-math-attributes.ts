import type { AssetEditorPage } from '@e2e/pages'
import type { Locator, Page, Route } from '@playwright/test'
import type { ItemSet } from '@shared/schema'
import { WORKFLOW_STEPS_TERMINAL } from '@shared/types/model/workflow/types'
import type { Asset } from './input/input'

/**
 * NOTE:
 * This script works with v3-3-1
 *
 * Using this script with a future version may require some reworking.
 */

//TODO #3097 use stage instead (check if step is ready for assembly or stage is sending to vault)
const checkIfLocked = ({ workflowStep }: ItemSet) => WORKFLOW_STEPS_TERMINAL.has(workflowStep)
// if the workflow state is 'Ready For Use' or 'Sent To Vault' then the itemSet is locked

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getMathElementCount(itemSetProperty: any): number {
  if (itemSetProperty && typeof itemSetProperty === 'string') {
    return (itemSetProperty.match(/<\/math>/g) || []).length
  }

  // if the itemSetProperty is null or not a string then there are no math elements
  // to count
  return 0
}

async function countMathElementsFromApiCall(itemSet: ItemSet): Promise<number> {
  let mathElementCount = 0
  const { stimuli, items } = itemSet

  // count math elements at root level itemSet fields
  Object.values(itemSet).forEach((value) => {
    mathElementCount += getMathElementCount(value)
  })

  // loop through stimulu to count math elements
  stimuli.forEach((stimulus) => {
    Object.values(stimulus).forEach((value) => {
      mathElementCount += getMathElementCount(value)
    })
  })

  // loop through items to count math elements
  items.forEach((item) => {
    Object.values(item).forEach((itemValue) => {
      mathElementCount += getMathElementCount(itemValue)

      if (itemValue && Array.isArray(itemValue)) {
        itemValue.forEach((value) => {
          Object.values(value).forEach((nestedItemValue) => {
            mathElementCount += getMathElementCount(nestedItemValue)
          })
        })
      }
    })
  })

  return mathElementCount
}

async function checkIfNewAttributesWereAssigned(mathElement: Locator): Promise<boolean> {
  const dataMathMlAttribute = await mathElement.getAttribute('data-mathml')

  if (!dataMathMlAttribute) {
    return false
  }

  return dataMathMlAttribute.includes('alttext')
}

async function getMathElementsOnTheUI(
  accordionContainers: Locator[],
  page: Page
): Promise<Locator[]> {
  let mathElements: Locator[] = []
  for (const container of accordionContainers) {
    // check if the accordion is open. Accordion heading will either be in an <a> tag or a <div> tag
    const isClosed =
      (await container.locator('div.cb-accordion-heading.cb-active').count()) === 0 &&
      (await container.locator('a.cb-accordion-heading.cb-active').count()) === 0

    if (isClosed) {
      await container.click()
      await page.waitForTimeout(2000)
    }

    const hasRationales = await container
      .getByRole('button', { name: 'Show Rationales', exact: true })
      .all()
    if (hasRationales.length !== 0) {
      await hasRationales.at(0)?.click()
      await page.waitForTimeout(2000)
    }

    mathElements = [...mathElements, ...(await container.locator('img.Wirisformula').all())]
  }
  return mathElements
}

async function openMathEditorAndPressInsert(
  mathElement: Locator,
  page: Page,
  mathEditorId: number,
  WIRIS_END_POINT: string
): Promise<number> {
  const responsePromise = page
    .waitForResponse((response) => response.url() === WIRIS_END_POINT && response.ok(), {
      timeout: 5000,
    })
    .then(null, () => {
      const errorMessage = 'Error getting response from WIRIS'
      console.info(errorMessage)
      throw new Error(errorMessage)
    })

  // select math element
  // selecting at the given position allows playwright to open equations that are longer than their editor box,
  // i.e. ones that scroll
  await mathElement.dblclick({ position: { x: 2, y: 2 }, timeout: 3000 }).then(null, () => {
    const errorMessage = 'Could not locate math element to click'
    console.info(errorMessage)
    throw new Error(errorMessage)
  })
  await responsePromise
  await page
    .locator(`button[id='wrs_modal_button_accept[${mathEditorId}]']`)
    .waitFor({ timeout: 3000 })
    .then(null, async () => {
      const mathEditorsStillInDOM = await page
        .locator(`div.wrs_modal_wrapper.wrs_modal_desktop.wrs_closed`)
        .all()

      // if the math editor can't be found, then either the previous editors were cleared from the DOM
      // and the id was reset to 0 or the math equation is in the same box as the previous equation
      // and will use the same math editor id. So set the id to 0 or to the previous id
      mathEditorId = mathEditorsStillInDOM.length === 0 ? 0 : --mathEditorId
    })
  await page.waitForTimeout(1000)
  await page
    .locator(`button[id='wrs_modal_button_accept[${mathEditorId}]']`)
    .click({ timeout: 3000 })
    .then(null, () => {
      const errorMessage = 'Math Editor did not open'
      console.info(errorMessage)
      throw new Error(errorMessage)
    })
  await responsePromise

  return mathEditorId
}

async function saveAsset(page: Page): Promise<void> {
  const waitForSave = page
    .waitForResponse(
      async (response) => {
        await page.waitForTimeout(2000)
        const responseJson = await response.json()
        return (
          // will check for a save done through trpc
          response.request().url().includes('itemSetUpdateOne') &&
          response.ok() &&
          !responseJson.errors
        )
      },
      { timeout: 5000 }
    )
    .then(null, () => {
      const errorMessage = 'An error occurred while trying to save the asset'
      console.info(errorMessage)
      throw new Error(errorMessage)
    })

  await page.locator('use#save-Mask').click()
  await waitForSave
}

async function checkIfSavingIsActive(page: Page): Promise<boolean> {
  return (await page.locator('div.toolbar-icon-container.icon-disabled').count()) === 0
}

export default async function applyMathAttributes(
  assetPage: AssetEditorPage,
  asset: Asset
): Promise<{ id: string; reason: string }> {
  const { id: assetId } = asset
  const { page } = assetPage
  const successMessage = 'Asset ran through the automation successfully'
  const WIRIS_END_POINT =
    process.env.ENV === 'prod'
      ? 'https://wiris.itemcloudgreen-prod.collegeboard.org/editor/mathml2internal?httpstatus=true'
      : 'https://wiris.itemcloudgreen-nonprod.collegeboard.org/editor/mathml2internal?httpstatus=true'
  const routeToWatch = '**/itemSetReadOne**'
  let mathElementCount = 0
  let locked

  const routeHandler = async (route: Route) => {
    const response = await route.fetch()
    const json = await response.json()
    const itemSet = json[0].result.data

    locked = checkIfLocked(itemSet)

    mathElementCount = await countMathElementsFromApiCall(itemSet)
    route.fulfill({ response })
  }

  // intercept the GetItemSet request, make sure the asset isn't locked and count math elements
  // in the api call before continuing with a given asset
  await page.route(routeToWatch, routeHandler)

  // go to the asset editor of a given asset
  await assetPage.goToPageWith({
    assetId,
    subject: 'precalculus',
    pwOptions: {
      waitUntil: 'networkidle',
    },
  })
  await page.getByText('General Content').waitFor({ state: 'visible' })
  console.info(`Asset page is ready`)
  page.unroute(routeToWatch, routeHandler)

  // if asset is locked then return
  if (locked) {
    const message = 'Asset is locked'
    console.info(message)
    return { id: assetId, reason: message }
  }

  // if asset has zero math elements then return
  if (mathElementCount === 0) {
    const message = "The asset didn't have any math elements"
    console.info(message)
    return { id: assetId, reason: message }
  }
  console.info(`Number of math elements counted from API call: ${mathElementCount}`)

  // get all the accordion containers in the asset editor page
  const accordionContainers = await page.locator('div.cb-accordion-container div.Collapsible').all()

  // get all the math elements within each accordion container
  const mathElementsOnTheUI = await getMathElementsOnTheUI(accordionContainers, page)
  console.info(`Number of math element counted on the UI: ${mathElementsOnTheUI.length}`)

  // if the number of math elements on UI does not match math elements counted from the API call
  // then return as failed for further troubleshooting
  if (mathElementsOnTheUI.length !== mathElementCount) {
    const message = 'Count of math elements on UI did not match math element count from API call'
    console.info(message)
    return {
      id: assetId,
      reason: message,
    }
  }

  // open math editor for each math element
  let mathEditorId = 0
  let currentMathElement = 1
  let checkIfAssetIsSavable = true
  for (const mathElement of mathElementsOnTheUI) {
    if (await mathElement.isHidden()) {
      // make math equation visible
      for (const container of accordionContainers) {
        await container.locator('a.cb-accordion-heading').click()
        page.waitForTimeout(2000)
        if (await mathElement.isVisible()) break
      }
    }
    console.info(`Working on math element #${currentMathElement}`)

    mathEditorId = await openMathEditorAndPressInsert(
      mathElement,
      page,
      mathEditorId,
      WIRIS_END_POINT
    )
    await page.waitForTimeout(1000)
    const newAttributesAssigned = await checkIfNewAttributesWereAssigned(mathElement)
    if (!newAttributesAssigned) {
      const message = 'Alttext attribute was not assigned'
      console.info(message)
      return {
        id: assetId,
        reason: message,
      }
    }
    mathEditorId++
    currentMathElement++

    if (checkIfAssetIsSavable) {
      const activeSave = await checkIfSavingIsActive(page)
      if (activeSave) {
        await saveAsset(page)
        checkIfAssetIsSavable = false
        console.info(`Asset can successfully be saved. Continuing with the script...`)
        // if this is the last math element and the save was successful, then the run was successful
        if (mathElement === mathElementsOnTheUI.at(-1)) {
          console.info(successMessage)
          return { id: assetId, reason: successMessage }
        }
      }
    }
  }

  // save asset
  const activeSave = await checkIfSavingIsActive(page)
  if (!activeSave) {
    // if 'checkIfAssetIsSavable' is still true then the save icon was disabled for the whole run
    const message = checkIfAssetIsSavable
      ? 'There were no changes in the asset to save'
      : 'Asset was saved earlier but there were no more changes to save'
    console.info(message)
    return { id: assetId, reason: message }
  }

  await saveAsset(page)

  console.info(successMessage)
  return { id: assetId, reason: successMessage }
}
