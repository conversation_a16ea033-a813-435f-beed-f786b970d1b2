# Apply Math Attributes Automation

This holds information specific to this automation, make sure you're set up for playwright using the
[main Readme](../README.md)

# Environment Variables

APPLY_MATH_ATTRIBUTES_INPUT_FILENAME should be added to your environment variables.

# Run this automation

`pnpm --filter e2e automation apply-math-attributes`

Or with the extension, run from within the file itself or test explorer.

# Results

A resulting CSV file of any failed asset ids and the reasons they failed can be found in
[the output folder](../../output/) in a file called `asset-report.csv`.
