import { test as automation } from '../cb-test'
import { NewAssetPage, ProgramsPage } from '../pages'

automation('Example Single Page (Tab) Automation', async ({ homePage, newAssetPage }) => {
  await homePage.goToPageWith()
  await newAssetPage.goToPageWith()
})

automation('Example Multi Page (Tab) Automation', async ({ context }) => {
  const homePage = new ProgramsPage({ page: await context.newPage() })
  const newAssetPage = new NewAssetPage({ page: await context.newPage() })
  await Promise.all([homePage, newAssetPage].map((aPage) => aPage.goToPageWith()))
})
