/* eslint-disable @typescript-eslint/no-non-null-assertion */
import type { FullConfig } from '@playwright/test'
import { SubjectIdentifier } from '@shared/config'
import { testUsers } from '../bin/data/users'
import { getEnv } from './util/envUtils'

export const courseIDsE2E = [
  SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
  SubjectIdentifier.PRECALCULUS,
]

export const userE2E = testUsers.find((user) => user.username === 'svc-icgautomation01')

export default async function globalSetup(config: FullConfig) {
  try {
    console.info('Starting global setup...')

    const { baseURL } = config.projects[0]!.use
    const hbEnv = getEnv()

    if (!baseURL) {
      throw new Error('baseURL must be defined!')
    }

    console.info('Global info for this run:')
    console.info(`environment: ${hbEnv}`)
    console.info(`baseUrl: ${baseURL}`)
    console.info('End of global info')

    console.info('Finished global setup')
  } catch (e) {
    console.error('Error during global setup!')
    throw e
  }
}
