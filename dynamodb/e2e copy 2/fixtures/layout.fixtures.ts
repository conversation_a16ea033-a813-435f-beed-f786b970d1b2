import { SideMenu, TopBar } from '@e2e/components/layout'
import type { TestExtendParams } from './types'

export const layoutFixtures: TestExtendParams[0] = {
  // mainLocator: async ({ page }, use) => {
  //   await use(page.getByRole('main'));
  // },
  topBar: async ({ page }, use) => {
    await use(new TopBar({ page }))
  },
  sideMenu: async ({ page }, use) => {
    await use(new SideMenu({ page }))
  },
}
