import type { BaseDialog } from '@e2e/components/dialogs/BaseDialog'
import type { SideMenu, TopBar } from '@e2e/components/layout'
import type { E2EAsset, E2ECollection, E2EDirection, E2EForm } from '@e2e/lib'
import type {
  AddAssetsPage,
  AdminFeaturesPage,
  AdminMigrationsPage,
  AdminPage,
  AdminReportsPage,
  AdminUsersPage,
  AssetEditorPage,
  AssetListPage,
  CollectionListPage,
  CollectionPage,
  CollectionsPage,
  FormListPage,
  FormPage,
  MSLoginPage,
  NewAssetPage,
  NextGenAssemblyUnitListPage,
  PreviewPage,
  ProgramsPage,
  ZScalerLoginPage,
} from '@e2e/pages'
import type { BasePage } from '@e2e/pages/BasePage'
import { CourseSettingsPage } from '@e2e/pages/CourseSettingsPage'
import type { NextGenAssemblyUnitAuthoringPage } from '@e2e/pages/NextGenAssemblyUnitAuthoringPage'
import type { DestructuredParams } from '@e2e/types'
import type { ContextSupplier } from '@e2e/util/ContextSupplier'
import type { DataController } from '@e2e/util/dataUtils'
import type { E2EUser } from '@e2e/util/userUtils'
import type { Locator, test } from '@playwright/test'

export type ConsolidatedFixtures = LayoutFixtures &
  PagesFixtures &
  DialogFixtures &
  SafeguardFixtures &
  DataFixtures &
  BuiltinRelatedFixtures &
  UserFixtures &
  FeatureFlagsFixtures

// Way to get the type of the object as if it was being passed into
// test.extend as its first parameter
export type TestExtendParams = Parameters<typeof test.extend<ConsolidatedFixtures>>

export type DialogFixtures = {
  /**
   * Component representing the confirmation dialog that displays
   * after hitting the 'Restart Session' button in the account dropdown
   */
  restartSessionDialog: BaseDialog
}

export type LayoutFixtures = {
  /**
   * Locator we can use to locate other elements solely within the main content
   * of the page, rather than the outer layout/navigation pieces.
   *
   * Will probably be used in place of `page` for that purpose, except when
   * specifically working with the outer layout itself, which will be their
   * own fixtures
   */
  // mainLocator: Locator,

  /**
   * Component representing the top navigation bar of the app
   */
  topBar: TopBar

  /**
   * Component representing the side navigation menu of the app
   */
  sideMenu: SideMenu
}

/**
 * Locator we can use to locate other elements solely within the main content
 * of the page, rather than the outer layout/navigation pieces.
 *
 * Will probably be used in place of `page` for that purpose, except when
 * specifically working with the outer layout itself, which will be their
 * own fixtures
 */
export type mainLocator = Locator

export type PagesFixtures = {
  basePageConstructorParams: DestructuredParams<typeof BasePage>
  msLoginPage: MSLoginPage
  zscalerLoginPage: ZScalerLoginPage
  /** An alias for whatever page is the home page */
  homePage: ProgramsPage
  programsPage: ProgramsPage
  assetEditorPage: AssetEditorPage
  assetListPage: AssetListPage
  newAssetPage: NewAssetPage
  adminPage: AdminPage
  adminMigrationsPage: AdminMigrationsPage
  adminFeaturesPage: AdminFeaturesPage
  adminUsersPage: AdminUsersPage
  adminReportsPage: AdminReportsPage
  assemblyUnitAddAssetsPage: AddAssetsPage
  nextGenAssemblyUnitListPage: NextGenAssemblyUnitListPage
  nextGenAssemblyUnitAuthoringPage: NextGenAssemblyUnitAuthoringPage
  collectionAddAssetsPage: AddAssetsPage
  collectionsPage: CollectionsPage
  collectionPage: CollectionPage
  collectionListPage: CollectionListPage
  formPage: FormPage
  formListPage: FormListPage
  formAddDirectionsPage: AddAssetsPage
  previewPage: PreviewPage
  courseSettingsPage: CourseSettingsPage
}

export type DataFixtures = {
  /**
   * Fixture used to generate, register, and finally delete data used
   * for the test.
   *
   * The only thing that should need to be done in the test itself
   * is generating - registering and deleting should happen automatically
   */
  dataController: DataController

  /**
   * Convenience fixture when just wanting a random data, doesn't
   * matter the subject or any other value
   */
  randomAsset: E2EAsset
  randomCollection: E2ECollection
  randomExamDirection: E2EDirection
  randomSectionDirection: E2EDirection
  randomForm: E2EForm

  /* Automatic fixtures */
  automaticRegisterUIEndpointAssetsCreateMany: void
  automaticRegisterUIEndpointEventsReadOne: void
  automaticIntjectTimeToLive: void
}

export type SafeguardFixtures = {
  /**
   * Option to specify that a specific test or automation can run in prod
   */
  allowProdRun: boolean

  /**
   * Helps ensure that any e2e test or automation run in prod is intentional
   */
  prodRunSafeguard: void
}

export type BuiltinRelatedFixtures = {
  /**
   * Fixture meant to be used directly solely to replace browser.newContext() for
   * supplying tests with contexts set up for specific users when multiple are needed
   *
   * If only one user/context is needed, the builtin context should be used and the
   * user specified with test.use()
   */
  contextSupplier: ContextSupplier

  /**
   * Boolean flag whose purpose is to indicate whether a project is for auth setup.
   * Used to handle whether to set storage state or not. Defaults to false.
   */
  isAuthSetup: boolean
}

export type UserFixtures = {
  /**
   * Option to specify which user this test should use as default
   */
  currentUser: E2EUser
}

export type FeatureFlagsFixtures = {
  /**
   * Option to specify feature flags status for the test
   */
  featureFlags: { name: string; checked: boolean }[]

  /**
   * Automatic fixture that sets any specified feature flags for the test
   */
  setFeatureFlags: void
}
