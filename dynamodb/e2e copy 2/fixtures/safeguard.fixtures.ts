import { getEnv } from '@e2e/util/envUtils'
import type { TestExtendParams } from './types'

export const safeguardFixtures: TestExtendParams[0] = {
  /* Automatic Safeguards */

  allowProdRun: [false, { option: true }],

  prodRunSafeguard: [
    async ({ allowProdRun }, use, testInfo) => {
      if (getEnv() === 'prod') {
        // Will likely set up config or project for ability to run just
        // smoke tests, which will match tests and set allowProdRun
        // if (!testInfo.title.includes('@smoke') && !allowProdRun) {
        if (!allowProdRun) {
          throw new Error(`Cannot run this test in prod: ${testInfo.title}`)
        }
      }
      await use()
    },
    { auto: true },
  ],
}
