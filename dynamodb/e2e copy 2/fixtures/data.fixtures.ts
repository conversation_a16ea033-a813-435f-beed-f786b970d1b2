import casual from 'casual'
import { courseIDsE2E, userE2E } from '@e2e/global.setup'
import type { E2EAsset, E2ECollection, E2EForm } from '@e2e/lib'
import type { E2EDirection } from '@e2e/lib/E2EDirection'
import { <PERSON><PERSON>ontroller, DataControllerModel } from '@e2e/util/dataUtils'
import { DirectionsTypeId } from '@shared/types/model'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
import type { TestExtendParams } from './types'

export const dataFixtures: TestExtendParams[0] = {
  dataController: async ({}, use) => {
    const dataController = new DataController()
    await use(dataController)
  },

  // Random Data

  randomAsset: async ({ dataController }, use) => {
    const allSteps = Object.values(WorkflowStepID)
    const excludedSteps = [
      WorkflowStepID.SENT_TO_VAULT,
      WorkflowStepID.READY_FOR_USE,
      WorkflowStepID.SENT_TO_VAULT_FAILED,
      WorkflowStepID.SENT_TO_VAULT_PENDING,
    ]

    const filteredSteps = allSteps.filter((step) => !excludedSteps.includes(step))
    const assets = (await dataController.generate(DataControllerModel.ASSET, {
      user: userE2E,
      courseIDs: [casual.random_element(courseIDsE2E)],
      length: 1,
      workflowSteps: filteredSteps,
    })) as E2EAsset[]
    if (assets && assets[0]) {
      await use(assets[0])
    } else {
      throw new Error('Unable to generate a random asset')
    }
  },

  randomCollection: async ({ dataController }, use) => {
    const collections = (await dataController.generate(DataControllerModel.COLLECTION, {
      user: userE2E,
      courseIDs: [casual.random_element(courseIDsE2E)],
      length: 1,
      assetLength: 3,
    })) as E2ECollection[]
    if (collections && collections[0]) {
      await use(collections[0])
    } else {
      throw new Error('Unable to generate a random collection')
    }
  },

  randomExamDirection: async ({ dataController }, use) => {
    const directions = (await dataController.generate(DataControllerModel.DIRECTION, {
      user: userE2E,
      courseIDs: [casual.random_element(courseIDsE2E)],
      length: 1,
      assetProps: { directionsType: DirectionsTypeId.DIRECTIONS_EXAM },
    })) as E2EDirection[]
    if (directions && directions[0]) {
      await use(directions[0])
    } else {
      throw new Error('Unable to generate a random exam direction')
    }
  },

  randomSectionDirection: async ({ dataController }, use) => {
    const directions = (await dataController.generate(DataControllerModel.DIRECTION, {
      user: userE2E,
      courseIDs: [casual.random_element(courseIDsE2E)],
      length: 1,
      assetProps: { directionsType: DirectionsTypeId.DIRECTIONS_SECTION },
    })) as E2EDirection[]
    if (directions && directions[0]) {
      await use(directions[0])
    } else {
      throw new Error('Unable to generate a random section direction')
    }
  },

  randomForm: async ({ dataController }, use) => {
    const forms = (await dataController.generate(DataControllerModel.FORM, {
      user: userE2E,
      courseIDs: [casual.random_element(courseIDsE2E)],
      length: 1,
      nextGenAssemblyUnitProps: { adminYear: '26' },
      assemblyUnitLength: 3,
      assetLength: 3,
    })) as E2EForm[]
    if (forms && forms[0]) {
      await use(forms[0])
    } else {
      throw new Error('Unable to generate a random form')
    }
  },
}
