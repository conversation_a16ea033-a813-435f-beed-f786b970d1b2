import { builtinOverridesFixtures } from './builtinOverrides.fixtures'
import { dataFixtures } from './data.fixtures'
import { dialogFixtures } from './dialog.fixtures'
import { featureFlagsFixtures } from './featureFlags.fixtures'
import { layoutFixtures } from './layout.fixtures'
import { pagesFixtures } from './pages.fixtures'
import { safeguardFixtures } from './safeguard.fixtures'
import type { TestExtendParams } from './types'
import { userFixtures } from './user.fixtures'

export const consolidatedFixtures: TestExtendParams[0] = {
  ...layoutFixtures,
  ...pagesFixtures,
  ...builtinOverridesFixtures,
  ...dialogFixtures,
  ...safeguardFixtures,
  ...dataFixtures,
  ...userFixtures,
  ...featureFlagsFixtures,
}
