import { AdminFeaturesPage } from '@e2e/pages'
import { expect } from '@playwright/test'
import type { TestExtendParams } from './types'

export const featureFlagsFixtures: TestExtendParams[0] = {
  featureFlags: [[], { option: true }],

  // Automatic fixture
  setFeatureFlags: [
    async ({ featureFlags, context }, use) => {
      if (featureFlags.length === 0) {
        await use()
        return
      }

      const adminFeaturesPage = new AdminFeaturesPage({ page: await context.newPage() })
      await adminFeaturesPage.goToPageWith()

      for (const flagSetting of featureFlags) {
        await adminFeaturesPage.setFeatureFlag(flagSetting)
        await expect(adminFeaturesPage.getFeatureFlagSwitch(flagSetting)).toHaveAttribute(
          'aria-checked',
          flagSetting.checked ? 'true' : 'false'
        )
      }

      await adminFeaturesPage.page.close()

      await use()

      // No need to revert the feature flags since they're stored in
      // localStorage and the context is isolated to this test
    },
    { auto: true },
  ],
}
