import { registerCustomSelectorEngines } from '@e2e/config/selectors/registerCustomSelectorEngines'
import { ContextSupplier } from '@e2e/util/ContextSupplier'
import type { TestExtendParams } from './types'

export const builtinOverridesFixtures: TestExtendParams[0] = {
  browser: async ({ browser }, use) => {
    // This ensures the custom selector engines are available before any
    // page is created, whether the builtin page fixture or otherwise
    await registerCustomSelectorEngines()
    console.info('Custom selectors registered.')
    await use(browser)
  },

  // Not a builtin, but an option that affects how to override the builtin
  isAuthSetup: [false, { option: true }],

  storageState: async ({ storageState, currentUser, isAuthSetup }, use) => {
    // Allows specifying storageState, but otherwise defaults to currentUser's,
    // unless it's an auth setup where we're not trying to use any
    await use(storageState ?? (isAuthSetup ? undefined : currentUser.storageState))
  },

  context: async ({ context, contextSupplier, currentUser, dataController }, use) => {
    await contextSupplier.setupContext({ context, user: currentUser, dataController })
    await use(context)
  },

  // Not really a builtin fixture override, but does replace the builtin
  // context creation approach of browser.newContext(), as indicated in its jsdocs
  contextSupplier: async ({ browser, dataController }, use) => {
    const contextSupplier = new ContextSupplier({ browser, dataController })
    await use(contextSupplier)
    await contextSupplier.closeContexts()
  },
}
