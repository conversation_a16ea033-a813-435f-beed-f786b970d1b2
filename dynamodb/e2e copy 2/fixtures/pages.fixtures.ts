import {
  AddAssetsPage,
  AdminFeaturesPage,
  AdminMigrationsPage,
  AdminPage,
  AdminReportsPage,
  AdminUsersPage,
  AssetEditorPage,
  AssetListPage,
  CollectionListPage,
  CollectionPage,
  CollectionsPage,
  FormListPage,
  FormPage,
  MSLoginPage,
  NewAssetPage,
  NextGenAssemblyUnitAuthoringPage,
  NextGenAssemblyUnitListPage,
  PreviewPage,
  ProgramsPage,
  ZScalerLoginPage,
} from '@e2e/pages'
import { CourseSettingsPage } from '@e2e/pages/CourseSettingsPage'
import type { TestExtendParams } from './types'

export const pagesFixtures: TestExtendParams[0] = {
  basePageConstructorParams: async ({ page }, use) => {
    await use({ page })
  },
  msLoginPage: async ({ page }, use) => {
    await use(new MSLoginPage(page))
  },
  zscalerLoginPage: async ({ page }, use) => {
    await use(new ZScalerLoginPage(page))
  },
  homePage: async ({ programsPage }, use) => {
    await use(programsPage)
  },
  programsPage: async ({ basePageConstructorParams }, use) => {
    await use(new ProgramsPage(basePageConstructorParams))
  },
  assetEditorPage: async ({ basePageConstructorParams }, use) => {
    await use(new AssetEditorPage(basePageConstructorParams))
  },
  newAssetPage: async ({ basePageConstructorParams }, use) => {
    await use(new NewAssetPage(basePageConstructorParams))
  },
  assetListPage: async ({ basePageConstructorParams }, use) => {
    await use(new AssetListPage(basePageConstructorParams))
  },
  adminPage: async ({ basePageConstructorParams }, use) => {
    await use(new AdminPage(basePageConstructorParams))
  },
  adminMigrationsPage: async ({ basePageConstructorParams }, use) => {
    await use(new AdminMigrationsPage(basePageConstructorParams))
  },
  adminFeaturesPage: async ({ basePageConstructorParams }, use) => {
    await use(new AdminFeaturesPage(basePageConstructorParams))
  },
  adminUsersPage: async ({ basePageConstructorParams }, use) => {
    await use(new AdminUsersPage(basePageConstructorParams))
  },
  adminReportsPage: async ({ basePageConstructorParams }, use) => {
    await use(new AdminReportsPage(basePageConstructorParams))
  },
  collectionsPage: async ({ basePageConstructorParams }, use) => {
    await use(new CollectionsPage(basePageConstructorParams))
  },
  collectionPage: async ({ basePageConstructorParams }, use) => {
    await use(new CollectionPage(basePageConstructorParams))
  },
  collectionListPage: async ({ basePageConstructorParams }, use) => {
    await use(new CollectionListPage(basePageConstructorParams))
  },
  nextGenAssemblyUnitListPage: async ({ basePageConstructorParams }, use) => {
    await use(new NextGenAssemblyUnitListPage(basePageConstructorParams))
  },
  nextGenAssemblyUnitAuthoringPage: async ({ basePageConstructorParams }, use) => {
    await use(new NextGenAssemblyUnitAuthoringPage(basePageConstructorParams))
  },
  formPage: async ({ basePageConstructorParams }, use) => {
    await use(new FormPage(basePageConstructorParams))
  },
  formListPage: async ({ basePageConstructorParams }, use) => {
    await use(new FormListPage(basePageConstructorParams))
  },
  previewPage: async ({ basePageConstructorParams }, use) => {
    await use(new PreviewPage(basePageConstructorParams))
  },
  courseSettingsPage: async ({ basePageConstructorParams }, use) => {
    await use(new CourseSettingsPage(basePageConstructorParams))
  },
  collectionAddAssetsPage: async ({ basePageConstructorParams }, use) => {
    await use(new AddAssetsPage({ ...basePageConstructorParams, structureType: 'Collection' }))
  },
  assemblyUnitAddAssetsPage: async ({ basePageConstructorParams }, use) => {
    await use(new AddAssetsPage({ ...basePageConstructorParams, structureType: 'Assembly Unit' }))
  },
  formAddDirectionsPage: async ({ basePageConstructorParams }, use) => {
    // Will probably need to adjust naming
    await use(new AddAssetsPage({ ...basePageConstructorParams, structureType: 'Form' }))
  },
}
