import { test as setup } from '@e2e/cb-test'
import { tables } from '@shared/dynamo-hummingbird'

setup(`should be able to connect to <PERSON>`, async ({}) => {
  // Quick fail for general AWS interface issues
  try {
    await tables.friendlyId.exists()

    // Simplest way to handle this annoying case (if left as unknown, didn't
    // like .code, and wanted to allow multiple e types)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    if (e?.code === 'ECONNREFUSED') {
      throw new Error('Local app not started.')
    } else if ([e?.code, e?.name].includes('ExpiredTokenException')) {
      throw new Error('Token expired, please do MFA!')
    } else {
      throw e
    }
  }
})
