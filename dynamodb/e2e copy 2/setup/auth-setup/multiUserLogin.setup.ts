import { test as setup } from '@e2e/cb-test'
import { getUsers } from '@e2e/util/userUtils'
import { performSharedAuthSetup } from './performSharedAuthSetup'

getUsers().forEach((user) => {
  setup(
    `should set up user ${user.email} auth`,
    async ({ msLoginPage, zscalerLoginPage, homePage }) => {
      await performSharedAuthSetup({ msLoginPage, zscalerLoginPage, homePage, user })
    }
  )
})
