import type { CBTestFixtures } from '@e2e/cb-test'
import { getEnv, getIsExternal } from '@e2e/util/envUtils'
import type { getUsers } from '@e2e/util/userUtils'

export const performSharedAuthSetup = async ({
  msLoginPage,
  zscalerLoginPage,
  homePage,
  user,
}: CBTestFixtures<'msLoginPage' | 'zscalerLoginPage' | 'homePage'> & {
  user: ReturnType<typeof getUsers>[number]
}) => {
  const page = homePage.page
  const context = page.context()
  const env = getEnv()

  console.info(`Starting user ${user.email} auth setup...`)
  const log = (message = '') => console.info(`${user.email} auth: ${message}`)

  // Log in
  log('Logging in...')

  // Local is handled using a fake api call elsewhere

  await homePage.goToPageWith()

  // Do the normal login flow when not local
  if (env !== 'local') {
    // If accessing via external access, will go through zscaler portal first
    if (getIsExternal()) {
      log('Logging in to ZScaler...')
      await zscalerLoginPage.login({
        email: user.email,
      })
    }

    // await page.waitForURL(/microsoftonline/)
    log('Logging in to Microsoft...')
    await msLoginPage.login({
      email: user.email,
      password: await user.getPassword(),
    })
    await homePage.page.waitForURL(homePage.url(), { timeout: 120000 })
    log('Redirected to Hummingbird')
  }

  // Save the authenticated state for reuse in all the tests
  log('Storing auth...')
  await context.storageState({ path: user.storageState })

  console.info(`Finished user ${user.email} auth setup`)
}
