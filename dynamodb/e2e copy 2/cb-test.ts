import { test as base } from '@playwright/test'
import { consolidatedFixtures } from './fixtures/consolidated.fixtures'
import type { ConsolidatedFixtures } from './fixtures/types'

export const test = base.extend<ConsolidatedFixtures>({ ...consolidatedFixtures })
export { expect } from '@playwright/test'

type AllCBTestFixtures = Parameters<Parameters<typeof test>[1]>[0]
export type CBTestFixtures<KeysUsed extends keyof AllCBTestFixtures> = Pick<
  AllCBTestFixtures,
  KeysUsed
>
