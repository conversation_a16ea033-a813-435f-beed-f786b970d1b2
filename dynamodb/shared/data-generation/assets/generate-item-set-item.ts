import type { CourseConfiguration } from '@shared/schema'
import type { ItemUpdateInput } from '@shared/schema/item-set/item'
import { ApiaryItemTypeUtil, ExtendedApiaryItem } from '@shared/types/model/apiary-extended'
import { TRPCTransformer } from '@shared/types/transform/apiary-to-trpc-transformer'
import { ApiaryTransformer } from '@shared/types/transform/trpc-to-apiary-transformer'
import { buildNewItemRequest, toItemUpdateInput } from './helper-functions'
import type { GenerateItemSetItemProps } from './types'

export const generateItemSetItem = (
  {
    parentItemSetId,
    itemType,
    requiredItemMetadata,
    username,
    stimulusFormat,
    isMultiSelect,
    subType,
    answerOptionsFriendlyIds,
  }: GenerateItemSetItemProps,
  config: CourseConfiguration
): ItemUpdateInput | undefined => {
  try {
    // Hack into Apiary item so we can re-use existing new item code
    const newItem = buildNewItemRequest(
      {
        itemType: ApiaryItemTypeUtil.toItemTypeValue(itemType),
        subType,
        answerOptionsFriendlyIds,
        setAssetId: parentItemSetId,
        setLeaderAssetId: ApiaryTransformer.toUniqueSetLeaderId(parentItemSetId),
        requiredItemMetadata,
        username,
        stimulusFormat,
        isMultiSelect,
      },
      config
    )
    // Map to GraphQL input type
    const items = toItemUpdateInput([
      {
        assetId: `new_${TRPCTransformer.DRAFT_ASSET_ID_PREFIX}`,
        externalId: 'new',
        ...newItem,
      } as ExtendedApiaryItem,
    ])
    return items
  } catch (e) {
    throw new Error(`Error generating item set item: ${(e as Error).message}`)
  }
}
