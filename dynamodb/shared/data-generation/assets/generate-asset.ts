import { convertItemSetToAsset } from '@shared/conversions'
import type { Asset } from '@shared/dynamo-hummingbird'
import { getEmptyMetadataRootRecord } from '@shared/metadata/metadata-root-record'
import type { ItemSet } from '@shared/schema'
import { StimulusFormat } from '@shared/schema'
import type { GenerateAssetProps } from './types'

export const generateAsset = async ({
  itemSetId,
  displayName,
  newItemSet,
  ctx,
}: GenerateAssetProps): Promise<Asset> => {
  try {
    const itemSet: Partial<ItemSet> = {
      ...newItemSet,
      displayName,
      items: [],
      userId: ctx.user.userId || newItemSet.userId,
      created: {
        ...newItemSet.created,
        userId: ctx.user.userId || newItemSet.created.userId,
        userName: ctx.user.username || newItemSet.created.userName,
      },
      updated: {
        ...newItemSet.updated,
        userId: ctx.user.userId || newItemSet.updated.userId,
        userName: ctx.user.username || newItemSet.updated.userName,
      },
      active: true,
    }
    const now = new Date()
    return convertItemSetToAsset({
      itemSetId,
      directionsType: itemSet.directionsType,
      itemId: itemSetId,
      itemSet: JSON.stringify(itemSet),
      created: now,
      createdById: itemSet.created?.userId || '',
      createdByUsername: itemSet.created?.userName || '',
      updated: now,
      modifiedById: itemSet.created?.userId || '',
      modifiedByUsername: itemSet.created?.userName || '',
      workflowState: itemSet.workflowState || '',
      version: 0,
      revision: 0,
      stimulusFormat: itemSet?.stimulusFormat || StimulusFormat.DEFAULT,
      newMetadata: {
        [newItemSet.metadataVersion]: getEmptyMetadataRootRecord({
          id: itemSetId,
          items: itemSet.items ?? [],
          stimuli: itemSet.stimuli ?? [],
          dimensions: itemSet.dimensions ?? [],
        }),
      },
    })
  } catch (e) {
    throw new Error(`Error generating asset: ${(e as Error).message}`)
  }
}
