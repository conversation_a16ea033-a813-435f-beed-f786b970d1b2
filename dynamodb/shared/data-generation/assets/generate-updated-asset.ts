import { convertItemSetToAsset } from '@shared/conversions'
import type { Asset } from '@shared/dynamo-hummingbird'
import { getEmptyMetadataRootRecord } from '@shared/metadata/metadata-root-record'
import { ItemSet, StimulusFormat } from '@shared/schema'

/** Returns empty metadata records. */
export async function generateUpdatedAsset(itemSet: ItemSet, asset: Asset) {
  try {
    const { metadata, combinedMetadata, stimuli, items, ...convertedData } = convertItemSetToAsset({
      itemSetId: asset.itemSetId || '',
      itemSet: JSON.stringify(itemSet),
      workflowState: itemSet.workflowState,
      modifiedById: itemSet.updated?.userId,
      modifiedByUsername: itemSet.updated?.userName,
      version: 0,
      accnum: itemSet?.accnum || undefined,
      stimulusFormat: itemSet?.stimulusFormat || StimulusFormat.DEFAULT,
      newMetadata: { [itemSet.metadataVersion]: getEmptyMetadataRootRecord(itemSet) },
    })

    // we need to remove undefined from these arrays and objects
    const cleanedMetadata = JSON.parse(JSON.stringify(metadata))
    const cleanedCombinedMetadata = JSON.parse(JSON.stringify(combinedMetadata))
    const cleanedStimuli = JSON.parse(JSON.stringify(stimuli))
    const cleanedItems = JSON.parse(JSON.stringify(items))
    const cleanedConvertedData = JSON.parse(JSON.stringify(convertedData))

    return {
      ...asset,
      ...cleanedConvertedData,
      metadata: cleanedMetadata,
      combinedMetadata: cleanedCombinedMetadata,
      stimuli: cleanedStimuli,
      items: cleanedItems,
      newMetadata: { [itemSet.metadataVersion]: getEmptyMetadataRootRecord(itemSet) },
    }
  } catch (e) {
    throw new Error(`Error generating updating asset: ${(e as Error).message}`)
  }
}
