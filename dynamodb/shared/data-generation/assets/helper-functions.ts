import { ulid } from 'ulid'
import { Apiary } from '@kobayashi/types'
import { SubjectIdentifier } from '@shared/config'
import {
  CourseConfiguration,
  ItemType,
  ItemUpdateInput,
  MultipleChoiceAnswerOption,
  StimulusFormat,
  SubType,
} from '@shared/schema'
import {
  FreeResponseSubtypeId,
  SetLayoutId,
  SetLayouts,
  SetLayoutsIdMappings,
} from '@shared/types/model'
import {
  ApiaryItemTypeUtil,
  ApiaryQuestionItemTypeString,
  ExtendedApiaryItem,
  ExtendedFreeResponseQuestion,
  ExtendedMultipleChoiceQuestion,
} from '@shared/types/model/apiary-extended'
import { TRPCTransformer } from '@shared/types/transform'
import { toMetadataNodes } from '@shared/types/transform/util'
import { validateAnswerOptionIds } from '@shared/utils/common-functions/common-functions'
import type { NewItemRequestProps, NewSetItemRequest, NewSetItemRequestBase } from './types'

export const defaultItems = (config: CourseConfiguration, current: ItemType) => {
  return config?.authoring?.[current]?.items?.min || 0
}

const defaultOptions = (
  config: CourseConfiguration,
  questionType: 'MultipleChoiceQuestion' | 'FreeResponseQuestion'
) => {
  return config?.authoring?.[questionType]?.options?.min || 0
}

const generateItemAnswerOptions = (numOptions?: number, answerOptionsFriendlyIds?: string[]) => {
  const constructParagraphTag = (className: string, body: string) => {
    return `<p class="${className}">${body}</p>`
  }
  const answerOptions = Array.from(
    { length: numOptions === undefined ? 4 : numOptions },
    (_, index) => {
      const id = answerOptionsFriendlyIds ? answerOptionsFriendlyIds[index] : ulid()

      const answerValue = ''
      return MultipleChoiceAnswerOption.parse({
        id,
        ordinal: index,
        content: constructParagraphTag('dap_body', answerValue),
        correct: false,
        label: '',
        informalRationale: constructParagraphTag(
          'dap_body',
          answerValue ? `rationale ${answerValue}` : ''
        ),
        key: `${id}`,
      })
    }
  )
  return answerOptionsFriendlyIds
    ? answerOptions
    : validateAnswerOptionIds(answerOptions as { id: number }[])
}

export const getDefaultSetLayout = (
  subjectId?: string,
  stimulusFormat?: StimulusFormat,
  subType?: SubType
): SetLayoutId => {
  if (subjectId === SubjectIdentifier.ESSAY) {
    return SetLayoutsIdMappings[SetLayouts.SAT_ESSAY].toString() as SetLayoutId
  } else if (
    subType === SubType.CHOICE_QUESTION ||
    stimulusFormat === StimulusFormat.SOURCE ||
    stimulusFormat === StimulusFormat.DOCUMENT
  ) {
    return SetLayoutsIdMappings[
      SetLayouts.DIRECTIONS_LEFT_PANE_STEM_RIGHT_PANE
    ].toString() as SetLayoutId
  } else {
    return SetLayoutsIdMappings[SetLayouts.STEM_SINGLE_PANE].toString() as SetLayoutId
  }
}

export const buildNewItemRequest = (
  {
    itemType,
    setAssetId,
    setLeaderAssetId,
    requiredItemMetadata,
    username,
    stimulusFormat,
    isMultiSelect,
    subType,
    answerOptionsFriendlyIds,
  }: NewItemRequestProps,
  config: CourseConfiguration
): NewSetItemRequest => {
  const baseItem: NewSetItemRequestBase = {
    encodedAssetId: '',
    assetType: itemType,
    program: requiredItemMetadata.program.id as string,
    subject: requiredItemMetadata.subject.id as string,
    setAssetId,
    parentItemSetId: setAssetId,
    stimulusId: setLeaderAssetId,
    sharedStimulusAssetId: setLeaderAssetId,
    userId: username,
    createdByUserId: username,
    createdBy: username,
    createdOn: new Date().getTime().toString(),
    metadata: toMetadataNodes(requiredItemMetadata, []),
    graphics: [],
    accnum: '',
    stimulusFormat,
  }
  switch (itemType) {
    case Apiary.ItemTypeValue.MultipleChoiceQuestion:
      const options = defaultOptions(config, 'MultipleChoiceQuestion')
      return {
        ...baseItem,
        multipleChoiceQuestion: {
          stimulus: '',
          prompt: '',
          decimalAlign: false,
          answerOptions: generateItemAnswerOptions(options),
          points: 1,
          part: [],
          isMultiSelect,
        } as ExtendedMultipleChoiceQuestion,
      }
    case Apiary.ItemTypeValue.FreeResponseQuestion:
      if (subType === FreeResponseSubtypeId.CHOICE_QUESTION) {
        const options = defaultOptions(config, 'FreeResponseQuestion')
        return {
          ...baseItem,
          freeResponseQuestion: {
            stem: '',
            answerOptions: generateItemAnswerOptions(options, answerOptionsFriendlyIds),
          } as ExtendedFreeResponseQuestion,
        }
      } else {
        return {
          ...baseItem,
          freeResponseQuestion: {
            stem: '',
            part: [],
            answer: '',
          } as ExtendedFreeResponseQuestion,
        }
      }
    default:
      throw new Error(`Unsupported item type ${itemType}`)
  }
}

export const toItemUpdateInput = (
  dirtyItems: ExtendedApiaryItem[]
): ItemUpdateInput | undefined => {
  const resInput: ItemUpdateInput = {
    multipleChoiceQuestion: [],
    freeResponseQuestion: [],
  }

  const res = dirtyItems.reduce((agg, item) => {
    const key = ApiaryItemTypeUtil.questionTypeStringToItemTypeValue(
      item.assetType as ApiaryQuestionItemTypeString
    )?.toString()
    if (key && key.length > 0) {
      const aggKey = (key.charAt(0).toLowerCase() + key.slice(1)) as keyof ItemUpdateInput
      const curValueArray = agg[aggKey] ?? []
      const question = TRPCTransformer.toQuestion(item)
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore: Fake type mismatch
      curValueArray.push(question)
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore: Fake type mismatch
      agg[aggKey] = curValueArray
    }
    return agg
  }, resInput)
  return Object.keys(res).length === 0 ? undefined : res
}

export const toSetType = (itemType: string, subjectID?: string) => {
  if (itemType === 'directions') return 'directions'
  else {
    return subjectID === SubjectIdentifier.PRECALCULUS || subjectID === SubjectIdentifier.ESSAY
      ? 'discrete'
      : 'setLeader'
  }
}
