import type { Apiary } from '@kobayashi/types'
import type { ItemSetDetached, StimulusFormat, SubType } from '@shared/schema'
import type { RequiredItemMetadata } from '@shared/schema'
import type { Context } from '@shared/trpc-lambda/trpc'
import type { DirectionsTypeId, FreeResponseSubtypeId } from '@shared/types/model'
import type {
  ApiaryQuestionItemTypeString,
  ExtendedApiaryItem,
} from '@shared/types/model/apiary-extended'

export type NewItemRequestProps = Readonly<{
  itemType: Apiary.ItemTypeValue
  setLeaderAssetId: string
  setAssetId: string
  requiredItemMetadata: RequiredItemMetadata
  username: string
  stimulusFormat: StimulusFormat
  isMultiSelect?: boolean
  subType?: SubType
  answerOptionsFriendlyIds?: string[]
}>

export type NewItemExtendedApiaryItem = Readonly<
  Pick<
    ExtendedApiaryItem,
    | 'assetType'
    | 'program'
    | 'subject'
    | 'parentItemSetId'
    | 'sharedStimulusAssetId'
    | 'userId'
    | 'createdByUserId'
    | 'createdBy'
    | 'createdOn'
    | 'metadata'
    | 'graphics'
    | 'encodedAssetId'
    | 'accnum'
    | 'stimulusFormat'
  >
>

export interface NewSetItemRequestBase extends NewItemExtendedApiaryItem {
  setAssetId: string
  stimulusId: string
}

export interface NewMultipleChoiceItemRequest
  extends NewSetItemRequestBase,
    Pick<ExtendedApiaryItem, 'multipleChoiceQuestion'> {}

export interface NewFreeResponseItemRequest
  extends NewSetItemRequestBase,
    Pick<ExtendedApiaryItem, 'freeResponseQuestion'> {}

export type NewSetItemRequest = NewMultipleChoiceItemRequest | NewFreeResponseItemRequest

export interface GenerateItemSetProps {
  type: Apiary.ItemTypeValue
  directionsType?: DirectionsTypeId
  subType?: FreeResponseSubtypeId
  username: string
  stimulusFormat: StimulusFormat
  program: {
    id: string
    name: string
  }
  subject: {
    id: string
    name: string
  }
}

export interface GenerateItemSetItemProps {
  parentItemSetId: string
  itemType: ApiaryQuestionItemTypeString
  requiredItemMetadata: RequiredItemMetadata
  username: string
  stimulusFormat: StimulusFormat
  isMultiSelect: boolean
  subType?: FreeResponseSubtypeId
  answerOptionsFriendlyIds?: string[]
}

export interface GenerateAssetProps {
  itemSetId: string
  displayName: string
  newItemSet: ItemSetDetached
  ctx: Context
}
