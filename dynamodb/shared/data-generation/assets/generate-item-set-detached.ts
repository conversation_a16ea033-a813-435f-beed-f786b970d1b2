import { type CourseConfiguration, ItemSetDetached } from '@shared/schema'
import { StimulusFormat } from '@shared/schema'
import type { readMetadataInputSchema as MetadataInput } from '@shared/schema/input/assets/assets-read'
import { WorkflowStageLookup, WorkflowStep } from '@shared/types/model/workflow/types'
import { WorkflowStage as WorkflowState } from '@shared/types/model/workflow/workflow-stage'
import { getDefaultMetadata } from '@shared/utils/metadata'
import { getDefaultSetLayout, toSetType } from './helper-functions'
import type { GenerateItemSetProps } from './types'

export const generateItemSetDetached = (
  {
    type,
    directionsType,
    program,
    subject,
    subType,
    username,
    stimulusFormat = StimulusFormat.DEFAULT,
  }: GenerateItemSetProps,
  courseConfig: CourseConfiguration,
  metadataVersionInput: string | undefined
): ItemSetDetached => {
  try {
    const setType = toSetType(type, subject.id)
    const { updated: metadataVersion, id: metadataId } = metadataVersionInput
      ? courseConfig.metadata.find((val) => val.updated === metadataVersionInput) ?? {
          updated: new Date(-1).toISOString(),
          source: 'unknown',
        }
      : getDefaultMetadata(courseConfig)

    const detachedItemSet: ItemSetDetached = ItemSetDetached.parse({
      setType,
      directionsType,
      subType,
      metadata: {
        program: {
          id: program.id,
          value: program.name,
        } as unknown as typeof MetadataInput,
        subject: {
          id: subject.id,
          value: subject.name,
        } as unknown as typeof MetadataInput,
      },
      additionalMetadata: [],
      dimensions: [],
      active: true,
      userId: username,
      created: {
        userId: username,
        userName: username,
        modifiedOn: new Date(),
      },
      updated: {
        userId: username,
        userName: username,
        modifiedOn: new Date(),
      },
      // the minimum/starting/default workflow step for the new item's course Id - NL
      workflowState: WorkflowState.INTERNAL_AUTHORING.id,
      workflowStage: WorkflowStageLookup[WorkflowStep.InternalContentReview1],
      workflowStep: WorkflowStep.InternalContentReview1,
      stimuli: [],
      graphics: [],
      title: '',
      instructions: '',
      accnum: null,
      layout: getDefaultSetLayout(subject.id, stimulusFormat, subType),
      sourceDocumentInstructions: stimulusFormat === StimulusFormat.SOURCE ? '' : undefined,
      stimulusFormat,
      metadataVersion,
      metadataId,
    })
    return detachedItemSet
  } catch (e) {
    throw new Error(`Error generating item set detached: ${(e as Error).message}`)
  }
}
