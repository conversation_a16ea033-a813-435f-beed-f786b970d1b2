import { Apiary } from '@kobayashi/types'
import { SubjectIdentifier, SubjectKey } from '@shared/config'
import { mockConfig } from '@shared/config/fixtures'
import { StimulusFormat } from '@shared/schema'
import { ItemType } from '@shared/schema'
import { SetLayoutId } from '@shared/types/model'
import type { ExtendedApiaryItem } from '@shared/types/model/apiary-extended'
import {
  buildNewItemRequest,
  defaultItems,
  getDefaultSetLayout,
  toItemUpdateInput,
  toSetType,
} from './helper-functions'

describe('helper-functions tests', () => {
  it.each([
    [
      SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
      StimulusFormat.SOURCE,
      SetLayoutId.DIRECTIONS_LEFT_PANE_STEM_RIGHT_PANE,
    ],
    [
      SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
      StimulusFormat.DEFAULT,
      SetLayoutId.STEM_SINGLE_PANE,
    ],
    [SubjectIdentifier.PRECALCULUS, StimulusFormat.DEFAULT, SetLayoutId.STEM_SINGLE_PANE],
    [SubjectIdentifier.ESSAY, StimulusFormat.SOURCE, SetLayoutId.SAT_ESSAY],
    [SubjectIdentifier.ESSAY, StimulusFormat.DEFAULT, SetLayoutId.SAT_ESSAY],
  ])(
    'getDefaultSetLayout: should return the correct layout for %s %s',
    (subjectId, stimulusFormat, expectedLayout) => {
      expect(getDefaultSetLayout(subjectId, stimulusFormat)).toBe(expectedLayout)
    }
  )
  it.each([
    [SubjectKey.AFRICAN_AMERICAN_STUDIES, ItemType.FreeResponseQuestion],
    [SubjectKey.AFRICAN_AMERICAN_STUDIES, ItemType.MultipleChoiceQuestion],
    [SubjectKey.PRECALCULUS, ItemType.FreeResponseQuestion],
    [SubjectKey.PRECALCULUS, ItemType.MultipleChoiceQuestion],
  ])('defaultItems: should return the correct item count for %s %s', (subjectKey, itemType) => {
    expect(defaultItems(mockConfig[SubjectIdentifier.AFRICAN_AMERICAN_STUDIES], itemType)).toBe(1)
  })
  it.each([
    [Apiary.ItemTypeValue.MultipleChoiceQuestion, null],
    [Apiary.ItemTypeValue.FreeResponseQuestion, null],
    [Apiary.ItemTypeValue.SetLeader, `Unsupported item type ${Apiary.ItemTypeValue.SetLeader}`],
  ])(
    'buildNewItemRequest: should return a new item object with %s object',
    (itemType, expectedError) => {
      const input = {
        itemType,
        setAssetId: 'ABC000001',
        setLeaderAssetId: 'ABC000000',
        requiredItemMetadata: {
          program: {
            id: '59tWw',
            nodeType: 'Program',
            value: 'AP',
          },
          subject: {
            id: '116',
            nodeType: 'Subject',
            value: 'African American Studies',
          },
        } as unknown,
        username: 'mmorales',
        stimulusFormat: StimulusFormat.DEFAULT,
      }
      if (!expectedError) {
        const response = buildNewItemRequest(input, mockConfig)
        expect(response).toHaveProperty(itemType)
      } else {
        try {
          buildNewItemRequest(input, mockConfig)
          throw new Error('test failed')
        } catch (e: unknown) {
          expect((e as Error).message).toBe(expectedError)
        }
      }
    }
  )
  it('ss', () => {
    const mockDate = new Date()
    const mockDateMs = mockDate.getTime()
    const mockdirtyItems: ExtendedApiaryItem[] = [
      {
        assetId: 'new_DRAFT_',
        externalId: 'new',
        encodedAssetId: '',
        assetType: 'multipleChoiceQuestion',
        program: 'AP',
        subject: 'African American Studies',
        setAssetId: 'YYZ000000',
        parentItemSetId: 'YYZ000000',
        stimulusId: 'setLeader_YYZ000000',
        sharedStimulusAssetId: 'setLeader_YYZ000000',
        userId: 'mmorales',
        createdByUserId: 'mmorales',
        createdBy: 'mmorales',
        createdOn: mockDateMs,
        metadata: [
          { id: '59tWw', value: 'AP', nodeType: 'Program' },
          {
            id: '116',
            value: 'African American Studies',
            nodeType: 'Subject',
          },
        ],
        graphics: [],
        accnum: '',
        stimulusFormat: StimulusFormat.DEFAULT,
        multipleChoiceQuestion: {
          stimulus: '',
          prompt: '',
          decimalAlign: false,
          answerOptions: [],
          points: 1,
          part: [],
        },
      },
    ] as unknown as ExtendedApiaryItem[]
    const mockResponse = {
      freeResponseQuestion: [],
      multipleChoiceQuestion: [
        {
          accessibilityContent: '',
          accnum: '',
          additionalMetadata: [],
          answerOptions: [],
          created: {
            modifiedOn: mockDate,
            userId: 'mmorales',
            userName: 'mmorales',
          },
          decimalAlign: false,
          displayName: 'new',
          graphics: [],
          id: 'new_DRAFT_',
          itemTitle: '',
          itemType: 'MultipleChoiceQuestion',
          metadata: {
            program: {
              id: '59tWw',
              value: 'AP',
            },
            subject: {
              id: '116',
              value: 'African American Studies',
            },
          },
          ordinalValue: 0,
          parentSetId: 'YYZ000000',
          part: [],
          points: 1,
          prompt: '',
          stimulus: '',
          stimulusFormat: StimulusFormat.DEFAULT,
          updated: { modifiedOn: new Date('1970-01-01T00:00:00.000Z'), userId: '', userName: '' },
          userId: 'mmorales',
          version: 0,
        },
      ],
    }
    const response = toItemUpdateInput(mockdirtyItems)
    expect(response).toMatchObject(mockResponse)
  })
  it.each([
    ['setLeader', 'anItemType', SubjectIdentifier.AFRICAN_AMERICAN_STUDIES],
    ['discrete', 'anItemType', SubjectIdentifier.PRECALCULUS],
    ['directions', 'directions', 'anySubject'],
  ])('should return the correct set type %s', (expectedType, inputType, subjectID) => {
    expect(toSetType(inputType, subjectID)).toEqual(expectedType)
  })
})
