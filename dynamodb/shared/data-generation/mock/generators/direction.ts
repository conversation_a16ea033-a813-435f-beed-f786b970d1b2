import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import { Asset, tables } from '@shared/dynamo-hummingbird'
import { ItemType } from '@shared/schema'
import { loadCourseConfigFromDb } from '@shared/trpc-lambda/context/course-service'
import { getCourseConfigFromFile } from '@shared/trpc-lambda/mocks'
import { DirectionsTypeLabel } from '@shared/types/model'
import { oldWorkflowStageToNewWorkflowStep } from '@shared/types/model/workflow/types'
import { getDefaultMetadata } from '@shared/utils/metadata'
import { generateAsset } from '../assets'
import type {
  DataGeneratorResults,
  AssetGeneratorProps as DirectionGeneratorProps,
  SubjectKeyMapping,
} from '../types'
import { ensureSafeToGenerateData, subjectData } from '../utils'

export default async function directionGenerator({
  ids,
  consola,
  progressBar,
  courseIDs,
  user,
  length = 10,
  workflowSteps,
  assetProps = {},
  ttl,
}: DirectionGeneratorProps): Promise<DataGeneratorResults> {
  const AssetModel = tables.itemCloudGreen.getModel('Asset')
  const prefix = '[direction-generator]:'
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()
  const useDbConfig = process.env.NODE_ENV !== 'test' && process.env.ENV !== 'local'
  const directions: Asset[] = []
  // regular for-loop lets us generate data sequentially
  for (const subjectId of courseIDs) {
    if (subjectData && subjectData[subjectId]) {
      const subject = subjectData[subjectId] as SubjectKeyMapping
      // check if subject is null or undefined
      if (subject == null) {
        throw new Error('subjectID is invalid')
      }
      const generatedIDs = ids || (await generateBatchIDs(length, undefined, ttl))
      consola?.info(
        prefix,
        `Generating ${generatedIDs.length} ${
          assetProps.directionsType
            ? `${DirectionsTypeLabel[assetProps.directionsType].toLowerCase()}`
            : 'random directions'
        } for ${subject.name}`
      )
      progressBar?.start(generatedIDs.length, 0)
      // create the directions using batchWrite
      let batch = {}
      for (let i = 0; i < generatedIDs.length; i++) {
        const id = generatedIDs[i] as string
        // generate a new direction
        const workflowStateId = assetProps?.workflowStateId
          ? assetProps.workflowStateId
          : workflowSteps
          ? workflowSteps[i % workflowSteps.length]
          : undefined
        let { metadataVersion, metadataId } = assetProps
        if (!metadataVersion) {
          const config = useDbConfig
            ? await loadCourseConfigFromDb(subjectId, false)
            : getCourseConfigFromFile(subjectId)
          const metadataConfig = getDefaultMetadata(config)
          metadataVersion = metadataConfig.updated
          metadataId = metadataConfig.id
        }
        const direction = await generateAsset({
          ...assetProps,
          metadataVersion,
          metadataId,
          id,
          subjectId,
          itemType: ItemType.directions,
          userId: user?.userId,
          username: user?.username,
          workflowStepId:
            workflowStateId && workflowStateId !== 'RANDOM'
              ? oldWorkflowStageToNewWorkflowStep[workflowStateId]
              : undefined,
          ttl,
        })
        if (!direction) continue
        progressBar?.increment()
        AssetModel.create(direction, { batch })
        // track the new directions that were created
        directions.push(direction)
        if ((i > 0 && i % 20 === 0) || i === generatedIDs.length - 1) {
          await tables.itemCloudGreen.batchWrite(batch)
          batch = {}
        }
      }
      progressBar?.stop()
    }
  }
  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: directions, timeElapsed }
}
