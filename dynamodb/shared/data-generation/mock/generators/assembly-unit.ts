import { capitalCase } from 'case-anything'
import casual from 'casual'
import type { SingleBar } from 'cli-progress'
/* eslint-disable max-lines-per-function */
import { getCourseConfigFromFile } from '@bin/data/course-config'
import { users } from '@bin/data/users'
import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import { makeAssemblyUnitName } from '@shared/data-generation/utils'
import type { Asset, User } from '@shared/dynamo-hummingbird'
import { WriteEntry, writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
  makeAssemblyUnit,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { AssemblyUnitCreatedEvent } from '@shared/event-models'
import type { AssemblyUnit } from '@shared/schema/split-models'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
import chunk from '@shared/utils/chunk'
import type { ItemType } from '../assets/types'
import type { AssemblyUnitGeneratorProps, DataGeneratorResults, SubjectKeyMapping } from '../types'
import {
  subjectData as courseData,
  ensureSafeToGenerateData,
  getRandomAssets,
  prepareAssociationRecords,
} from '../utils'
import assetGenerator from './asset'

const assemblyUnitTypeChildMapping: Record<AssemblyUnitType, AssemblyUnitType | null> = {
  [AssemblyUnitType.UNSPECIFIED]: null,
  [AssemblyUnitType.BASE]: AssemblyUnitType.SEQUENCE,
  [AssemblyUnitType.SEQUENCE]: AssemblyUnitType.COMPLETE,
  [AssemblyUnitType.COMPLETE]: null,
}

const adminYears = ['24', '25', '26']
const formCodes = ['075', '085', '095']
const baseForms = Object.values(BaseForm)
const questionTypes = Object.values(QuestionType).filter((type) => type !== QuestionType.PROJECT)
const questionTypeNumbers = [1, 2, 3, 4]

async function assemblyUnitGeneratorHelper({
  id,
  parentAssemblyUnit,
  assets,
  progressBar,
  sequenceLength,
  completeLength,
}: {
  id: string
  parentAssemblyUnit: Partial<AssemblyUnit>
  assets?: Asset[]
  progressBar?: SingleBar
  sequenceLength?: number
  completeLength?: number
}): Promise<AssemblyUnit[]> {
  const assemblyUnits: AssemblyUnit[] = []
  const {
    createdByUsername: username,
    adminYear,
    courseId,
    assemblyUnitType = AssemblyUnitType.BASE,
    questionType,
    questionTypeNumber,
    baseForm,
    name: parentAssemblyUnitName,
    sequenceNum: defaultSequenceNum = -1,
    formCode,
  } = parentAssemblyUnit
  const sequenceNum = assemblyUnitType === AssemblyUnitType.BASE ? defaultSequenceNum : -1
  const courseCode = getCourseConfigFromFile(courseId as string).courseCode
  const pk = `AssemblyUnit#${id}`
  const name = makeAssemblyUnitName({
    sequence: defaultSequenceNum,
    assemblyUnitType,
    adminYear,
    baseForm,
    courseCode,
    questionType,
    questionTypeNumber,
    parentAssemblyUnitName,
  })
  const assemblyUnit = makeAssemblyUnit({
    pk,
    sk: pk,
    id,
    createdByUsername: username as string,
    courseId: courseId as string,
    isActive: true,
    assemblyUnitType,
    name,
    adminYear,
    formCode,
    baseForm,
    questionType,
    sequenceNum,
    questionTypeNumber,
  })
  const event = AssemblyUnitCreatedEvent.make({
    pk,
    sk: pk,
    courseId: courseId as string,
    eventAgent: username as string,
    id,
    assemblyUnit,
  })
  await writeMany([
    { table: 'authoring', record: assemblyUnit },
    { table: 'events', record: event },
  ])
  assemblyUnits.push(assemblyUnit)
  const associationRecord: WriteEntry[] = []
  if (parentAssemblyUnit.id) {
    // create the AU association if there's a parent ID
    const assemblyUnitAssociationRecord = prepareAssociationRecords({
      parentEntity: parentAssemblyUnit,
      childEntities: [assemblyUnit],
      courseId: courseId as string,
      createdByUsername: username as string,
      associationChildType: 'AssemblyUnit',
    })
    associationRecord.push(...assemblyUnitAssociationRecord)
  }
  if (assets && assets.length) {
    const assetAssociationRecord = prepareAssociationRecords({
      parentEntity: assemblyUnit,
      childEntities: assets,
      courseId: courseId as string,
      createdByUsername: username as string,
      associationChildType: 'Asset',
    })
    associationRecord.push(...assetAssociationRecord)
  }
  const chunkedWrites = chunk(associationRecord, 99)
  await Promise.all(chunkedWrites.map((writes) => writeMany(writes)))
  // check if more children AUs needs to be created
  if (assemblyUnitTypeChildMapping[assemblyUnitType]) {
    // max is 9 sequence for sequence AUs but making it small on purpose to reduce time to generate data
    const processedSequenceLength =
      sequenceLength != null ? Math.min(sequenceLength, 10) : casual.integer(1, 3)

    // making complete AUs so length is different from sequenceNum but making it small on purpose to reduce time to generate data
    const processedCompleteLength =
      completeLength != null ? Math.min(completeLength, 10) : casual.integer(1, 3)

    const length =
      assemblyUnitTypeChildMapping[assemblyUnitType] === AssemblyUnitType.COMPLETE
        ? processedCompleteLength
        : processedSequenceLength

    const generatedChildrenIds = await generateBatchIDs(length)
    progressBar?.setTotal(progressBar.getTotal() + length)
    for (let j = 0; j < length; j++) {
      const childAssemblyUnits = await assemblyUnitGeneratorHelper({
        id: generatedChildrenIds[j] as string,
        parentAssemblyUnit: {
          ...assemblyUnit,
          assemblyUnitType: assemblyUnitTypeChildMapping[assemblyUnitType],
          sequenceNum: j,
        },
        assets,
        sequenceLength: processedSequenceLength,
        completeLength: processedCompleteLength,
      })
      assemblyUnits.push(...childAssemblyUnits)
      progressBar?.increment()
    }
  }
  return assemblyUnits
}

export default async function assemblyUnitGenerator({
  ids,
  consola,
  progressBar,
  courseIDs,
  user,
  length = 10,
  assetLength = 10,
  nextGenAssemblyUnitProps = {},
  assetProps = {},
  useExistingAssets,
  sequenceLength,
  completeLength,
}: AssemblyUnitGeneratorProps): Promise<DataGeneratorResults> {
  const prefix = '[assembly-unit-generator]:'
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()

  const assetIdsForE2E: string[][] = []
  const assemblyUnits: AssemblyUnit[] = []
  const {
    questionType: defaultQuestionType,
    adminYear: defaultAdminYear,
    questionTypeNumber: defaultQuestionTypeNumber,
    baseForm: defaultBaseForm,
    formCode: defaultFormCode,
  } = nextGenAssemblyUnitProps
  const adminYearList = defaultAdminYear ? [defaultAdminYear] : adminYears
  const baseFormList = defaultBaseForm ? [defaultBaseForm] : baseForms
  const formCodeList = defaultFormCode ? [defaultFormCode] : formCodes
  const questionTypeNumberList = defaultQuestionTypeNumber
    ? [defaultQuestionTypeNumber]
    : questionTypeNumbers

  // regular for-loop lets us generate data sequentially
  for (const courseId of courseIDs) {
    if (courseData && courseData[courseId]) {
      const course = courseData[courseId] as SubjectKeyMapping
      // check if subject is null or undefined
      if (course == null) {
        throw new Error('courseID is invalid')
      }
      consola?.info(
        prefix,
        `Generating ${length} base assembly units for ${adminYearList.length} admin years along with children assembly units for ${course.name}`
      )
      progressBar?.start(length * adminYearList.length, 0)
      for (let adminYearIndex = 0; adminYearIndex < adminYearList.length; adminYearIndex++) {
        const adminYear = adminYearList[adminYearIndex]
        const generatedIDs = ids || (await generateBatchIDs(length))
        // create the assembly units
        for (let i = 0; i < generatedIDs.length; i++) {
          const id = generatedIDs[i] as string
          const username =
            user?.username ?? ((casual.random_element(users) as User).username as string)
          const questionType = defaultQuestionType || casual.random_element(questionTypes)
          const assetsToAdd: Asset[] = []
          if (useExistingAssets) {
            const randomAssetDataSubset = await getRandomAssets({
              properties: { subjectID: courseId },
              params: {
                limit: 100,
                index: 'subjectIDGSI',
                where:
                  '(${questionType} = @{questionType}) AND ((${workflowState} = @{sentToVaultID}) OR (${workflowState} = @{readyForUse}))',
                substitutions: {
                  questionType,
                  sentToVaultID: WorkflowStepID.SENT_TO_VAULT,
                  readyForUse: WorkflowStepID.READY_FOR_USE,
                },
              },
              length: assetLength,
            })
            assetsToAdd.push(...randomAssetDataSubset)
          } else {
            const { data } = await assetGenerator({
              user,
              courseIDs: [courseId],
              length: assetLength,
              workflowSteps: [WorkflowStepID.READY_FOR_USE, WorkflowStepID.SENT_TO_VAULT],
              assetProps: {
                ...assetProps,
                itemType: capitalCase(questionType || '', {
                  keepSpecialCharacters: false,
                }).replace(/ /g, '') as ItemType,
              },
            })
            assetsToAdd.push(...(data as unknown as Asset[]))
          }
          const baseForm = casual.random_element(baseFormList)
          const questionTypeNumber = casual.random_element(questionTypeNumberList)
          const formCode = casual.random_element(formCodeList)
          const allAssemblyUnits = await assemblyUnitGeneratorHelper({
            id,
            parentAssemblyUnit: {
              createdByUsername: username,
              courseId,
              questionType,
              baseForm,
              questionTypeNumber,
              formCode,
              adminYear,
            },
            assets: assetsToAdd,
            progressBar,
            sequenceLength,
            completeLength,
          })
          assetIdsForE2E.push(assetsToAdd.map(({ id }) => id as string))
          assemblyUnits.push(...allAssemblyUnits)
          progressBar?.increment()
        }
      }
      progressBar?.stop()
    }
  }

  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: assemblyUnits, assetIdsForE2E, timeElapsed }
}
