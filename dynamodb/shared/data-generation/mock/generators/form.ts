/* eslint-disable max-lines-per-function */
import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import type { Asset } from '@shared/dynamo-hummingbird'
import { WriteEntry, writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import { AssemblyUnitType, FormModel } from '@shared/dynamo-hummingbird/tables/split-models'
import { AssemblyUnitSnapshot, EventNamePrefixConstants } from '@shared/event-sourcing'
import type { AssemblyUnit } from '@shared/schema/split-models'
import { DirectionsTypeId } from '@shared/types/model'
import {
  generateFormAssemblyUnitsAdded,
  generateFormCreated,
  generateFormDirectionsAdded,
} from '../forms'
import generateFormWorkflowUpdated from '../forms/generate-form-workflow-updated'
import type { DataGeneratorResults, FormGeneratorProps, SubjectKeyMapping } from '../types'
import {
  ensureSafeToGenerateData,
  getRandomAssetsWithUpdatedAssociations,
  getRandomNextGenAssemblyUnitsWithoutDuplicateAssets,
  getRandomSubsetFromArray,
  subjectData,
} from '../utils'
import assemblyUnitGenerator from './assembly-unit'
import directionGenerator from './direction'

export default async function formGenerator({
  ids,
  consola,
  progressBar,
  courseIDs,
  length = 10,
  assemblyUnitLength = 10,
  assetLength = 10,
  examDirectionLength = 1,
  sectionDirectionLength = 2,
  user,
  formProps = {},
  assetProps = {},
  nextGenAssemblyUnitProps = {},
  directionProps = {},
  workflowSteps,
  useExistingDirections,
  useExistingAssemblyUnits,
}: FormGeneratorProps): Promise<DataGeneratorResults> {
  const prefix = '[form-generator]:'
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()

  const forms: FormModel[] = []
  const assetIdsForE2E: string[][] = []
  const directionIdsForE2E: string[][] = []
  // regular for-loop lets us generate data sequentially
  for (const subjectId of courseIDs) {
    if (subjectData && subjectData[subjectId]) {
      const subject = subjectData[subjectId] as SubjectKeyMapping
      // check if subject is null or undefined
      if (subject == null) {
        throw new Error('subjectID is invalid')
      }
      const generatedIDs = ids || (await generateBatchIDs(length))
      consola?.info(
        prefix,
        `Generating ${generatedIDs.length} forms for ${subject.name} with ${assemblyUnitLength} assembly units spread between 2 sections`
      )
      progressBar?.start(generatedIDs.length, 0)
      // create the forms using writeMany
      const eventWrites: WriteEntry[] = []
      const authoringWrites: WriteEntry[] = []
      for (let i = 0; i < generatedIDs.length; i++) {
        const id = generatedIDs[i] as string
        // creating a form
        const {
          event: formCreatedEvent,
          form,
          sections,
        } = await generateFormCreated({
          ...(formProps as Omit<FormModel, 'id' | 'courseId'>),
          id,
          courseId: subjectId,
        })
        eventWrites.push({ table: 'events', record: formCreatedEvent })
        authoringWrites.push({ table: 'authoring', record: form })
        sections.forEach((section) => authoringWrites.push({ table: 'authoring', record: section }))

        // generate exam directions for form
        const examDirections: Asset[] = []
        if (useExistingDirections) {
          const randomDirectionDataSubset = await getRandomAssetsWithUpdatedAssociations({
            properties: {
              subjectID: subjectId,
              directionsType: DirectionsTypeId.DIRECTIONS_EXAM,
            },
            params: { limit: 100, index: 'subjectIDGSI' },
            associationID: id,
            associationType: EventNamePrefixConstants.Form,
            length: 1,
          })
          examDirections.push(...randomDirectionDataSubset)
        } else {
          const { data } = await directionGenerator({
            user,
            courseIDs: [subjectId],
            length: examDirectionLength,
            assetProps: {
              ...directionProps,
              directionsType: DirectionsTypeId.DIRECTIONS_EXAM,
            },
          })
          examDirections.push(...(data as unknown as Asset[]))
        }
        // add exam directions to form
        const examDirectionId = examDirections[0]?.id
        if (examDirectionId) {
          const { event: examDirectionAddedEvent, association: examDirectionAssociation } =
            generateFormDirectionsAdded({
              form,
              directionId: examDirectionId,
              directionType: DirectionsTypeId.DIRECTIONS_EXAM,
            })
          eventWrites.push({ table: 'events', record: examDirectionAddedEvent })
          authoringWrites.push({ table: 'authoring', record: examDirectionAssociation })
        }

        // generate section directions for form
        const sectionDirections: Asset[] = []
        if (useExistingDirections) {
          const randomDirectionDataSubset = await getRandomAssetsWithUpdatedAssociations({
            properties: {
              subjectID: subjectId,
              directionsType: DirectionsTypeId.DIRECTIONS_SECTION,
            },
            params: { limit: 100, index: 'subjectIDGSI' },
            associationID: id,
            associationType: EventNamePrefixConstants.Form,
            length: 2,
          })
          sectionDirections.push(...randomDirectionDataSubset)
        } else {
          const { data } = await directionGenerator({
            user,
            courseIDs: [subjectId],
            length: sectionDirectionLength,
            assetProps: {
              ...directionProps,
              directionsType: DirectionsTypeId.DIRECTIONS_SECTION,
            },
            workflowSteps,
          })
          sectionDirections.push(...(data as unknown as Asset[]))
        }
        // adding section 1 directions to form
        const sectionOneId = sections[0]?.id as string
        const sectionOneDirectionId = sectionDirections[0]?.id
        if (sectionOneDirectionId) {
          const {
            event: sectionOneDirectionAddedEvent,
            association: sectionOneDirectionAssociation,
          } = generateFormDirectionsAdded({
            form,
            directionId: sectionOneDirectionId,
            directionType: DirectionsTypeId.DIRECTIONS_SECTION,
            sectionId: sectionOneId,
          })
          eventWrites.push({ table: 'events', record: sectionOneDirectionAddedEvent })
          authoringWrites.push({ table: 'authoring', record: sectionOneDirectionAssociation })
        }

        // adding section 2 directions to form
        const sectionTwoId = sections[1]?.id as string
        const sectionTwoDirectionId = sectionDirections[1]?.id
        if (sectionTwoDirectionId) {
          const {
            event: sectionTwoDirectionAddedEvent,
            association: sectionTwoDirectionAssociation,
          } = generateFormDirectionsAdded({
            form,
            directionId: sectionTwoDirectionId,
            directionType: DirectionsTypeId.DIRECTIONS_SECTION,
            sectionId: sectionTwoId,
          })
          eventWrites.push({ table: 'events', record: sectionTwoDirectionAddedEvent })
          authoringWrites.push({ table: 'authoring', record: sectionTwoDirectionAssociation })
        }

        // generate assembly units for form
        const assemblyUnits: AssemblyUnitSnapshot[] = []
        if (useExistingAssemblyUnits) {
          const randomAssemblyUnitDataSubset =
            await getRandomNextGenAssemblyUnitsWithoutDuplicateAssets({
              assemblyUnitType: AssemblyUnitType.COMPLETE,
              courseId: subjectId,
              length: assemblyUnitLength,
            })
          assemblyUnits.push(...randomAssemblyUnitDataSubset)
        } else {
          // create assembly units
          const { data, assetIdsForE2E: assemblyUnitAssetIdsForE2E } = await assemblyUnitGenerator({
            progressBar,
            consola,
            courseIDs: [subjectId],
            length: assemblyUnitLength,
            assetLength,
            nextGenAssemblyUnitProps: {
              adminYear: '26',
              ...nextGenAssemblyUnitProps,
            },
            assetProps,
            sequenceLength: 1,
            completeLength: 1,
          })
          const newCompleteAssemblyUnits = (data as AssemblyUnit[]).filter(
            (au: AssemblyUnit) => au.assemblyUnitType === AssemblyUnitType.COMPLETE
          )
          const newAssemblyUnits = getRandomSubsetFromArray(
            assemblyUnitLength,
            newCompleteAssemblyUnits
          ) as AssemblyUnit[]
          assemblyUnits.push(...newAssemblyUnits)
          assetIdsForE2E.push(assemblyUnitAssetIdsForE2E?.flat().filter(Boolean) ?? [])
        }
        const assemblyUnitIDs = assemblyUnits.map((au) => au.id as string)
        const isDefined = <T>(a: T | undefined): a is T => a !== undefined
        const allDirections = examDirections.concat(sectionDirections)
        directionIdsForE2E.push(allDirections.map((direction) => direction.id).filter(isDefined))
        const assemblyUnitIDMidLength = Math.trunc(assemblyUnitIDs.length / 2)
        // add form assembly unit to section 1
        assemblyUnitIDs.slice(0, assemblyUnitIDMidLength).forEach((assemblyUnitId, idx) => {
          const {
            event: sectionOneAssemblyUnitsAddedEvent,
            association: sectionOneAssemblyUnitsAddedAssociation,
          } = generateFormAssemblyUnitsAdded({
            form,
            assemblyUnitId,
            ordinal: idx + 1,
            sectionId: sectionOneId,
          })
          eventWrites.push({ table: 'events', record: sectionOneAssemblyUnitsAddedEvent })
          authoringWrites.push({
            table: 'authoring',
            record: sectionOneAssemblyUnitsAddedAssociation,
          })
        })

        // add form assembly unit to section 2
        assemblyUnitIDs.slice(assemblyUnitIDMidLength).forEach((assemblyUnitId, idx) => {
          const {
            event: sectionTwoAssemblyUnitsAddedEvent,
            association: sectionTwoAssemblyUnitsAddedAssociation,
          } = generateFormAssemblyUnitsAdded({
            form,
            assemblyUnitId,
            ordinal: idx + 1,
            sectionId: sectionTwoId,
          })
          eventWrites.push({ table: 'events', record: sectionTwoAssemblyUnitsAddedEvent })
          authoringWrites.push({
            table: 'authoring',
            record: sectionTwoAssemblyUnitsAddedAssociation,
          })
        })

        // generate form workflow steps
        const { event: workflowStepsUpdatedEvent, workflowSteps: formWorkflowSteps } =
          generateFormWorkflowUpdated({
            form,
            workflowSteps: (formProps as FormModel).workflowSteps,
            chooseRandomWorkflowSteps: !(formProps as FormModel).workflowSteps,
          })
        if (workflowStepsUpdatedEvent)
          eventWrites.push({ table: 'events', record: workflowStepsUpdatedEvent })

        // update workflow steps
        if (formWorkflowSteps) form.workflowSteps = formWorkflowSteps
        // update assembly unit count
        form.assemblyUnitCount = assemblyUnitIDs.length
        // track the new forms that were created
        forms.push(form)
        // make the progress bar move
        progressBar?.increment()
        // write all the request for this form
        await writeMany([...eventWrites, ...authoringWrites])
      }
      progressBar?.stop()
    }
  }
  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: forms, timeElapsed, assetIdsForE2E, directionIdsForE2E }
}
