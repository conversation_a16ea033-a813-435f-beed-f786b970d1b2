import casual from 'casual'
import { users } from '@bin/data/users'
import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import type { Asset, User } from '@shared/dynamo-hummingbird'
import { writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import { SUBJECT_ID_GSI } from '@shared/dynamo-hummingbird/tables/item-cloud-green'
import { QuestionType } from '@shared/dynamo-hummingbird/tables/split-models'
import {
  CollectionAssociationModel,
  type CollectionModel,
  makeCollection,
  makeCollectionAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models/collections'
import { CollectionCreatedEvent } from '@shared/event-models/collections'
import { EventNamePrefixConstants } from '@shared/event-sourcing'
import { ItemType as ItemTypeEnum } from '@shared/schema'
import type { CollectionGeneratorProps, DataGeneratorResults, SubjectKeyMapping } from '../types'
import {
  ensureSafeToGenerateData,
  getRandomAssetsWithUpdatedAssociations,
  subjectData,
} from '../utils'
import assetGenerator from './asset'

const questionTypes = Object.values(QuestionType).filter((type) => type !== QuestionType.PROJECT)

async function collectionGeneratorHelper({
  id,
  collectionProps,
  assets = [],
}: {
  id: string
  collectionProps: Partial<Omit<CollectionModel, 'id'>>
  assets?: Asset[]
}) {
  const { name, courseId, createdByUsername, ...rest } = collectionProps
  const pk = `Collection#${id}`
  const hbid = id

  const itemCount = assets.reduce((a, b) => a + (b.items?.length || 0), 0)
  const collection: CollectionModel = makeCollection({
    pk: pk,
    sk: id,
    id: id,
    hbid,
    courseId: courseId as string,
    name: name as string,
    createdByUsername: createdByUsername as string,
    assetCount: assets.length,
    itemCount: itemCount,
    updatedByUsername: createdByUsername as string,
    updatedAtMs: new Date().getTime(),
    ...rest,
  })

  const { associations, collectionAssets } = assets.reduce(
    (out, asset, i) => {
      out.associations.push(
        makeCollectionAssociation({
          pk: pk,
          sk: `Association#${asset.id}`,
          associationChildType: 'Asset',
          associationChildId: asset.id as string,
          createdByUsername: createdByUsername as string,
          courseId: asset.subjectID as string,
          on: {
            ordinal: i,
            items: asset.items?.map((item) => ({ id: item.id as string, active: true })) ?? [],
          },
        })
      )

      out.collectionAssets.push(asset.id as string)

      return out
    },
    { associations: [], collectionAssets: [] } as {
      associations: CollectionAssociationModel[]
      collectionAssets: string[]
    }
  )

  const createdEvent = CollectionCreatedEvent.make({
    pk: pk,
    sk: '',
    id: id,
    collectionId: id,
    hbid: hbid,
    collection,
    assets: collectionAssets,
    courseId: courseId as string,
    eventAgent: createdByUsername as string,
  })

  // Write records, then we'll do events, since it could have tons of associations
  await writeMany([
    { table: 'authoring', record: collection },
    ...associations.map((a) => ({
      table: 'authoring' as const,
      record: a,
    })),
    { table: 'events', record: createdEvent },
  ])

  return collection
}

export default async function collectionGenerator({
  ids,
  consola,
  progressBar,
  courseIDs,
  user,
  length = 10,
  assetLength = 10,
  collectionProps = {},
  assetProps = {},
  workflowSteps,
  assetIds,
  useExistingAssets,
  ttl,
}: CollectionGeneratorProps): Promise<DataGeneratorResults> {
  const prefix = '[collection-generator]:'
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()

  const { itemType } = assetProps
  const defaultQuestionType = itemType
    ? itemType === ItemTypeEnum.MultipleChoiceQuestion
      ? QuestionType.MULTIPLE_CHOICE_QUESTION
      : itemType === ItemTypeEnum.FreeResponseQuestion
      ? QuestionType.FREE_RESPONSE_QUESTION
      : undefined
    : undefined

  const assetIdsForE2E: string[][] = []
  const collections: CollectionModel[] = []
  // regular for-loop lets us generate data sequentially
  for (const subjectId of courseIDs) {
    if (subjectData && subjectData[subjectId]) {
      const subject = subjectData[subjectId] as SubjectKeyMapping
      // check if subject is null or undefined
      if (subject == null) {
        throw new Error('subjectID is invalid')
      }
      const generatedIDs = ids || (await generateBatchIDs(length, undefined, ttl))
      consola?.info(
        prefix,
        `Generating ${generatedIDs.length} collections for ${subject.name} with ${assetLength} assets`
      )
      progressBar?.start(generatedIDs.length, 0)
      // create the collections
      for (let i = 0; i < generatedIDs.length; i++) {
        const id = generatedIDs[i] as string
        const username =
          user?.username ?? ((casual.random_element(users) as User).username as string)
        const questionType = defaultQuestionType || casual.random_element(questionTypes)
        const name = collectionProps.name || casual.title
        // generate assets to add to collection
        const assetsToAdd: Asset[] = []
        if (assetIds && assetIds.length > 0) {
          const assetIdSet = new Set(assetIds)
          const randomAssetDataSubset = await getRandomAssetsWithUpdatedAssociations({
            properties: { subjectID: subjectId, sk: 'Asset#v0' },
            params: {
              limit: 100,
              where: '${id} IN (@{...assetIds})',
              substitutions: { assetIds: [...assetIdSet] },
              index: SUBJECT_ID_GSI,
            },
            associationID: id,
            associationType: EventNamePrefixConstants.Collection,
            length: assetIdSet.size,
          })
          assetsToAdd.push(...randomAssetDataSubset)
        } else if (useExistingAssets) {
          let where = '(${questionType} = @{questionType})'
          if (workflowSteps && workflowSteps.length) {
            where += ' AND (${workflowState} IN (@{...workflowSteps}))'
          }
          const randomAssetDataSubset = await getRandomAssetsWithUpdatedAssociations({
            properties: { subjectID: subjectId },
            params: {
              limit: 100,
              index: 'subjectIDGSI',
              where,
              substitutions: { questionType, workflowSteps },
            },
            associationID: id,
            associationType: EventNamePrefixConstants.Collection,
            length: assetLength,
          })
          assetsToAdd.push(...randomAssetDataSubset)
        } else {
          const { data } = await assetGenerator({
            user,
            courseIDs: [subjectId],
            length: assetLength,
            workflowSteps,
            assetProps: {
              ...assetProps,
              associations: [
                {
                  id,
                  type: EventNamePrefixConstants.Collection,
                },
              ],
            },
            ttl,
          })
          assetsToAdd.push(...(data as unknown as Asset[]))
        }
        const collection = await collectionGeneratorHelper({
          id,
          collectionProps: {
            ...collectionProps,
            name,
            createdByUsername: username,
            courseId: subjectId,
          },
          assets: assetsToAdd,
        })
        progressBar?.increment()
        collections.push(collection)
        assetIdsForE2E.push(assetsToAdd.map((asset) => asset.id as string))
      }
      progressBar?.stop()
    }
  }

  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: collections, assetIdsForE2E, timeElapsed }
}
