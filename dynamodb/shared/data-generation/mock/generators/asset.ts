import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import { Asset, tables } from '@shared/dynamo-hummingbird'
import { WriteEntry, writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import { AssetCreatedEvent } from '@shared/event-models/assets'
import { loadCourseConfigFromDb } from '@shared/trpc-lambda/context/course-service'
import { getCourseConfigFromFile } from '@shared/trpc-lambda/mocks'
import { oldWorkflowStageToNewWorkflowStep } from '@shared/types/model/workflow/types'
import { getDefaultMetadata } from '@shared/utils/metadata'
import { generateAsset } from '../assets'
import type { AssetGeneratorProps, DataGeneratorResults, SubjectKeyMapping } from '../types'
import { ensureSafeToGenerateData, subjectData } from '../utils'

/** Bulk generate assets to populate a database */
export default async function assetGenerator({
  ids,
  consola,
  progressBar,
  courseIDs,
  user,
  length = 10,
  workflowSteps,
  assetProps = {},
  ttl,
}: AssetGeneratorProps): Promise<DataGeneratorResults> {
  const AssetModel = tables.itemCloudGreen.getModel('Asset')
  const prefix = '[asset-generator]:'
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()
  const useDbConfig = process.env.NODE_ENV !== 'test' && process.env.ENV !== 'local'
  const assets: Asset[] = []
  // regular for-loop lets us generate data sequentially
  for (const subjectId of courseIDs) {
    if (subjectData && subjectData[subjectId]) {
      const subject = subjectData[subjectId] as SubjectKeyMapping
      // check if subject is null or undefined
      if (subject == null) {
        throw new Error('subjectID is invalid')
      }
      
      // Use provided IDs or generate new ones
      const generatedIDs = ids ?? (await generateBatchIDs(length, undefined, ttl))
      consola?.info(prefix, `Generating ${generatedIDs.length} assets for ${subject.name}`)
      progressBar?.start(generatedIDs.length, 0)
      // create the assets using batchWrite
      let batch = {}
      let assetCreatedWriteEntries: WriteEntry<AssetCreatedEvent>[] = []
      for (let i = 0; i < generatedIDs.length; i++) {
        const id = generatedIDs[i] as string
        // generate a new asset
        const workflowStateId = assetProps?.workflowStateId
          ? assetProps.workflowStateId
          : workflowSteps
          ? workflowSteps[i % workflowSteps.length]
          : undefined
        let { metadataVersion, metadataId } = assetProps
        if (!metadataVersion) {
          const config = useDbConfig
            ? await loadCourseConfigFromDb(subjectId, false)
            : getCourseConfigFromFile(subjectId)
          const metadataConfig = getDefaultMetadata(config)
          metadataVersion = metadataConfig.updated
          metadataId = metadataConfig.id
        }
        const asset = await generateAsset({
          ...assetProps,
          metadataVersion,
          metadataId,
          id,
          subjectId,
          userId: user?.userId,
          username: user?.username,
          workflowStepId:
            workflowStateId && workflowStateId !== 'RANDOM'
              ? oldWorkflowStageToNewWorkflowStep[workflowStateId]
              : undefined,
          ttl,
        })
        if (!asset) continue
        progressBar?.increment()
        AssetModel.create(asset, { batch })
        const event = AssetCreatedEvent.parse({
          pk: `Asset#${id}`,
          sk: `${asset.created}#AssetCreatedEvent`,
          type: 'AssetCreatedEvent',
          id,
          assetId: id,
          courseId: asset.subjectID,
          sequenceNumber: 0,
          asset,
          items: asset.items ?? [],
          stimuli: asset.stimuli ?? [],
          eventTimestamp: asset.created,
          eventAgent: asset.createdByUsername,
        })

        assetCreatedWriteEntries.push({
          table: 'events',
          record: event,
          remove: false,
        })
        // track the new assets that were created
        assets.push(asset)
        if ((i > 0 && i % 20 === 0) || i === generatedIDs.length - 1) {
          await tables.itemCloudGreen.batchWrite(batch)
          await writeMany(assetCreatedWriteEntries.filter(Boolean), { transact: false })
          batch = {}
          assetCreatedWriteEntries = []
        }
      }
      progressBar?.stop()
    }
  }
  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: assets, timeElapsed }
}
