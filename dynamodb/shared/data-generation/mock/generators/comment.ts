import casual from 'casual'
import { WriteEntry, writeMany } from '@shared/dynamo-hummingbird/tables/io/write'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'
import {
  generateCommentCreated,
  generateCommentEdited,
  generateCommentRemoved,
  generateCommentResolved,
} from '../comments'
import type { CommentDataGeneratorResults, CommentGeneratorProps } from '../types'
import { ensureSafeToGenerateData } from '../utils'

export default async function commentGenerator({
  progressBar,
  commentPropsList = [],
  length = 1,
}: CommentGeneratorProps): Promise<CommentDataGeneratorResults> {
  const start = Date.now()
  // make sure we're not in prod
  ensureSafeToGenerateData()

  const commentEventKeyList = ['CommentContentChangedEvent', 'CommentRemovedEvent']
  const commentEventGeneratorMap = {
    CommentContentChangedEvent: generateCommentEdited,
    CommentRemovedEvent: generateCommentRemoved,
    CommentResolved: generateCommentResolved,
  }
  const comments: CommentModel[] = []
  progressBar?.start(commentPropsList.length * length, 0)
  // regular for-loop lets us generate data sequentially
  for (const commentProps of commentPropsList) {
    for (let index = 0; index < length; index++) {
      const { randomEventLength = casual.integer(0, 1), ...rest } = commentProps

      // creating a comment
      const { comment, event } = generateCommentCreated({
        ...rest,
      } as CommentModel)

      if (!event) {
        throw new Error("Unable to generate 'CommentCreated' event")
      }
      if (!comment) {
        throw new Error("Unable to generate 'Comment' object following a 'CommentCreated' event")
      }

      await writeMany([
        { table: 'authoring', record: comment },
        { table: 'events', record: event },
      ])

      const recordsToWrite: WriteEntry[] = []
      for (let i = 0; i < randomEventLength; i++) {
        const randomEvent: string = casual.random_element(commentEventKeyList)
        const eventGenerator =
          commentEventGeneratorMap[randomEvent as keyof typeof commentEventGeneratorMap]
        if (eventGenerator) {
          const { event: newEvent, comment: newComment } = eventGenerator(comment as CommentModel)

          newComment &&
            recordsToWrite.push({
              table: 'authoring',
              record: newComment,
              ...(randomEvent.includes('Removed') ? { remove: true } : {}),
            })
          recordsToWrite.push({ table: 'events', record: newEvent })
        }
      }

      if (recordsToWrite.length) await writeMany(recordsToWrite)

      // track the new comments that were created
      comments.push(comment as CommentModel)
      progressBar?.increment()
    }
  }

  progressBar?.stop()
  const end = Date.now()
  const timeElapsed = (end - start) / 1000
  return { data: comments, timeElapsed }
}
