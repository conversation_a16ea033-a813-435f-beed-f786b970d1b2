import type { SingleBar } from 'cli-progress'
import type { Consol<PERSON> } from 'consola'
import type { One<PERSON>arams, OneProperties } from 'dynamodb-onetable'
import type { SubjectIdentifier } from '@shared/config'
import type { AssemblyUnitCreated, Asset, FormCreated, User } from '@shared/dynamo-hummingbird'
import type { AssemblyUnitModel, FormModel } from '@shared/dynamo-hummingbird/tables/split-models'
import type { CollectionModel } from '@shared/dynamo-hummingbird/tables/split-models/collections'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'
import type { Event, EventNamePrefix, Snapshot } from '@shared/event-sourcing'
import type { ItemType as ItemTypeEnum, StimulusFormat, SubType } from '@shared/schema'
import type { AssemblyUnit, Comment } from '@shared/schema/split-models'
import type { AssetAssociations } from '@shared/types/associations'
import type { DirectionsTypeId, SetLayouts } from '@shared/types/model'
import { DisplayTypeId } from '@shared/types/model/previewer/display-type'
import type { WorkflowStep, WorkflowStepID } from '@shared/types/model/workflow/types'

export type ItemType =
  | typeof ItemTypeEnum.MultipleChoiceQuestion
  | typeof ItemTypeEnum.FreeResponseQuestion
  | typeof ItemTypeEnum.directions

export interface GenerateAssetProps {
  id: string
  subjectId?: string
  subType?: SubType
  itemType?: ItemType
  userId?: string
  username?: string
  workflowStepId?: WorkflowStep
  workflowStateId?: WorkflowStepID
  associations?: AssetAssociations
  directionsType?: DirectionsTypeId
  itemCount?: number
  isMultiSelect?: boolean
  setSetLayout?: SetLayouts
  stimulusType?: StimulusFormat
  excludeVaultId?: boolean
  ttl?: number
  metadataId?: string
  metadataVersion?: string
  displayType?: DisplayTypeId
  customId?: string
}

export interface GenerateAssetOpts {
  checkProgramAllowedSendToVault?: boolean
}

export interface GenerateAssetItemsProps extends GenerateAssetProps {
  friendlyID: string
  user: {
    userId: string
    username: string
  }
  isMultiSelect?: boolean
  questionType: ItemType
  subject: SubjectKeyMapping
}

export interface GenerateAssetItemSetProps {
  asset: Asset
  subject: SubjectKeyMapping
  subType?: SubType
  questionType: ItemType
}

export interface SubjectKeyMapping {
  upperCaseName: string // e.g. PRECALCULUS
  id: string
  name: string // e.g. Precalculus
  program: {
    id: string
    name: string
  }
  itemTypes: ItemType[]
}

export type SubjectIdentifierType = Exclude<SubjectIdentifier, SubjectIdentifier.UNKNOWN_SUBJECT>

export type EntityModel = AssemblyUnitModel

export interface DataGeneratorResults {
  data: (Asset | Snapshot | EntityModel)[]
  timeElapsed: number
  assetIdsForE2E?: string[][]
  directionIdsForE2E?: string[][]
}
export interface CommentDataGeneratorResults {
  data: CommentModel[]
  timeElapsed: number
}

interface DataGeneratorProps {
  courseIDs: string[]
  length?: number
  consola?: Consola // if passed in, will be used to print log messages
  progressBar?: SingleBar // if passed in, will be used to print progress bar
  user?: User
  ttl?: number
}

export interface CommentProps {
  commentTargetType: Comment['commentTargetType']
  commentTargetId: Comment['commentTargetId']
  id?: string
  parentCommentId?: string
  createdById?: string
  content?: string
  courseId?: string
  randomEventLength?: number // number of random events to generate (does not include created events)
  on?: Comment['on']
}

export interface AssetGeneratorProps extends DataGeneratorProps {
  ids?: string[]
  workflowSteps?: (WorkflowStepID | 'RANDOM')[]
  //TODO #3097 fix this type
  assetProps?: Omit<GenerateAssetProps, 'id' | 'subjectId' | 'workflowStepId'> & {
    workflowStateId?: WorkflowStepID
  }
  customIds?: string[] // custom IDs to use for the assets
}

export interface CollectionGeneratorProps extends AssetGeneratorProps {
  collectionProps?: Partial<Omit<CollectionModel, 'id'>>
  assetLength?: number
  useExistingAssets?: boolean
  assetIds?: string[]
}

export interface AssemblyUnitGeneratorProps extends Omit<AssetGeneratorProps, 'workflowSteps'> {
  assemblyUnitProps?: Omit<AssemblyUnitCreated, 'id' | 'subjectID' | 'programID'>
  nextGenAssemblyUnitProps?: Partial<AssemblyUnit>
  assetLength?: number
  useExistingAssets?: boolean
  sequenceLength?: number
  completeLength?: number
}

export interface FormGeneratorProps extends AssetGeneratorProps, AssemblyUnitGeneratorProps {
  formProps?:
    | Omit<FormCreated, 'id' | 'subjectID' | 'programID'>
    | Omit<FormModel, 'id' | 'courseId'>
  directionProps?: Omit<GenerateAssetProps, 'id' | 'subjectId'>
  examDirectionLength?: number
  sectionDirectionLength?: number
  assemblyUnitLength?: number
  useExistingAssets?: boolean
  useExistingDirections?: boolean
  useExistingAssemblyUnits?: boolean
}

export interface CommentGeneratorProps {
  consola?: Consola // if passed in, will be used to print log messages
  progressBar?: SingleBar // if passed in, will be used to print progress bar
  commentPropsList?: CommentProps[]
  length?: number // number of comments per commentProps
}

export interface GenerateEventResults {
  event: Event
  snapshot?: Snapshot
  // TODO: Only use new comments
  comment?: Comment | CommentModel
}

export interface GetRandomDataParams {
  properties: OneProperties
  params: OneParams
  associationID: string
  associationType: EventNamePrefix
  length: number
  existingAssociations?: { id?: string; type?: string }[]
}
