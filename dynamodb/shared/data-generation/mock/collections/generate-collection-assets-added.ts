import { users } from '@bin/data/users'
import type { CollectionAssetsAdded, CollectionSnapshot } from '@shared/dynamo-hummingbird'
import { collectionEventHandlers } from '@shared/event-sourcing/snapshot/collection'
import { COLLECTION_MODEL_VERSION } from '@shared/utils/constants/constants'
import casual from 'casual'
import type { GenerateEventResults } from '../types'

const generateCollectionAssetsAdded = async (
  props: CollectionAssetsAdded,
  snapshot: CollectionSnapshot
): Promise<GenerateEventResults> => {
  const user = !props.userID ? casual.random_element(users) : null
  const snapshotSequenceNum =
    snapshot && snapshot.snapshotSequenceNum != null ? snapshot.snapshotSequenceNum + 1 : 1
  const collectionAssetsAdded: CollectionAssetsAdded = {
    ...props,
    id: snapshot.id,
    userID: user ? user.username : props.userID,
    sequenceNum: snapshotSequenceNum,
    modelVersion: COLLECTION_MODEL_VERSION,
    type: 'CollectionAssetsAdded',
    timestampMs: new Date().getTime(),
  }
  // every form action is followed by a snapshot
  if (!collectionEventHandlers.CollectionAssetsAdded) {
    throw new Error('CollectionAssetsAdded event handler is missing')
  }
  const collectionSnapshot = collectionEventHandlers.CollectionAssetsAdded(
    collectionAssetsAdded,
    snapshot
  )
  return {
    event: collectionAssetsAdded,
    snapshot: collectionSnapshot,
  }
}

export default generateCollectionAssetsAdded
