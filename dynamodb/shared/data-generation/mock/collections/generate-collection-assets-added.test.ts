import type { CollectionAssetsAdded, CollectionSnapshot } from '@shared/dynamo-hummingbird'
import generateCollectionAssetsAdded from './generate-collection-assets-added'

const inputTestCollectionSnapshot: CollectionSnapshot = {
  sequenceNum: 0,
  modelVersion: 1,
  assetIDs: [],
  snapshotSequenceNum: 1,
  type: 'CollectionSnapshot',
  id: 'ABC000000',
  timestampMs: 1685570318797,
}

const outputTestCollectionEvent: CollectionAssetsAdded = {
  sequenceNum: 2,
  modelVersion: 1,
  assetIDs: ['KTZ000000', 'WMX000000', 'TII000000', 'FFT000000', 'AEH000000'],
  id: 'ABC000000',
  type: 'CollectionAssetsAdded',
  userID: 'jmiller',
  timestampMs: 1685570318797,
}

const outputTestCollectionSnapshot: CollectionSnapshot = {
  sequenceNum: 0,
  modelVersion: 1,
  assetIDs: ['KTZ000000', 'WMX000000', 'TII000000', 'FFT000000', 'AEH000000'],
  snapshotSequenceNum: 2,
  type: 'CollectionSnapshot',
  id: 'ABC000000',
  timestampMs: 1685570318797,
}

describe('generate-collection-assets-added.ts unit tests', () => {
  it('should create two entries for "CollectionAssetsAdded" and "CollectionSnapshot" with the appropriate fields', async () => {
    // create an asset
    const { event: collectionAssetCreatedEvent, snapshot: collectionAssetCreatedSnapshot } =
      await generateCollectionAssetsAdded(
        {
          assetIDs: ['KTZ000000', 'WMX000000', 'TII000000', 'FFT000000', 'AEH000000'],
        },
        inputTestCollectionSnapshot
      )
    // expected
    const expectedCollectionAssetAddedFields = Object.keys(outputTestCollectionEvent).sort()
    const expectedCollectionSnapshotFields = Object.keys(outputTestCollectionSnapshot).sort()
    // received
    const collectionAssetAddedFields = Object.keys(collectionAssetCreatedEvent).sort()
    const collectionSnapshotFields = Object.keys(collectionAssetCreatedSnapshot).sort()
    // test object keys, ignore values as they are random
    expect(collectionAssetAddedFields).toEqual(expectedCollectionAssetAddedFields)
    expect(collectionSnapshotFields).toEqual(expectedCollectionSnapshotFields)
  })
})
