import { users } from '@bin/data/users'
import type { CollectionCreated } from '@shared/dynamo-hummingbird'
import { EventNamePrefixConstants } from '@shared/event-sourcing'
import { collectionEventHandlers } from '@shared/event-sourcing/snapshot/collection'
import { COLLECTION_MODEL_VERSION } from '@shared/utils/constants/constants'
import casual from 'casual'
import type { GenerateEventResults } from '../types'
import { getLatestSnapshotSequenceNum } from '../utils'

const generateCollectionCreated = async (
  props: CollectionCreated
): Promise<GenerateEventResults> => {
  const user = !props.creatorID ? casual.random_element(users) : null
  const snapshotSequenceNum = await getLatestSnapshotSequenceNum(
    EventNamePrefixConstants.Collection,
    props.id
  )
  const name = props.name ? props.name : casual.title
  const collectionCreated: CollectionCreated = {
    ...props,
    creatorID: user ? user.username : props.creatorID,
    name: name,
    lowercaseName: name.toLowerCase(),
    assetIDs: [],
    sequenceNum: snapshotSequenceNum,
    modelVersion: COLLECTION_MODEL_VERSION,
    type: 'CollectionCreated',
    timestampMs: new Date().getTime(),
    isActive: true,
  }
  // every form action is followed by a snapshot
  if (!collectionEventHandlers.CollectionCreated) {
    throw new Error('CollectionCreated event handler is missing')
  }
  // every collection action is followed by a snapshot
  const collectionSnapshot = collectionEventHandlers.CollectionCreated(collectionCreated, {})
  return {
    event: collectionCreated,
    snapshot: collectionSnapshot,
  }
}

export default generateCollectionCreated
