import type { CollectionCreated, CollectionSnapshot } from '@shared/dynamo-hummingbird'
import { COLLECTION_MODEL_VERSION } from '@shared/utils/constants/constants'
import generateCollectionCreated from './generate-collection-created'

const outputTestCollectionEvent: CollectionCreated = {
  sequenceNum: 1,
  modelVersion: COLLECTION_MODEL_VERSION,
  creatorID: 'japatterson',
  assetIDs: [],
  type: 'CollectionCreated',
  subjectID: '116',
  name: 'aut',
  lowercaseName: 'aut',
  id: 'ABC000000',
  programID: '59tWw',
  timestampMs: 1685570318627,
  isActive: true,
}

const outputTestCollectionSnapshot: CollectionSnapshot = {
  sequenceNum: 0,
  modelVersion: COLLECTION_MODEL_VERSION,
  creatorID: 'japatterson',
  assetIDs: [],
  snapshotSequenceNum: 2,
  type: 'CollectionSnapshot',
  subjectID: '116',
  name: 'aut',
  lowercaseName: 'aut',
  id: 'ABC000000',
  programID: '59tWw',
  timestampMs: 1685570318797,
  isActive: true,
}

describe('generate-collection-created.ts unit tests', () => {
  it('should create two entries for "CollectionCreated" and "CollectionSnapshot" with the appropriate fields', async () => {
    // create an asset
    const { event: collectionCreatedEvent, snapshot: collectionCreatedSnapshot } =
      await generateCollectionCreated({
        id: '6EE-C87899',
        subjectID: '116',
        programID: '59tWw',
      })
    // expected
    const expectedCollectionCreatedFields = Object.keys(outputTestCollectionEvent).sort()
    const expectedCollectionSnapshotFields = Object.keys(outputTestCollectionSnapshot).sort()
    // received
    const collectionCreatedFields = Object.keys(collectionCreatedEvent).sort()
    const collectionSnapshotFields = Object.keys(collectionCreatedSnapshot).sort()
    // test object keys, ignore values as they are random
    expect(collectionCreatedFields).toEqual(expectedCollectionCreatedFields)
    expect(collectionSnapshotFields).toEqual(expectedCollectionSnapshotFields)
  })
})
