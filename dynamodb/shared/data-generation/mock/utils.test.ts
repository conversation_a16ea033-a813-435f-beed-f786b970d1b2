import casual from 'casual'
import type { OneProperties } from 'dynamodb-onetable'
import { AssemblyUnitSnapshotModel, AssetModel, tables } from '@shared/dynamo-hummingbird'
import {
  AssemblyUnitModel,
  AssemblyUnitType,
  BaseForm,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { EventNamePrefixConstants } from '@shared/event-sourcing'
import * as mocks from '@shared/trpc-lambda/mocks'
import { WorkflowStep } from '@shared/types/model/workflow/types'
import {
  constructParagraphTag,
  generateDisplayName,
  getRandomAssemblyUnitInfo,
  getRandomAssemblyUnitsWithoutDuplicateAssets,
  getRandomAssetsWithUpdatedAssociations,
  getRandomWorkflowStep,
  populateAssociationOnProperty,
  prepareAssociationRecords,
} from './utils'

describe('utils tests', () => {
  afterEach(() => {
    jest.restoreAllMocks()
  })
  it('should build a paragpraph', () => {
    const expectedOutput = `<p class="atest">coool test</p>`
    const result = constructParagraphTag('atest', 'coool test')
    expect(result).toEqual(expectedOutput)
  })
  it('should generate a display name', () => {
    jest.spyOn(casual, 'integer').mockReturnValue(10)
    const result = generateDisplayName('atestprogram', 'atestCourse')
    expect(result).toContain('atestprogram')
    expect(result).toContain('atestCourse')
    expect(result).toContain('10')
    jest.resetAllMocks()
  })

  it('should get a random workflowstate', () => {
    const result = getRandomWorkflowStep()
    const workflows = Object.values(WorkflowStep) as unknown[]
    expect(workflows.includes(result)).toBe(true)
  })

  it('should get specific workflowstate if called', () => {
    const result = getRandomWorkflowStep(WorkflowStep.InternalContentReview1)
    expect(result).toEqual(WorkflowStep.InternalContentReview1)
  })
  it('should generate a list of assets with updated associations', async () => {
    const list = [mocks.assetFRQ, mocks.assetMCQ]
    jest.spyOn(tables.itemCloudGreen, 'transact').mockImplementation(jest.fn())
    jest.spyOn(tables.itemCloudGreen, 'getModel').mockImplementation(() => {
      return {
        find: () =>
          list.map((asset) => ({
            ...asset,
            associations: [
              ...(asset.associations || []),
              { id: 'XYZ000000', type: EventNamePrefixConstants.Form },
            ],
          })),
        update: (properties: OneProperties) => {
          const { hk } = properties
          if (hk === 'Asset#DEF000000') {
            return {
              ...list[0],
              associations: [
                ...(list[0]?.associations || []),
                { id: 'XYZ000000', type: EventNamePrefixConstants.Form },
              ],
            }
          }
          return {
            ...list[1],
            associations: [
              ...(list[1]?.associations || []),
              { id: 'XYZ000000', type: EventNamePrefixConstants.Form },
            ],
          }
        },
      } as unknown as AssetModel
    })
    const data = await getRandomAssetsWithUpdatedAssociations({
      properties: {},
      params: {},
      associationID: 'XYZ000000',
      associationType: EventNamePrefixConstants.Form,
      length: 2,
    })
    expect(data).toHaveLength(2)
    expect(data.flatMap((asset) => asset.associations)).toHaveLength(
      list.flatMap((asset) => asset.associations).length + 2
    )
  })
  it('should generate a list of AUs without overlapping assets', async () => {
    jest.spyOn(tables.itemCloudGreen, 'transact').mockImplementation(jest.fn())
    jest.spyOn(tables.hummingbird, 'transact').mockImplementation(jest.fn())
    jest.spyOn(tables.itemCloudGreen, 'getModel').mockImplementation(() => {
      return {
        find: () => [mocks.assetFRQ, mocks.assetMCQ],
        update: jest.fn(),
      } as unknown as AssetModel
    })
    jest.spyOn(tables.hummingbird, 'getModel').mockImplementation((name: string) => {
      if (name === 'AssemblyUnitSnapshot') {
        return {
          find: () => [
            ...mocks.assemblyUnitSnapshotsArray,
            {
              ...mocks.assemblyUnit,
              assetIDs: [],
              id: 'BER000000',
              customItemOrder: JSON.stringify({
                ABC000000: ['ABC000001', 'ABC000002'],
                BCD000000: ['BCD000001', 'BCD000002'],
              }),
            },
            {
              ...mocks.assemblyUnit,
              assetIDs: ['JKL000000'],
              id: 'RED000000',
              customItemOrder: JSON.stringify({
                ABC000000: ['ABC000001', 'ABC000002'],
                BCD000000: ['BCD000001', 'BCD000002'],
              }),
            },
          ],
          update: jest.fn(),
        } as unknown as AssemblyUnitSnapshotModel
      }
      return {
        update: jest.fn(),
      } as unknown as AssemblyUnitSnapshotModel
    })
    const data = await getRandomAssemblyUnitsWithoutDuplicateAssets({
      properties: {},
      params: {},
      associationID: 'XYZ000000',
      associationType: EventNamePrefixConstants.Form,
      length: 3,
    })
    expect(data).toHaveLength(3)
  })
  it.each([
    [
      mocks.assetMCQ,
      'Asset',
      { ordinal: undefined, items: mocks.assetMCQ.items?.map(({ id }) => ({ id, active: true })) },
    ],
    [mocks.assetMCQ, '', {}],
  ])(
    'will return an association "on" object',
    (childEntity, associationChildType, expectedObject) => {
      expect(populateAssociationOnProperty(childEntity, associationChildType)).toEqual(
        expectedObject
      )
    }
  )
  it('will return association record to write', () => {
    const childEntities = [mocks.assetMCQ]
    const recordsToWrite = prepareAssociationRecords({
      parentEntity: mocks.newAssemblyUnitFull,
      childEntities,
      courseId: '',
      createdByUsername: '',
      associationChildType: '',
    })
    expect(recordsToWrite.length).toBe(childEntities.length)

    const tableOccurrences = recordsToWrite.reduce<Record<string, number>>(
      (acc, curr) => ({
        ...acc,
        [curr.table]: (acc[curr.table] ?? 0) + 1,
      }),
      {}
    )
    expect(tableOccurrences['authoring']).toBe(childEntities.length)
    expect(tableOccurrences['events']).toBeFalsy()
  })

  const adminYears = ['24', '25', '26', '27']
  const baseAuObjects = Array.from(
    Array(Object.keys(BaseForm).length * adminYears.length).keys()
  ).reduce((acc, _curr, index) => {
    const adminYear = adminYears[index % adminYears.length]
    const baseForm = Object.values(BaseForm)[index % Object.values(BaseForm).length]
    return {
      ...acc,
      [`${adminYear}_${baseForm}`]: [{ adminYear, formCode: '085', baseForm }],
    }
  }, {})
  it.each([
    [
      new Map([[AssemblyUnitType.BASE, {}]]),
      ['assemblyUnitType', 'adminYear', 'formCode', 'questionType'],
    ],
    [
      new Map([[AssemblyUnitType.BASE, baseAuObjects]]),
      ['assemblyUnitType', 'adminYear', 'formCode', 'parentAssemblyUnit', 'questionType'],
    ],
    [
      new Map([
        [AssemblyUnitType.BASE, baseAuObjects],
        [AssemblyUnitType.SEQUENCE, { '1': {}, '2': {}, '3': {} }],
      ]),
      ['assemblyUnitType', 'adminYear', 'formCode', 'parentAssemblyUnit', 'questionType'],
    ],
  ])('will return assembly unit information', (assemblyUnitsMap, expectedProperties) => {
    const assemblyUnitInfo = getRandomAssemblyUnitInfo(
      assemblyUnitsMap as Map<AssemblyUnitType, Record<string, Set<AssemblyUnitModel>>>
    )
    expectedProperties.forEach((property) => {
      expect(assemblyUnitInfo).toHaveProperty(property)
    })
  })
})
