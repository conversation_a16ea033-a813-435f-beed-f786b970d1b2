import casual from 'casual'
import type { Model } from 'dynamodb-onetable'
import { ulid } from 'ulid'
import { CourseIdValues, SubjectIdentifier, getCourseConfigFromFile } from '@bin/data/course-config'
import { faker } from '@faker-js/faker'
import { Asset, tables } from '@shared/dynamo-hummingbird'
import { ASSEMBLY_UNIT_TYPE_GSI } from '@shared/dynamo-hummingbird/tables/authoring'
import { queryPaginated } from '@shared/dynamo-hummingbird/tables/io/read'
import type { WriteEntry } from '@shared/dynamo-hummingbird/tables/io/write'
import {
  AssemblyUnitModel,
  AssemblyUnitType,
  AssociationModelType,
  BaseForm,
  QuestionType,
  makeAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { AssemblyUnitAssetsAddedEvent } from '@shared/event-models'
import type {
  AssemblyUnitSnapshot,
  EntitySnapshot,
  EventNamePrefix,
  Snapshot,
} from '@shared/event-sourcing'
import { CourseConfiguration, ItemType, StimulusFormat } from '@shared/schema'
import type { AssemblyUnit } from '@shared/schema/split-models'
import {
  AssetItemAnswerOption,
  DirectionsTypeId,
  SetLayoutId,
  SetLayouts,
  SetLayoutsIdMappings,
} from '@shared/types/model'
import { WorkflowStep } from '@shared/types/model/workflow/types'
import type { GenerateAssetItemsProps, GetRandomDataParams, SubjectKeyMapping } from './types'

const config = CourseIdValues.reduce<Record<string, CourseConfiguration>>(
  (accum, courseId) => ({ ...accum, [courseId]: getCourseConfigFromFile(courseId) }),
  {}
)

export const constructParagraphTag = (className: string, body: string) => {
  return `<p class="${className}">${body}</p>`
}

export const generateDisplayName = (programName: string, courseName: string) => {
  const assetNumber = casual.integer(1, 99999)
  const paddedAssetNumber = `${assetNumber}`.padStart(5, '0')
  return `${programName} ${courseName} ${paddedAssetNumber}`
}

/** Never returns PendingSentToVault nor FailedToSendToVault unless explicitly provided */
export const getRandomWorkflowStep = (workflowStep?: WorkflowStep): WorkflowStep | null => {
  const omittedWorkflowStep = [WorkflowStep.PendingSentToVault, WorkflowStep.FailedToSendToVault]
  if (workflowStep) return workflowStep
  const acceptableWorkflowStep = Object.values(WorkflowStep).filter(
    (val) => !omittedWorkflowStep.includes(val)
  )
  if (acceptableWorkflowStep.length === 0) {
    return null
  }
  return casual.random_element(acceptableWorkflowStep)
}

export const getRandomSetLayout = (
  subjectId: string,
  directionsType: DirectionsTypeId | undefined,
  selectSetLayout?: SetLayouts | undefined
): SetLayoutId => {
  // remove SAT_ESSAY from the list because only Essay course should have it
  const setLayoutsList = Object.values(SetLayouts).filter(
    (setLayout) => setLayout !== SetLayouts.SAT_ESSAY
  )
  const setLayout =
    selectSetLayout ??
    (subjectId === SubjectIdentifier.ESSAY
      ? SetLayouts.SAT_ESSAY
      : directionsType
      ? SetLayouts.STEM_SINGLE_PANE
      : (casual.random_element(setLayoutsList) as SetLayouts))

  return SetLayoutsIdMappings[setLayout].toString() as SetLayoutId
}

export const generateItemAnswerOptions = (itemType: 'frq' | 'mcq') => {
  const correctAnswer = casual.integer(0, 3)
  const answerOptions: AssetItemAnswerOption[] = Array.from({ length: 4 }, (_, index) => {
    const id = ulid()
    const answerValue = casual.word
    return {
      content: constructParagraphTag('dap_body', answerValue),
      isCorrect: index === correctAnswer,
      rationale: constructParagraphTag('dap_body', answerValue ? `rationale ${answerValue}` : ''),
      key: itemType === 'mcq' ? `${id}` : mockFriendlyId(),
    }
  })
  return answerOptions
}

export const getRandomStimulusFormat = (
  questionType: GenerateAssetItemsProps['questionType']
): StimulusFormat => {
  if (questionType === 'MultipleChoiceQuestion') {
    return 'dF4Lt' as StimulusFormat
  }
  return casual.random_element(['dF4Lt', 's0rC3'])
}

export const getSubjectData = (): { [key: string]: SubjectKeyMapping } =>
  Object.entries(SubjectIdentifier).reduce((acc, [key, value]) => {
    const courseConfig = config[value]
    if (!courseConfig) return acc
    const pascalCaseName = courseConfig.courseKey
    if (pascalCaseName) {
      const programKey = courseConfig.programKey
      const programId = courseConfig.programId
      // remove any key with value of null
      const itemTypes = Object.entries(courseConfig.authoring)
        .filter(
          ([type, value]) =>
            value != null &&
            (type === ItemType.MultipleChoiceQuestion ||
              type === ItemType.FreeResponseQuestion ||
              type === ItemType.directions)
        )
        .map(([type]) => type)
      const res = {
        itemTypes,
        id: value,
        name: courseConfig.name,
        program: {
          id: programId,
          name: programKey,
        },
        upperCaseName: key,
      }

      if (!programKey)
        throw new Error(`getSubjectData: Program Key Not Found for ${JSON.stringify(res, null, 2)}`)
      if (!programId)
        throw new Error(`getSubjectData: Program ID Not Found for ${JSON.stringify(res, null, 2)}`)
      return { ...acc, [value]: res }
    }
    return { ...acc }
  }, {})

export const getRandomItemLength = (
  subject: GenerateAssetItemsProps['subject'],
  questionType: GenerateAssetItemsProps['questionType'],
  itemCount: GenerateAssetItemsProps['itemCount']
) => {
  const courseConfig = config[subject.id]
  if (!courseConfig) return 0
  const itemsConfig = courseConfig.authoring[questionType]?.items
  if (itemsConfig) {
    const min = itemsConfig.min ?? 0
    // 4 is max because there's no reason to create so much items for one asset
    const max = itemsConfig.max ?? 4
    return itemCount && itemCount >= min && itemCount <= max ? itemCount : casual.integer(min, max)
  }
  return itemCount ?? 0
}

export const ensureSafeToGenerateData = () => {
  if (process.env.ENV === 'prod') {
    throw new Error('Mock data can only be generated locally and in lower environments')
  }
}

const SnapshotModel: Record<EventNamePrefix, Model<EntitySnapshot>> = {
  AssemblyUnit: tables.hummingbird.getModel('AssemblyUnitSnapshot'),
  Collection: tables.hummingbird.getModel('CollectionSnapshot'),
  Form: tables.hummingbird.getModel('FormSnapshot'),
}

export const getLatestSnapshotSequenceNum = async (
  eventNamePrefix: EventNamePrefix,
  id?: string
) => {
  try {
    if (!id) {
      throw new Error('Missing Collection ID')
    }
    const snapshot = await SnapshotModel[eventNamePrefix].get({ id, sequenceNum: 0 })
    return snapshot && snapshot.snapshotSequenceNum != null ? snapshot.snapshotSequenceNum + 1 : 1
  } catch {
    return 1
  }
}

// aggregate data for each subject
export const subjectData = getSubjectData()
export const subjectIDs = Object.keys(subjectData)

export const getTimeElapsedString = (timeElapsed: number) =>
  timeElapsed > 60
    ? `${Math.round(timeElapsed / 60)} minutes`
    : `${Math.round(timeElapsed)} seconds`

export const getRandomSubsetFromArray = (size: number, elements: (Asset | Snapshot)[]) => {
  const maxIndex = elements.length - 1
  const indices = new Set<number>()
  // get {size} number of indexes with no duplicate
  // only go as high as the number of elements
  while (indices.size < size && indices.size < elements.length) {
    indices.add(casual.integer(0, maxIndex))
  }
  const subset = [...indices].map((num: number) => elements[num])
  return subset
}

export const getRandomAssets = async ({
  properties,
  params,
  length,
}: Pick<GetRandomDataParams, 'properties' | 'params' | 'length'>) => {
  const AssetModel = tables.itemCloudGreen.getModel('Asset')
  const assetData = await AssetModel.find(properties, params)
  return getRandomSubsetFromArray(length, assetData) as Asset[]
}

export const getRandomAssetsWithUpdatedAssociations = async ({
  properties,
  params,
  associationID,
  associationType,
  length,
  existingAssociations = [],
}: GetRandomDataParams) => {
  const AssetModel = tables.itemCloudGreen.getModel('Asset')
  const randomAssetDataSubset = await getRandomAssets({ properties, params, length })
  // update existing assets with new associations using transactions
  let transaction = {}
  for (let i = 0; i < randomAssetDataSubset.length; i++) {
    const asset = randomAssetDataSubset[i] as Asset
    AssetModel.update(
      { hk: asset.hk, sk: asset.sk },
      {
        transaction,
        set: {
          associations:
            'list_append(if_not_exists(associations, @{empty_list}), @{newAssociations})',
        },
        substitutions: {
          newAssociations: [
            {
              id: associationID,
              type: associationType,
            },
            ...existingAssociations,
          ],
          empty_list: [],
        },
      }
    )
    if ((i > 0 && i % 20 === 0) || i === randomAssetDataSubset.length - 1) {
      await tables.itemCloudGreen.transact('write', transaction)
      transaction = {}
    }
  }
  return randomAssetDataSubset
}

export const getRandomAssemblyUnitsWithoutDuplicateAssets = async ({
  properties,
  params,
  associationID,
  associationType,
  length,
}: GetRandomDataParams) => {
  const AssemblyUnitSnapshotModel = tables.hummingbird.getModel('AssemblyUnitSnapshot')
  const AssemblyUnitCreatedModel = tables.hummingbird.getModel('AssemblyUnitCreated')
  const assemblyUnitData = await AssemblyUnitSnapshotModel.find(properties, params)
  const existingAssets: Record<string, boolean> = {}
  const randomDataSubset = getRandomSubsetFromArray(
    assemblyUnitData.length, // use all the data found to increase the chance of finding more AUs with non duplicate assets
    assemblyUnitData
  ) as AssemblyUnitSnapshot[]
  const assemblyUnitsWithoutDuplicateAssets = randomDataSubset
    .map((assemblyUnit) => {
      const { assetIDs } = assemblyUnit
      if (assetIDs && assetIDs.length > 0) {
        const hasDuplicateIDs = assetIDs.some((id) => {
          return id in existingAssets
        })
        // don't include any AUs with assets that's already found in other existing AUs
        // if no duplicate asset IDs, then add AUs to the list to return
        if (hasDuplicateIDs) {
          return null
        }
        assetIDs.forEach((id) => {
          existingAssets[id] = true
        })
        return assemblyUnit
      }
      // if no assetIDs, then it's safe to add it to the list
      return assemblyUnit
    })
    .filter((item) => item)
    .slice(0, length) as AssemblyUnitSnapshot[]
  // update assembly units with new associations
  const assetIDs = new Set<string>([])
  let transaction = {}
  for (let i = 0; i < assemblyUnitsWithoutDuplicateAssets.length; i++) {
    const assemblyUnit = assemblyUnitsWithoutDuplicateAssets[i] as AssemblyUnitSnapshot
    assemblyUnit.assetIDs?.forEach((id) => {
      assetIDs.add(id)
    })
    // update snapshot with new associations
    AssemblyUnitSnapshotModel.update(
      { id: assemblyUnit.id, sequenceNum: 0 },
      {
        transaction,
        set: {
          associations:
            'list_append(if_not_exists(associations, @{empty_list}), @{newAssociations})',
        },
        substitutions: {
          newAssociations: [
            {
              id: associationID,
              type: associationType,
            },
          ],
          empty_list: [],
        },
      }
    )
    // update created snaptshot with new associations
    AssemblyUnitCreatedModel.update(
      { id: assemblyUnit.id, sequenceNum: 1 },
      {
        transaction,
        set: {
          associations:
            'list_append(if_not_exists(associations, @{empty_list}), @{newAssociations})',
        },
        substitutions: {
          newAssociations: [
            {
              id: associationID,
              type: associationType,
            },
          ],
          empty_list: [],
        },
      }
    )
    if ((i > 0 && i % 10 === 0) || i === assemblyUnitsWithoutDuplicateAssets.length - 1) {
      await tables.hummingbird.transact('write', transaction)
      transaction = {}
    }
  }
  // update asset associations
  await getRandomAssetsWithUpdatedAssociations({
    properties: { ...properties, sk: 'Asset#v0' },
    params: {
      ...params,
      limit: assetIDs.size,
      where: '${id} IN (@{...assetIDs})',
      substitutions: { assetIDs: [...assetIDs] },
    },
    associationID,
    associationType,
    length: assetIDs.size,
  })
  return assemblyUnitsWithoutDuplicateAssets
}

export const mockFriendlyId = () =>
  faker.random.alpha({ count: 3, casing: 'upper' }) + faker.random.numeric(6)

/**
 * Create Association records for AssemblyUnit:AssemblyUnit or AssemblyUnit:Assets.
 * For Assets, also creates an AssemblyUnitsAssetsAdded Event for the provided assets.
 */
export const prepareAssociationRecords = <T extends Asset | AssemblyUnitModel>({
  parentEntity,
  childEntities,
  courseId,
  createdByUsername,
  associationChildType,
}: {
  parentEntity: any
  childEntities: T[]
  courseId: string
  createdByUsername: string
  associationChildType: string
}): WriteEntry[] => {
  const assetsAddedEventEntry: WriteEntry | undefined =
    associationChildType === 'Asset'
      ? {
          table: 'events',
          record: AssemblyUnitAssetsAddedEvent.make({
            pk: parentEntity.pk,
            sk: parentEntity.pk,
            courseId,
            eventAgent: createdByUsername,
            id: parentEntity.id,
            assetIds: ((childEntities as Asset[]).map((childEntity) => childEntity.id) ??
              []) as string[],
          }),
        }
      : undefined
  const associationEntries = childEntities.flatMap<WriteEntry>((childEntity, index) => {
    const childId =
      (childEntity as Asset).id || ((childEntity as AssemblyUnitModel).pk.split('#')[1] as string)
    const pk = parentEntity.pk
    const sk = `Association#${childId}`
    const association = makeAssociation({
      pk,
      sk,
      associationChildId: childId,
      courseId,
      associationChildType,
      on: populateAssociationOnProperty(childEntity, associationChildType, index + 1),
      createdByUsername,
    })
    return [{ table: 'authoring', record: association }]
  })
  return assetsAddedEventEntry ? [assetsAddedEventEntry, ...associationEntries] : associationEntries
}

export const populateAssociationOnProperty = (
  childEntity: unknown,
  associationChildType: string,
  ordinal?: number
): Record<string, unknown> => {
  switch (associationChildType) {
    case 'Asset':
      return {
        // Asset order
        ordinal,
        // Sorted array of item IDs
        items: (childEntity as Asset).items?.map((item) => ({ id: item.id, active: true })),
      }
    default:
      return {}
  }
}

export const getRandomAssemblyUnitInfo = (
  assemblyUnitsByTypeMap: Map<AssemblyUnitType, Record<string, Set<AssemblyUnitModel>>>
): Pick<
  AssemblyUnitModel,
  'assemblyUnitType' | 'adminYear' | 'formCode' | 'baseForm' | 'questionType' | 'questionTypeNumber'
> & {
  parentAssemblyUnit?: AssemblyUnitModel
} => {
  const adminYears = ['24', '25', '26', '27']
  const formCodes = ['075', '085', '095']
  const baseForms = Object.values(BaseForm)
  const assemblyUnitTypeParentMapping: Record<string, AssemblyUnitType> = {
    [AssemblyUnitType.COMPLETE]: AssemblyUnitType.SEQUENCE,
    [AssemblyUnitType.SEQUENCE]: AssemblyUnitType.BASE,
  }
  const randomQuestionType = casual.random_element(
    Object.values(QuestionType).filter((type) => type !== QuestionType.PROJECT)
  )
  const randomQuestionTypeNumber = casual.random_element([0, 1, 2, 3, 4])

  // populate base assembly units first
  const presentBaseAssemblyUnitLength = Object.keys(
    assemblyUnitsByTypeMap.get(AssemblyUnitType.BASE) ?? {}
  ).length
  if (presentBaseAssemblyUnitLength < baseForms.length * adminYears.length) {
    // will populate all possible adminYear_baseForm combinations
    const adminYear = adminYears[presentBaseAssemblyUnitLength % adminYears.length] as string
    const baseForm = baseForms[presentBaseAssemblyUnitLength % baseForms.length] as BaseForm
    const formCode = casual.random_element(formCodes)
    return {
      assemblyUnitType: AssemblyUnitType.BASE,
      adminYear,
      formCode,
      baseForm,
      questionType: randomQuestionType,
      questionTypeNumber: randomQuestionTypeNumber,
    }
  }

  // populate sequence assembly units next
  const presentSequenceAssemblyUnitLength = Object.keys(
    assemblyUnitsByTypeMap.get(AssemblyUnitType.SEQUENCE) ?? {}
  ).length
  if (presentSequenceAssemblyUnitLength < baseForms.length * adminYears.length) {
    // will populate all possible adminYear_baseForm combinations
    const adminYear = adminYears[presentSequenceAssemblyUnitLength % adminYears.length] as string
    const baseForm = baseForms[presentSequenceAssemblyUnitLength % baseForms.length] as BaseForm
    const parentAssemblyUnit = casual.random_element([
      ...(assemblyUnitsByTypeMap.get(AssemblyUnitType.BASE)?.[`${adminYear}_${baseForm}`] ?? []),
    ]) as AssemblyUnitModel

    // sequence AU inherit some data from base AU
    return {
      assemblyUnitType: AssemblyUnitType.SEQUENCE,
      adminYear: parentAssemblyUnit.adminYear,
      formCode: parentAssemblyUnit.formCode,
      baseForm: parentAssemblyUnit.baseForm,
      parentAssemblyUnit,
      questionType: parentAssemblyUnit.questionType,
      questionTypeNumber: parentAssemblyUnit.questionTypeNumber,
    }
  }

  // otherwise grab random information
  const randomAdminYear = casual.random_element(adminYears)
  const randomFormCode = casual.random_element(formCodes)
  const randomBaseForm = casual.random_element(baseForms)
  const randomAssemblyUnitType = casual.random_element(Object.values(AssemblyUnitType))
  const parentType = assemblyUnitTypeParentMapping[randomAssemblyUnitType]
  const parentAssemblyUnit = parentType
    ? casual.random_element([
        ...(assemblyUnitsByTypeMap.get(parentType)?.[`${randomAdminYear}_${randomBaseForm}`] ?? []),
      ])
    : undefined

  return {
    assemblyUnitType: randomAssemblyUnitType,
    adminYear: parentAssemblyUnit?.adminYear || randomAdminYear,
    formCode: randomFormCode,
    parentAssemblyUnit,
    baseForm: parentAssemblyUnit?.baseForm || randomBaseForm,
    questionType: parentAssemblyUnit?.questionType || randomQuestionType,
    questionTypeNumber: parentAssemblyUnit?.questionTypeNumber || randomQuestionTypeNumber,
  }
}

export const getRandomNextGenAssemblyUnitsWithoutDuplicateAssets = async ({
  assemblyUnitType,
  courseId,
  length,
}: {
  assemblyUnitType: AssemblyUnitType
  courseId: string
  length: number
}) => {
  const existingAssetIds = new Set<string>()
  const existingAssemblyUnits = new Set<AssemblyUnit>()
  const assemblyUnits = await queryPaginated<AssemblyUnitModel>(
    tables.authoring,
    assemblyUnitType,
    { type: ['=', 'AssemblyUnit'], courseId: ['=', courseId] },
    {
      index: ASSEMBLY_UNIT_TYPE_GSI,
      limit: 99,
    }
  )
  const randomDataSubset = getRandomSubsetFromArray(
    assemblyUnits.length, // use all the data found to increase the chance of finding more AUs with non duplicate assets
    assemblyUnits
  ) as AssemblyUnit[]
  for (let i = 0; i < randomDataSubset.length; i++) {
    if (existingAssemblyUnits.size >= length) break
    const assemblyUnit = randomDataSubset[i] as AssemblyUnit
    const assetAssociations = await queryPaginated<AssociationModelType>(
      tables.authoring,
      `AssemblyUnit#${assemblyUnit.id}`,
      {
        type: ['=', 'Association'],
        courseId: ['=', courseId],
        associationChildType: ['=', 'Asset'],
      },
      {
        limit: 99,
      }
    )
    const hasNoDuplicateAsset = !assetAssociations.some((association) =>
      existingAssetIds.has(association.associationChildId)
    )
    if (hasNoDuplicateAsset) {
      existingAssemblyUnits.add(assemblyUnit)
      assetAssociations.forEach((association) =>
        existingAssetIds.add(association.associationChildId)
      )
    }
  }
  return Array.from(existingAssemblyUnits)
}
