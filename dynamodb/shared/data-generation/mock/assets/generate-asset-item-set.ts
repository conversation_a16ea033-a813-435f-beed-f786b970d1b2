import type { StimulusFormat, SubType } from '@shared/schema'
import { ItemSet } from '@shared/schema/item-set/item-set'
import type { DirectionsTypeId, SetLayoutId } from '@shared/types/model'
import type { GenerateAssetItemSetProps } from '../types'
import generateAssetItemSetItem from './generate-asset-item-set-items'

type GenerateAssetItemSetPropsAugmented = GenerateAssetItemSetProps & {
  setLayout: SetLayoutId
  directionsType?: DirectionsTypeId
  subType?: SubType
}

const generateAssetItemSet = (props: GenerateAssetItemSetPropsAugmented) => {
  const { asset, subject, questionType, setLayout, directionsType, subType } = props
  const itemSet: ItemSet = ItemSet.parse({
    //TODO:remove setLeader and instead use assetType
    setType: directionsType ? 'directions' : 'setLeader',
    userId: asset.createdByID as string,
    workflowState: asset.workflowState as string,
    active: true,
    metadata: {
      program: {
        id: subject.program.id,
        value: subject.program.name,
      },
      subject: {
        id: subject.id,
        value: subject.name,
      },
    },
    additionalMetadata: [],
    dimensions: undefined,
    stimuli: [],
    layout: setLayout,
    title: asset.title || null,
    graphics: [],
    instructions: asset.instructions || '',
    accnum: asset.accnum || null,
    stimulusFormat: asset.stimulusFormat as StimulusFormat,
    sourceDocumentInstructions: asset.sourceDocumentInstructions || null,
    subType,
    directionsType: asset.directionsType,
    created: {
      userId: asset.createdByID as string,
      userName: asset.createdByUsername as string,
      modifiedOn: new Date(asset.created as number),
    },
    updated: {
      userId: asset.updatedByID as string,
      userName: asset.updatedByUsername as string,
      modifiedOn: new Date(asset.updated as number),
    },
    displayName: asset.friendlyID as string,
    items: generateAssetItemSetItem({ asset, subject, questionType, subType }),
    id: asset.id as string,
    version: 0,
    metadataVersion: asset.metadataVersion,
    metadataId: asset.metadataId,
    workflowStep: asset.workflowStep,
    workflowStage: asset.workflowStage,
  })
  return itemSet
}

export default generateAssetItemSet
