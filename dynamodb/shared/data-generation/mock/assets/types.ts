import type { SubjectIdentifier } from '@shared/config'
import type { Asset } from '@shared/dynamo-hummingbird'
import type { DirectionsTypeId } from '@shared/types/model'
import type { ItemType } from '@shared/schema'

export interface GenerateAssetSetLayout {
  id: string
  name: string
  label: string
  upperCaseName: string
}
export type { ItemType }
export interface GenerateAssetParams {
  id: string
  subjectId?: string
  itemType?: ItemType
  userId?: string
  username?: string
  workflowStateId?: string
  associatedCollections?: string[]
  directionsType?: DirectionsTypeId
}

export interface GenerateAssetItemsParams extends GenerateAssetParams {
  friendlyID: string
  user: {
    userId: string
    username: string
  }
  questionType: ItemType
  subject: SubjectKeyMapping
}

export interface GenerateAssetItemSetParams {
  asset: Asset
  subject: SubjectKeyMapping
  questionType: ItemType
}

export interface SubjectKeyMapping {
  upperCaseName: string // e.g. PRECALCULUS
  id: string
  name: string // e.g. Precalculus
  program: {
    id: string
    name: string
  }
  itemTypes: ItemType[]
}

export type SubjectIdentifierType = Exclude<SubjectIdentifier, SubjectIdentifier.UNKNOWN_SUBJECT>
