import { constantCase } from 'case-anything'
import casual from 'casual'
import { getCourseConfigFromFile } from '@bin/data/course-config'
import { users } from '@bin/data/users'
import { SubjectIdentifier } from '@shared/config'
import getSearchTerms from '@shared/conversions/get-search-terms'
import { generateWordCount } from '@shared/conversions/word-count-util'
import type { Asset } from '@shared/dynamo-hummingbird'
import { getEmptyMetadataRootRecord } from '@shared/metadata/metadata-root-record'
import { ItemSet, ItemType, StimulusFormat } from '@shared/schema'
import { DirectionsTypeId, SetLayoutId } from '@shared/types/model'
import {
  WorkflowStageLookup,
  newWorkflowStepToOldWorkflowStage,
} from '@shared/types/model/workflow/types'
import { ASSET_MODEL_VERSION } from '@shared/utils/constants/constants'
import { getDefaultMetadata } from '@shared/utils/metadata'
import type { GenerateAssetProps, SubjectKeyMapping } from '../types'
import {
  constructParagraphTag,
  generateDisplayName,
  getRandomSetLayout,
  getRandomStimulusFormat,
  getRandomWorkflowStep,
  subjectData,
  subjectIDs,
} from '../utils'
import generateAssetItemSet from './generate-asset-item-set'
import generateAssetItems from './generate-asset-items'

export const generateAsset = async (props: GenerateAssetProps): Promise<Asset> => {
  const {
    id,
    subjectId,
    itemType,
    subType,
    userId,
    username,
    workflowStepId,
    directionsType,
    itemCount,
    isMultiSelect,
    associations,
    setSetLayout,
    excludeVaultId,
    ttl,
    stimulusType,
    metadataId,
    metadataVersion,
    displayType,
  } = props
  if (!metadataVersion) {
    throw new Error('generateAsset: Metadata Version Must Be Provided')
  }
  const _subjectId =
    subjectId ||
    casual.random_element(
      // This filter is a cludge to prevent asset types unsupported for Vault
      // from being generated unless explicitly requested in the input.
      subjectIDs.filter(
        (subjectId) =>
          !(
            [
              SubjectIdentifier.NETWORKING_FUNDAMENTALS,
              SubjectIdentifier.CYBERSECURITY_FUNDAMENTALS,
              SubjectIdentifier.ESL,
              SubjectIdentifier.WRITING,
              SubjectIdentifier.READING,
            ] as string[]
          ).includes(subjectId)
      )
    )
  const subject = subjectData[_subjectId] as SubjectKeyMapping
  if (!subject) {
    throw new Error('subjectId is not valid.')
  }

  // if only one of them is specified and not both, throw an error
  if ((userId && !username) || (!userId && username)) {
    throw new Error('userId and username are both required if one is specified.')
  }
  const user = userId && username ? { userId, username } : casual.random_element(users)
  const questionType = itemType
    ? subject.itemTypes.find((type) => type === itemType)
    : casual.random_element(subject.itemTypes.filter((type) => type !== ItemType.directions)) // if itemType is not specified, it will never be DIRECTIONS
  const _directionsType: DirectionsTypeId | undefined =
    itemType && itemType === ItemType.directions
      ? directionsType || casual.random_element(Object.values(DirectionsTypeId))
      : undefined
  if (!questionType) {
    throw new Error('itemType is not valid.')
  }
  const workflowStep = getRandomWorkflowStep(workflowStepId)
  if (!workflowStep) {
    throw new Error('workflowStep is not valid')
  }
  const friendlyID = generateDisplayName(subject.program.name, subject.name)
  const stimulusFormat = stimulusType ? stimulusType : getRandomStimulusFormat(questionType)
  const setLayout = getRandomSetLayout(subject.id, _directionsType, setSetLayout)
  const vaultId = excludeVaultId ? '' : `${id}-vault-id`

  const asset: Asset = {
    id,
    friendlyID,
    stimulusFormat,
    directionsType: _directionsType,
    instructions: constructParagraphTag('dap_instructions', casual.text),
    accnum: !_directionsType ? casual.word : undefined,
    metadata: [],
    createdByUsername: user.username,
    hk: `Asset#${id}`,
    modelVersion: ASSET_MODEL_VERSION,
    isActive: true,
    title: casual.title,
    updatedByID: user.userId,
    modifiedById: user.userId,
    stimuli: [],
    combinedMetadata: [],
    sk: 'Asset#v0',
    createdById: user.userId,
    programID: subject.program.id,
    updatedByUsername: user.username,
    created: new Date().getTime(),
    modifiedByUsername: user.username,
    indexedRevision: 0,
    version: 0,
    subjectID: subject.id,
    revision: 0,
    itemId: id,
    workflowState: newWorkflowStepToOldWorkflowStage[workflowStep],
    workflowStep,
    workflowStage: WorkflowStageLookup[workflowStep],
    itemSetId: id,
    sourceDocumentInstructions:
      stimulusFormat === StimulusFormat.SOURCE && !_directionsType ? casual.text : undefined,
    items: await generateAssetItems({
      id,
      subType,
      isMultiSelect,
      friendlyID,
      user,
      questionType,
      subject,
      itemCount,
      ttl,
      metadataVersion,
    }),
    questionType: constantCase(questionType),
    updated: new Date().getTime(),
    createdByID: user.userId,
    layout: setLayout as SetLayoutId,
    metadataVersion,
    metadataId,
    courseAlignmentRationale: '',
    associations: associations ?? [],
    vaultId,
    displayType,
    ttl,
  }
  const itemSet = generateAssetItemSet({
    asset,
    subject,
    questionType,
    subType,
    setLayout,
    directionsType: _directionsType,
  })
  return {
    ...asset,
    wordCount: JSON.stringify(generateWordCount(itemSet)),
    searchTerms: getSearchTerms(id, itemSet as ItemSet).join(','),
    itemSet: JSON.stringify(itemSet),
    newMetadata: { [metadataVersion]: getEmptyMetadataRootRecord(itemSet) },
  }
}

export const generateTestAsset = async (
  props: Partial<GenerateAssetProps> & Pick<GenerateAssetProps, 'id'>
): Promise<Asset> => {
  if (!props.id) throw new Error('ID must be explicitly provided for test assets')
  // If metadata version is not provided as argument, get metadataId from file.

  const metadata = props?.subjectId && getDefaultMetadata(getCourseConfigFromFile(props?.subjectId))

  const defaultedProps: GenerateAssetProps = {
    metadataVersion: new Date(-1).toISOString(),
    metadataId: metadata ? metadata.id : 'FAKE_ID',
    ...props,
  }
  return generateAsset(defaultedProps)
}
