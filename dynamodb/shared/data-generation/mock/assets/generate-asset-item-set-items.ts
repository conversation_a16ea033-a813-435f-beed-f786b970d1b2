import { type MultipleChoiceAnswerOption, type StimulusFormat, SubType } from '@shared/schema'
import type { AssetItemAnswerOption } from '@shared/types/model'
import type { GenerateAssetItemSetProps } from '../types'
import { mockFriendlyId } from '../utils'

const generateItemSpecificProps = (
  item: any,
  questionType: GenerateAssetItemSetProps['questionType'],
  subType: GenerateAssetItemSetProps['subType']
) => {
  const {
    stimulus,
    prompt,
    decimalAlign,
    answerOptions,
    points,
    formalDistractorRational,
    isMultiSelect,
    stem,
    answer,
  } = item

  switch (questionType) {
    case 'MultipleChoiceQuestion':
      return {
        stimulus,
        prompt,
        decimalAlign,
        answerOptions: answerOptions.map(
          (option: AssetItemAnswerOption, index: number): MultipleChoiceAnswerOption => ({
            /* This field is permitted in ItemSet logic to be a number or a string,
               depending on whether the key is a valid number. This logic enables that. */
            id: isNaN(Number(option.key)) ? option.key : Number(option.key),
            ordinal: index,
            correct: option.isCorrect ?? false,
            informalRationale: option.rationale,
            key: option.key,
            content: option.content ?? '',
            label: String.fromCharCode(65 + index),
          })
        ),
        points,
        formalDistractorRational,
        isMultiSelect,
      }
    case 'FreeResponseQuestion':
      return subType === SubType.CHOICE_QUESTION
        ? {
            stimulus,
            prompt,
            decimalAlign,
            answerOptions: answerOptions.map(
              (option: AssetItemAnswerOption, index: number): MultipleChoiceAnswerOption => ({
                id: mockFriendlyId(),
                ordinal: index,
                correct: option.isCorrect ?? false,
                informalRationale: option.rationale,
                key: option.key,
                content: option.content ?? '',
                label: String.fromCharCode(65 + index),
              })
            ),
            points,
            formalDistractorRational,
          }
        : {
            stem,
            answer,
          }
    default:
      throw new Error('questionType is not known')
  }
}

const generateAssetItemSetItems = (props: GenerateAssetItemSetProps) => {
  const { asset, subject, questionType, subType } = props
  const items =
    asset.items?.map((item) => ({
      ...generateItemSpecificProps(item, questionType, subType),
      created: {
        userId: asset.createdByID as string,
        userName: asset.createdByUsername as string,
        modifiedOn: new Date(asset.created as number),
      },
      updated: {
        userId: asset.updatedByID as string,
        userName: asset.updatedByUsername as string,
        modifiedOn: new Date(asset.updated as number),
      },
      itemTitle: item.title as string,
      itemType: questionType,
      parentSetId: asset.id as string,
      id: item.id as string,
      displayName: item.friendlyID as string,
      ordinalValue: 0,
      metadata: {
        program: {
          id: subject.program.id,
          value: subject.program.name,
        },
        subject: {
          id: subject.id,
          value: subject.name,
        },
        unit: [],
        topic: [],
        learningObjectives: [],
        essentialKnowledge: [],
        skill: [],
        practiceOrSkillCategory: [],
      },
      additionalMetadata: [],
      userId: asset.createdByUsername as string,
      active: true,
      accessibilityContent: '',
      graphics: [],
      version: 1,
      accnum: item.accnum as string,
      stimulusFormat: item.stimulusFormat as StimulusFormat,
      part: item.parts,
      courseAlignmentRationale: item.courseAlignmentRationale,
    })) || []
  return items
}

export default generateAssetItemSetItems
