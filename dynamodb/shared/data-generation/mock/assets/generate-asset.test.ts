import { ItemType, SubjectIdentifier } from '@shared/config'
import { EventNamePrefixConstants } from '@shared/event-sourcing'
import { SetLayoutId } from '@shared/types/model'
import { DisplayTypeId } from '@shared/types/model/previewer/display-type'
import { WorkflowStageLookup, WorkflowStep } from '@shared/types/model/workflow/types'
import { ASSET_MODEL_VERSION } from '@shared/utils/constants/constants'
import { generateTestAsset } from './generate-asset'

const outputTestAsset = {
  layout: SetLayoutId.STEM_RIGHT_PANE,
  instructions:
    '<p class="dap_instructions">Reiciendis necessitatibus voluptatem sit qui. Voluptatem vel quia accusantium et aliquam consequatur consequatur. Provident itaque sunt.</p>',
  accnum: 'placeat',
  metadata: [],
  createdByUsername: 'mdonovan',
  hk: 'Asset#POE000000',
  searchTerms:
    'american,voluptatem,01,02,ratione,consequatur.,poe000000,studies,provident,magnam,sunt.</p>,poe000001,consequatur,poe000002,qui.,09872,itaque,-,necessitatibus,aliquam,ap,et,eligendi,class="dap_instructions">reiciendis,african,<p,accusantium,placeat,quia,sint,vel,sit,assumenda',
  modelVersion: ASSET_MODEL_VERSION,
  isActive: true,
  title: 'ratione',
  updatedByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
  friendlyID: 'AP African American Studies 09872',
  modifiedById: '01G4NFW3MJBXP9SQ5259A1QGVM',
  stimuli: [],
  combinedMetadata: [],
  sk: 'Asset#v0',
  stimulusFormat: 'SOURCE_DOCUMENT',
  id: 'POE000000',
  createdById: '01G4NFW3MJBXP9SQ5259A1QGVM',
  programID: '59tWw',
  updatedByUsername: 'mdonovan',
  created: 1685571118274,
  modifiedByUsername: 'mdonovan',
  indexedRevision: 0,
  itemSet:
    '{"accessibilityContent":"","workflowStage":"","workflowStep":"","layout":"515912341","setType":"setLeader","userId":"01G4NFW3MJBXP9SQ5259A1QGVM","courseAlignmentRationale": "","workflowState":"5tFW9","active":true,"metadata":{"program":{"id":"59tWw","nodeType":"Program","classifier":"setLeader","value":"AP"},"subject":{"id":"116","nodeType":"Subject","classifier":"setLeader","value":"African American Studies"}},"additionalMetadata":[],"stimuli":[],"title":"ratione","graphics":[],"instructions":"<p class=\\"dap_instructions\\">Reiciendis necessitatibus voluptatem sit qui. Voluptatem vel quia accusantium et aliquam consequatur consequatur. Provident itaque sunt.</p>","accnum":"placeat","stimulusFormat":"SOURCE_DOCUMENT","sourceDocumentInstructions":"Ipsa voluptatibus impedit quia et minus iusto. Perspiciatis sint dolorem eaque perspiciatis debitis rerum. Illo quam voluptatem eligendi sed. Esse sapiente omnis quis.","created":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"updated":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"displayName":"AP African American Studies 09872","items":[{"stem":"<p class=\\"dap_body\\">Aliquid non est neque quasi sunt consequatur praesentium eos iste. Optio sed aut sit. Sed adipisci accusamus. Sit autem inventore numquam tempora rerum. Voluptate autem quas.</p>","answer":"<p class=\\"dap_body\\">Quibusdam molestiae eligendi id voluptates natus unde reprehenderit. Est dicta qui eos. Voluptas nam omnis repellendus. Et aliquam eligendi assumenda rerum unde dignissimos reprehenderit error. Aut rem impedit iusto laudantium impedit accusantium. Dolorem ut explicabo tempora praesentium quos.</p>","created":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"updated":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"itemTitle":"eligendi","itemType":"FreeResponseQuestion","parentSetId":"POE000000","id":"POE000001","displayName":"AP African American Studies 09872 - 01","ordinalValue":0,"metadata":{"program":{"id":"59tWw","nodeType":"Program","classifier":"item","value":"AP"},"subject":{"id":"116","nodeType":"Subject","classifier":"item","value":"African American Studies"},"unit":[],"topic":[],"learningObjectives":[],"essentialKnowledge":[],"skill":[],"practiceOrSkillCategory":[]},"additionalMetadata":[],"userId":"mdonovan","active":true,"accessibilityContent":"","graphics":[],"version":1,"accnum":"magnam","stimulusFormat":"DEFAULT","part":[]},{"stem":"<p class=\\"dap_body\\">Totam esse sunt ab culpa consequatur id. Recusandae rerum voluptas. Iure est aut non sed laudantium libero omnis et. Harum officiis et at et in rerum maxime expedita velit. Ab laudantium inventore praesentium nihil et dolorem. Qui ut adipisci minima.</p>","answer":"<p class=\\"dap_body\\">Est voluptatem qui ut et quisquam doloribus. Facilis consectetur quisquam ratione est repudiandae numquam. Dolores quia ut odit et eos. Quo nihil aut quia dolorum dolore magnam nisi similique qui.</p>","created":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"updated":{"userId":"01G4NFW3MJBXP9SQ5259A1QGVM","userName":"mdonovan","modifiedOn":"2023-05-31T22:11:58.274Z"},"itemTitle":"assumenda","itemType":"FreeResponseQuestion","parentSetId":"POE000000","id":"POE000002","displayName":"AP African American Studies 09872 - 02","ordinalValue":0,"metadata":{"program":{"id":"59tWw","nodeType":"Program","classifier":"item","value":"AP"},"subject":{"id":"116","nodeType":"Subject","classifier":"item","value":"African American Studies"},"unit":[],"topic":[],"learningObjectives":[],"essentialKnowledge":[],"skill":[],"practiceOrSkillCategory":[]},"additionalMetadata":[],"userId":"mdonovan","active":true,"accessibilityContent":"","graphics":[],"version":1,"accnum":"sint","stimulusFormat":"DEFAULT","part":[]}],"id":"POE000000","version":0,"metadataVersion":"1970-01-01T00:00:00.000Z","metadataId":"some-uuid"}',
  version: 0,
  subjectID: '116',
  revision: 1,
  itemId: 'POE000000',
  workflowState: '5tFW9',
  workflowStage: WorkflowStageLookup[WorkflowStep.PreparationForItemFinalization],
  workflowStep: WorkflowStep.PreparationForItemFinalization,
  itemSetId: 'POE000000',
  sourceDocumentInstructions:
    'Ipsa voluptatibus impedit quia et minus iusto. Perspiciatis sint dolorem eaque perspiciatis debitis rerum. Illo quam voluptatem eligendi sed. Esse sapiente omnis quis.',
  items: [
    {
      metadata: [],
      accnum: 'magnam',
      createdAtMs: 1685571118274,
      title: 'eligendi',
      isActive: true,
      updatedByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
      friendlyID: 'AP African American Studies 09872 - 01',
      answer:
        '<p class="dap_body">Quibusdam molestiae eligendi id voluptates natus unde reprehenderit. Est dicta qui eos. Voluptas nam omnis repellendus. Et aliquam eligendi assumenda rerum unde dignissimos reprehenderit error. Aut rem impedit iusto laudantium impedit accusantium. Dolorem ut explicabo tempora praesentium quos.</p>',
      updatedAtMs: 1685571118274,
      parts: [],
      stimulusFormat: 'DEFAULT',
      id: 'POE000001',
      createdByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
      stem: '<p class="dap_body">Aliquid non est neque quasi sunt consequatur praesentium eos iste. Optio sed aut sit. Sed adipisci accusamus. Sit autem inventore numquam tempora rerum. Voluptate autem quas.</p>',
    },
    {
      metadata: [],
      accnum: 'sint',
      createdAtMs: 1685571118274,
      title: 'assumenda',
      isActive: true,
      updatedByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
      friendlyID: 'AP African American Studies 09872 - 02',
      answer:
        '<p class="dap_body">Est voluptatem qui ut et quisquam doloribus. Facilis consectetur quisquam ratione est repudiandae numquam. Dolores quia ut odit et eos. Quo nihil aut quia dolorum dolore magnam nisi similique qui.</p>',
      updatedAtMs: 1685571118274,
      parts: [],
      stimulusFormat: 'DEFAULT',
      id: 'POE000002',
      createdByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
      stem: '<p class="dap_body">Totam esse sunt ab culpa consequatur id. Recusandae rerum voluptas. Iure est aut non sed laudantium libero omnis et. Harum officiis et at et in rerum maxime expedita velit. Ab laudantium inventore praesentium nihil et dolorem. Qui ut adipisci minima.</p>',
    },
  ],
  questionType: 'FREE_RESPONSE_QUESTION',
  displayType: DisplayTypeId.PAGED,
  updated: 1685571155604,
  createdByID: '01G4NFW3MJBXP9SQ5259A1QGVM',
  metadataVersion: new Date(-1).toISOString(),
  metadataId: 'some-uuid',
  associations: [
    {
      id: 'ABC000000',
      type: EventNamePrefixConstants.Collection,
    },
    {
      id: 'ZYX000000',
      type: EventNamePrefixConstants.AssemblyUnit,
    },
  ],
  wordCount: '',
  directionsType: undefined,
  courseAlignmentRationale: '',
  vaultId: 'this-is-a-vault-id',
  ttl: undefined,
  newMetadata: {
    ['1970-01-01T00:00:00.000Z']: { ['POE000000']: [], ['POE000001']: [], ['POE000002']: [] },
  },
}

describe('generate-asset.ts unit tests', () => {
  it.each([
    [
      ItemType.MultipleChoiceQuestion,
      SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
      [{ id: 'ABC000000', type: EventNamePrefixConstants.Collection }],
      [outputTestAsset.associations[0]],
    ],
    [
      ItemType.FreeResponseQuestion,
      SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
      [{ id: 'ZYX000000', type: EventNamePrefixConstants.AssemblyUnit }],
      [outputTestAsset.associations[1]],
    ],
    [ItemType.MultipleChoiceQuestion, SubjectIdentifier.PRECALCULUS, [], []],
    [ItemType.FreeResponseQuestion, SubjectIdentifier.PRECALCULUS, [], []],
  ])(
    'should create a %s asset for courseId %s with the appropriate fields',
    async (itemType, courseId, associations, expectedAssociations) => {
      // expected
      const expectedAssetFields = Object.keys(outputTestAsset).sort()
      const expectedItemSet = JSON.parse(outputTestAsset.itemSet)
      const expectedItemSetFields = Object.keys(expectedItemSet).sort()
      // create an asset
      const asset = await generateTestAsset({
        id: 'POE000000',
        subjectId: courseId,
        itemType: itemType,
        associations: associations,
      })
      // received
      const assetFields = Object.keys(asset).sort()
      const itemSet = JSON.parse(asset.itemSet || '')
      const itemSetFields = Object.keys(itemSet).sort()
      const assetAssociations = asset.associations
      // test object keys, ignore values as they are random
      expect(assetFields).toEqual(expectedAssetFields)
      expect(asset.items?.length).toBeGreaterThanOrEqual(1)
      expect(itemSetFields).toEqual(expectedItemSetFields)
      expect(assetAssociations).toEqual(expectedAssociations)
    }
  )
})
