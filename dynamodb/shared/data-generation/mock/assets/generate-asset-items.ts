import casual from 'casual'
import { generateBatchIDs } from '@lambdas/api/src/friendly-id/id-service'
import type { Asset } from '@shared/dynamo-hummingbird'
import { Metadata, StimulusFormat, SubType } from '@shared/schema'
import type { GenerateAssetItemsProps } from '../types'
import { constructParagraphTag, generateItemAnswerOptions, getRandomItemLength } from '../utils'

const generateItemSpecificProps = (props: {
  questionType: GenerateAssetItemsProps['questionType']
  isMultiSelect?: boolean
  subType?: SubType
}) => {
  const { questionType, isMultiSelect, subType } = props

  switch (questionType) {
    case 'MultipleChoiceQuestion':
      return {
        stimulus: '',
        prompt: constructParagraphTag('dap_body', casual.text),
        decimalAlign: false,
        answerOptions: generateItemAnswerOptions('mcq'),
        points: 1,
        formalDistractorRational: null,
        isMultiSelect,
      }

    case 'FreeResponseQuestion':
      return subType === SubType.CHOICE_QUESTION
        ? {
            stimulus: '',
            prompt: constructParagraphTag('dap_body', casual.text),
            decimalAlign: false,
            answerOptions: generateItemAnswerOptions('frq'),
            points: 1,
            formalDistractorRational: null,
          }
        : {
            stem: constructParagraphTag('dap_body', casual.text),
            answer: constructParagraphTag('dap_body', casual.text),
          }
    default:
      throw new Error('questionType is not known')
  }
}

/**
 * Generate Items to populate an Asset
 * @param props.id The unique ID of the _Asset_
 * @returns
 */
const generateAssetItems = async (props: GenerateAssetItemsProps) => {
  const { id, friendlyID, user, subject, questionType, itemCount, ttl, subType, isMultiSelect } =
    props
  const length = getRandomItemLength(subject, questionType, itemCount)
  let itemIdsArray: string[] = []
  if (process.env.NODE_ENV === 'test') {
    itemIdsArray = Array.from({ length }, (_, idx) => `${id.slice(0, -1)}${idx + 1}`)
  } else {
    itemIdsArray = await generateItemIds(length, props.id, ttl)
  }
  const items: Asset['items'] = itemIdsArray.map((itemId, index) => {
    const sequence = index + 1
    return {
      ...generateItemSpecificProps({ questionType, subType, isMultiSelect }),
      metadata: [] as Metadata[],
      accnum: casual.word,
      createdAtMs: new Date().getTime(),
      title: casual.title,
      isActive: true,
      updatedByID: user.userId,
      friendlyID: `${friendlyID} - 0${sequence}`,
      updatedAtMs: new Date().getTime(),
      parts: [],
      stimulusFormat: StimulusFormat.DEFAULT,
      id: itemId,
      createdByID: user.userId,
      courseAlignmentRationale: constructParagraphTag('dap_body', casual.text),
      vaultId: `${itemId}-vault-id`,
    }
  })
  return items
}

const generateItemIds = async (count: number, assetId: string, ttl?: number) => {
  const itemIds = await generateBatchIDs(count, assetId, ttl)
  return itemIds
}

export default generateAssetItems
