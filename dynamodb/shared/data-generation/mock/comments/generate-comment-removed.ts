import type { GenerateEventResults } from '../types'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'
import { CommentRemovedEvent } from '@shared/event-models'

const generateCommentRemoved = (props: CommentModel): GenerateEventResults => {
  const { commentId, pk, sk, createdByUsername, courseId } = props

  if (!pk || !sk) {
    throw new Error('pk and sk are required fields to create remove event')
  }

  const event = CommentRemovedEvent.make({
    pk,
    sk,
    id: commentId as string,
    courseId,
    eventAgent: createdByUsername,
  })

  const comment = { ...props }

  console.log(`Deleting comment ${commentId}`)

  return {
    event,
    comment,
  }
}

export default generateCommentRemoved
