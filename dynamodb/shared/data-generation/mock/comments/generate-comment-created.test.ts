import { Role } from '@shared/rbac/roles'
import generateCommentCreated from './generate-comment-created'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'

const outputTestCommentEvent = {
  sequenceNumber: 0,
  eventAgent: 'mmorales',
  sk: '1708978929957#CommentCreatedEvent#01HQKHA094TS5Q6NYR74327E3B',
  comment: {
    createdByUsername: 'mmorales',
    attachments: [],
    createdAtMs: Date.now(),
    parentCommentId: '01HQKHA094TS5Q6NYR74327E3B',
    type: 'Comment',
    content: 'actual commen',
    commentTargetType: 'Form',
    commentTargetId: 'WVI000000',
    sk: 'Comment#01HQKHA094TS5Q6NYR74327E3B',
    commentId: '01HQKHA094TS5Q6NYR74327E3B',
    reactions: [],
    pk: 'Form#WVI000000',
    createdByRoles: ['ADMIN'],
    courseId: '116',
    on: {
      type: 'Form',
    },
  },
  pk: 'Form#WVI000000',
  id: '01HQKHA094TS5Q6NYR74327E3B',
  type: 'CommentCreatedEvent',
  courseId: '116',
  eventTimestamp: 1708978929957,
}

const outputTestComment = {
  pk: 'Form#WVI000000',
  sk: 'Comment#01HQKHP4KVMEYEQAX62Z4WB46J',
  createdByUsername: 'mmorales',
  attachments: [],
  createdAtMs: Date.now(),
  parentCommentId: '01HQKHP4KVMEYEQAX62Z4WB46J',
  content: 'actual comment',
  commentTargetType: 'Form',
  commentTargetId: 'WVI000000',
  commentId: '01HQKHP4KVMEYEQAX62Z4WB46J',
  reactions: [],
  createdByRoles: [Role.ADMIN],
  courseId: '116',
  on: {
    type: 'Form',
  },
  type: 'Comment',
}

// TODO: Clean up type references
describe('generate-comment-created.ts unit tests', () => {
  it('should create two entries for "CommentCreatedEvent" and "Comment" OBJ with the appropriate fields', () => {
    // create an asset
    const { event: commentCreatedEvent, comment: commentCreatedComment } = generateCommentCreated({
      commentTargetId: outputTestComment.commentTargetId,
      commentTargetType: outputTestComment.commentTargetType as CommentModel['commentTargetType'],
      courseId: outputTestComment.courseId,
      on: {
        type: outputTestComment.commentTargetType,
      },
    } as any as CommentModel)

    // expected
    const expectedCommentCreatedFields = Object.keys(outputTestCommentEvent).sort()
    const expectedCommentFields = Object.keys(outputTestComment).sort()
    // received
    const commentCreatedFields = Object.keys(commentCreatedEvent).sort()
    const commentObjFields = Object.keys(commentCreatedComment ?? {}).sort()
    // test object keys, ignore values as they are random
    expect(commentCreatedFields).toEqual(expectedCommentCreatedFields)
    expect(commentObjFields).toEqual(expectedCommentFields)
  })
  it('should throw an error if required fields are missing', () => {
    try {
      generateCommentCreated({} as CommentModel)
    } catch (e) {
      const expectedError = new Error('Comment type and entity ID are missing. Unable to create')
      expect(e).toStrictEqual(expectedError)
    }
  })
})
