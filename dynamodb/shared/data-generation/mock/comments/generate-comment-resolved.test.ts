import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models'
import * as mocks from '@shared/trpc-lambda/mocks'
import generateCommentResolved from './generate-comment-resolved'

const inputTestCommentObj = mocks.comment

const outputTestCommentEvent = mocks.commentResolvedEvent

const outputTestCommentObj = {
  ...mocks.comment,
  resolved: true,
  resolvedByUsername: mocks.reviewerUser.username,
  resolvedAtMs: Date.now(),
}

describe('generate-comment-resolved.ts unit tests', () => {
  it('should create two entries for "CommentResolved" and "Comment" with the appropriate fields', () => {
    // create an asset
    const { event: commentResolvedEvent, comment: commentResolveObj } =
      generateCommentResolved(inputTestCommentObj)
    // expected
    const expectedCommentResolvedFields = Object.keys(outputTestCommentEvent).sort()
    const expectedCommentObjFields = Object.keys(outputTestCommentObj).sort()
    // received
    const commentResolvedFields = Object.keys(commentResolvedEvent).sort()
    const commentObjFields = Object.keys(commentResolveObj ?? {}).sort()
    // test object keys, ignore values as they are random
    expect(commentResolvedFields).toEqual(expectedCommentResolvedFields)
    expect(commentObjFields).toEqual(expectedCommentObjFields)
    // check value(s) that actually matter
    expect((commentResolveObj ?? {}).resolved).toBe(outputTestCommentEvent.resolved)
  })
  it('should throw an error if required fields are missing', () => {
    try {
      generateCommentResolved({} as CommentModel)
    } catch (e) {
      const expectedError = new Error('pk and sk are required fields to create resolve event')
      expect(e).toStrictEqual(expectedError)
    }
  })
})
