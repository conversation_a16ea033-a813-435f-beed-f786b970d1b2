import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models'
import { CommentResolvedEvent } from '@shared/event-models'
import type { GenerateEventResults } from '../types'

const generateCommentResolved = (props: CommentModel): GenerateEventResults => {
  const { commentId, pk, sk, createdByUsername, courseId } = props

  if (!pk || !sk) {
    throw new Error('pk and sk are required fields to create resolve event')
  }

  const resolvedObj = {
    resolved: true,
    resolvedByUsername: 'bsolo',
    resolvedAtMs: new Date().getTime(),
  }

  const event = CommentResolvedEvent.make({
    pk,
    sk,
    id: commentId,
    courseId,
    eventAgent: createdByUsername,
    ...resolvedObj,
  })

  const comment = { ...props, ...resolvedObj }

  return {
    event,
    comment,
  }
}

export default generateCommentResolved
