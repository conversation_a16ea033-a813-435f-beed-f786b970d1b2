import casual from 'casual'
import type { GenerateEventResults } from '../types'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'
import { CommentContentChangedEvent } from '@shared/event-models'

const generateCommentEdited = (props: CommentModel): GenerateEventResults => {
  const { commentId, pk, sk, content, createdByUsername, courseId } = props

  if (!pk || !sk) {
    throw new Error('pk and sk are required fields to create edit event')
  }

  const editedContent = casual.sentences(2)
  const event = CommentContentChangedEvent.make({
    pk,
    sk,
    id: commentId,
    courseId,
    eventAgent: createdByUsername,
    oldContent: content,
    newContent: editedContent,
  })

  const comment = { ...props }
  comment.content = editedContent
  comment.updatedAtMs = new Date().getTime()
  comment.updatedByUsername = createdByUsername

  return {
    event,
    comment,
  }
}

export default generateCommentEdited
