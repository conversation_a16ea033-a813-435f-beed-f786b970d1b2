import * as mocks from '@shared/trpc-lambda/mocks'
import generateCommentRemoved from './generate-comment-removed'

const inputTestComment = mocks.comment
const outputTestCommentEvent = mocks.commentRemovedEvent
const outputTestComment = mocks.comment

describe('generate-comment-created.ts unit tests', () => {
  it('should create two entries for "CommentRemovedEvent"  with the appropriate fields', () => {
    // create an asset
    const { event: commentRemovedEvent, comment: commentRemoved } =
      generateCommentRemoved(inputTestComment)

    // expected
    const expectedCommentArchivedFields = Object.keys(outputTestCommentEvent).sort()
    const expectedCommentObjFields = Object.keys(outputTestComment).sort()
    // received
    const commentArchivedFields = Object.keys(commentRemovedEvent).sort()
    const commentObjFields = Object.keys(commentRemoved ?? {}).sort()
    // test object keys, ignore values as they are random
    expect(commentArchivedFields).toEqual(expectedCommentArchivedFields)
    expect(commentObjFields).toEqual(expectedCommentObjFields)
  })
  it('should throw an error if required fields are missing', () => {
    try {
      generateCommentRemoved({} as any)
    } catch (e) {
      const expectedError = new Error('pk and sk are required fields to create remove event')
      expect(e).toStrictEqual(expectedError)
    }
  })
})
