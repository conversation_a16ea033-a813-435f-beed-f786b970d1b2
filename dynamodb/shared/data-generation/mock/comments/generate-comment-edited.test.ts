import casual from 'casual'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'
import * as mocks from '@shared/trpc-lambda/mocks'
import generateCommentEdited from './generate-comment-edited'

const inputTestComment = mocks.comment

const outputTestCommentEvent = mocks.commentEditedEvent

const outputTestComment = {
  ...mocks.comment,
  updatedAtMs: '1234567890',
  updatedByUsername: 'mmmorales',
}

describe('generate-comment-created.ts unit tests', () => {
  beforeAll(() => {
    jest.spyOn(casual, 'sentences').mockReturnValue('actual comment')
  })
  it('should create two entries for "CommentEdited" and "Comment" with the appropriate fields', () => {
    const { event: commentEditedEvent, comment: commentEditedComment } =
      generateCommentEdited(inputTestComment)
    // expected
    const expectedCommentEditedFields = Object.keys(outputTestCommentEvent).sort()
    const expectedCommentFields = Object.keys(outputTestComment).sort()
    // received
    const commentEditedEventFields = Object.keys(commentEditedEvent).sort()
    const commentEditedCommentFields = Object.keys(commentEditedComment ?? {}).sort()
    // test object keys, ignore values as they are random
    expect(commentEditedEventFields).toEqual(expectedCommentEditedFields)
    expect(commentEditedCommentFields).toEqual(expectedCommentFields)
    // check value(s) that actually matter
    expect((commentEditedComment as CommentModel).content).toBe(outputTestCommentEvent.newContent)
  })
  it('should throw an error if required fields are missing', () => {
    try {
      generateCommentEdited({} as CommentModel)
    } catch (e) {
      const expectedError = new Error('pk and sk are required fields to create edit event')
      expect(e).toStrictEqual(expectedError)
    }
  })
})
