import { users } from '@bin/data/users'
import { CourseIdValues } from '@shared/config'
import { makeComment } from '@shared/dynamo-hummingbird/tables/split-models'
import { CommentCreatedEvent } from '@shared/event-models/comments'
import { Role } from '@shared/rbac/roles'
import casual from 'casual'
import { ulid } from 'ulid'
import type { GenerateEventResults } from '../types'
import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models/comments'

const generateCommentCreated = (props: CommentModel): GenerateEventResults => {
  const {
    parentCommentId,
    on = {},
    createdByUsername,
    content = casual.sentences(2),
    courseId = casual.random_element(CourseIdValues as unknown as string[]),
    commentTargetId,
    commentTargetType,
  } = props

  if (!commentTargetId || !commentTargetType)
    throw Error('Comment type and entity ID are missing. Unable to create')
  const user = !createdByUsername ? casual.random_element(users) : null
  const commentId = ulid()
  const pk = `${commentTargetType}#${commentTargetId}`
  const username = user ? user.username : createdByUsername

  const comment = makeComment({
    pk: `${commentTargetType}#${commentTargetId}`,
    sk: commentId,
    commentId: commentId,
    courseId: courseId,
    parentCommentId: parentCommentId || commentId, // Its own ID is its parent for top-level, makes queries easier
    content,
    createdByUsername: username,
    createdByRoles: [Role.ADMIN],
    commentTargetType,
    commentTargetId,
    on,
  })

  const event = CommentCreatedEvent.make({
    pk: pk,
    sk: commentId,
    id: commentId,
    courseId,
    eventAgent: username,
    comment: comment,
  })

  return {
    event,
    comment,
  }
}

export default generateCommentCreated
