import type { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models'
import * as mocks from '@shared/trpc-lambda/mocks'
import generateCommentUnresolved from './generate-comment-unresolved'

const inputTestCommentObj = mocks.comment

const outputTestCommentEvent = mocks.commentUnresolvedEvent

const outputTestCommentObj = {
  ...mocks.comment,
  resolved: false,
  resolvedByUsername: mocks.reviewerUser.username,
  resolvedAtMs: Date.now(),
}

describe('generate-comment-unresolved.ts unit tests', () => {
  it('should create two entries for "CommentUnresolved" and "Comment" with the appropriate fields', () => {
    // create an asset
    const { event: commentUnresolvedEvent, comment: commentUnresolvedObj } =
      generateCommentUnresolved(inputTestCommentObj)
    // expected
    const expectedCommentUnresolvedFields = Object.keys(outputTestCommentEvent).sort()
    const expectedCommentObjFields = Object.keys(outputTestCommentObj).sort()
    // received
    const commentUnresolvedFields = Object.keys(commentUnresolvedEvent).sort()
    const commentObjFields = Object.keys(commentUnresolvedObj ?? {}).sort()
    // test object keys, ignore values as they are random
    expect(commentUnresolvedFields).toEqual(expectedCommentUnresolvedFields)
    expect(commentObjFields).toEqual(expectedCommentObjFields)
    //heck value(s) that actually matter
    expect(commentUnresolvedObj?.resolved).toBe(outputTestCommentEvent.resolved)
  })
  it('should throw an error if required fields are missing', () => {
    try {
      generateCommentUnresolved({} as CommentModel)
    } catch (e) {
      const expectedError = new Error('pk and sk are required fields to create unresolve event')
      expect(e).toStrictEqual(expectedError)
    }
  })
})
