import casual from 'casual'
import type { FormModel } from '@shared/dynamo-hummingbird/tables/split-models'
import { FormWorkflowUpdatedEvent } from '@shared/event-models'
import { FormStepValues, constructStoredFormWorkflow } from '@shared/types/model/workflow/forms'

const generateFormWorkflowUpdated = ({
  form,
  workflowSteps,
  chooseRandomWorkflowSteps,
}: {
  form: FormModel
  workflowSteps?: FormModel['workflowSteps']
  chooseRandomWorkflowSteps?: boolean
}): {
  event: FormWorkflowUpdatedEvent | undefined
  workflowSteps: FormModel['workflowSteps'] | undefined
} => {
  let newWorkflowSteps: FormModel['workflowSteps']
  const emptyReturn = {
    event: undefined,
    workflowSteps: undefined,
  }

  if (chooseRandomWorkflowSteps && !workflowSteps) {
    const workflowStepIndexesToGrab = Array.from({ length: casual.integer(0, 5) }, () =>
      casual.integer(0, FormStepValues.length - 1)
    )
    // If no steps to grab, return early
    if (!workflowStepIndexesToGrab.length) return emptyReturn

    const workflowSteps = workflowStepIndexesToGrab.map((index) => FormStepValues[index]!)
    newWorkflowSteps = constructStoredFormWorkflow(workflowSteps)
  } else if (workflowSteps) newWorkflowSteps = workflowSteps
  else return emptyReturn

  const event = FormWorkflowUpdatedEvent.make({
    pk: form.pk,
    sk: '',
    id: form.id,
    hbid: form.id,
    formId: form.id,
    courseId: form.courseId,
    eventAgent: form.createdByUsername,
    oldWorkflowSteps: form.workflowSteps,
    newWorkflowSteps,
  })
  return {
    event,
    workflowSteps: newWorkflowSteps,
  }
}

export default generateFormWorkflowUpdated
