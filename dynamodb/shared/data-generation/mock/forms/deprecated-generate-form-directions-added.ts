import casual from 'casual'
import { users } from '@bin/data/users'
import type { FormDirectionsAdded, FormSnapshot } from '@shared/dynamo-hummingbird'
import { formEventHandlers } from '@shared/event-sourcing/snapshot/form'
import { FORM_MODEL_VERSION } from '@shared/utils/constants/constants'
import type { GenerateEventResults } from '../types'

/** @deprecated */
const generateFormDirectionsAdded = async (
  props: FormDirectionsAdded,
  snapshot: FormSnapshot
): Promise<GenerateEventResults> => {
  const user = !props.userID ? casual.random_element(users) : null
  const snapshotSequenceNum =
    snapshot && snapshot.snapshotSequenceNum != null ? snapshot.snapshotSequenceNum + 1 : 1
  const formDirectionsAdded: FormDirectionsAdded = {
    ...props,
    id: snapshot.id,
    userID: user ? user.username : props.userID,
    sequenceNum: snapshotSequenceNum,
    modelVersion: FORM_MODEL_VERSION,
    type: 'FormDirectionsAdded',
    timestampMs: new Date().getTime(),
  }
  // every form action is followed by a snapshot
  if (!formEventHandlers.FormDirectionsAdded) {
    throw new Error('FormDirectionsAdded event handler is missing')
  }
  const formSnapshot = formEventHandlers.FormDirectionsAdded(formDirectionsAdded, snapshot)
  return {
    event: formDirectionsAdded,
    snapshot: formSnapshot,
  }
}

export default generateFormDirectionsAdded
