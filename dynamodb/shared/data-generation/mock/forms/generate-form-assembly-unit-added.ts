import {
  FormAssociationModel,
  FormModel,
  makeFormAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { FormAssemblyUnitAddedEvent } from '@shared/event-models'

const generateFormAssemblyUnitsAdded = ({
  form,
  assemblyUnitId,
  ordinal,
  sectionId,
}: {
  form: FormModel
  assemblyUnitId: string
  ordinal: number
  sectionId: string
}): {
  event: FormAssemblyUnitAddedEvent
  association: FormAssociationModel
} => {
  const event = FormAssemblyUnitAddedEvent.make({
    pk: form.pk,
    sk: assemblyUnitId,
    id: form.id,
    hbid: form.id,
    formId: form.id,
    courseId: form.courseId,
    eventAgent: form.createdByUsername,
    sectionId,
  })
  const association = makeFormAssociation({
    pk: form.pk,
    sk: `Association#${assemblyUnitId}`,
    associationChildId: assemblyUnitId,
    associationChildType: 'AssemblyUnit',
    createdByUsername: form.createdByUsername,
    courseId: form.courseId,
    on: {
      section: sectionId,
      ordinal,
    },
  })
  return {
    event,
    association,
  }
}

export default generateFormAssemblyUnitsAdded
