import {
  FormAssociationModel,
  FormModel,
  makeFormAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { FormDirectionsAddedEvent } from '@shared/event-models'
import type { DirectionsTypeId } from '@shared/types/model'

const generateFormDirectionsAdded = ({
  form,
  directionId,
  directionType,
  sectionId,
}: {
  form: FormModel
  directionId: string
  directionType: DirectionsTypeId
  sectionId?: string
}): {
  event: FormDirectionsAddedEvent
  association: FormAssociationModel
} => {
  const event = FormDirectionsAddedEvent.make({
    pk: form.pk,
    sk: directionId,
    id: form.id,
    hbid: form.id,
    formId: form.id,
    courseId: form.courseId,
    eventAgent: form.createdByUsername,
    directionId,
    directionType,
    sectionId,
  })
  const association = makeFormAssociation({
    pk: form.pk,
    sk: `Association#${directionId}`,
    associationChildId: directionId,
    associationChildType: 'Asset',
    createdByUsername: form.createdByUsername,
    courseId: form.courseId,
    on: {
      directionType,
      section: sectionId,
    },
  })
  return {
    event,
    association,
  }
}

export default generateFormDirectionsAdded
