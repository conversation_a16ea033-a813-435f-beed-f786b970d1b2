import casual from 'casual'
import { ulid } from 'ulid'
import { users } from '@bin/data/users'
import type { FormCreated } from '@shared/dynamo-hummingbird'
import { EventNamePrefixConstants } from '@shared/event-sourcing'
import { formEventHandlers } from '@shared/event-sourcing/snapshot/form'
import { FORM_MODEL_VERSION } from '@shared/utils/constants/constants'
import type { GenerateEventResults } from '../types'
import { getLatestSnapshotSequenceNum } from '../utils'

/** @deprecated */
const generateFormCreated = async (props: FormCreated): Promise<GenerateEventResults> => {
  const user = !props.creatorID ? casual.random_element(users) : null
  const snapshotSequenceNum = await getLatestSnapshotSequenceNum(
    EventNamePrefixConstants.Form,
    props.id
  )
  const name = props.name ? props.name : casual.title
  const formCreated: FormCreated = {
    ...props,
    creatorID: user ? user.username : props.creatorID,
    name: name,
    lowercaseName: name.toLowerCase(),
    sequenceNum: snapshotSequenceNum,
    modelVersion: FORM_MODEL_VERSION,
    type: 'FormCreated',
    timestampMs: new Date().getTime(),
    examDirections: [],
    formSections: [
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
      {
        sectionDirections: [],
        assemblyUnitIDs: [],
        uniqueId: ulid(),
      },
    ],
    isActive: true,
  }
  // every form action is followed by a snapshot
  if (!formEventHandlers.FormCreated) {
    throw new Error('FormCreated event handler is missing')
  }
  const formSnapshot = formEventHandlers.FormCreated(formCreated, {})
  return {
    event: formCreated,
    snapshot: formSnapshot,
  }
}

export default generateFormCreated
