import type { FormDirectionsAdded, FormSnapshot } from '@shared/dynamo-hummingbird'
import generateFormDirectionsAdded from './deprecated-generate-form-directions-added'

const inputTestFormSnapshotExamDirection: FormSnapshot = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 1,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: [],
  programID: '59tWw',
  timestampMs: 1695137610207,
}

const outputTestFormEventExamDirection: FormDirectionsAdded = {
  sequenceNum: 2,
  modelVersion: 0,
  id: 'ABC000000',
  type: 'FormDirectionsAdded',
  userID: 'mmorales',
  timestampMs: 1695141665014,
  examDirections: ['DEF000000'],
}

const outputTestFormSnapshotExamDirection: FormSnapshot & {
  oldDirectionsToDisassociate?: string[]
} = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 2,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: ['DEF000000'],
  programID: '59tWw',
  timestampMs: 1695137610207,
  oldDirectionsToDisassociate: [],
}

const inputTestFormSnapshotSectionDirection = outputTestFormSnapshotExamDirection

const outputTestFormEventSectionDirection: FormDirectionsAdded = {
  sequenceNum: 2,
  modelVersion: 0,
  id: 'ABC000000',
  type: 'FormDirectionsAdded',
  userID: 'mmorales',
  timestampMs: 1695141665014,
  formSections: {
    sectionIndex: 0,
    sectionDirections: ['GHI000000'],
  },
}

const outputTestFormSnapshotSectionDirection: FormSnapshot & {
  oldDirectionsToDisassociate?: string[]
} = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 3,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: ['GHI000000'],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: ['DEF000000'],
  programID: '59tWw',
  timestampMs: 1695137610207,
  oldDirectionsToDisassociate: [],
}

const testMap = {
  Exam: {
    inputSnapshot: inputTestFormSnapshotExamDirection,
    outputEvent: outputTestFormEventExamDirection,
    outputSnapshot: outputTestFormSnapshotExamDirection,
  },
  Section: {
    inputSnapshot: inputTestFormSnapshotSectionDirection,
    outputEvent: outputTestFormEventSectionDirection,
    outputSnapshot: outputTestFormSnapshotSectionDirection,
  },
}

describe('generate-form-directions-added.ts unit tests', () => {
  it.each(Object.keys(testMap))(
    '%s: should create two entries for "FormDirectionsAdded" and "FormSnapshot" with the appropriate fields',
    async (name) => {
      const key = name as 'Exam' | 'Section'
      const inputTestFormSnapshot = testMap[key].inputSnapshot
      const outputTestFormEvent = testMap[key].outputEvent
      const outputTestFormSnapshot = testMap[key].outputSnapshot
      // create an asset
      const { event: formEvent, snapshot: formSnapshot } = await generateFormDirectionsAdded(
        {
          ...(outputTestFormEvent.examDirections
            ? { examDirections: outputTestFormEvent.examDirections }
            : {}),
          ...(outputTestFormEvent.formSections
            ? { formSections: outputTestFormEvent.formSections }
            : {}),
        },
        inputTestFormSnapshot
      )
      // expected
      const expectedFormEventFields = Object.keys(outputTestFormEvent).sort()
      const expectedFormSnapshotFields = Object.keys(outputTestFormSnapshot).sort()
      // received
      const formEventFields = Object.keys(formEvent).sort()
      const formSnapshotFields = Object.keys(formSnapshot).sort()
      // test object keys, ignore values as they are random
      expect(formEventFields).toEqual(expectedFormEventFields)
      expect(formSnapshotFields).toEqual(expectedFormSnapshotFields)
    }
  )
})
