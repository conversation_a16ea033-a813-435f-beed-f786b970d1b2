import type { FormAssemblyUnitsAdded, FormSnapshot } from '@shared/dynamo-hummingbird'
import generateFormAssemblyUnitsAdded from './deprecated-generate-form-assembly-unit-added'

const inputTestFormSnapshot: FormSnapshot = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 1,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: [],
  programID: '59tWw',
  timestampMs: 1695137610207,
}

const outputTestFormEvent: FormAssemblyUnitsAdded = {
  sequenceNum: 2,
  modelVersion: 0,
  id: 'ABC000000',
  type: 'FormAssemblyUnitsAdded',
  userID: 'mmorales',
  timestampMs: 1695141665014,
  formSections: {
    sectionIndex: 0,
    assemblyUnitIDs: ['DEF000000'],
  },
}

const outputTestFormSnapshot: FormSnapshot & {
  bypassAssociationUpdateMap?: { [key: string]: boolean }
} = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 2,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: ['DEF000000'],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: [],
  programID: '59tWw',
  timestampMs: 1695137610207,
  bypassAssociationUpdateMap: {},
}

describe('generate-form-directions-added.ts unit tests', () => {
  it('should create two entries for "FormAssemblyUnitsAdded" and "FormSnapshot" with the appropriate fields', async () => {
    // create an asset
    const { event: formEvent, snapshot: formSnapshot } = await generateFormAssemblyUnitsAdded(
      { formSections: outputTestFormEvent.formSections },
      inputTestFormSnapshot
    )
    // expected
    const expectedFormEventFields = Object.keys(outputTestFormEvent).sort()
    const expectedFormSnapshotFields = Object.keys(outputTestFormSnapshot).sort()
    // received
    const formEventFields = Object.keys(formEvent).sort()
    const formSnapshotFields = Object.keys(formSnapshot).sort()
    // test object keys, ignore values as they are random
    expect(formEventFields).toEqual(expectedFormEventFields)
    expect(formSnapshotFields).toEqual(expectedFormSnapshotFields)
  })
})
