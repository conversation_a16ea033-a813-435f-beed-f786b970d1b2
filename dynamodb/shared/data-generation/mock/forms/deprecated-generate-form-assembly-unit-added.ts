import casual from 'casual'
import { users } from '@bin/data/users'
import type { FormAssemblyUnitsAdded, FormSnapshot } from '@shared/dynamo-hummingbird'
import { formEventHandlers } from '@shared/event-sourcing/snapshot/form'
import { FORM_MODEL_VERSION } from '@shared/utils/constants/constants'
import type { GenerateEventResults } from '../types'

/** @deprecated */
const generateFormAssemblyUnitsAdded = async (
  props: FormAssemblyUnitsAdded,
  snapshot: FormSnapshot
): Promise<GenerateEventResults> => {
  const user = !props.userID ? casual.random_element(users) : null
  const snapshotSequenceNum =
    snapshot && snapshot.snapshotSequenceNum != null ? snapshot.snapshotSequenceNum + 1 : 1
  const formAssemblyUnitsAdded: FormAssemblyUnitsAdded = {
    ...props,
    id: snapshot.id,
    userID: user ? user.username : props.userID,
    sequenceNum: snapshotSequenceNum,
    modelVersion: FORM_MODEL_VERSION,
    type: 'FormAssemblyUnitsAdded',
    timestampMs: new Date().getTime(),
  }
  // every form action is followed by a snapshot
  if (!formEventHandlers.FormAssemblyUnitsAdded) {
    throw new Error('FormAssemblyUnitsAdded event handler is missing')
  }
  const formSnapshot = formEventHandlers.FormAssemblyUnitsAdded(formAssemblyUnitsAdded, snapshot)
  return {
    event: formAssemblyUnitsAdded,
    snapshot: formSnapshot,
  }
}

export default generateFormAssemblyUnitsAdded
