import casual from 'casual'
import { ulid } from 'ulid'
import { users } from '@bin/data/users'
import {
  FormModel,
  FormSectionModel,
  makeForm,
  makeFormSection,
} from '@shared/dynamo-hummingbird/tables/split-models'
import { FormCreatedEvent } from '@shared/event-models'
import { loadCourseConfigFromDb } from '@shared/trpc-lambda/context/course-service'

const generateFormCreated = async (
  props: Pick<FormModel, 'id' | 'courseId'> & Partial<FormModel>
): Promise<{
  form: FormModel
  sections: FormSectionModel[]
  event: FormCreatedEvent
}> => {
  const courseConfig = await loadCourseConfigFromDb(props.courseId)
  const username = props.createdByUsername || casual.random_element(users).username
  const name = props.name ? props.name : casual.title
  const pk = `Form#${props.id}`
  // make form
  const form = makeForm({
    ...props,
    pk,
    sk: props.id,
    id: props.id,
    hbid: props.id,
    courseId: props.courseId,
    name,
    createdByUsername: username,
    updatedByUsername: username,
    updatedAtMs: Date.now(),
  })
  // make form sections
  const sectionIds = Array.from({ length: courseConfig?.authoring.Form.formSection.min || 6 }, () =>
    ulid()
  )
  const sections = sectionIds.map((sectionId, i) =>
    makeFormSection({
      pk: pk,
      sk: sectionId,
      id: sectionId,
      hbid: sectionId,
      courseId: props.courseId,
      name: `Section ${i + 1}`,
      createdByUsername: username,
      updatedByUsername: username,
      updatedAtMs: Date.now(),
      ordinal: i + 1,
    })
  )
  // make form created event
  const event = FormCreatedEvent.make({
    pk,
    sk: '',
    id: props.id,
    formId: props.id,
    hbid: props.id,
    form,
    sections,
    courseId: props.courseId,
    eventAgent: username,
  })

  return {
    event,
    form,
    sections,
  }
}

export default generateFormCreated
