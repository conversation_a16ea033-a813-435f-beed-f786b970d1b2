import type { FormCreated, FormSnapshot } from '@shared/dynamo-hummingbird'
import generateFormCreated from './deprecated-generate-form-created'

const outputTestFormEvent: FormCreated = {
  sequenceNum: 1,
  modelVersion: 0,
  creatorID: 'mmorales',
  type: 'FormCreated',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: [],
  programID: '59tWw',
  timestampMs: 1695137610207,
  isActive: true,
}

const outputTestFormSnapshot: FormSnapshot = {
  sequenceNum: 0,
  modelVersion: 0,
  creatorID: 'mmorales',
  snapshotSequenceNum: 1,
  type: 'FormSnapshot',
  lowercaseName: 'test1',
  subjectID: '116',
  formSections: [
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
    {
      sectionDirections: [],
      assemblyUnitIDs: [],
    },
  ],
  name: 'test1',
  id: 'ABC000000',
  examDirections: [],
  programID: '59tWw',
  timestampMs: 1695137610207,
  isActive: true,
}

describe('generate-form-created.ts unit tests', () => {
  it('should create two entries for "FormCreated" and "FormSnapshot" with the appropriate fields', async () => {
    // create an asset
    const { event: formCreatedEvent, snapshot: formCreatedSnapshot } = await generateFormCreated({
      id: 'ABC000000',
      subjectID: '116',
      programID: '59tWw',
    })
    // expected
    const expectedFormCreatedFields = Object.keys(outputTestFormEvent).sort()
    const expectedFormSnapshotFields = Object.keys(outputTestFormSnapshot).sort()
    // received
    const formCreatedFields = Object.keys(formCreatedEvent).sort()
    const formSnapshotFields = Object.keys(formCreatedSnapshot).sort()
    // test object keys, ignore values as they are random
    expect(formCreatedFields).toEqual(expectedFormCreatedFields)
    expect(formSnapshotFields).toEqual(expectedFormSnapshotFields)
  })
})
