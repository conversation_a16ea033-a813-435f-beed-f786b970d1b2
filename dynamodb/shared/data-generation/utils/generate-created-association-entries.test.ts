import { newAssemblyUnit } from '@shared/trpc-lambda/mocks'
import generateCreatedAssociationEntries from './generate-created-association-entries'

describe('generateCreatedAssociationEntries tests', () => {
  beforeAll(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date(2024, 1, 1))
  })
  afterAll(() => {
    jest.clearAllMocks()
  })
  it('will return event emitting entries for new association (default)', () => {
    const username = 'ikhov'
    const createdAtMs = new Date(2024, 1, 1).getTime()
    const associationChildId = 'XYZ000000'
    const associationChildType = 'AssemblyUnit'
    const courseId = '117'
    const pk = `AssemblyUnit#${newAssemblyUnit.id}`
    const sk = `Association#${associationChildId}`
    const assemblyUnitAssetAssociationRecord = generateCreatedAssociationEntries({
      parentId: newAssemblyUnit.id,
      childId: associationChildId,
      courseId,
      createdByUsername: username,
    })
    const expctedAssociation = {
      associationChildId,
      associationChildType,
      courseId,
      createdAtMs,
      createdByUsername: username,
      on: {},
      pk,
      sk,
      type: 'Association',
    }
    expect(assemblyUnitAssetAssociationRecord).toStrictEqual([
      {
        table: 'authoring',
        record: expctedAssociation,
      },
    ])
  })
  it('will return event emitting entries for new association with different association child type and on object', () => {
    const username = 'ikhov'
    const createdAtMs = new Date(2024, 1, 1).getTime()
    const associationChildId = 'XYZ000000'
    const associationChildType = 'Asset'
    const courseId = '117'
    const pk = `AssemblyUnit#${newAssemblyUnit.id}`
    const sk = `Association#${associationChildId}`
    const assemblyUnitAssetAssociationRecord = generateCreatedAssociationEntries({
      parentId: newAssemblyUnit.id,
      childId: associationChildId,
      courseId,
      createdByUsername: username,
      associationParentType: 'AssemblyUnit',
      associationChildType,
      on: {},
    })
    const expctedAssociation = {
      associationChildId,
      associationChildType,
      courseId,
      createdAtMs,
      createdByUsername: username,
      on: {},
      pk,
      sk,
      type: 'Association',
    }
    expect(assemblyUnitAssetAssociationRecord).toStrictEqual([
      {
        table: 'authoring',
        record: expctedAssociation,
      },
    ])
  })
  it('will throw an error with missing required fields', () => {
    expect(() => {
      generateCreatedAssociationEntries({
        parentId: newAssemblyUnit.id,
        childId: 'XYZ000000',
      })
    }).toThrowError('courseId and createdByUsername are required in the associationProps')
  })
})
