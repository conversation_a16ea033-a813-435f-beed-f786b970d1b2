import { generateBatchIDs, generateId } from '@lambdas/api/src/friendly-id/id-service'
import type { FriendlyIdModel } from '@lambdas/api/src/main'

/**
 * Generate a single ID, either from a custom ID or auto-generated
 */
export async function generateIdWithCustom({
  idModel,
  customId,
  ttl,
  parentID,
}: {
  idModel: FriendlyIdModel
  customId?: string
  ttl?: number
  parentID?: string
}): Promise<string> {
  const result = await generateId({
    idModel,
    customId,
    ttl,
    parentID,
  })
  return result.id
}

/**
 * Generate batch IDs, either from custom IDs array or auto-generated
 */
export async function generateBatchIDsWithCustom({
  length,
  customIds,
  ttl,
  parentID,
}: {
  length: number
  customIds?: string[]
  ttl?: number
  parentID?: string
}): Promise<string[]> {
  return generateBatchIDs(length, parentID, ttl, customIds)
}

/**
 * Validates an array of custom IDs for asset generation
 */
export function validateCustomIdsForAssets(customIds: string[], count: number): void {
  if (customIds.length !== count) {
    throw new Error(`Number of custom IDs (${customIds.length}) must match count (${count})`)
  }
  
  const duplicates = customIds.filter((id, index) => customIds.indexOf(id) !== index)
  if (duplicates.length > 0) {
    throw new Error(`Duplicate custom IDs found: ${duplicates.join(', ')}`)
  }
}
