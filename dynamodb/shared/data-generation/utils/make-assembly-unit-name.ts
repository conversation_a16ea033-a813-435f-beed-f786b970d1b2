import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
  QuestionTypeInitials,
} from '@shared/dynamo-hummingbird/tables/split-models'

interface MakeAssemblyUnitNameParams {
  sequence?: number
  assemblyUnitType: AssemblyUnitType
  adminYear?: string | null
  baseForm?: BaseForm | null
  courseCode?: string
  questionType?: QuestionType | null
  parentAssemblyUnitName?: string
  questionTypeNumber?: number
}

export const ALL_AU_PREFIX = 'AU'
export const SEQUENCE_AU_PREFIX = 'S'
export const COMPLETE_AU_PREFIX = 'P'

const makeAssemblyUnitName = ({
  sequence,
  assemblyUnitType,
  adminYear,
  baseForm,
  courseCode,
  questionType,
  parentAssemblyUnitName,
  questionTypeNumber,
}: // TODO use parent form name?
MakeAssemblyUnitNameParams) => {
  const questionTypeInitial =
    QuestionTypeInitials[questionType || QuestionType.MULTIPLE_CHOICE_QUESTION]
  switch (assemblyUnitType) {
    case AssemblyUnitType.UNSPECIFIED:
      // Format: AU24_AAS
      return `${ALL_AU_PREFIX}${adminYear}_${courseCode}`
    case AssemblyUnitType.BASE:
      // Format: AU24_AAS_F1MC00
      return `${ALL_AU_PREFIX}${adminYear}_${courseCode}_${baseForm}${questionTypeInitial}0${questionTypeNumber}`
    case AssemblyUnitType.SEQUENCE:
      // Format: AU24_AAS_F1MC00_S0
      return `${parentAssemblyUnitName}_${SEQUENCE_AU_PREFIX}${sequence}`
    case AssemblyUnitType.COMPLETE:
      // Format: AU24_AAS_F1MC00_S0_P001
      return `${parentAssemblyUnitName}_${COMPLETE_AU_PREFIX}${String(sequence).padStart(3, '0')}`
    default:
      return ''
  }
}

export default makeAssemblyUnitName
