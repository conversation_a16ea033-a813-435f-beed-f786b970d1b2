import {
  AssemblyUnitType,
  BaseForm,
  QuestionType,
} from '@shared/dynamo-hummingbird/tables/split-models'
import makeAssemblyUnitName from './make-assembly-unit-name'

describe('makeAssemblyUnitName tests', () => {
  it.each([
    [AssemblyUnitType.UNSPECIFIED, 'AU24_AAS'],
    [AssemblyUnitType.BASE, 'AU24_AAS_F1MC00'],
    [AssemblyUnitType.SEQUENCE, 'AU24_AAS_F1MC00_S1'],
    [AssemblyUnitType.COMPLETE, 'AU24_AAS_F1MC00_S1_P001'],
    ['no case' as AssemblyUnitType, ''],
  ])('will return the expected assembly unit name', (assemblyUnitType, expectedName) => {
    let parentAssemblyUnitName = undefined
    if (assemblyUnitType === AssemblyUnitType.SEQUENCE) {
      parentAssemblyUnitName = 'AU24_AAS_F1MC00'
    } else if (assemblyUnitType === AssemblyUnitType.COMPLETE) {
      parentAssemblyUnitName = 'AU24_AAS_F1MC00_S1'
    }
    expect(
      makeAssemblyUnitName({
        sequence: 1,
        assemblyUnitType,
        adminYear: '24',
        baseForm: BaseForm.F1,
        courseCode: 'AAS',
        questionType:
          assemblyUnitType !== AssemblyUnitType.UNSPECIFIED
            ? QuestionType.MULTIPLE_CHOICE_QUESTION
            : undefined,
        parentAssemblyUnitName,
        questionTypeNumber: 0,
      })
    ).toEqual(expectedName)
  })
})
