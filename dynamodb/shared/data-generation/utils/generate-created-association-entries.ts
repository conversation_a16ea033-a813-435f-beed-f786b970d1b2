import type { WriteEntry } from '@shared/dynamo-hummingbird/tables/io/write'
import {
  AssociationModelType,
  makeAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models'

interface GenerateAssociationEntriesParams {
  parentId: string
  childId: string
  courseId?: string
  createdByUsername?: string
  associationParentType?: string
  associationChildType?: string
  on?: object
}

export const generateCreatedAssociationEntries = ({
  parentId,
  childId,
  courseId,
  createdByUsername,
  associationParentType = 'AssemblyUnit',
  associationChildType = 'AssemblyUnit',
  on = {},
}: GenerateAssociationEntriesParams): WriteEntry[] => {
  if (!courseId || !createdByUsername) {
    throw new Error('courseId and createdByUsername are required in the associationProps')
  }
  const pk = `${associationParentType}#${parentId}`
  const sk = `Association#${childId}`
  const entity = {
    pk,
    sk,
    associationChildId: childId,
    courseId: courseId,
    associationChildType: associationChildType,
    on: on,
    createdByUsername: createdByUsername,
  } as AssociationModelType
  const association = makeAssociation({ ...entity })
  return [{ table: 'authoring', record: association }]
}

export default generateCreatedAssociationEntries
