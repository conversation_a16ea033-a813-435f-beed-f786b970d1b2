import { pascalCase } from 'case-anything'
import type { WriteEntry } from '@shared/dynamo-hummingbird/tables/io/write'
import type { AssemblyUnitModel } from '@shared/dynamo-hummingbird/tables/split-models'
import type { AssemblyUnitEventType } from '@shared/event-models'

type DataType = Record<string, string | number | boolean | string[] | object | undefined>

const prefixKey = (data: DataType, prefix: string): DataType => {
  return Object.entries(data).reduce((acc, [key, value]) => {
    acc[`${prefix}${pascalCase(key)}`] = value
    return acc
  }, {} as DataType)
}

const generateEditedAssemblyUnitEntries = ({
  assemblyUnit,
  username,
  oldData,
  newData,
  assemblyUnitEvent,
  addPrefix,
  originAssemblyUnitId,
}: {
  assemblyUnit: AssemblyUnitModel
  username: string
  oldData: DataType
  newData: DataType
  assemblyUnitEvent: AssemblyUnitEventType
  addPrefix?: boolean
  originAssemblyUnitId?: string
}): WriteEntry[] => {
  const { pk, sk, courseId } = assemblyUnit
  const id = pk.split('#').pop() as string
  const before = addPrefix ? prefixKey(oldData, 'old') : oldData
  const after = addPrefix ? prefixKey(newData, 'new') : newData

  const event = assemblyUnitEvent.make({
    pk,
    sk,
    id,
    courseId,
    eventAgent: username,
    originAssemblyUnitId,
    ...before,
    ...after,
  })
  const updatedAtMs = new Date().getTime()
  return [
    { table: 'authoring', record: { ...assemblyUnit, updatedAtMs, updatedByUsername: username } },
    { table: 'events', record: event },
  ]
}

export default generateEditedAssemblyUnitEntries
