import type { WriteEntry } from '@shared/dynamo-hummingbird/tables/io/write'
import {
  AssociationModelType,
  makeAssociation,
} from '@shared/dynamo-hummingbird/tables/split-models'

interface GenerateRemovedAssociationEntriesParams {
  parentId: string
  childId: string
  courseId?: string
  createdByUsername?: string
  associationParentType?: string
  associationChildType?: string
  on?: object
}

const generateRemovedAssociationEntries = ({
  parentId,
  childId,
  courseId,
  createdByUsername,
  associationParentType = 'AssemblyUnit',
  associationChildType = 'AssemblyUnit',
}: GenerateRemovedAssociationEntriesParams): WriteEntry[] => {
  if (!courseId || !createdByUsername) {
    throw new Error('courseId and createdByUsername are required in the associationProps')
  }
  const pk = `${associationParentType}#${parentId}`
  const sk = `Association#${childId}`
  const entity = {
    pk,
    sk,
    associationChildId: childId,
    courseId: courseId,
    associationChildType: associationChildType,
    createdByUsername: createdByUsername,
    on: {},
  } as AssociationModelType
  const association = makeAssociation({ ...entity })
  return [{ table: 'authoring', record: association, remove: true }]
}

export default generateRemovedAssociationEntries
