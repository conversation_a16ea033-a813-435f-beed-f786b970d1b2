import { newAssemblyUnit } from '@shared/trpc-lambda/mocks'
import generateRemovedAssociationEntries from './generate-removed-association-entries'

describe('generateRemovedAssociationEntries tests', () => {
  beforeAll(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date(2024, 1, 1))
  })
  afterAll(() => {
    jest.clearAllMocks()
  })
  it('will return event emitting entries for new association (default)', () => {
    const username = 'clarkkent'
    const createdAtMs = new Date(2024, 1, 1).getTime()
    const associationChildId = 'XYZ000000'
    const associationChildType = 'Asset'
    const courseId = '117'
    const pk = `AssemblyUnit#${newAssemblyUnit.id}`
    const sk = `Association#${associationChildId}`
    const assemblyUnitAssetAssociationRecord = generateRemovedAssociationEntries({
      parentId: newAssemblyUnit.id,
      childId: associationChildId,
      courseId,
      createdByUsername: username,
      associationChildType,
    })
    const expctedAssociation = {
      associationChildId,
      associationChildType,
      courseId,
      createdAtMs,
      createdByUsername: username,
      on: {},
      pk,
      sk,
      type: 'Association',
    }
    expect(assemblyUnitAssetAssociationRecord).toStrictEqual([
      {
        table: 'authoring',
        record: expctedAssociation,
        remove: true,
      },
    ])
  })

  it('will throw an error with missing required fields', () => {
    expect(() => {
      generateRemovedAssociationEntries({
        parentId: newAssemblyUnit.id,
        childId: 'XYZ000000',
      })
    }).toThrowError('courseId and createdByUsername are required in the associationProps')
  })
})
