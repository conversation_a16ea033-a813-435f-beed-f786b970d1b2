import {
  AssemblyUnitArchivedEvent,
  AssemblyUnitAssetsAddedEvent,
  AssemblyUnitAssetsRemovedEvent,
  AssemblyUnitChildAddedEvent,
  AssemblyUnitEventType,
  AssemblyUnitItemDeactivatedEvent,
  AssemblyUnitItemReorderedEvent,
  AssemblyUnitRenamedEvent,
  AssemblyUnitRestoredEvent,
} from '@shared/event-models'
import { newAssemblyUnitFull } from '@shared/trpc-lambda/mocks'
import generateEditedAssemblyUnitEntries from './generate-edited-assembly-unit-entries'

const eventTypes = [
  AssemblyUnitChildAddedEvent,
  AssemblyUnitRenamedEvent,
  AssemblyUnitArchivedEvent,
  AssemblyUnitRestoredEvent,
  AssemblyUnitAssetsAddedEvent,
  AssemblyUnitAssetsRemovedEvent,
  AssemblyUnitItemReorderedEvent,
  AssemblyUnitItemDeactivatedEvent,
]

const oldAssetsAndItemOrder = [
  {
    children: [
      {
        itemDeactivatedInAu: true,
        id: 'BUQ000001',
      },
    ],
    id: 'BUQ000000',
    ordinal: 0,
  },
  {
    children: [
      {
        itemDeactivatedInAu: true,
        id: 'HQG000001',
      },
    ],
    id: 'HQG000000',
    ordinal: 1,
  },
  {
    children: [
      {
        itemDeactivatedInAu: true,
        id: 'PEY000001',
      },
    ],
    id: 'PEY000000',
    ordinal: 2,
  },
  {
    children: [
      {
        itemDeactivatedInAu: true,
        id: 'KPT000001',
      },
    ],
    id: 'KPT000000',
    ordinal: 3,
  },
  {
    children: [
      {
        itemDeactivatedInAu: true,
        id: 'CGJ000001',
      },
    ],
    id: 'CGJ000000',
    ordinal: 4,
  },
]
const newAssetsAndItemOrder = [
  {
    children: [
      {
        itemDeactivatedInAu: false,
        id: 'HQG000001',
      },
    ],
    ordinal: 1,
    id: 'HQG000000',
  },
  {
    children: [
      {
        itemDeactivatedInAu: false,
        id: 'BUQ000001',
      },
    ],
    ordinal: 2,
    id: 'BUQ000000',
  },
  {
    children: [
      {
        itemDeactivatedInAu: false,
        id: 'PEY000001',
      },
    ],
    ordinal: 3,
    id: 'PEY000000',
  },
  {
    children: [
      {
        itemDeactivatedInAu: false,
        id: 'KPT000001',
      },
    ],
    ordinal: 4,
    id: 'KPT000000',
  },
  {
    children: [
      {
        itemDeactivatedInAu: false,
        id: 'CGJ000001',
      },
    ],
    ordinal: 5,
    id: 'CGJ000000',
  },
]

const eventTestCases: [
  string,
  AssemblyUnitEventType,
  Record<string, object>,
  Record<string, string | string[] | boolean | object>,
  boolean
][] = [
  [
    'AssemblyUnitChildAddedEvent',
    eventTypes[0] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: { children: ['ASD000001'] },
    },
    { children: ['ASD000001'] },
    false,
  ],
  [
    'AssemblyUnitRenamedEvent',
    eventTypes[1] as AssemblyUnitEventType,
    {
      oldData: { name: 'oldName' },
      newData: { name: 'newName' },
    },
    { oldName: 'oldName', newName: 'newName' },
    true,
  ],
  [
    'AssemblyUnitArchivedEvent',
    eventTypes[2] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: { isActive: false },
    },
    { isActive: false },
    false,
  ],
  [
    'AssemblyUnitRestoredEvent',
    eventTypes[3] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: { isActive: true },
    },
    { isActive: true },
    false,
  ],
  [
    'AssemblyUnitAssetsAddedEvent',
    eventTypes[4] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: { assetIds: ['FGH000001'] },
    },
    { assetIds: ['FGH000001'] },
    false,
  ],
  [
    'AssemblyUnitAssetsRemovedEvent',
    eventTypes[5] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: { assetIds: ['FGH000001'] },
    },
    { assetIds: ['FGH000001'] },
    false,
  ],
  [
    'AssemblyUnitItemReorderedEvent',
    eventTypes[6] as AssemblyUnitEventType,
    {
      oldData: { assetsAndItemOrder: oldAssetsAndItemOrder },
      newData: { assetsAndItemOrder: newAssetsAndItemOrder },
    },
    {
      oldAssetsAndItemOrder,
      newAssetsAndItemOrder,
    },
    true,
  ],
  [
    'AssemblyUnitItemDeactivatedEvent',
    eventTypes[7] as AssemblyUnitEventType,
    {
      oldData: {},
      newData: {
        item: {
          itemId: 'GXC000001',
          assetId: 'GXC000000',
        },
      },
    },
    {
      item: {
        itemId: 'GXC000001',
        assetId: 'GXC000000',
      },
    },
    false,
  ],
]

describe('generateEditedAssemblyUnitEntries tests', () => {
  beforeAll(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date(2024, 1, 1))
  })
  afterAll(() => {
    jest.clearAllMocks()
  })
  it.each(eventTestCases)(
    'will return event emitting entries for %s',
    (name, eventType, input, expectedOutput, addPrefix) => {
      const username = 'ikhov'
      const updatedAtMs = new Date(2024, 1, 1).getTime()
      const updatedAssemblyUnitEntries = generateEditedAssemblyUnitEntries({
        assemblyUnit: newAssemblyUnitFull,
        username,
        assemblyUnitEvent: eventType as AssemblyUnitEventType,
        oldData: { ...input.oldData },
        newData: { ...input.newData },
        addPrefix,
      })
      const expectedEvent = {
        courseId: newAssemblyUnitFull.courseId,
        eventAgent: username,
        eventTimestamp: updatedAtMs,
        id: newAssemblyUnitFull.pk.split('#')[1],
        pk: newAssemblyUnitFull.pk,
        sequenceNumber: 0,
        sk: `${updatedAtMs}#${name}#${newAssemblyUnitFull.pk}`,
        type: name,
        ...expectedOutput,
      }
      expect(updatedAssemblyUnitEntries).toStrictEqual([
        {
          table: 'authoring',
          record: { ...newAssemblyUnitFull, updatedAtMs, updatedByUsername: 'ikhov' },
        },
        { table: 'events', record: expectedEvent },
      ])
    }
  )
})
