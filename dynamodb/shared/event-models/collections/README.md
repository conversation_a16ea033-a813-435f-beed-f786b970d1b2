# Collection Events

All collection events extend [`Event`](../README.md#event).

### CollectionEvent

```ts
interface CollectionEvent extends Event {
  pk: `Collection#${collectionId}`
  collectionId: friendlyId
  hbid: friendlyId
}
```

### CollectionCreatedEvent

Created whenever a user creates a new collection.

```ts
interface CollectionCreatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionCreatedEvent#${collectionId}`
  /**
   * The full collection database record that was created
   */
  collection: Collection
  /**
   * Assets that are in the collection from the start
   */
  assets: friendlyId[]
}
```

### CollectionRenamedEvent

Created whenever a user edits a collection's name.

```ts
interface CollectionRenamedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionRenamedEvent#${collectionId}`
  /**
   * The name of the collection before it was edited
   */
  oldName: string
  /**
   * The new, edited name for the collection
   */
  newName: string
}
```

### CollectionArchivedEvent

Created whenever a collection is archived.

```ts
interface CollectionArchivedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionArchivedEvent#${collectionId}`
  reason?: string
}
```

### CollectionRestoredEvent

Created whenever a collection is unarchived.

```ts
interface CollectionRestoredEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionRestoredEvent#${collectionId}`
  reason?: string
}
```

### CollectionAssetsAddedEvent

Created whenever a collection has assets added.

```ts
interface CollectionAssetsAddedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionAssetsAddedEvent#${collectionId}`
  /**
   * The list of asset IDs that were added
   */
  assets: friendlyId[]
  /**
   * Where in the asset list they were added (-1 = at the end)
   */
  position: number = -1
}
```

### CollectionAssetsRemovedEvent

Created whenever a collection has assets added.

```ts
interface CollectionAssetsRemovedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionAssetsRemovedEvent#${collectionId}`
  /**
   * The list of asset IDs that were removed
   */
  assets: friendlyId[]
}
```

### CollectionAssetsReorderedEvent

Created whenever a collection has assets reordered.

```ts
interface CollectionAssetsReorderedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionAssetsReorderedEvent#${collectionId}`
  /**
   * The sorted, full list of associated asset IDs within the collection
   */
  assetOrder: friendlyId[]
}
```

### CollectionItemReorderedEvent

Created whenever a collection has assets reordered.

```ts
interface CollectionItemReorderedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionItemReorderedEvent#${collectionId}`
  /**
   * The sorted, full list of associated asset IDs within the collection
   */
  oldAssetsAndItemOrder: {
    id: string
    ordinal: number
    children: { id: string; active: boolean }[]
  }[]
  newAssetsAndItemOrder: {
    id: string
    ordinal: number
    children: { id: string; active: boolean }[]
  }[]
}
```

### CollectionDeletedEvent

Created whenever a collection and it's associated records are deleted.

```ts
interface CollectionDeletedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionDeletedEvent#${collectionId}`
  reason?: string
}
```

### CollectionAssetAssociationConsolidatedEvent

Created whenever there's a mismatch between the assets and the collection asset associations

```ts
interface CollectionAssetAssociationConsolidatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionAssetAssociationConsolidatedEvent#${collectionId}`
  mismatchedData: Record<string, { add: string[]; remove: string[] }>
}
```

### CollectionItemDeactivatedEvent

Created when an item in an asset within collection is deactivated.

```ts
interface CollectionItemDeactivatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionItemDeactivatedEvent`
  item: z.object({ assetId: z.string(), itemId: z.string(), active: z.boolean() })
}
```

### CollectionItemReactivatedEvent

Created when an item in an asset within collection is reactivated.

```ts
interface CollectionItemReactivatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionItemReactivatedEvent`
  item: z.object({ assetId: z.string(), itemId: z.string(), active: z.boolean() })
}
```

### CollectionTestManifestFormTypeUpdatedEvent

Event when the test manifest form type of a collection is updated.

```ts
interface CollectionTestManifestFormTypeUpdatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionTestManifestFormTypeUpdatedEvent`
  oldTestManifestFormType: CollectionModel.shape.testManifestFormType
  newTestManifestFormType: CollectionModel.shape.testManifestFormType
}
```

### CollectionCodeUpdatedEvent

Event when collection has its form codes updated

```ts
interface CollectionCodeUpdatedEvent extends CollectionEvent {
  sk: `${eventTimestamp}#CollectionCodeUpdatedEvent`
  oldCodes: {
    adminCode?: string
  }
  newCodes: {
    adminCode?: string
  }
}
```
