import { Zod<PERSON>awShape, z } from 'zod'

export type Model<T extends Record<string, z.ZodTypeAny>> = {
  [K in keyof T]: z.infer<T[K]>
}

type AutomatedEventProps = 'type' | 'eventTimestamp'

export type DesiredEventProps<T> = {
  // Filter out computed props and optionals
  [K in keyof T as T[K] extends z.ZodOptional<any>
    ? never
    : T[K] extends z.ZodDefault<any>
    ? never
    : K extends AutomatedEventProps
    ? never
    : K]: T[K]
} & {
  // Re-include optionals as optional
  [K in keyof T as K extends AutomatedEventProps
    ? never
    : T[K] extends z.ZodOptional<any>
    ? K
    : T[K] extends z.ZodDefault<any>
    ? K
    : never]?: T[K]
}

export const Event = {
  pk: z.string(),
  sk: z.string(),
  type: z.string(),
  /**
   * This is used for a GSI to get the revision history of a specific piece of data, even if it's a child record
   */
  id: z.string(),
  eventTimestamp: z.number(),
  /**
   * Who or what caused the event to occur
   */
  eventAgent: z.string(),
  sequenceNumber: z.number().default(0),
  courseId: z.string(),
} as const

export interface EventModel<N extends string, T extends ZodBasedEvent> {
  readonly name: N
  readonly schema: z.ZodObject<T>
  readonly params: T

  make(params: EventInput<T>): { [K in keyof EventObject<N, T>]: EventObject<N, T>[K] }
  parse(obj: unknown): { [K in keyof EventObject<N, T>]: EventObject<N, T>[K] }
  safeParse(
    obj: unknown
  ): z.SafeParseReturnType<EventInput<T>, { [K in keyof EventObject<N, T>]: EventObject<N, T>[K] }>

  readonly _type: { [K in keyof EventObject<N, T>]: EventObject<N, T>[K] }
}

export type ZodBasedEvent = ZodRawShape & typeof Event
export type EventShape<T extends ZodBasedEvent> = { [K in keyof T]: z.infer<T[K]> }
export type EventInput<T extends ZodBasedEvent> = {
  [K in keyof DesiredEventProps<T>]: z.infer<T[K]>
}
// Might seem silly to triple union, but I want type to always come first in Intellisense lol
export type EventObject<N extends string, T extends ZodBasedEvent> = { type: N } & EventShape<T> & {
    eventTimestamp: number
  }

export function event<N extends string, T extends ZodBasedEvent>(
  name: N,
  params: T
): EventModel<N, T> {
  const _type = undefined as any as {
    // This is just so it doesn't say the result is EventObject lol
    [K in keyof EventObject<N, T>]: EventObject<N, T>[K]
  }

  const schema = z.object(params).extend({ type: z.literal(name) })
  const parseOmitDerived = (obj: unknown) =>
    schema.omit({ eventTimestamp: true, type: true } as any).parse(obj) as unknown as Omit<
      EventObject<N, T>,
      'eventTimestamp' | 'type'
    >
  const parse = (obj: unknown) => schema.parse(obj)
  const safeParse = (obj: unknown) => schema.safeParse(obj)
  const make: EventModel<N, T>['make'] = (params: EventInput<T>) => {
    const { pk, sk, ...result } = parseOmitDerived(params)
    const eventTimestamp = new Date().getTime()

    let eventSK = `${eventTimestamp}#${name}`
    if (sk) {
      eventSK += `#${sk}`
    }

    return {
      type: name,
      pk,
      // This is a lazy way to do sequencing and guaranteed unique events, plus sort order for free!
      sk: eventSK,
      ...result,
      eventTimestamp: eventTimestamp,
    } as {
      // This is just so it doesn't say the result is EventObject lol
      [K in keyof EventObject<N, T>]: EventObject<N, T>[K]
    }
  }

  return {
    name,
    schema: schema as unknown as z.ZodObject<T>,
    params,
    make,
    parse: parse as unknown as EventModel<N, T>['parse'],
    safeParse: safeParse as unknown as EventModel<N, T>['safeParse'],
    _type,
  }
}
