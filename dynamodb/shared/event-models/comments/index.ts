import { z } from 'zod'
import { CommentModel } from '@shared/dynamo-hummingbird/tables/split-models'
import { Event, event } from '../utils'

export const BaseCommentEvent = {
  ...Event,
} as const

export type CommentCreatedEvent = (typeof CommentCreatedEvent)['_type']
export const CommentCreatedEvent = event('CommentCreatedEvent', {
  ...BaseCommentEvent,
  comment: CommentModel,
})

export type CommentContentChangedEvent = (typeof CommentContentChangedEvent)['_type']
export const CommentContentChangedEvent = event('CommentContentChangedEvent', {
  ...BaseCommentEvent,
  oldContent: z.string(),
  newContent: z.string(),
})

export type CommentRemovedEvent = (typeof CommentRemovedEvent)['_type']
export const CommentRemovedEvent = event('CommentRemovedEvent', {
  ...BaseCommentEvent,
})

export type CommentReactionAddedEvent = (typeof CommentReactionAddedEvent)['_type']
export const CommentReactionAddedEvent = event('CommentReactionAddedEvent', {
  ...BaseCommentEvent,
  reaction: z.string(),
})

export type CommentReactionRemovedEvent = (typeof CommentReactionRemovedEvent)['_type']
export const CommentReactionRemovedEvent = event('CommentReactionRemovedEvent', {
  ...BaseCommentEvent,
  reaction: z.string(),
})

export type CommentResolvedEvent = (typeof CommentResolvedEvent)['_type']
export const CommentResolvedEvent = event('CommentResolvedEvent', {
  ...Event,
  resolved: z.boolean(),
  resolvedByUsername: z.string(),
  resolvedAtMs: z.number(),
})

export type CommentUnresolvedEvent = (typeof CommentUnresolvedEvent)['_type']
export const CommentUnresolvedEvent = event('CommentUnresolvedEvent', {
  ...Event,
  resolved: z.boolean(),
  resolvedByUsername: z.string(),
  resolvedAtMs: z.number(),
})

export type CommentEvent =
  | CommentCreatedEvent
  | CommentContentChangedEvent
  | CommentRemovedEvent
  | CommentReactionAddedEvent
  | CommentReactionRemovedEvent
  | CommentResolvedEvent
  | CommentUnresolvedEvent
