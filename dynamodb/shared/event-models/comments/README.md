# Comment Events

All comment events extend [`Event`](../README.md#event).

### BaseCommentEvent

```ts
interface BaseCommentEvent extends Event {}
```

### CommentCreatedEvent

Created whenever a user adds a comment to something.

```ts
interface CommentCreatedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentCreatedEvent#${commentId}`;
    /**
     * The full comment database record that was created
     */
    comment: Comment;
}
```

### CommentContentChangedEvent

Created whenever a user edits a comment.

```ts
interface CommentContentChangedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentContentChangedEvent#${commentId}`;
    /**
     * The content of the comment before it was edited
     */
    oldContent: string;
    /**
     * The new, edited content of the comment
     */
    newContent: string;
}
```

### CommentRemovedEvent

Created whenever a comment is deleted.

```ts
interface CommentRemovedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentRemovedEvent#${commentId}`;
}
```

### CommentReactionAddedEvent

Created whenever a comment has a reaction added. **Note**: This feature is currently gated behind
the `developer.comments.reactions` permission.

```ts
interface CommentReactionAddedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentReactionAddedEvent#${commentId}`;
    /**
     * Generally unicode emojis, but can be any arbitrary data
     */
    reaction: string;
}
```

### CommentReactionRemovedEvent

Created whenever a comment has a reaction removed. **Note**: This feature is currently gated behind
the `developer.comments.reactions` permission.

```ts
interface CommentReactionRemovedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentReactionRemovedEvent#${commentId}`;
    /**
     * Generally unicode emojis, but can be any arbitrary data
     */
    reaction: string;
}
```

### CommentResolvedEvent

Created whenever a comment is resolved.

```ts
interface CommentResolvedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentResolvedEvent#${commentId}`;
    resolved: boolean;
    resolvedByUsername: string;
    resolvedAtMs: string;
}
```

### CommentUnresolvedEvent

Created whenever a comment is unresolved.

```ts
interface CommentUnresolvedEvent extends BaseCommentEvent {
    sk: `${eventTimestamp}#CommentUnresolvedEvent#${commentId}`;
    resolved: boolean;
    resolvedByUsername: string;
    resolvedAtMs: string;
}
```
