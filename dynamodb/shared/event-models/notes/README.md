# Note Events

All note events extend [`Event`](../README.md#event).

### BaseNoteEvent

```ts
interface BaseNoteEvent extends Event {}
```

### NoteCreatedEvent

Created whenever a user adds a note to something.

```ts
interface NoteCreatedEvent extends BaseNoteEvent {
  sk: `${eventTimestamp}#NoteCreatedEvent#${noteId}`
  /**
   * The full note database record that was created
   */
  note: Note
}
```

### NoteContentChangedEvent

Created whenever a user edits a note.

```ts
interface NoteContentChangedEvent extends BaseNoteEvent {
  sk: `${eventTimestamp}#NoteContentChangedEvent#${noteId}`
  /**
   * The content of the note before it was edited
   */
  oldContent: string
  /**
   * The new, edited content of the note
   */
  newContent: string
}
```

### NoteRemovedEvent

Created whenever a note is deleted.

```ts
interface NoteRemovedEvent extends BaseNoteEvent {
  sk: `${eventTimestamp}#NoteRemovedEvent#${noteId}`
}
```
