import { ZodError, z } from 'zod'
import { Event, event } from './utils'

const TestEvent = {
  ...Event,
  testId: z.string(),
  optional: z.boolean().default(false),
} as const

const testEventBody = {
  courseId: '117',
  eventAgent: 'test-user',
  id: '1234',
  pk: '2345',
  sk: '3456',
  testId: '4567',
} as const

describe('Event Model utilities', () => {
  it('event() - Creates an event model', () => {
    const impl = event('TestEvent', TestEvent)
    expect(impl.name).toEqual('TestEvent')
    expect(impl).toHaveProperty('make')
    expect(impl).toHaveProperty('parse')
    expect(impl).toHaveProperty('schema')
    expect(impl).toHaveProperty('params')
  })

  it('make() - Successfully creates an event with defaults', () => {
    const impl = event('TestEvent', TestEvent)
    const testEvent = impl.make(testEventBody)

    expect(testEvent.eventTimestamp).toBeDefined()

    const timestamp = testEvent.eventTimestamp

    expect(testEvent).toEqual({
      type: 'TestEvent',
      courseId: testEventBody.courseId,
      eventAgent: testEventBody.eventAgent,
      id: testEventBody.id,
      pk: testEventBody.pk,
      sk: `${timestamp}#TestEvent#${testEventBody.sk}`,
      testId: testEventBody.testId,
      eventTimestamp: timestamp,
      sequenceNumber: 0,
      optional: false,
    })
  })

  it('make() - Overrides bad inputs', () => {
    const impl = event('TestEvent', TestEvent)
    const badDate = -1

    const testEvent = impl.make({
      ...testEventBody,
      type: 'BadEvent',
      eventTimestamp: badDate,
    } as any)

    expect(testEvent.eventTimestamp).not.toEqual(badDate)
    expect(testEvent.type).not.toEqual('BadEvent')
  })

  it('parse() - Throws on type mismatch', () => {
    const impl = event('TestEvent', TestEvent)

    const testEventInput = {
      ...testEventBody,
      eventTimestamp: -1,
      type: 'BadEvent',
    } as any

    const expectedError = new ZodError([
      {
        received: 'BadEvent',
        code: 'invalid_literal',
        expected: 'TestEvent',
        path: ['type'],
        message: 'Invalid literal value, expected "TestEvent"',
      },
    ])

    expect(() => impl.parse(testEventInput)).toThrow(expectedError)
  })

  it('safeParse() - Still fails on bad event type', () => {
    const impl = event('TestEvent', TestEvent)
    const badDate = -1

    const testEvent = impl.safeParse({
      ...testEventBody,
      type: 'BadEvent',
      eventTimestamp: badDate,
      optional: true,
    } as any)

    expect(testEvent.success).toBeFalsy()
  })
})
