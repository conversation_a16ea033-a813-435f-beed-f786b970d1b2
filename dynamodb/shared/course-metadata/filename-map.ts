import { CourseIds, SubjectIdentifier } from '@shared/config'
import type { SatchelMetadataType } from '@shared/types/metadata'

interface CourseNameIdMap {
  [course: string]: CourseIds
}

interface CourseIdNameMap {
  [id: string]: string
}

export interface MetadataMap {
  [id: string]: SatchelMetadataType
}

export const courseYamlNameToId: CourseNameIdMap = {
  precalculus: SubjectIdentifier.PRECALCULUS,
}

export const courseIdToYamlName: CourseIdNameMap = {
  [SubjectIdentifier.PRECALCULUS]: 'precalculus',
}
