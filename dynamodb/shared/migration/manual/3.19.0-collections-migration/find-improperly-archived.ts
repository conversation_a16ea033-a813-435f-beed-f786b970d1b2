import { tables } from '@shared/dynamo-hummingbird'
import { readOne } from '@shared/dynamo-hummingbird/tables/io/read'
import type { CollectionModel } from '@shared/dynamo-hummingbird/tables/split-models/collections'
import type * as EventSourced from '@shared/dynamo-hummingbird/types/collections'
import { validateId } from '@shared/schema'
import * as util from '../../util'
import { getCollectionRecords } from './main'

async function findMismatchedArchivalRecordsForCollections() {
  for await (const ids of util.groupingIdsGenerator()) {
    const collectionIds = ids.Collection
    await Promise.all(
      collectionIds.map(async (id) => {
        if (!validateId(id)) return
        const collectionRecords = await getCollectionRecords(id)
        const collectionSnapshot = collectionRecords.find(
          (record) => record.type === 'CollectionSnapshot'
        ) as EventSourced.CollectionSnapshot
        const lastArchiveRestoreState = collectionRecords
          .sort((a, b) => a.sequenceNum! - b.sequenceNum!)
          .reduce<boolean>((accum, record) => {
            if (record.type === 'CollectionArchived') {
              return false
            }
            if (record.type === 'CollectionRestored') {
              return true
            }
            return accum
          }, true)
        const isMatch = collectionSnapshot.isActive === lastArchiveRestoreState
        if (!isMatch) {
          console.log(
            `Collection ${id} Active State ${collectionSnapshot.isActive} - Event State ${lastArchiveRestoreState}`
          )
        }
      })
    )
  }
}

async function findArchivedCollections() {
  for await (const ids of util.groupingIdsGenerator()) {
    const collectionIds = ids.Collection
    console.log('--EVENT SOURCED--')
    await Promise.all(
      collectionIds.map(async (id) => {
        if (!validateId(id)) return
        const snapshot = await readOne<EventSourced.CollectionSnapshot>(tables.hummingbird, id, 0)
        if (!snapshot)
          throw new Error(
            `Snapshot not found for ${id}, impossible because that snapshot was used to get ${id}`
          )
        !snapshot.isActive &&
          console.log(
            [
              `Collection ${id}`,
              `Active State ${snapshot.isActive}`,
              `Subject ID ${snapshot.subjectID}`,
              `Name: ${snapshot.name}`,
            ].join(' - ')
          )
      })
    )
    console.log('--EVENT EMITTING--')
    await Promise.all(
      collectionIds.map(async (id) => {
        if (!validateId(id)) return
        const snapshot = await readOne<CollectionModel>(
          tables.authoring,
          `Collection#${id}`,
          `Collection#${id}`
        )
        if (!snapshot) {
          console.log(`Collection ${id} Failed to Migrate to Event Emitting`)
          return
        }
        snapshot &&
          snapshot.archived &&
          console.log(
            [
              `Collection ${id}`,
              `Active State ${!snapshot.archived}`,
              `Subject ID ${snapshot.courseId}`,
              `Name: ${snapshot.name}`,
            ].join(' - ')
          )
      })
    )
  }
}

async function main() {
  await findMismatchedArchivalRecordsForCollections()
  await findArchivedCollections()
}
if (require.main === module) {
  main()
}
