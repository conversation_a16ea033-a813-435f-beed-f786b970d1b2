import type { Table } from 'dynamodb-onetable'

export async function tableExists(table: any) {
  try {
    const result = await table.describeTable()
    return !!result?.Table
  } catch (ex) {
    return false
  }
}

export async function createTables(tables: Table[]) {
  console.info('CREATING TABLES')
  await Promise.all(
    tables.map(async (table) => {
      if (await tableExists(table)) {
        console.info('  Table already exists')
      } else {
        console.info('  Creating table...')
        await table.createTable({
          StreamSpecification: {
            StreamEnabled: true,
            StreamViewType: 'NEW_IMAGE',
          },
        })
      }
    })
  )
    .then(() => {
      console.info('  ...Done creating tables!')
    })
    .catch((err) => {
      console.error('  Failed to create tables')
      console.error(err.message)
    })
}

export * from './types'
export * as tables from './tables'
export * as models from './tables/models'
export { default as client, client as dynamoClient, documentClient } from './client'
export type { Entity } from 'dynamodb-onetable'
