import type { Entity, Model } from 'dynamodb-onetable'
import type * as models from '../tables/models/assembly-units'

export type AssemblyUnitSnapshot = Entity<typeof models.AssemblyUnitSnapshot>
export type AssemblyUnitCreated = Entity<typeof models.AssemblyUnitCreated>
export type AssemblyUnitRenamed = Entity<typeof models.AssemblyUnitRenamed>
export type AssemblyUnitAssetsAdded = Entity<typeof models.AssemblyUnitAssetsAdded>
export type AssemblyUnitAssetsReordered = Entity<typeof models.AssemblyUnitAssetsReordered>
export type AssemblyUnitAssetItemsReordered = Entity<typeof models.AssemblyUnitAssetItemsReordered>
export type AssemblyUnitAssetsRemoved = Entity<typeof models.AssemblyUnitAssetsRemoved>
export type AssemblyUnitItemActivated = Entity<typeof models.AssemblyUnitItemActivated>
export type AssemblyUnitItemDeactivated = Entity<typeof models.AssemblyUnitItemDeactivated>
export type AssemblyUnitRestored = Entity<typeof models.AssemblyUnitRestored>
export type AssemblyUnitArchived = Entity<typeof models.AssemblyUnitArchived>
export type AssemblyUnitMigrated = Entity<typeof models.AssemblyUnitMigrated>

export type AssemblyUnitEvent =
  | AssemblyUnitSnapshot
  | AssemblyUnitItemActivated
  | AssemblyUnitItemDeactivated
  | AssemblyUnitCreated
  | AssemblyUnitRenamed
  | AssemblyUnitAssetsAdded
  | AssemblyUnitAssetsReordered
  | AssemblyUnitAssetItemsReordered
  | AssemblyUnitAssetsRemoved
  | AssemblyUnitRestored
  | AssemblyUnitArchived

export type AssemblyUnitEventType = keyof typeof models
export type AssemblyUnitSnapshotModel = Model<AssemblyUnitSnapshot>
export type AssemblyUnitCreatedModel = Model<AssemblyUnitCreated>
export type AssemblyUnitRenamedModel = Model<AssemblyUnitRenamed>
export type AssemblyUnitAssetsAddedModel = Model<AssemblyUnitAssetsAdded>
export type AssemblyUnitItemActivatedModel = Model<AssemblyUnitItemActivated>
export type AssemblyUnitItemDeactivatedModel = Model<AssemblyUnitItemDeactivated>
export type AssemblyUnitAssetsReorderedModel = Model<AssemblyUnitAssetsReordered>
export type AssemblyUnitAssetItemsReorderedModel = Model<AssemblyUnitAssetItemsReordered>
export type AssemblyUnitAssetsRemovedModel = Model<AssemblyUnitAssetsRemoved>
export type AssemblyUnitArchivedModel = Model<AssemblyUnitArchived>
export type AssemblyUnitRestoredModel = Model<AssemblyUnitRestored>
