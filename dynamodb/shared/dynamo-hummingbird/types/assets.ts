import type { Entity, Model } from 'dynamodb-onetable'
import type { MetadataRootRecord } from '@shared/schema'
import type * as models from '../tables/models/assets'
import type { association } from '../tables/models/common-fields'

export type ItemSet = Entity<typeof models.ItemSet>
export type Asset = Entity<typeof models.Asset> & { newMetadata: MetadataRootRecord | null }
export type AssetComment = Entity<typeof models.ItemSetComment>
export type AssetAssociation = Entity<typeof association>
/**
@deprecated
*/
export type AssetOldComment = Entity<typeof models.Comment>
export type SubjectSequenceGenerator = Entity<typeof models.SubjectSequenceGenerator>
export type AssetModel = Model<Asset>
export type AssetCommentModel = Model<AssetComment>
export type SubjectSequenceGeneratorModel = Model<SubjectSequenceGenerator>
