import type { Entity, Model } from 'dynamodb-onetable'
import type { FormPreviewHierarchy } from '@lambdas/api/src/events/types'
import type * as models from '../tables/models/forms'
import type { Asset } from './assets'

/** @deprecated */
export type FormSnapshot = Entity<typeof models.FormSnapshot>
export type FormCreated = Entity<typeof models.FormCreated>
export type FormRenamed = Entity<typeof models.FormRenamed>
export type FormSectionRenamed = Entity<typeof models.FormSectionRenamed>
export type FormMigrated = Entity<typeof models.FormMigrated>
export type FormRestored = Entity<typeof models.FormRestored>
export type FormArchived = Entity<typeof models.FormArchived>
export type FormAssemblyUnitsAdded = Entity<typeof models.FormAssemblyUnitsAdded>
export type FormAssemblyUnitsRemoved = Entity<typeof models.FormAssemblyUnitsRemoved>
export type FormDirectionsAdded = Entity<typeof models.FormDirectionsAdded>
export type FormDirectionsRemoved = Entity<typeof models.FormDirectionsRemoved>
export type FormStatusUpdated = Entity<typeof models.FormStatusUpdated>
/** @deprecated FormCodesUpdated is the current type*/
export type FormSubFormCodeUpdated = Entity<typeof models.FormCodesUpdated>
export type FormCodesUpdated = Entity<typeof models.FormCodesUpdated>

export type FormEvent =
  | FormSnapshot
  | FormCreated
  | FormRenamed
  | FormSectionRenamed
  | FormMigrated
  | FormRestored
  | FormArchived
  | FormAssemblyUnitsAdded
  | FormAssemblyUnitsRemoved
  | FormDirectionsAdded
  | FormDirectionsRemoved
  | FormStatusUpdated
  | FormSubFormCodeUpdated
  | FormCodesUpdated

export type FormEventType = keyof typeof models
export type FormSnapshotModel = Model<FormSnapshot>
export type FormCreatedModel = Model<FormCreated>
export type FormRenamedModel = Model<FormRenamed>
export type FormSectionRenamedModel = Model<FormSectionRenamed>
export type FormMigratedModel = Model<FormMigrated>
export type FormArchivedModel = Model<FormArchived>
export type FormRestoredModel = Model<FormRestored>
export type FormAssemblyUnitsAddedModel = Model<FormAssemblyUnitsAdded>
export type FormAssemblyUnitsRemovedModel = Model<FormAssemblyUnitsRemoved>
export type FormDirectionsAddedModel = Model<FormDirectionsAdded>
export type FormDirectionsRemovedModel = Model<FormDirectionsRemoved>
export type FormStatusUpdatedModel = Model<FormStatusUpdated>
/** @deprecated FormCodesUpdatedModel is the current model */
export type FormSubFormCodeUpdatedModel = Model<FormSubFormCodeUpdated>
export type FormCodesUpdatedModel = Model<FormCodesUpdated>

export type ManyAssetsFormResponse = {
  formAssets: Asset[]
  formPreviewHierarchy: FormPreviewHierarchy
  assemblyUnitIds: string[]
}
