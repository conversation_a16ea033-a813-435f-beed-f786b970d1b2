import type { Entity, Model } from 'dynamodb-onetable'
import type * as models from '../tables/models/collections'

export type CollectionSnapshot = Entity<typeof models.CollectionSnapshot>
export type CollectionCreated = Entity<typeof models.CollectionCreated>
export type CollectionRenamed = Entity<typeof models.CollectionRenamed>
export type CollectionAssetsAdded = Entity<typeof models.CollectionAssetsAdded>
export type CollectionAssetsReordered = Entity<typeof models.CollectionAssetsReordered>
export type CollectionAssetsRemoved = Entity<typeof models.CollectionAssetsRemoved>
export type CollectionRestored = Entity<typeof models.CollectionRestored>
export type CollectionArchived = Entity<typeof models.CollectionArchived>
export type CollectionMigrated = Entity<typeof models.CollectionMigrated>

export type CollectionEvent =
  | CollectionSnapshot
  | CollectionCreated
  | CollectionRenamed
  | CollectionAssetsAdded
  | CollectionAssetsReordered
  | CollectionAssetsRemoved
  | CollectionRestored
  | CollectionArchived

export type CollectionEventType = keyof typeof models
export type CollectionSnapshotModel = Model<CollectionSnapshot>
export type CollectionCreatedModel = Model<CollectionCreated>
export type CollectionRenamedModel = Model<CollectionRenamed>
export type CollectionAssetsAddedModel = Model<CollectionAssetsAdded>
export type CollectionAssetsReorderedModel = Model<CollectionAssetsReordered>
export type CollectionAssetsRemovedModel = Model<CollectionAssetsRemoved>
export type CollectionArchivedModel = Model<CollectionArchived>
export type CollectionRestoredModel = Model<CollectionRestored>
