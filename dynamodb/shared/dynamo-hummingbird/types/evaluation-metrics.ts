import type { Entity, Model } from 'dynamodb-onetable'
import type * as models from '../tables/models/evaluation-metrics'

export type EvaluationMetricsMetadata = Entity<typeof models.EvaluationMetricsMetadata>
export type EvaluationMetricsMetadataModel = Model<EvaluationMetricsMetadata>

export type EvaluationMetricsMetadataItem = Entity<typeof models.EvaluationMetricsMetadataItem>
export type EvaluationMetricsMetadataItemModel = Model<EvaluationMetricsMetadataItem>
