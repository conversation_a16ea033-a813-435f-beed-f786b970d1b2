import { Table } from 'dynamodb-onetable'
import {
  COURSE_CONFIG_GSI,
  CREATED_BY_LSI,
  CREATED_LSI,
  ITEM_STATUS_GSI,
  SUBJECT_ID_GSI,
  UPDATED_BY_LSI,
  UPDATED_LSI,
  USERNAME_GSI,
  assets,
} from '@infra/hummingbird/databases/assets'
import { getOneTableIndexes } from '@infra/lib/databases/onetable'
import client from '../client'
import * as models from './models'

export {
  SUBJECT_ID_GSI,
  COURSE_CONFIG_GSI,
  ITEM_STATUS_GSI,
  USERNAME_GSI,
  CREATED_LSI,
  UPDATED_LSI,
  CREATED_BY_LSI,
  UPDATED_BY_LSI,
}

export default new Table({
  client,
  name: assets.name,
  schema: {
    format: 'onetable:2.5.1',
    version: '1.1',
    indexes: getOneTableIndexes(assets),
    models: {
      ...models.assets,
      ...models.users,
      ...models.courses,
    },
    params: {
      isoDates: false,
      timestamps: false,
    },
  },
  partial: true,
})
