import { id, sequenceNum, timestampMs } from '../common-fields'
import EvaluationMetricsMetadata from './evaluation-metrics-metadata'

const EvaluationMetrics = {
  id,
  sequenceNum: {
    ...sequenceNum,
    default: 0,
  },
  subjectID: id,
  programID: id,
  createdAtMs: timestampMs,
  updatedAtMs: timestampMs,
  timestampMs,
  metadata: {
    type: Array,
    default: [],
    items: {
      type: Object,
      schema: EvaluationMetricsMetadata,
    },
  },
  templateVersion: {
    ...sequenceNum,
    default: -1,
  },
}

export default EvaluationMetrics
