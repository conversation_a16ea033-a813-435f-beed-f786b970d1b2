import { AllPermissions } from '@shared/rbac/permissions'
import { Role, Roles } from '@shared/rbac/roles'
import { id, name, timestampMs } from '../common-fields'

/** @deprecated See shared/dynamo-hummingbird/tables/split-models/auth */
export const User = {
  hk: {
    type: String,
    value: '${_type}#${username}',
    required: true,
    hidden: false,
  },
  sk: {
    type: String,
    value: '${_type}#${userId}',
    required: true,
    hidden: false,
  },
  userId: id,
  username: name,
  firstName: {
    ...name,
    required: false,
  },
  lastName: {
    ...name,
    required: false,
  },
  emailAddress: {
    type: String,
    required: true,
  },
  allowedCourses: {
    type: Array,
    required: true,
  },
  isActive: {
    type: Boolean,
    required: true,
    default: true,
  },
  roles: {
    type: Array,
    items: { type: String, enum: Object.keys(Roles) as Role[] },
    required: false,
    default: [],
  },
  permissions: {
    type: Array,
    items: { type: String, enum: AllPermissions },
    required: false,
    default: [],
  },
  created: timestampMs,
  updated: timestampMs,
}
