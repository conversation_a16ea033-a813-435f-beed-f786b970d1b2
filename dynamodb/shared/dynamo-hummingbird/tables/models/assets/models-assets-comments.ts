import { id, name, timestampMs, ttl } from '../common-fields'

export const ItemSetComment = {
  hk: {
    type: String,
    value: 'ItemSet#${itemSetId}',
    required: true,
    hidden: false,
  },
  sk: {
    type: String,
    value: 'Comment#v${version}#Item#${itemId}#${id}',
    required: true,
    hidden: false,
  },
  id: id,
  itemSetId: id,
  itemId: id,
  content: {
    type: String,
    required: true,
  },
  resolved: {
    type: Boolean,
    required: false,
  },
  deleted: {
    type: Boolean,
    required: false,
  },
  created: timestampMs,
  updated: timestampMs,
  createdByUsername: name,
  createdByRoles: {
    type: Array,
    required: false,
    items: {
      type: String,
    },
  },
  modifiedByUsername: {
    ...id,
    required: false,
  },
  version: {
    type: Number,
    required: true,
  },
  revision: {
    type: Number,
    required: true,
  },
  indexedRevision: {
    type: Number,
    required: false,
    default: 0,
  },
  ttl,
  //DEPRECATED
  createdById: { ...id, required: false },
  modifiedById: {
    ...id,
    required: false,
  },
}

/**
@deprecated
*/
export const Comment = {
  ...ItemSetComment,
  created: {
    ...ItemSetComment.created,
    type: String,
  },
  updated: {
    ...ItemSetComment.updated,
    type: String,
  },
}
