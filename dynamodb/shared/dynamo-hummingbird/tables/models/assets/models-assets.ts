import { SetLayoutsIdValues } from '@shared/types/model'
import { DisplayTypeIdValues } from '@shared/types/model/previewer/display-type'
import {
  WorkflowStage,
  WorkflowStageLookup,
  WorkflowStep,
} from '@shared/types/model/workflow/types'
import { WorkflowStage as WorkflowState } from '@shared/types/model/workflow/workflow-stage'
import {
  associations,
  id,
  metadata,
  name,
  stimulusFormat,
  timestampMs,
  ttl,
  value,
  vaultError,
  vaultId,
} from '../common-fields'

export const AssetBasic = {
  createdByUsername: name,
  modifiedByUsername: {
    ...name,
    required: false,
  },
  version: {
    type: Number,
    required: true,
  } /* Track which version of the model the record was written with */,
  modelVersion: {
    type: Number,
    required: true,
    default: 1,
  },
  /* New fields for the purpose of normalizing searchable & filterable terms */
  searchTerms: name,
  id,
  vaultId,
  friendlyID: id, // This is actually the Display Name used in the UI
  isActive: {
    type: Boolean,
    required: true,
    default: true,
  },
  created: timestampMs,
  updated: timestampMs,
  updatedByUsername: name,
  instructions: value,
  programID: id,
  subjectID: id,
  vaultError,
  ttl,
}

export const ItemSet = {
  hk: {
    ...id,
    value: '${_type}#${itemSetId}',
    hidden: false,
  },
  sk: {
    ...id,
    value: '${_type}#v${version}',
    hidden: false,
  },
  itemSetId: id,
  itemId: id,
  externalId: { ...id, required: false },
  created: {
    type: Date,
  },
  updated: {
    type: Date,
  },
  createdByUsername: name,
  modifiedByUsername: {
    ...name,
    required: false,
  },
  version: {
    type: Number,
    required: true,
  },
  revision: {
    type: Number,
    required: true,
  },
  indexedRevision: {
    type: Number,
    required: false,
    default: 0,
  },
  workflowStage: {
    type: String,
    required: true,
    enum: Object.values(WorkflowStage),
    default: WorkflowStageLookup[WorkflowStep.InternalContentReview1],
  },
  workflowStep: {
    type: String,
    required: true,
    enum: Object.values(WorkflowStep),
    default: WorkflowStep.InternalContentReview1,
  },
  versionHistoryRow: {
    // `true` if the row represents part of the version history for this ItemSet id
    type: Boolean,
    required: false,
  },
  accnum: {
    type: String,
    required: false,
  },
  layout: {
    type: String,
    enum: SetLayoutsIdValues,
    required: true,
  },
  stimulusFormat,
  sourceDocumentInstructions: {
    type: String,
    required: false,
  },
  /* DEPRECATED - Will retain until all references to this field are removed */
  workflowState: {
    type: String,
    required: true,
    default: WorkflowState.INTERNAL_AUTHORING.id,
  },
  itemSet: {
    type: String,
    required: true,
  },
  createdById: { ...id, required: false },
  modifiedById: {
    ...id,
    required: false,
  },
}

export const Asset = {
  ...ItemSet,
  ...AssetBasic,
  title: value,
  combinedMetadata: metadata,
  sourceDocumentInstructions: {
    type: String,
    required: false,
  },
  stimulusFormat,
  _type: { type: String, required: true, enum: ['Asset'] },
  questionType: {
    ...id,
    enum: ['FREE_RESPONSE_QUESTION', 'MULTIPLE_CHOICE_QUESTION', 'DIRECTIONS'],
  },
  directionsType: {
    type: String,
    required: false,
    enum: ['v7drU', 'saf4e'],
  },
  status: {
    type: String,
    required: false,
  },
  courseAlignmentRationale: { type: String, default: '' },
  metadata,
  stimuli: {
    type: Array,
    items: {
      type: Object,
      schema: {
        id: value,
        caption: value,
        content: value,
        creditLine: value,
        sourceAttribution: value,
        title: value,
        passageIntroduction: value,
        metadata,
        ordinal: { type: Number },
        courseAlignmentRationale: { type: String, default: '' },
        displayName: { type: String, default: '' },
      },
    },
    default: [],
  },
  items: {
    type: Array,
    items: {
      type: Object,
      schema: {
        id,
        vaultId,
        friendlyID: id,
        title: value,
        isActive: {
          type: Boolean,
          required: true,
          default: true,
        },
        createdAtMs: timestampMs,
        //TODO: When moving to event-sourcing, change createdByID and updatedByID to be college board ids rather than generated ULIDs
        createdByID: id,
        updatedAtMs: timestampMs,
        updatedByID: id,
        stem: value,
        stimulusFormat,
        answerOptions: {
          type: Array,
          items: {
            type: Object,
            schema: {
              key: id,
              rationale: value,
              content: value,
              isCorrect: {
                type: Boolean,
              },
              accnum: {
                type: String,
                required: false,
              },
            },
          },
        },
        metadata,
        parts: {
          type: Array,
          items: {
            type: Object,
            schema: {
              id,
              friendlyId: {
                type: String,
                default: '',
              },
              subStem: value,
              answer: value,
              courseAlignmentRationale: { type: String, default: '' },
            },
          },
        },
        accnum: {
          type: String,
          required: false,
        },
        courseAlignmentRationale: { type: String, default: '' },
      },
    },
    default: [],
  },
  accnum: {
    type: String,
    required: false,
  },
  associations,
  metadataVersion: {
    type: String,
    required: true,
  },
  metadataId: {
    type: String,
    required: false,
  },
  wordCount: {
    type: String,
    required: false,
  },
  newMetadata: {
    type: Object,
    required: true,
    default: {},
  },
  displayType: {
    type: String,
    required: false,
    enum: DisplayTypeIdValues,
  },
  //DEPRECATED
  createdByID: { ...id, required: false },
  updatedByID: { ...id, required: false },
}
