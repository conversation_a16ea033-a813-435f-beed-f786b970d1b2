import { EventNamePrefixConstants } from '../../../event-sourcing'

export const hk = {
  type: String,
  required: true,
}

export const sk = {
  type: String,
  required: true,
  hidden: false,
}

export const id = {
  type: String,
  required: true,
}

export const vaultId = {
  type: String,
  required: false,
}

export const ids = {
  type: Array,
  items: id,
}

export const sequenceNum = {
  type: Number,
  required: true,
}

export const timestampMs = {
  type: Number,
  required: true,
  timestamp: true,
}

export const modelVersion = {
  type: Number,
  required: true,
}

export const assetIDs = {
  type: Array,
  items: { type: String },
  required: true,
  default: [],
}

export const name = {
  type: String,
  required: true,
}

export const value = {
  type: String,
}

export const type = {
  type: String,
  required: true,
  value: '${_type}',
  hidden: false,
}

export const stimulusFormat = {
  type: String,
  required: true,
  default: 'dF4Lt',
  enum: ['dF4Lt', 'pFDSv', 's0rC3'],
}

export const metadata = {
  type: Array,
  items: {
    type: Object,
    schema: {
      key: id,
      value: {
        ...value,
        required: true,
      },
    },
  },
  required: true,
  default: [],
}

export const isActive = {
  type: Boolean,
  required: true,
}

export const documentLinks = {
  type: Array,
  items: { type: String },
  required: false,
  default: [],
}

/**
 * These two fields are typed as strings because Onetable does not allow dynamic object keys in a
 * schema definition, which would be the ideal structure. The ideal structure (defined below the
 * constants) will be used in the application and then converted to a string for db storage.
 */
export const customItemOrder = value
export const deactivatedItemIDs = value
/**
 * customItemOrder: {
 *  assetID: [
 *    itemID,
 *    itemID,
 *    ...
 *  ],
 *  ...
 * }
 *
 * deactivatedItemIDs: {
 *  itemID: true,
 *  itemID: true,
 *  ...
 * }
 */

export const formSections = {
  type: Array,
  items: {
    type: Object,
    schema: {
      label: {
        type: String,
        required: false,
      },
      sectionDirections: ids,
      assemblyUnitIDs: ids,
      uniqueId: {
        type: String,
        required: false,
      },
    },
  },
}

const associationsEnum = {
  type: String,
  required: false,
  enum: [
    EventNamePrefixConstants.AssemblyUnit,
    EventNamePrefixConstants.Collection,
    EventNamePrefixConstants.Form,
  ],
}

export const association = {
  id: id,
  type: associationsEnum,
}

export const associations = {
  type: Array,
  default: [],
  required: false,
  items: {
    type: Object,
    schema: association,
  },
}

// Stringified JSON vault error
//"{
//   "IDX000001": {
//     "errorMsg": "errorMsg",
//     "errorDate": "date"
// }".
export const vaultError = {
  type: String,
  required: false,
}

export const status = {
  type: String,
  default: 'n2OPt',
}

export const subFormCode = {
  type: String,
  required: false,
}

export const adminCode = {
  type: String,
  required: false,
}

export const systemFormCode = {
  type: String,
  required: false,
}

export const createdByRoles = {
  type: Array,
  items: { type: String },
  required: true,
  default: [],
}

export const ttl = {
  ...timestampMs,
  required: false,
}

export const sectionIndex = {
  type: Number,
  required: false,
}
