export const CourseMetadata = {
  hk: {
    type: String,
    required: true,
    value: 'Course#${courseId}',
    hidden: false,
  },
  sk: {
    type: String,
    required: true,
    value: '${_type}#${lastChange}',
    hidden: false,
  },
  _type: {
    type: String,
    value: 'CourseMetadata',
  },
  id: {
    type: String,
    required: true,
  },
  createdMs: {
    type: Number,
    required: true,
  },
  lastChange: {
    type: String,
    required: true,
    describe: 'The date the _metadata_ was last updated, _outside HB_',
  },
  source: {
    type: String,
    required: true,
  },
  courseTitle: {
    type: String,
    describe: 'Course Name as provided by Satchel Framework Document at import time',
  },
  savePath: {
    type: String,
    required: false,
    describe: 'Path to saved file in S3',
  },
  archiveFilename: {
    type: String,
    required: false,
    describe: 'Filename used internally by <PERSON><PERSON><PERSON> to identify the source JSON',
  },
  archiveNote: {
    type: String,
    required: false,
    describe: 'Archive Note associated with stamped Satchel Version',
  },
  description: {
    type: String,
    required: false,
    describe: 'User-supplied description of the metadata version. Updated via Hummingbird UI.',
  },
  label: {
    type: String,
    required: false,
    describe: 'Short label for metadata',
  },
  isActive: {
    type: Boolean,
    required: true,
    default: false,
    describe: 'Active status for whether metadata can be used in new asset creation.',
  },
  allLastChanges: {
    type: Array,
    default: [],
    required: true,
    describe: 'All minor changes to the major version',
    items: {
      type: Object,
      schema: {
        lastChange: String,
        // for version history purposes
        importedDate: String,
        archiveNote: String,
      },
    },
  },
}
