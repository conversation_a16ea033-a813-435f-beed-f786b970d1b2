export const Course = {
  hk: {
    type: String,
    required: true,
    value: '${_type}#${courseId}',
    hidden: false,
  },
  sk: {
    type: String,
    required: true,
    value: '${_type}#${courseId}',
    hidden: false,
  },
  _type: {
    type: String,
    value: 'Course',
  },
  courseConfigHk: {
    type: String,
    required: true,
    value: '${hk}',
    hidden: false,
  },
  courseId: {
    type: String,
    required: true,
  },
  programId: {
    type: String,
    required: true,
  },
  courseName: {
    type: String,
    required: true,
  },
  authoring: {
    type: String,
    required: false,
    describe: 'Stringified JSON of everything from courses-config.ts except metadata.',
  },
  isActive: {
    type: Boolean,
    required: true,
  },
  courseKey: {
    type: String,
  },
  programKey: {
    type: String,
  },
  courseCode: {
    type: String,
  },
  exporting: {
    type: Array,
    required: false,
  },
  primaryMetadataVersion: {
    type: String,
    required: false,
    describe: 'Primary metadata version',
  },
  compression: {
    type: Object,
    required: false,
    schema: {
      graphics: {
        type: Boolean,
        required: false,
        default: false,
      },
    },
  },
  audio: {
    type: Object,
    required: false,
    // sync with buildAudioData
    // shared/types/vault/converters/audio-converter/audio-converter.ts
    schema: {
      /**  in seconds, converted from mm:ss */
      initialCountdown: {
        type: Number,
        required: false,
      },
      repeatCountdown: {
        type: Number,
        required: false,
      },
      timeToAnswer: {
        type: Number,
        required: false,
      },
      recordingLength: {
        type: Number,
        required: false,
      },
      // counts, 1 to 5
      playbackLimit: {
        type: Number,
        required: false,
      },
    },
  },
}
