import { Table } from 'dynamodb-onetable'
import {
  ITEM_ID_GSI,
  PARENT_ID_GSI,
  ULID_GSI,
  friendlyId,
} from '@infra/hummingbird/databases/friendly-id'
import { getOneTableIndexes } from '@infra/lib/databases/onetable'
import client from '../client'
import * as models from './models'

export { ULID_GSI, PARENT_ID_GSI, ITEM_ID_GSI }

export default new Table({
  client,
  name: friendlyId.name,
  schema: {
    format: 'onetable:2.5.1',
    version: '1',
    indexes: getOneTableIndexes(friendlyId),
    models: {
      ...models.friendlyIds,
    },
    params: {
      isoDates: false,
      timestamps: true,
    },
  },
  partial: true,
})
