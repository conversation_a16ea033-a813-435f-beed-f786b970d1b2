import { Table } from 'dynamodb-onetable'
import {
  ASSEMBLY_UNIT_TYPE_GSI,
  ASSOCIATION_CHILD_ID_GSI,
  ATTACHMENT_ID_GSI,
  COMMENT_ID_GSI,
  COURSE_ID_GSI,
  HBID_GSI,
  PARENT_COMMENT_ID_GSI,
  PARTITION_OWNER_TYPE_GSI,
  authoring,
} from '@infra/hummingbird/databases/authoring'
import { getOneTableIndexes } from '@infra/lib/databases/onetable'
import client from '../client'

export {
  COURSE_ID_GSI,
  ASSOCIATION_CHILD_ID_GSI,
  COMMENT_ID_GSI,
  PARENT_COMMENT_ID_GSI,
  ATTACHMENT_ID_GSI,
  ASSEMBLY_UNIT_TYPE_GSI,
  PARTITION_OWNER_TYPE_GSI,
  HBID_GSI,
}

export default new Table({
  client,
  name: authoring.name,
  schema: {
    format: 'onetable:2.5.1',
    version: '0',
    indexes: getOneTableIndexes(authoring),
    models: {},
    params: {
      timestamps: true,
      createdField: 'createdAtMs',
      updated<PERSON>ield: 'updatedAtMs',
    },
  },
  partial: true,
})
