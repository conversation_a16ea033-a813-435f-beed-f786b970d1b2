export type WriteFns = keyof typeof import('./write')

const mocks: Record<WriteFns, jest.Mock> = {
  writeSome: jest.fn(() => Promise.resolve()),
  writeMany: jest.fn(() => Promise.resolve()),
  updateOne: jest.fn(() => Promise.resolve()),
  deleteOne: jest.fn(() => Promise.resolve()),
  writeOne: jest.fn(() => Promise.resolve()),
}

jest.mock('@shared/dynamo-hummingbird/tables/io/write', () => mocks)
jest.mock('@shared/dynamodb/io/write', () => mocks)

type MockWrite = (...args: unknown[]) => unknown

export function mockWrite(method: WriteFns, impl: MockWrite = () => {}) {
  return mocks[method].mockImplementation(impl)
}

export function mockOneWrite(method: WriteFns, impl: MockWrite = () => {}) {
  return mocks[method].mockImplementationOnce(impl)
}

export function getWriteMock(method: WriteFns) {
  return mocks[method]
}

export function clearWriteMocks() {
  for (const method in mocks) {
    mocks[method as WriteFns].mockReset()
  }
}
