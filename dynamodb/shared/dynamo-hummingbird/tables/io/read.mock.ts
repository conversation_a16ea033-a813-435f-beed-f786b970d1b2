export type ReadFns = keyof typeof import('./read')

const mocks: Record<ReadFns, jest.Mock> = {
  getRecordKey: jest.fn(),
  readOne: jest.fn(),
  queryPaginated: jest.fn(),
  queryTable: jest.fn(),
  readMany: jest.fn(),
  readUnprocessedKeys: jest.fn(),
}

jest.mock('@shared/dynamo-hummingbird/tables/io/read', () => mocks)
jest.mock('@shared/dynamodb/io/read', () => mocks)

type MockRead = (...args: unknown[]) => unknown

export function mockRead(method: ReadFns, impl: MockRead = () => {}) {
  return mocks[method].mockImplementation(impl)
}

export function mockOneRead(method: ReadFns, impl: MockRead = () => {}) {
  return mocks[method].mockImplementationOnce(impl)
}

export function getReadMock(method: ReadFns) {
  return mocks[method]
}

export function clearReadMocks() {
  for (const method in mocks) {
    mocks[method as ReadFns].mockReset()
  }
}
