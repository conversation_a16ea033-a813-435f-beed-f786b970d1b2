import { z } from 'zod'
import { CoureSettingsMetadataDefinition } from '@shared/schema/metadata/satchel'
import { ChangeTrackingFields, courseId, makeInstantiator, programId } from './common'

export type EvaluationMetricsMetadata = z.infer<typeof EvaluationMetricsMetadata>
export const EvaluationMetricsMetadata = CoureSettingsMetadataDefinition.pick({
  label: true,
  description: true,
  id: true,
  matchingScopeMappingkey: true,
  displayOrder: true,
}).extend({
  min: z.number().nonnegative().optional(),
  max: z.number().nonnegative().optional(),
  isFasRequired: z.boolean().default(false),
  isDisplayed: z.boolean().default(false),
})

export type EvaluationMetrics = z.infer<typeof EvaluationMetrics>
export const EvaluationMetrics = ChangeTrackingFields.extend({
  pkType: z.literal('EvaluationMetrics').default('EvaluationMetrics'),
  type: z.literal('EvaluationMetrics'),
  courseId,
  programId,
  metadataVersion: z.string(),
  metadataFrameworkId: z.string().optional(),
  label: z.string().optional(),
  s3Location: z.string().optional(),
})

export const makeEvaluationMetrics = makeInstantiator(EvaluationMetrics)
