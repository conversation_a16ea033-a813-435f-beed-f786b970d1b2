import { CompositeKeys, makeInstantiator } from './common'
import { z } from 'zod'

describe('Split Model Utilities', () => {
  describe('makeInstantiator()', () => {
    const modelName = 'TestModel' as const
    const pk = 'ABC123' as const
    const sk = 'EXTRA' as const

    const TestModel = CompositeKeys.extend({
      type: z.literal(modelName),
      additional: z.string().optional(),
    })

    it('Creates a working instantiator', () => {
      const makeTest = makeInstantiator(TestModel)
      expect(makeTest).toBeDefined()

      const test = makeTest({ pk, sk })
      expect(test.type).toEqual(modelName)
      expect(test.pk).toEqual(pk)
      expect(test.sk).toEqual(`${modelName}#${sk}`)
      expect(test.additional).not.toBeDefined()

      const test2 = makeTest({ pk, sk, additional: 'Additional!' })
      expect(test2.additional).toEqual('Additional!')
    })
  })
})
