import { capitalCase } from 'case-anything'
import { z } from 'zod'
import { AssociationModel } from './associations'
import { ChangeTrackingFields, courseId, friendlyId, makeInstantiator, placeholder } from './common'

export enum AssemblyUnitType {
  UNSPECIFIED = '0522Y',
  BASE = '1C57W',
  SEQUENCE = '2F3QU',
  COMPLETE = '3CC6A',
}
export const AssemblyUnitTypeIdToLabel: Record<string, keyof typeof AssemblyUnitType> =
  Object.entries(AssemblyUnitType).reduce(
    (acc, [key, value]) => ({ ...acc, [value]: capitalCase(key) }),
    {}
  )

export const AssemblyUnitTypeNames = {
  [AssemblyUnitType.UNSPECIFIED]: 'Unspecified',
  [AssemblyUnitType.BASE]: 'Base',
  [AssemblyUnitType.SEQUENCE]: 'Sequence',
  [AssemblyUnitType.COMPLETE]: 'Complete',
}

export enum BaseForm {
  F0 = 'F0',
  F1 = 'F1',
  F2 = 'F2',
  F3 = 'F3',
  F4 = 'F4',
  F5 = 'F5',
  F6 = 'F6',
  F7 = 'F7',
  PR = 'PR',
}

export const BaseFormNames = {
  [BaseForm.F0]: 'Standalone Pretesting (F0)',
  [BaseForm.F1]: 'Main Operational East (F1)',
  [BaseForm.F2]: 'Main Operational West (F2)',
  [BaseForm.F3]: 'Alternate (F3)',
  [BaseForm.F4]: 'Exception (F4)',
  [BaseForm.F5]: 'International (F5)',
  [BaseForm.F6]: 'Braille (F6)',
  [BaseForm.F7]: 'Print Accommodation (F7)',
  [BaseForm.PR]: 'Nonoperational (PR)',
}

export enum QuestionType {
  MULTIPLE_CHOICE_QUESTION = 'MULTIPLE_CHOICE_QUESTION',
  FREE_RESPONSE_QUESTION = 'FREE_RESPONSE_QUESTION',
  PROJECT = 'PROJECT',
}

export const QuestionTypeNames = {
  [QuestionType.MULTIPLE_CHOICE_QUESTION]: 'Multiple Choice Question (MC)',
  [QuestionType.FREE_RESPONSE_QUESTION]: 'Free Response Question (FR)',
  [QuestionType.PROJECT]: 'Project (PT)',
}

export const QuestionTypeInitials = {
  [QuestionType.MULTIPLE_CHOICE_QUESTION]: 'MC',
  [QuestionType.FREE_RESPONSE_QUESTION]: 'FR',
  [QuestionType.PROJECT]: 'PT',
}

export const AssemblyUnitTypeEnum = z.nativeEnum(AssemblyUnitType)
export const BaseFormEnum = z.nativeEnum(BaseForm)
export const QuestionTypeEnum = z.nativeEnum(QuestionType)
export type AssemblyUnitModel = z.infer<typeof AssemblyUnitModel>
export const AssemblyUnitModel = ChangeTrackingFields.extend({
  id: friendlyId,
  type: z.literal('AssemblyUnit'),
  courseId: courseId,
  isActive: z.boolean(),
  assemblyUnitType: AssemblyUnitTypeEnum,
  name: z.string().optional().default(''),
  label: z.string().optional().nullable(),
  adminYear: z.string().optional().nullable(),
  formCode: z.string().optional().default(''),
  questionType: QuestionTypeEnum.optional().nullable(),
  questionTypeNumber: z.number().optional().default(0),
  baseForm: BaseFormEnum.optional().nullable(),
  sequenceNum: z.number().optional().default(-1),
  metadataVersion: z.string().optional().nullable(),
  assetAndPlaceholderSequence: z.array(friendlyId.or(placeholder)).optional(),
})

export type AssemblyUnitAssociationModel = z.infer<typeof AssemblyUnitAssociationModel>
export const AssemblyUnitAssociationModel = AssociationModel.extend({
  associationChildType: z.literal('Asset'),
  on: z.object({
    ordinal: z.number(),
    items: z.array(
      z.object({
        active: z.boolean(),
        id: friendlyId,
        useCode: z.object({ id: z.string().uuid(), label: z.string() }).optional(),
      })
    ),
  }),
})

export const makeAssemblyUnit = makeInstantiator(AssemblyUnitModel)
export const makeAssemblyUnitAssociation = makeInstantiator(AssemblyUnitAssociationModel)
