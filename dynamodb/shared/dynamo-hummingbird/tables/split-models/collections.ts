import { z } from 'zod'
import { CollectionTestManifestFormType } from '@shared/types/vault/const'
import { AssociationModel } from './associations'
import { ChangeTrackingFields, courseId, friendlyId, makeInstantiator } from './common'

export type CollectionModel = z.infer<typeof CollectionModel>
export const CollectionModel = ChangeTrackingFields.extend({
  //   id: z.string().ulid(),
  id: friendlyId,
  hbid: friendlyId, // GSI
  pkType: z.literal('Collection').default('Collection'),
  type: z.literal('Collection'),
  courseId: courseId,
  archived: z.boolean().default(false),
  name: z.string(),
  testManifestFormType: z.nativeEnum(CollectionTestManifestFormType).optional(),
  adminCode: z.string().optional(),

  // As of 9/26/2024, these are populated on create, event side effect, and refresh-collection-cached-props
  assetCount: z.number().default(0),
  itemCount: z.number().default(0),
})

export type CollectionAssociationModel = z.infer<typeof CollectionAssociationModel>
export const CollectionAssociationModel = AssociationModel.extend({
  associationChildType: z.literal('Asset'),
  on: z.object({
    ordinal: z.number(),
    items: z.array(z.object({ id: friendlyId, active: z.boolean().default(true) })).optional(),
  }),
})

export const makeCollection = makeInstantiator(CollectionModel)
export const makeCollectionAssociation = makeInstantiator(CollectionAssociationModel)
