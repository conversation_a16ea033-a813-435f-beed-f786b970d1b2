import { z } from 'zod'
import { DirectionsTypeEnum } from '@shared/schema'
import { FormStep } from '@shared/types/model/workflow/forms'
import { WorkflowStepID } from '@shared/types/model/workflow/types'
import { TestManifestFormType } from '@shared/types/vault/const'
import { AssociationModel } from './associations'
import {
  ChangeTrackingFields,
  courseId,
  friendlyId,
  makeInstantiator,
  name,
  sectionId,
} from './common'

export type FormCodeType = z.infer<typeof FormCodeType>
export const FormCodeType = z.enum(['AdminCode', 'SubFormCode', 'SystemFormCode'])

export type FormStatusType = z.infer<typeof FormStatusType>
export const FormStatusType = z.enum([
  WorkflowStepID.IN_ASSEMBLY,
  WorkflowStepID.SENT_TO_VAULT,
  WorkflowStepID.SENT_TO_VAULT_FAILED,
  WorkflowStepID.SENT_TO_VAULT_PENDING,
])

export type FormSectionModel = z.infer<typeof FormSectionModel>
export const FormSectionModel = ChangeTrackingFields.extend({
  id: sectionId, // ulid
  hbid: sectionId, // GSI
  type: z.literal('FormSection'),
  courseId: courseId,
  name,
  ordinal: z.number(),
})

export const makeFormSection = makeInstantiator(FormSectionModel)

export type FormModel = z.infer<typeof FormModel>
export const FormModel = ChangeTrackingFields.extend({
  id: friendlyId,
  hbid: friendlyId, // GSI
  pkType: z.literal('Form').default('Form'),
  type: z.literal('Form'),
  courseId: courseId,
  archived: z.boolean().default(false),
  name,
  subFormCode: z.string().optional().nullable(),
  adminCode: z.string().optional().nullable(),
  systemFormCode: z.string().optional().nullable(),
  assemblyUnitCount: z.number().default(0),
  status: FormStatusType.default(WorkflowStepID.IN_ASSEMBLY),
  sentToVaultAt: z.string().optional().nullable(),
  workflowSteps: z.record(z.nativeEnum(FormStep), z.boolean()).nullish().default(null),
  metadataVersion: z.string().optional().nullable(),
  testManifestFormType: z.nativeEnum(TestManifestFormType).optional(),
})

export type FormAssociationModel = z.infer<typeof FormAssociationModel>
export const FormAssociationModel = AssociationModel.extend({
  associationChildType: z.literal('Asset').or(z.literal('AssemblyUnit')),
  on: z.object({
    ordinal: z.number().optional(),
    section: sectionId.optional(),
    directionType: DirectionsTypeEnum.optional(),
  }),
})

export const makeForm = makeInstantiator(FormModel)
export const makeFormAssociation = makeInstantiator(FormAssociationModel)
