import { z } from 'zod'
import { ChangeTrackingFields, courseId, makeInstantiator } from './common'

export const associationChildTypeZodType = z.string()

export type AssociationModelType = z.infer<typeof AssociationModel>
export const AssociationModel = ChangeTrackingFields.extend({
  type: z.literal('Association'),
  associationChildType: associationChildTypeZodType,
  associationChildId: z.string(),
  courseId: courseId.optional(), // KEEP? no -ajenks
  on: z.object({}).passthrough(),
})

export const makeAssociation = makeInstantiator(AssociationModel)
