import { z } from 'zod'
import { AcceptedFileType, AttachmentStatus } from '@shared/utils/constants/attachments'
import { ChangeTrackingFields, courseId, id, makeInstantiator } from './common'

export enum ScanStatus {
  CLEAN = 'FJfTI',
  INFECTED = 'oTkSm',
  SCAN_FAILED = 'URkMe',
}

/** 0 / undefined is assumed Internal */
export enum AttachmentVisibility {
  ADMIN = -1,
  INTERNAL = 0,
  EXTERNAL_L1 = 1,
  EXTERNAL_L2 = 2,
}

export type AttachmentModelType = z.infer<typeof AttachmentModel>
export const AttachmentModel = ChangeTrackingFields.extend({
  attachmentId: id,
  attachmentTargetType: z.string(),
  attachmentTargetId: z.string(),
  courseId: courseId,
  uploadedByRoles: z.array(z.string()),
  name: z.string(),
  uploadedAtMs: z.number().default(() => new Date().getTime()),
  userId: z.string(),
  size: z.number(),
  type: z.literal('Attachment'),
  /** Location of the attachment in s3. */
  location: z.string().nullable(),
  /** See accepted file/MIME types here {@link AcceptedFileType}. */
  mimeType: z.nativeEnum(AcceptedFileType),
  /** See attachment statuses here {@link AttachmentStatus}. */
  status: z.nativeEnum(AttachmentStatus),
  /** See scan statuses here {@link ScanStatus}. */
  scanStatus: z.nativeEnum(ScanStatus).nullable().default(null),
  visibilityRestriction: z
    .nativeEnum(AttachmentVisibility)
    .optional()
    .default(AttachmentVisibility.INTERNAL),
  on: z.object({}).passthrough(),
})

export const makeAttachment = makeInstantiator(AttachmentModel)
