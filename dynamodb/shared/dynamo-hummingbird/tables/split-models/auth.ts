import { z } from 'zod'
import { AllPermissions, type FlattenedPermissionName } from '@shared/rbac/permissions'
import { AzureRoles, Role } from '@shared/rbac/roles'

// This is ugly but it does the thing
export const permissionEnum = z.enum(AllPermissions as [FlattenedPermissionName])
export const azureRole = z.enum(AzureRoles)

export type UserModel = z.infer<typeof UserModel>
export const UserModel = z.object({
  hk: z.string(), // User#username
  sk: z.string(), // User#userId
  _type: z.literal('User').default('User'), // Remnant from OneTable
  username: z
    .string()
    .min(3)
    .max(64)
    .regex(/^[a-z0-9-_]+$/i),
  userId: z.string().ulid(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  emailAddress: z.string(),
  allowedCourses: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  roles: z.array(z.nativeEnum(Role)).default([]),
  azureRoles: z.array(azureRole).default([]),
  permissions: z.array(permissionEnum).default([]),
  created: z.number().default(() => new Date().getTime()),
  updated: z.number().optional(),
  isDev: z.boolean().optional(),
  lastSeen: z.number().optional(),
})

/** Normally what is used in a query, 70% of the time */
export type User = z.infer<typeof User>
export const User = UserModel.omit({
  hk: true,
  sk: true,
  _type: true,
})

export type AzureUser = z.infer<typeof AzureUser>
export const AzureUser = User.extend({
  currentAzureRoles: z.array(azureRole).default([]),
})
