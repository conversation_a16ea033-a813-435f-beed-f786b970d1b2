import { z } from 'zod'
import { ChangeTrackingFields, courseId, id, makeInstantiator } from './common'

export type Reaction = z.infer<typeof Reaction>
export const Reaction = z.object({
  reactedByUsername: z.string(),
  reaction: z.string(), // Unicode emojis
})

export type CommentModel = z.infer<typeof CommentModel>
export const CommentModel = ChangeTrackingFields.extend({
  type: z.literal('Comment'),
  commentId: id,
  courseId: courseId,
  createdByRoles: z.array(z.string()),
  content: z.string(),
  attachments: z.array(z.never()).default([]),
  // The next two are essentially the same as the pk but need to be explicit
  commentTargetType: z.string(),
  commentTargetId: z.string(), // GSI - Should it be the full HK or just the id? Not sure.
  // Threading comes basically for free since you should basically always be fetching all comments in a pk or by parent
  parentCommentId: id, // GSI - Enables almost-infinitely-nested comments and deleting comments. Top-level comments are set to parentCommentId = id
  // Comment resolution
  resolved: z.boolean().optional(),
  resolvedAtMs: z.number().optional(),
  resolvedByUsername: z.string().optional(),
  // Comment association data - varies by comment type, intentionally vague model
  on: z.object({}).passthrough(),
  // This is a secret developer feature - ajenks
  reactions: z.array(Reaction).default([]),
})

const CommentModelRefine = CommentModel.superRefine((val, ctx) => {
  if (!val.createdByRoles || val.createdByRoles.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `No createdByRoles provided for user ${val?.createdByUsername ?? 'UNKNOWN USER'}`,
      path: ['createdByRoles'],
      fatal: true,
    })
    return z.NEVER
  }
  return z.NEVER
})

export const makeComment = makeInstantiator(CommentModelRefine)
