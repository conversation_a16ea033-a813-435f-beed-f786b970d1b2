# Refactor Plan - `@shared/dynamodb/models`

This folder was originally created to differentiate between the old OneTable models that were huge and the new, smaller, isolated models written in Zod schemas. It has since grown to encompass almost all new modeling, so it needs to be renamed.

## Proposal

- This folder should be moved to `@shared/dynamodb/models`
- `@shared/dynamo-hummingbird/tables/models` should be deprecated and moved to Zod schemas elsewhere
- `@shared/event-models` should be moved to `@shared/dynamodb/event-models`
- `@shared/dynamo-hummingbird/tables/io` should be moved to `@shared/dynamodb/io`
- The OneTable definitions in `@shared/dynamo-hummingbird` should be deprecated/deleted
- Matching types for `@shared/dynamo-hummingbird/types` should be inferred/extended from Zod schemas
- The `@shared/event-sourcing` folder should be deprecated/deleted
- Database models in `@shared/schema` should be moved/merged in `@shared/dynamodb`
- Database models in `@shared/types` should be moved/merged in `@shared/dynamodb`
