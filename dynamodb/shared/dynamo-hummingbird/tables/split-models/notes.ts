import { z } from 'zod'
import { ChangeTrackingFields, courseId, id, makeInstantiator } from './common'

// Cloned from Comments model
export type NoteModel = z.infer<typeof NoteModel>
export const NoteModel = ChangeTrackingFields.extend({
  type: z.literal('Note'),
  noteId: id,
  courseId: courseId,
  createdByRoles: z.array(z.string()),
  content: z.string(),
  attachments: z.array(z.never()).default([]),
  // The next two are essentially the same as the pk but need to be explicit
  noteTargetType: z.string(),
  noteTargetId: z.string(), // GSI - Should it be the full HK or just the id? Not sure.
  // Note association data - varies by note type, intentionally vague model
  on: z.object({}).passthrough(),
})

const NoteModelRefine = NoteModel.superRefine((val, ctx) => {
  if (!val.createdByRoles || val.createdByRoles.length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `No createdByRoles provided for user ${val?.createdByUsername ?? 'UNKNOWN USER'}`,
      path: ['createdByRoles'],
      fatal: true,
    })
    return z.NEVER
  }
  return z.NEVER
})

export const makeNote = makeInstantiator(NoteModelRefine)
