import { Zod<PERSON>rror, z } from 'zod'
import { escapeHtml } from '@shared/conversions/escape-html'

export const pageSizes = [10, 25, 50, 100] as const
export type PageSizes = (typeof pageSizes)[number]

export const id = z.string().ulid()
export const friendlyId = z.string().regex(/^[A-Z]{3}\d{6}$/, 'Invalid Friendly ID')
/** template: `placeholder:{number of items}:{ulid}` -> `placeholder:3:01ARZ3NDEKBARW0NTA110WME1N` */
export const placeholder = z.string().refine((str) => {
  const parts = str.split(':')
  return (
    parts.length === 3 &&
    parts[0] === 'placeholder' &&
    z.number().int().positive().safeParse(Number(parts[1])).success &&
    z.string().ulid().safeParse(parts[2]).success
  )
}, 'Invalid Placeholder. Must be in the format placeholder:{number of items}:{ulid}.')
export const programId = z.string()
export const courseId = z.string()
export const name = z
  .string()
  .transform((str) => escapeHtml(str))
  .pipe(z.string().min(4).max(50))
export const sectionId = z.string().ulid()
export const pageSize: z.ZodType<(typeof pageSizes)[number], z.ZodNumberDef> = z.coerce
  .number()
  .refine((num) => pageSizes.includes(num as any), `Must be ${pageSizes.join(', ')}`) as any

export function createPageSizes<N extends number[]>(...numbers: N) {
  return z.coerce
    .number()
    .refine(
      (num) => numbers.includes(num),
      `Must be ${numbers.join(', ')}`
    ) as unknown as z.ZodType<N[number], z.ZodNumberDef>
}

export const CompositeKeys = z.object({
  pk: z.string(),
  sk: z.string(),
})

export const Pagination = z.object({
  limit: pageSize.default(50),
  next: z.object({}).passthrough().optional(),
  sort: z.union([z.literal('ascending'), z.literal('descending')]).default('ascending'),
})

export const ChangeTrackingFields = CompositeKeys.extend({
  createdAtMs: z.number().default(() => new Date().getTime()),
  updatedAtMs: z.number().optional(),
  createdByUsername: z.string(),
  updatedByUsername: z.string().optional(),
})

// This is just a convenience so you can tell if an error came from parsing an API request or parsing a database request
export class ZodDynamoError extends ZodError {}

export function makeInstantiator<T extends z.AnyZodObject | z.ZodEffects<z.AnyZodObject>>(
  model: T
) {
  type ModelInput = Omit<z.input<T>, 'type'>
  const modelType: string =
    (model as z.AnyZodObject)?.shape?.type?.value ??
    (model as z.ZodEffects<z.AnyZodObject>)._def.schema.shape.type.value

  return (input: ModelInput) => {
    try {
      return model.parse({
        ...input,
        sk: input.sk?.startsWith(`${modelType}#`) ? input.sk : `${modelType}#${input.sk}`,
        type: modelType,
      }) as z.infer<T>
    } catch (ex) {
      if (ex instanceof ZodError) {
        throw new ZodDynamoError(ex.issues)
      }

      throw ex
    }
  }
}
