import { z } from 'zod'
import * as Models from './index'

const models = Object.entries(Models).filter(([name]) => name.endsWith('Model'))

describe('Split Models', () => {
  describe('Has valid types', () => {
    models.forEach(([name, model]) => {
      it(`${name} - Valid type`, () => {
        expect(model instanceof z.ZodObject).toEqual(true)
      })

      it(`${name} - Valid type literal`, () => {
        const casted = model as z.ZodObject<any, any, any>
        expect(casted.shape.type).toBeDefined()
        expect(casted.shape.type instanceof z.ZodLiteral).toEqual(true)
      })

      it(`${name} - Consistent naming`, () => {
        const casted = model as z.ZodObject<any, any, any>
        const type = casted.shape.type as z.ZodLiteral<any>
        expect(name).toContain(`${type.value}Model`)
      })
    })
  })
})
