import { Table } from 'dynamodb-onetable'
import { SUBJECT_ID_GSI, hummingbird } from '@infra/hummingbird/databases/hummingbird'
import { getOneTableIndexes } from '@infra/lib/databases/onetable'
import client from '../client'
import * as models from './models'

export { SUBJECT_ID_GSI }

export default new Table({
  client,
  name: hummingbird.name,
  schema: {
    format: 'onetable:2.5.1',
    version: '0',
    indexes: getOneTableIndexes(hummingbird),
    models: {
      ...models.collections,
      ...models.forms,
      ...models.assemblyUnits,
      ...models.evaluationMetrics,
    },
    params: {
      timestamps: true,
      createdField: 'createdAtMs',
      updatedField: 'updatedAtMs',
    },
  },
  partial: true,
})
