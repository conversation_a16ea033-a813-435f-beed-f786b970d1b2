import { Table } from 'dynamodb-onetable'
import { COURSE_ID_GSI, events } from '@infra/hummingbird/databases/events'
import { getOneTableIndexes } from '@infra/lib/databases/onetable'
import client from '../client'

export { COURSE_ID_GSI }

export default new Table({
  client,
  name: events.name,
  schema: {
    format: 'onetable:2.5.1',
    version: '0',
    indexes: getOneTableIndexes(events),
    models: {},
    params: {
      timestamps: true,
      createdField: 'createdAtMs',
      updatedField: 'updatedAtMs',
    },
  },
  partial: true,
})
