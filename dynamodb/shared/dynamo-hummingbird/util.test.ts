import { mockRead } from './tables/io/read.mock'
import { generateId, getAssetIdsByManifest, splitBatchWrites } from './utils'

describe('splitBatchWrites', () => {
  it('should not return undefined', () => {
    const generatedId = generateId()
    expect(generatedId).toBeDefined()
  })

  it('should not split if the batch size is smaller than the default', () => {
    const batch = {
      RequestItems: {
        TableA: [{ id: 1 }, { id: 2 }],
      },
    }

    const result = splitBatchWrites(batch)
    expect(result.length).toBe(1)
    expect(result[0].RequestItems.TableA).toEqual([{ id: 1 }, { id: 2 }])
  })

  it('should split if the batch size is larger than the default', () => {
    const items = Array.from({ length: 30 }, (_, i) => ({ id: i + 1 }))
    const batch = {
      RequestItems: {
        TableA: items,
      },
    }

    const result = splitBatchWrites(batch)
    expect(result.length).toBe(2)
    expect(result[0].RequestItems.TableA).toEqual(items.slice(0, 25))
    expect(result[1].RequestItems.TableA).toEqual(items.slice(25))
  })

  it('should split using a custom batch size', () => {
    const items = Array.from({ length: 50 }, (_, i) => ({ id: i + 1 }))
    const batch = {
      RequestItems: {
        TableA: items,
      },
    }

    const result = splitBatchWrites(batch, 20)
    expect(result.length).toBe(3)
    expect(result[0].RequestItems.TableA).toEqual(items.slice(0, 20))
    expect(result[1].RequestItems.TableA).toEqual(items.slice(20, 40))
    expect(result[2].RequestItems.TableA).toEqual(items.slice(40))
  })
})

describe('getAssetIdsByManifest', () => {
  const mockManifest = [
    {
      hk: 'Manifest#Asset',
      sk: 'Manifest#456',
      ['ABC123456']: { isActive: true, courseId: '456' },
      ['BCD123456']: { isActive: false, courseId: '456' },
    },
    {
      hk: 'Manifest#Asset',
      sk: 'Manifest#123',
      ['CDE123456']: { isActive: true, courseId: '123' },
      ['DEF123456']: { isActive: false, courseId: '123' },
    },
  ]
  test.each([
    {
      args: { includeInactive: false, courseIds: ['456'] },
      expected: { ['456']: ['ABC123456'] },
    },
    {
      args: { includeInactive: true, courseIds: ['456'] },
      expected: { '456': ['ABC123456', 'BCD123456'] },
    },
    {
      args: { includeInactive: false, courseIds: ['456', '123'] },
      expected: { ['456']: ['ABC123456'], ['123']: ['CDE123456'] },
    },
    {
      args: { includeInactive: true, courseIds: ['456', '123'] },
      expected: { ['456']: ['ABC123456', 'BCD123456'], ['123']: ['CDE123456', 'DEF123456'] },
    },
    {
      args: { includeInactive: true, courseIds: undefined },
      expected: { ['456']: ['ABC123456', 'BCD123456'], ['123']: ['CDE123456', 'DEF123456'] },
    },
  ])('should return the expected asset ids', async ({ args, expected }) => {
    mockRead('queryPaginated', () => mockManifest)
    const res = await getAssetIdsByManifest(args)
    ;(Object.keys(expected) as Array<keyof typeof expected>).forEach((courseId) => {
      if (expected[courseId]) {
        expect(res[courseId]).toEqual(expect.arrayContaining(expected[courseId]))
      } else {
        expect(res[courseId]).toBeUndefined()
      }
    })
  })
})
