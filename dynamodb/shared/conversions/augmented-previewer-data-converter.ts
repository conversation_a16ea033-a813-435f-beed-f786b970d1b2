/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable no-useless-escape */

import { load } from 'cheerio'
import {
  Item,
  ItemSet,
  ItemType,
  Metadata,
  MultipleChoiceAnswerOption,
  MultipleChoiceQuestion,
  RequiredMetadata,
} from '@shared/schema'
import type { ExtendedApiaryItem } from '@shared/types/model/apiary-extended'
import { StimulusFormatIdReadable } from '@shared/types/model/asset'
import { InfoTab, MetadataGroup } from '@shared/types/model/previewer/info-panel-types'
import { constructFrqPartName } from '@shared/types/vault/asset-send-to-vault'
import { constructStimulusDisplayName } from '@shared/utils/formatting'

type AllowedAltTextSource = string | undefined | null
export type AltTextSource = {
  rootId: string
  entityId?: string
  leafId?: string
  location: string
  specific?: string
  text: AllowedAltTextSource | AllowedAltTextSource[]
}

type NodeTypeMetadataPair = [string, Metadata | Metadata[]]
export type AltText = MathAltText | ImageAltText

export function isMathAltText(altText: BaseAltText): altText is MathAltText {
  return 'formula' in altText
}

export function isImageAltText(altText: BaseAltText): altText is ImageAltText {
  return 'fileName' in altText
}

export interface BaseAltText {
  source: AltTextSource
}

export interface MathAltText extends BaseAltText {
  formula: string
  altText: string
}

export interface ImageAltText extends BaseAltText {
  fileName: string
  src: string
  longAltText?: string
  shortAltText?: string
  regionLabel?: string
  language?: string
}

export type InfoPanelDropdownOption = {
  id: string
  type: 'stimulus' | 'item' | 'part' | 'asset'
  disabledTabs: InfoTab[]
  label: string
  ordinal?: string
  metadataGroup?: MetadataGroup
  parentAssetId?: string
}

export interface AugmentedPreviewerData {
  rationales: string[]
  altText: {
    maths: MathAltText[]
    images: ImageAltText[]
  }
  stimuliMetadata: { [key: string]: string }[]
  generalMetadata: NodeTypeMetadataPair[]
  itemMetadata: NodeTypeMetadataPair[]
  id: string
  subject: string
  answerKey?: string[]
  programAndSubject: string
  itemAlignment: string[]
  stimuliAlignment: string[]
  infoPanelDropdownOptions: InfoPanelDropdownOption[]
}

const nodeTypesWithNumberLabels = [
  'Unit',
  'Topic',
  'LearningObjective',
  'EssentialKnowledge',
  'Skill',
]

const getLabelNumberAndValue = (item: Metadata) => {
  // if the metadata object has a number in it's label (e.g. Unit, Topic, Essential Knowledge, LO)
  // then add that number before it's value

  const { label, nodeType, value } = item

  const itemLabelNumber =
    label && nodeType && nodeTypesWithNumberLabels.includes(nodeType)
      ? nodeType === nodeTypesWithNumberLabels[0] || nodeType === nodeTypesWithNumberLabels[1]
        ? label.replace(/[^0-9\.]+/g, '')
        : label
      : ''

  return value?.includes(itemLabelNumber)
    ? value
    : `${itemLabelNumber && `${itemLabelNumber}:`} ${value}`.trim()
}

const itemFieldsToIgnore = ['SkillCategory']

// const reduceMetadataForCollectionsPreview = (
//   metadata: RequiredSetMetadata | RequiredItemMetadata | Record<string, never>
// ) => {
//   return Object.values(metadata)
//     .filter((item) => typeof item !== 'string')
//     .reduce((acc, item) => {
//       if (Array.isArray(item)) {
//         if (item.length > 0) {
//           const updatedItem = item.map((itemObject) => {
//             return {
//               ...itemObject,
//               value: getLabelNumberAndValue(itemObject),
//             }
//           })
//           return {
//             ...acc,
//             [item[0].nodeType]: [...(acc[item[0].nodeType] || []), ...updatedItem],
//           }
//         } else {
//           return acc
//         }
//       } else {
//         if (itemFieldsToIgnore.includes(item.nodeType)) return acc
//         const updateItem = { ...item, value: getLabelNumberAndValue(item) }
//         return {
//           ...acc,
//           [item.nodeType]: [...(acc[item.nodeType] || []), updateItem],
//         }
//       }
//     }, {})
// }

const reduceAdditionalMetadata = (
  additionalMetadata: Array<Metadata>
): Record<string, Metadata[]> => {
  return additionalMetadata
    ?.filter((item) => typeof item !== 'string')
    .reduce<Record<string, Metadata[]>>((acc, item) => {
      const updatedItem = { ...item, label: getLabelNumberAndValue(item) }
      if (!item?.nodeType) {
        console.error(`Metadata with value ${item.value} has no nodeType, skipped for previewer`)
      } else {
        acc = { ...acc, [item.nodeType]: [...(acc?.[item.nodeType] ?? []), updatedItem] }
      }
      return acc
    }, {})
}

const reduceRequiredMetadataForPreview = (
  metadata: RequiredMetadata
): Record<string, Metadata[]> => {
  return Object.values(metadata).reduce<Record<string, Metadata[]>>((acc, item) => {
    const nodeType = item.nodeType
    if (!nodeType || itemFieldsToIgnore.includes(nodeType)) return acc
    const updatedItem = {
      id: item.id,
      nodeType: item.nodeType,
      label: item?.label ?? item.value,
      value: getLabelNumberAndValue(item),
    }
    acc = { ...acc, [nodeType]: [updatedItem] }
    return acc
  }, {})
}

const getMetadata = (asset: ItemSet | Item) => {
  return {
    ...reduceRequiredMetadataForPreview(asset.metadata || {}),
    ...reduceAdditionalMetadata(asset.additionalMetadata || []),
  }
}

const stimuliFieldsToCombine = ['Children', 'PermittedValue']
const stimuliFieldsToIgnore = ['MetadataField']

const getStimuliMetadata = (asset: ItemSet) => {
  const stimuliMetadata = asset.stimuli.map((stimulus) => {
    return (
      (stimulus?.metadata || [])
        .filter((item) => typeof item !== 'string')
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .reduce((acc: any, item) => {
          const updatedItem = { ...item, value: getLabelNumberAndValue(item) }
          if (stimuliFieldsToCombine.includes(item.nodeType)) item.nodeType = 'Stimulus Type'
          if (stimuliFieldsToIgnore.includes(item.nodeType)) return acc
          return {
            ...acc,
            [item.nodeType]: [...(acc[item.nodeType] || []), updatedItem],
          }
        }, {})
    )
  })

  return stimuliMetadata.every((stimulus) => Object.values(stimulus).length === 0)
    ? []
    : stimuliMetadata
}

const answerOptionLabels = ['A', 'B', 'C', 'D', 'E'] as const

const reduceRationales = (answerOptions: MultipleChoiceAnswerOption[]): string[] => {
  return answerOptions
    ?.map((option, index) => {
      const { label, informalRationale } = option
      if (informalRationale && informalRationale !== '') {
        const actualLabel = label && label !== '' ? label : answerOptionLabels[index]!
        return `${actualLabel} ${informalRationale}`
      }
      return ''
    })
    .filter((str) => str !== '')
}

const getRationale = (item: Item) => {
  return item.itemType === ItemType.MultipleChoiceQuestion
    ? reduceRationales((item as MultipleChoiceQuestion).answerOptions)
    : []
}

const getMathFormulae = (htmlContent: string): MathAltText[] => {
  const $ = load(htmlContent)
  const math = $('math').toArray()
  return math.reduce<MathAltText[]>((accum, formula) => {
    const formulaHTML = $.html(formula)
    const altText = formula.attribs['alttext']
    // source gets added from calling func so we have to cast
    if (formulaHTML && altText) accum.push({ formula: formulaHTML, altText } as MathAltText)
    return accum
  }, [])
}

const getImageDetails = (htmlContent: string): ImageAltText[] => {
  const $ = load(htmlContent)
  const figures = $('figure').toArray()

  return figures.reduce<ImageAltText[]>((altTexts, figure) => {
    if (!figure.firstChild) return altTexts

    const fileName = figure.attribs['data-original-file-name']
    const shortAltText = $(figure.firstChild).attr('aria-label')
    const longAltTextEncoded = $(figure.firstChild).attr('longdesc')
    const regionLabel = $(figure.firstChild).attr('data-accessibility-label')
    const language = $(figure.firstChild).attr('lang')
    const src = $(figure.firstChild).attr('src')

    // Logic mirrored from CKEditor plugin at `image-alt-text-sync-plugin.js`
    const longAltText = longAltTextEncoded
      ? decodeURIComponent(longAltTextEncoded.slice(29))
      : undefined

    if (fileName && (shortAltText || longAltText || regionLabel || language)) {
      altTexts.push({
        fileName,
        longAltText,
        shortAltText,
        regionLabel,
        language,
        src,
      } as ImageAltText) // source gets added from calling func so we have to cast
    }

    return altTexts
  }, [])
}

const stimulusName = (asset: ItemSet) => StimulusFormatIdReadable[asset.stimulusFormat]
const altTextSources: Array<(asset: ItemSet) => AltTextSource[]> = [
  (asset) =>
    asset.items?.flatMap((item) => [
      {
        rootId: asset.id,
        entityId: item.id,
        location: `Item ${item.id}`,
        specific: 'Course Alignment Rationale',
        text: item.courseAlignmentRationale,
      },
      {
        rootId: asset.id,
        entityId: item.id,
        location: `Item ${item.id}`,
        specific: 'Stem',
        text: (item as any).prompt,
      },
      {
        rootId: asset.id,
        entityId: item.id,
        location: `Item ${item.id}`,
        specific: 'Stimulus',
        text: (item as any).stimulus,
      },
      ...(item.part || []).flatMap((part, idx) => ({
        rootId: asset.id,
        entityId: item.id,
        leafId: part.partId,
        location: constructFrqPartName(part, idx),
        specific: 'Part',
        text: part.subStem,
      })),
      {
        rootId: asset.id,
        entityId: item.id,
        location: `Item ${item.id}`,
        specific: 'Distractor Rationale',
        text: (item as any).formalDistractorRationale,
      },
      {
        rootId: asset.id,
        entityId: item.id,
        location: `Item ${item.id}`,
        specific: 'Stem',
        text: (item as any).stem,
      },
      // TODO I am unsure if this ever provides something actionable.
      ...(item.graphics?.flatMap((graphic: any) => [
        {
          rootId: asset.id,
          entityId: item.id,
          leafId: graphic.id,
          location: `Item ${item.id} Graphic`,
          specific: 'Graphic Long Alt Text',
          text: graphic.longAltText,
        },
        {
          rootId: asset.id,
          entityId: item.id,
          leafId: graphic.id,
          location: `Item ${item.id} Graphic`,
          specific: 'Graphic Short Alt Text',
          text: graphic.shortAltText,
        },
      ]) || []),
      ...(item.answerOptions?.flatMap((answer: any) => [
        {
          rootId: asset.id,
          entityId: item.id,
          location: `Item ${item.id}`,
          specific: `Option ${answer.label} Answer`,
          text: answer.content,
        },
        {
          rootId: asset.id,
          entityId: item.id,
          location: `Item ${item.id}`,
          specific: `Option ${answer.label} Rationale`,
          text: answer.informalRationale,
        },
      ]) || []),
    ]),
  (asset) =>
    asset.stimuli?.flatMap((stimulus, i) => [
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Course Alignment Rationale',
        text: stimulus.courseAlignmentRationale,
      },
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Stimulus',
        text: stimulus.content,
      },
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Passage Introduction',
        text: stimulus.passageIntroduction,
      },
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Credit Line',
        text: stimulus.creditLine,
      },
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Source Attribution',
        text: stimulus.sourceAttribution,
      },
      {
        rootId: asset.id,
        entityId: stimulus.id,
        location: stimulus.displayName || `${stimulusName(asset)} ${i + 1}`,
        specific: 'Title',
        text: stimulus.title,
      },
    ]),
  (asset) => [
    {
      rootId: asset.id,
      location: 'General Content and Metadata',
      specific: 'Source Document Instructions',
      text: asset.sourceDocumentInstructions,
    },
    {
      rootId: asset.id,
      location: 'General Content and Metadata',
      specific: 'Instructions',
      text: asset.instructions,
    },
  ],
  //   (asset: any) => asset.setItems?.map(() => null),
]

export const getAnswerKey = (item: Item) => {
  if (item.itemType === ItemType.MultipleChoiceQuestion) {
    return (item as MultipleChoiceQuestion).answerOptions.reduce((acc, opt, idx) => {
      //65 is the ascii code for 'A'
      if (opt.correct) return [...acc, opt.label || String.fromCharCode(idx + 65)]
      return acc
    }, [] as string[])
  }
  return
}
// Not sure if this is expected or not (not I guess?).
// but on the apiModel the item metadata renders the label as the value: ['Program', [{ id: '142', label: 'AP', nodeType: 'Program', value: 'AP' }]],
// On the non api model the label is the node type  ['Program', [{ id: '142', label: 'Program', nodeType: 'Program', value: 'AP' }]],

const getCourseAlignmentRationaleForAPIModel = (item: Item): string[] => {
  const itemAndPartAlignment = [item.courseAlignmentRationale]
  item.part?.forEach((itemPart) => {
    itemAndPartAlignment.push(itemPart.courseAlignmentRationale)
  })
  // remove undefined values
  return itemAndPartAlignment.filter(Boolean)
}
const getCourseAlignmentRationaleForApiaryModel = (item: ExtendedApiaryItem): string[] => {
  const itemAndPartAlignment = [item.courseAlignmentRationale]

  ;(item?.freeResponseQuestion?.part ?? item?.multipleChoiceQuestion?.part ?? [])?.forEach(
    (itemPart) => {
      itemAndPartAlignment.push(itemPart.courseAlignmentRationale)
    }
  )
  // remove undefined values
  return itemAndPartAlignment.filter(Boolean)
}

const getCourseAlignmentRationaleForStimuli = (asset: ItemSet): string[] => {
  // remove undefined values
  return asset.stimuli.map((stimulus) => stimulus.courseAlignmentRationale).filter(Boolean)
}

const mapAPIModelToAugmentedPreviewer = (
  asset: ItemSet & { setItems?: ExtendedApiaryItem[] },
  item: Item & { partId?: string },
  sharedAltText: AugmentedPreviewerData['altText'],
  sharedMetadata: Record<string, Metadata[]>
) => {
  return {
    itemAlignment: getCourseAlignmentRationaleForAPIModel(item),
    stimuliAlignment: getCourseAlignmentRationaleForStimuli(asset),
    rationales: getRationale(item),
    altText: sharedAltText,
    stimuliMetadata: getStimuliMetadata(asset),
    generalMetadata: Object.entries(sharedMetadata),
    itemMetadata: Object.entries(getMetadata(item)),
    id: item.partId ?? item.id,
    subject: sharedMetadata['Subject']?.[0]?.value || 'err',
    answerKey: getAnswerKey(item),
    programAndSubject: `${sharedMetadata['Program']?.[0]?.value || ''} ${
      sharedMetadata['Subject']?.[0]?.value
    }`.trim(),
    infoPanelDropdownOptions: constructInfoPanelDropdown(asset),
  }
}

export const mapAssetToAugmentedPreviewer = (
  asset: ItemSet & { setItems?: ExtendedApiaryItem[] },
  isAPIModel: boolean
): AugmentedPreviewerData[] => {
  const sharedAltText = altTextSources
    .flatMap((getAltText) => getAltText(asset))
    .reduce<AugmentedPreviewerData['altText']>(
      (altText, source) => {
        if (typeof source?.text !== 'string') return altText

        for (const formula of getMathFormulae(source.text)) {
          ;(formula as any).source = source
          altText.maths.push(formula)
        }

        for (const alt of getImageDetails(source.text)) {
          ;(alt as any).source = source
          altText.images.push(alt)
        }

        return altText
      },
      { maths: [], images: [] }
    )

  if (isAPIModel) {
    const sharedMetadata = getMetadata(asset)
    // Trickery to get metadata for Directions, which lack Items
    return (asset.items.length > 0 ? asset.items : [{} as unknown as Item]).flatMap((item) => {
      if (item.part && Array.isArray(item.part) && item.part.length > 0) {
        return item.part.map((part) => {
          const partWithItemMetadata = {
            ...part,
            metadata: item.metadata,
            additionalMetadata: item.additionalMetadata,
          } as unknown as Item
          return mapAPIModelToAugmentedPreviewer(
            asset,
            partWithItemMetadata,
            sharedAltText,
            sharedMetadata
          )
        })
      }
      return mapAPIModelToAugmentedPreviewer(asset, item, sharedAltText, sharedMetadata)
    })
  } else if (Array.isArray(asset?.setItems)) {
    const generalMetadata = reduceAdditionalMetadata(asset.metadata as unknown as Metadata[])

    return (asset.setItems as ExtendedApiaryItem[]).reduce<AugmentedPreviewerData[]>(
      (accum, item, index) => {
        const metadata = reduceAdditionalMetadata(item.metadata || [])
        const program = metadata?.['Program']?.[0]?.value || 'err'
        const subject = metadata?.['Subject']?.[0]?.value || 'err'
        const programAndSubject = `${program} ${subject}`
        const augmentedPreviewerData = {
          itemAlignment: getCourseAlignmentRationaleForApiaryModel(item),
          stimuliAlignment: getCourseAlignmentRationaleForStimuli(asset),
          rationales: getRationale(asset.items[index]!),
          altText: sharedAltText,
          stimuliMetadata: getStimuliMetadata(asset),
          generalMetadata: Object.entries(generalMetadata),
          itemMetadata: Object.entries(metadata),
          id: item.assetId,
          subject: metadata['Subject']?.[0]?.value || '',
          answerKey: getAnswerKey(asset.items[index]!),
          programAndSubject: programAndSubject.trim(),
          infoPanelDropdownOptions: constructInfoPanelDropdown(asset),
        }
        // If there are parts present pad out the response; otherwise just add the data once.
        return accum.concat(
          new Array(item?.freeResponseQuestion?.part?.length ?? 1).fill(augmentedPreviewerData)
        )
      },
      [] as AugmentedPreviewerData[]
    )
  }
  return []
}

export const constructInfoPanelDropdown = (
  itemSet: ItemSet
): AugmentedPreviewerData['infoPanelDropdownOptions'] => {
  const options: AugmentedPreviewerData['infoPanelDropdownOptions'] = []

  itemSet.items.forEach((item) => {
    item.part?.forEach((part, idx) => {
      options.push({
        id: part.partId,
        type: 'part' as const,
        disabledTabs: [InfoTab.KEY_RATIONALES],
        metadataGroup: MetadataGroup.ITEM_METADATA,
        label: constructFrqPartName(part, idx),
        parentAssetId: itemSet.id,
      })
    })

    options.push({
      id: item.id,
      type: 'item' as const,
      disabledTabs: [],
      metadataGroup: MetadataGroup.ITEM_METADATA,
      label: `Item ${item.id}`,
      parentAssetId: itemSet.id,
    })
  })

  itemSet.stimuli?.forEach((stimulus) => {
    options.push({
      id: stimulus.id,
      type: 'stimulus' as const,
      disabledTabs: [InfoTab.KEY_RATIONALES],
      ordinal: stimulus.ordinal?.toString(),
      metadataGroup: MetadataGroup.STIMULUS_METADATA,
      label: stimulus.displayName || constructStimulusDisplayName(stimulus, itemSet),
      parentAssetId: itemSet.id,
    })
  })

  options.push({
    id: itemSet.id,
    type: 'asset' as const,
    disabledTabs: Object.values(InfoTab).filter((tab) => tab !== InfoTab.METADATA),
    metadataGroup: MetadataGroup.ASSET_METADATA,
    label: `Asset ${itemSet.id}`,
  })

  return options
}
