import { ProgramIdentifier, SubjectIdentifier } from '@shared/config'
import type { Asset, ItemSet as ItemSetModel } from '@shared/dynamo-hummingbird'
import { getEmptyMetadataRootRecord } from '@shared/metadata/metadata-root-record'
import type { ItemSet, MetadataRootRecord } from '@shared/schema'
import { Metadata, StimulusFormat } from '@shared/schema'

/* Mutates provided ItemSet to move the metadata where it needs to go */
/* istanbul ignore next */
function coerceMetadataShape(itemSet: ItemSet): ItemSet {
  const coerceItemOrSetShape = (itemOrSet: ItemSet | ItemSet['items'][number]) => {
    const movedReqMetadata = new Set<string>()
    Object.entries(itemOrSet?.metadata ?? {}).forEach(([key, metadata]) => {
      if (!['program', 'subject'].includes(key)) {
        movedReqMetadata.add(key)
        if (Array.isArray(metadata)) {
          metadata.forEach((metaArEl) => itemOrSet.additionalMetadata.push(metaArEl))
        } else {
          itemOrSet.additionalMetadata.push(metadata)
        }
      }
    })
    movedReqMetadata.forEach((key) => delete (itemOrSet.metadata as any)[key])
  }
  coerceItemOrSetShape(itemSet)
  itemSet.items?.forEach(coerceItemOrSetShape)
  ;(
    itemSet.stimuli as unknown as { stimulusMetadata?: Metadata[]; metadata?: Metadata[] }[]
  )?.forEach((stimulus) => {
    if (stimulus.stimulusMetadata) {
      stimulus.metadata = [...(stimulus?.metadata || []), ...stimulus.stimulusMetadata]
      delete stimulus.stimulusMetadata
    }
  })
  return itemSet
}

/**
 * Mutates the input itemSet to reflect the {@link Metadata} at `asset.newMetadata[metdataVersion]`.
 * @returns A reference to the input `itemSet`.
 */
function loadMetadataByVersion(
  itemSetEntity: NonNullable<Parameters<typeof toItemSet>[0]> & {
    metadataVersion?: string
    newMetadata?: MetadataRootRecord
  },
  itemSet: ItemSet,
  targetMetadataVersion?: string
) {
  console.debug(
    `loadMetadataByVersion: loading metadata for ${itemSet.id} with\
    previous version ${itemSet.metadataVersion}, target version ${targetMetadataVersion}`
  )
  const metadataver = targetMetadataVersion ?? itemSetEntity.metadataVersion
  const metadataRecord = itemSetEntity.newMetadata
  const metadataVersionDocument =
    metadataver && metadataRecord?.[metadataver]
      ? metadataRecord?.[metadataver]
      : getEmptyMetadataRootRecord(itemSet)
  const metadata = metadataVersionDocument.metadata
  if (metadataver && metadata && Object.keys(metadataRecord ?? {}).length) {
    console.debug('found version and document, overwriting')
    itemSet.metadataVersion = metadataver
    itemSet.additionalMetadata = metadata?.[itemSet.id] ?? []
    itemSet.items.forEach((item) => {
      item.additionalMetadata = metadata?.[item.id] ?? []
    })
    itemSet.stimuli.forEach((stimulus) => {
      stimulus.metadata = metadata?.[stimulus.id] ?? []
    })
    itemSet.dimensions = metadataVersionDocument.dimensions
  }
  return itemSet
}

type ToItemSetInputType =
  | Pick<
      Asset | (ItemSetModel & { associations?: unknown[] }),
      | 'itemSet'
      | 'itemSetId'
      | 'createdById'
      | 'createdByUsername'
      | 'modifiedById'
      | 'modifiedByUsername'
      | 'revision'
      | 'workflowState'
      | 'accnum'
      | 'associations'
    >
  | undefined

export function toItemSet(
  itemSetEntity: ToItemSetInputType,
  metadataVersion?: string
): ItemSet & { associations?: unknown[] } {
  /** This function does a safety check to ensure Items have valid required metadata matching their parent */
  /* istanbul ignore next */
  if (!itemSetEntity) {
    throw new Error('Provided item set is undefined')
  }
  function validateMatchedProgramSubject(item: ItemSet['items'][number], itemSet: ItemSet) {
    const { program, subject } = item.metadata
    if (!(program && Object.values(ProgramIdentifier).includes(program.id as ProgramIdentifier))) {
      console.debug(
        `Item ${item.id} found with no Program, using program from parent ${itemSet.id}`
      )
      item.metadata.program = itemSet.metadata.program
    }
    if (!(subject && Object.values(SubjectIdentifier).includes(subject.id as SubjectIdentifier))) {
      console.debug(
        `Item ${item.id} found with no Subject, using subject from parent ${itemSet.id}`
      )
      item.metadata.subject = itemSet.metadata.subject
    }
    return item
  }

  if (!itemSetEntity.itemSet) {
    throw new Error('ItemSet is not defined')
  }

  let itemSet: ItemSet
  try {
    itemSet = JSON.parse(
      itemSetEntity.itemSet,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      function (this: any, key: any, value: any) {
        const dateKeys = ['uploadedOn', 'modifiedOn']
        if (dateKeys.includes(key) && typeof value === 'string') {
          return new Date(value)
        }
        return value
      }
    )
  } catch {
    throw new Error('Could not parse item set')
  }

  const convertedItems = (itemSet.items || [])
    .map((item) => validateMatchedProgramSubject(item, itemSet))
    .sort((a, b) => {
      return (
        (a.ordinalValue !== undefined ? a.ordinalValue : Infinity) -
        (b.ordinalValue !== undefined ? b.ordinalValue : Infinity)
      )
    })
  const res = {
    ...itemSet,
    id: itemSetEntity.itemSetId || itemSet.id,
    items: convertedItems,
    created: {
      userId: itemSetEntity?.createdById || 'unknown',
      userName: itemSetEntity?.createdByUsername || 'unknown',
      modifiedOn: new Date(itemSet?.created?.modifiedOn ?? 0),
    },
    updated: {
      userId: itemSetEntity?.modifiedById || 'unknown',
      userName: itemSetEntity.modifiedByUsername || 'unknown',
      modifiedOn: new Date(itemSet?.updated?.modifiedOn ?? 0),
    },
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    version: itemSetEntity.revision!,
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    workflowState: itemSetEntity.workflowState!,
    accnum: itemSetEntity?.accnum || '',
    stimulusFormat: itemSet?.stimulusFormat || StimulusFormat.DEFAULT,
    associations: itemSetEntity?.associations,
    metadataVersion:
      (itemSetEntity as Asset)?.metadataVersion ?? itemSet?.metadataVersion ?? 'unknown',
  }
  loadMetadataByVersion(itemSetEntity, res, metadataVersion)
  return coerceMetadataShape(res)
}
