import { ProgramIdentifier, SubjectIdentifier } from '@shared/config'
import { generateTestAsset } from '@shared/data-generation/mock'
import { Item, ItemType } from '@shared/schema'
import {
  constructInfoPanelDropdown,
  getAnswerKey,
  mapAssetToAugmentedPreviewer,
} from './augmented-previewer-data-converter'
import { toItemSet } from './convert-asset-to-item-set'

const buildState = async () => {
  const mockAsset = await generateTestAsset({
    itemType: ItemType.directions,
    id: '515912342',
    subjectId: SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
  })
  const parsedItemSet = toItemSet(mockAsset)

  const commonState = {
    ...parsedItemSet,
    metadata: [
      {
        nodeType: 'Program',
        label: 'AP',
        value: 'AP',
        id: ProgramIdentifier.AP,
      },
      {
        nodeType: 'Subject',
        value: 'African American Studies',
        id: SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
      },
    ],
    items: [
      {
        id: 'test',
        itemType: ItemType.MultipleChoiceQuestion,
        answerOptions: [
          {
            id: 'test',
            label: 'label',
            ordinal: 0,
            content: '',
            correct: true,
            key: 'key',
            informalRationale: 'selection',
          },
        ],
      },
    ],
  }

  //if APIMODEL = false, this type is the UI type. I believe its the redux state that the previewer consumes.
  const reduxState = {
    ...commonState,
    setItems: [
      {
        assetId: 'asset ID',
        metadata: [
          {
            nodeType: 'Program',
            label: 'AP',
            value: 'AP',
            id: ProgramIdentifier.AP,
          },
          {
            nodeType: 'Subject',
            value: 'African American Studies',
            id: SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
          },
          { id: 'an id', nodeType: 'a nodetype', label: 'a label', value: 'value 1' },
        ],
      },
    ],
  }
  //if APImodel = true,  this state would be the backend model state, relies on mapping items instead of set items.
  const dynamoState = {
    ...commonState,
    items: commonState.items.map((item: any) => {
      return {
        ...item,
        metadata: [
          {
            nodeType: 'Program',
            value: 'AP',
            id: ProgramIdentifier.AP,
          },
          {
            nodeType: 'Subject',
            value: 'African American Studies',
            id: SubjectIdentifier.AFRICAN_AMERICAN_STUDIES,
          },
        ],
        additionalMetadata: [
          { id: 'an id', nodeType: 'a nodetype', label: 'a label', value: 'value 1' },
        ],
      }
    }),
  }
  return { reduxState, dynamoState }
}

describe('augemented previewer tests', () => {
  it.each([[false], [true]])(
    'should map the model state to the desired previewer state for isAPIModel %s',
    async (isAPIModel) => {
      const state = await buildState()
      const assetState = isAPIModel ? state.dynamoState : state.reduxState
      // @ts-expect-error: Mixed-use function
      const nonAPImodel = mapAssetToAugmentedPreviewer(assetState, isAPIModel)[0]
      expect(nonAPImodel?.id).toEqual(isAPIModel ? 'test' : 'asset ID')
      expect(nonAPImodel?.rationales).toEqual(['label selection'])
      expect(nonAPImodel?.programAndSubject).toEqual('AP African American Studies')
      expect(nonAPImodel?.answerKey).toEqual(['label'])
      // TODO Review value vs ID
      expect(nonAPImodel?.subject).toEqual('African American Studies')
      expect(nonAPImodel?.itemMetadata).toEqual([
        ['Program', [{ id: '59tWw', label: 'AP', nodeType: 'Program', value: 'AP' }]],
        [
          'Subject',
          [
            {
              id: '116',
              label: 'African American Studies',
              nodeType: 'Subject',
              value: 'African American Studies',
            },
          ],
        ],
        [
          'a nodetype',
          [{ id: 'an id', nodeType: 'a nodetype', label: 'value 1', value: 'value 1' }],
        ],
      ])
      expect(nonAPImodel?.generalMetadata).toEqual([
        ['Program', [{ id: '59tWw', label: 'AP', nodeType: 'Program', value: 'AP' }]],
        [
          'Subject',
          [
            {
              id: '116',
              label: 'African American Studies',
              nodeType: 'Subject',
              value: 'African American Studies',
            },
          ],
        ],
      ])
    }
  )
})

describe('get answer key tests', () => {
  const options = [
    {
      itemType: ItemType.MultipleChoiceQuestion,
      answerOptions: [
        {
          correct: true,
          label: 'A',
        },
        { correct: true },
      ],
    },
    {
      itemType: ItemType.MultipleChoiceQuestion,
      answerOptions: [
        {
          correct: false,
          label: 'A',
        },
        { correct: false },
      ],
    },
  ]
  const keys = [['A', 'B'], []]
  it.each([0, 1])('should return a list of correct keys', (opt) => {
    expect(getAnswerKey(options[opt] as Item)).toEqual(keys[opt])
  })
})

describe('construct info panel dropdown tests', () => {
  it('should return expected dropdown options given a valid itemSet', () => {
    const itemSetMock = {
      id: 'testAsset',
      items: [
        {
          id: 'item1',
          part: [{ parentItemId: 'item1', partId: 'part1' }],
        },
      ],
      stimuli: [
        {
          id: 'stim1',
          ordinal: 1,
          hbid: 'SB01',
        },
      ],
    }

    const result = constructInfoPanelDropdown(itemSetMock as any)
    expect(result).toHaveLength(4)

    expect(result[0]).toMatchObject({
      id: 'part1',
      label: 'item1 Part A',
    })
    expect(result[1]).toMatchObject({
      id: 'item1',
      label: 'Item item1',
    })
    expect(result[2].id).toBe('stim1')
    expect(result[3].id).toBe('testAsset')
  })

  it('should handle empty items and stimuli gracefully', () => {
    const itemSetMock = { id: 'emptyAsset', items: [], stimuli: [] }
    const result = constructInfoPanelDropdown(itemSetMock as any)
    expect(result).toHaveLength(1)
    expect(result[0].id).toBe('emptyAsset')
  })
})
