import * as mocks from '@shared/trpc-lambda/mocks'
import { constructAssetAndItemOrder } from './construct-asset-and-item-order'

const collectionAssetAssociations = [
  ...mocks.collectionAssetAssociations.slice(0, 1).map((x) => {
    delete x.on.items
    return x
  }),
  ...mocks.collectionAssetAssociations.slice(1),
]

const nestedTableData = [
  {
    id: 'ABC000000',
    ordinal: 1,
    entityType: 'Asset',
    children: [
      { id: 'DEF000000', deactivated: false },
      { id: 'GHI000000', deactivated: true },
    ],
  },
  {
    id: 'JKL000000',
  },
  {
    id: 'MNO000000',
    ordinal: 3,
    children: [{ id: 'PQR000000', deactivated: undefined }],
  },
]

describe('constructAssetAndItemOrder tests', () => {
  it('will successfuly map collection associations when missing items', () => {
    expect(constructAssetAndItemOrder(collectionAssetAssociations)).toEqual(
      collectionAssetAssociations.map((d) => ({
        id: d.associationChildId,
        ordinal: d.on.ordinal,
        entityType: d.associationChildType,
        children: d.on.items ?? [],
      }))
    )
  })

  it('will successfuly map assembly unit associations and use the provided key', () => {
    const result = constructAssetAndItemOrder(
      mocks.assemblyUnitAssetAssociations,
      'itemDeactivatedInAu'
    )
    expect(result).toEqual(
      mocks.assemblyUnitAssetAssociations.map((d) => ({
        id: d.associationChildId,
        ordinal: d.on.ordinal,
        entityType: d.associationChildType,
        children: (d.on.items as Array<{ id: string; active: boolean }>).map((i) => ({
          id: i.id,
          itemDeactivatedInAu: !i.active,
        })),
      }))
    )
  })

  it.each([
    ['active', (value: unknown) => !value],
    ['itemDeactivatedInAu', (value: unknown) => !!value],
  ])('will successfully map nested table data', (activeKey, negationFunction) => {
    expect(
      constructAssetAndItemOrder(nestedTableData, activeKey as 'active' | 'itemDeactivatedInAu')
    ).toEqual(
      nestedTableData.map((d, idx) => ({
        id: d.id,
        ordinal: d.ordinal ?? idx + 1,
        entityType: d.entityType,
        children: (d.children ?? []).map((c) => ({
          id: c.id,
          [activeKey]: negationFunction(c.deactivated),
        })),
      }))
    )
  })
})
