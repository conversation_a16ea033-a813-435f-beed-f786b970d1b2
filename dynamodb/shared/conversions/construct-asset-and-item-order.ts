import { AssociationModelType } from '@shared/dynamo-hummingbird/tables/split-models'
import { CollectionAssociationModel } from '@shared/dynamo-hummingbird/tables/split-models/collections'
import { AssemblyUnitToAssetAssociation } from '@shared/schema/split-models'

type NestedTableDataParams = {
  id: string
  children?: { id: string; deactivated?: boolean }[]
  ordinal?: number
  entityType?: string
}

type ConstructAssetAndItemOrderParams =
  | AssemblyUnitToAssetAssociation[]
  | CollectionAssociationModel[]
  | AssociationModelType[]
  | NestedTableDataParams[]

type ActiveKey = 'active' | 'itemDeactivatedInAu'

export const constructAssetAndItemOrder = (
  input: ConstructAssetAndItemOrderParams,
  activeKey: ActiveKey = 'active'
) => {
  return input.map((d, idx) => {
    const isAssociation = Object.hasOwn(d, 'associationChildId')
    const typedAssociationData = d as AssemblyUnitToAssetAssociation | CollectionAssociationModel
    const typedNestedTableData = d as NestedTableDataParams
    return {
      id: isAssociation ? typedAssociationData.associationChildId : typedNestedTableData.id,
      ordinal: isAssociation
        ? typedAssociationData.on.ordinal
        : typedNestedTableData.ordinal ?? idx + 1, // nested table data comes sorted
      entityType: isAssociation
        ? typedAssociationData.associationChildType
        : typedNestedTableData.entityType,
      children: isAssociation
        ? (typedAssociationData.on.items ?? []).map((i) => ({
            id: i.id,
            [activeKey]: activeKey === 'itemDeactivatedInAu' ? !i.active : i.active,
          }))
        : (typedNestedTableData.children ?? []).map((c) => ({
            id: c.id,
            [activeKey]: activeKey === 'itemDeactivatedInAu' ? !!c.deactivated : !c.deactivated,
          })),
    }
  })
}
