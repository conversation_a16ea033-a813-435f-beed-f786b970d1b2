import { z } from 'zod'
import {
  serializeDeleteExpression,
  serializeUpdateExpression,
  writeEntryToBatch,
  writeEntryToTransaction,
} from './serialize'

const getCompositeNameMock = jest.fn(() => ({
  tableName: 'mock-table',
  tableArn: 'arn:mock:table',
  hash: 'hk',
  sort: 'sk',
}))
jest.mock('../utils/keys', () => ({
  getCompositeName: () => getCompositeNameMock(),
}))

describe('DynamoIO Mutation Serialization', () => {
  afterAll(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  afterEach(() => {
    getCompositeNameMock.mockClear()
  })

  describe('serializeUpdateExpression()', () => {
    it('Ignores hash/sort keys', () => {
      const result = serializeUpdateExpression('itemCloudGreen', {
        hk: 'hash',
        sk: 'sort',
        other: 'prop',
      })

      expect(result.Key).toEqual({
        hk: 'hash',
        sk: 'sort',
      })

      expect(result.UpdateExpression).not.toContain('hk')
      expect(result.UpdateExpression).not.toContain('sk')
    })

    it('Serializes arrays correctly', () => {
      const result = serializeUpdateExpression('itemCloudGreen', {
        hk: 'hash',
        sk: 'sort',
        other: [1, 2, 3],
      })

      expect(result.ExpressionAttributeValues).toEqual({
        ':i_0': [1, 2, 3],
      })
    })

    it('Handles nested objects', () => {
      const result = serializeUpdateExpression('itemCloudGreen', {
        hk: 'hash',
        sk: 'sort',
        deeply: {
          nested: 'prop',
          and: {
            nested: 'object',
          },
        },
      })

      // set deeply.nested = "prop", deeply.and.nested = "object"
      expect(result.UpdateExpression).toEqual('set #i_0.#i_1 = :i_1, #i_0.#i_2.#i_3 = :i_3')
    })
  })

  describe('serializeDeleteExpression()', () => {
    it('Skips traversal by default', () => {
      const result = serializeDeleteExpression('itemCloudGreen', {
        hk: 'hash',
        sk: 'sort',
        deeply: {
          nested: 'prop',
          and: {
            nested: 'object',
          },
        },
      })

      expect(result.ConditionExpression).toEqual('')
      expect(result.Key).toEqual({
        hk: 'hash',
        sk: 'sort',
      })
    })

    it('Serializes arrays correctly', () => {
      const result = serializeDeleteExpression(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
          other: [1, 2, 3],
        },
        true
      )

      expect(result.ExpressionAttributeValues).toEqual({
        ':i_0': [1, 2, 3],
      })
    })

    it('Handles nested objects', () => {
      const result = serializeDeleteExpression(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
          deeply: {
            nested: 'prop',
            and: {
              nested: 'object',
            },
          },
        },
        true
      )

      // set deeply.nested = "prop", deeply.and.nested = "object"
      expect(result.ConditionExpression).toEqual('#i_0.#i_1 = :i_1 AND #i_0.#i_2.#i_3 = :i_3')
    })
  })

  describe('writeEntryToTransaction()', () => {
    it('Generates a good delete key', () => {
      const result = writeEntryToTransaction({
        table: 'itemCloudGreen',
        record: {
          hk: 'hash',
          sk: 'sort',
        },
        remove: true,
      })

      expect(result).toHaveProperty('Delete')
      expect(result.Delete.Key).toEqual({
        hk: 'hash',
        sk: 'sort',
      })
    })

    it('Generates a put', () => {
      const result = writeEntryToTransaction({
        table: 'itemCloudGreen',
        record: {
          hk: 'hash',
          sk: 'sort',
        },
      })

      expect(result).toHaveProperty('Put')
    })

    it('Handles a Zod schema first', () => {
      const schema = z.object({
        hk: z.string(),
        sk: z.string(),
        banana: z.literal('boat'),
      })

      try {
        writeEntryToTransaction({
          table: 'itemCloudGreen',
          record: {
            hk: 'hash',
            sk: 'sort',
            banana: 'dance', // It's peanut butter jelly time
          },
          schema: schema,
        })

        fail('Supposed to throw a ZodError')
      } catch (ex) {
        expect(ex).toBeDefined()
        expect(getCompositeNameMock).toHaveBeenCalledTimes(0)
      }
    })
  })

  describe('writeEntryToBatch()', () => {
    it('Generates a good delete key', () => {
      const result = writeEntryToBatch({
        table: 'itemCloudGreen',
        record: {
          hk: 'hash',
          sk: 'sort',
        },
        remove: true,
      })

      expect(result).toHaveProperty('DeleteRequest')
      expect(result.DeleteRequest!.Key).toEqual({
        hk: 'hash',
        sk: 'sort',
      })
    })

    it('Generates a put', () => {
      const result = writeEntryToBatch({
        table: 'itemCloudGreen',
        record: {
          hk: 'hash',
          sk: 'sort',
        },
      })

      expect(result).toHaveProperty('PutRequest')
    })

    it('Handles a Zod schema first', () => {
      const schema = z.object({
        hk: z.string(),
        sk: z.string(),
        banana: z.literal('boat'),
      })

      try {
        writeEntryToBatch({
          table: 'itemCloudGreen',
          record: {
            hk: 'hash',
            sk: 'sort',
            banana: 'dance', // It's peanut butter jelly time
          },
          schema: schema,
        })

        fail('Supposed to throw a ZodError')
      } catch (ex) {
        expect(ex).toBeDefined()
        expect(getCompositeNameMock).toHaveBeenCalledTimes(0)
      }
    })
  })
})
