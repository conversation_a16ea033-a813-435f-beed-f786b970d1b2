import type { DynamoRecord, TargetTable, TypedDynamoSchema } from '../types'

export type WriteEntry<T extends DynamoRecord = DynamoRecord> = {
  table: TargetTable
  record: T
  remove?: boolean
  schema?: TypedDynamoSchema<T>
}

export interface WriteEntryDelete {
  Delete: {
    TableName: string
    Key: Record<string, any>
  }
}

export interface WriteEntryUpdate {
  Put: {
    TableName: string
    Item: DynamoRecord
  }
}

export interface WriteTransactionOptions {
  transact?: boolean
}

export interface WriteValidationOptions<T extends DynamoRecord = DynamoRecord> {
  schema?: TypedDynamoSchema<T>
}

export interface DeleteOptions {
  retrieve?: boolean
  verify?: boolean
}
