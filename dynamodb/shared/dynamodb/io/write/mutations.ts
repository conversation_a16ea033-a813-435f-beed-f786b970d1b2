import {
  Batch<PERSON>rite<PERSON>ommand,
  DeleteCommand,
  TransactWriteCommand,
  UpdateCommand,
} from '@aws-sdk/lib-dynamodb'
import { getDocumentClient } from '@infra/hummingbird/databases/client'
import chunk from '@shared/utils/chunk'
import type { DynamoRecord, TargetTable } from '../types'
import {
  serializeDeleteExpression,
  serializeUpdateExpression,
  writeEntryToBatch,
  writeEntryToTransaction,
} from './serialize'
import type {
  DeleteOptions,
  WriteEntry,
  WriteTransactionOptions,
  WriteValidationOptions,
} from './types'

/**
 * With one transaction, write multiple records to multiple tables
 *
 *! NOTE: There is a four megabyte limit on transactions
 *
 * @param provider Array or other iterable (e.g. generator function)
 * @deprecated DO NOT USE THIS ON ITS OWN, use writeMany(), writeOne(), updateOne()
 */
export async function writeSome(
  provider: Iterable<WriteEntry>,
  { transact = true }: WriteTransactionOptions = {}
) {
  const documentClient = getDocumentClient()

  if (Array.isArray(provider)) {
    if (transact) {
      const batchWrite = new TransactWriteCommand({
        TransactItems: (provider as WriteEntry[]).map(writeEntryToTransaction),
      })

      return await documentClient.send(batchWrite)
    }

    const batchWrite = new BatchWriteCommand({
      RequestItems: (provider as WriteEntry[]).reduce((agg, write) => {
        const { TableName, ...request } = writeEntryToBatch(write)

        agg[TableName] ??= []
        agg[TableName]!.push(request)

        return agg
      }, {} as Record<string, any[]>),
    })

    return await documentClient.send(batchWrite)
  }

  // TODO: support transact = false for Iterable<WriteEntry>
  const requests: any[] = []
  // Generator functions and custom iterators
  for await (const write of provider) {
    const request = transact ? writeEntryToTransaction(write) : writeEntryToBatch(write)

    requests.push(request)
  }

  if (transact) {
    const batchWrite = new TransactWriteCommand({ TransactItems: requests })
    return await documentClient.send(batchWrite)
  }

  const batchWrite = new BatchWriteCommand({
    RequestItems: requests.reduce((agg, { TableName, ...request }) => {
      agg[TableName] ??= []
      agg[TableName]!.push(request)

      return agg
    }, {} as Record<string, any[]>),
  })

  return await documentClient.send(batchWrite)
}

export async function writeMany(
  provider: Iterable<WriteEntry>,
  { transact = true }: WriteTransactionOptions = {}
) {
  if (Array.isArray(provider)) {
    const segments = chunk(provider, 25)
    await Promise.all(segments.map((batch) => writeSome(batch, { transact })))
    return
  }

  const batch: WriteEntry[] = []
  for await (const entry of provider) {
    batch.push(entry)

    if (batch.length === 25) {
      await writeSome(batch, { transact })
      batch.length = 0 // Clear out the array
    }
  }

  if (batch.length) {
    await writeSome(batch, { transact })
  }
}

export async function updateOne<T extends DynamoRecord>(
  table: TargetTable,
  record: Partial<T>,
  { schema }: WriteValidationOptions<T> = {}
): Promise<T | null> {
  const documentClient = getDocumentClient()

  // This looks really weird, but it does work
  if (schema) {
    record = schema.parse(record) as T
  }

  const query = serializeUpdateExpression(table, record)

  const command = new UpdateCommand({
    ...query,
    ReturnValues: 'ALL_NEW',
  })

  const result = await documentClient.send(command)
  return (result.Attributes as T) ?? null
}

/** Alias for {@link updateOne} (thanks Luis!) */
export const writeOne = updateOne

// export async function deleteOne<T extends DynamoRecord>(
//     table: TargetTable,
//     record: Partial<T>,
//     options?: DeleteOptions
// ): Promise<null>

// export async function deleteOne<T extends DynamoRecord>(
//     table: TargetTable,
//     record: Partial<T>,
//     options: DeleteOptions & { retrieve: true }
// ): Promise<T>

export async function deleteOne<T extends DynamoRecord>(
  table: TargetTable,
  record: Partial<T>,
  { retrieve = false, verify = false }: DeleteOptions
) {
  const documentClient = getDocumentClient()
  const query = serializeDeleteExpression(table, record, verify)

  const command = new DeleteCommand({
    ...query,
    ReturnValues: retrieve ? 'ALL_OLD' : 'NONE',
  })

  const result = await documentClient.send(command)

  if (!retrieve) return null

  return (result.Attributes as T) ?? null
}
