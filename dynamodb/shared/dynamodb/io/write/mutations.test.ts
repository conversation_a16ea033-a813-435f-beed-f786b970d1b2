import { z } from 'zod'
import { deleteOne, updateOne, writeMany, writeSome } from './mutations'
import type { WriteEntry } from './types'

const clientMock = jest.fn()
jest.mock('@infra/hummingbird/databases/client', () => ({
  getDocumentClient: () => ({
    send: clientMock,
  }),
}))

jest.mock('../utils/keys', () => ({
  getCompositeName: () => ({
    tableName: 'mock-table',
    tableArn: 'arn:mock:table',
    hash: 'hk',
    sort: 'sk',
  }),
}))

describe('DynamoIO Mutations', () => {
  afterAll(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  afterEach(() => {
    clientMock.mockClear()
  })

  describe('writeSome()', () => {
    it('Transact writes an array', async () => {
      clientMock.mockResolvedValueOnce({})

      await writeSome([{ table: 'itemCloudGreen', record: {} }])

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          TransactItems: [
            {
              Put: {
                Item: {},
              },
            },
          ],
        },
      })
    })

    it('Batch writes an array', async () => {
      clientMock.mockResolvedValueOnce({})

      await writeSome([{ table: 'itemCloudGreen', record: {} }], { transact: false })

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          RequestItems: {
            'mock-table': [
              {
                PutRequest: {
                  Item: {},
                },
              },
            ],
          },
        },
      })
    })

    it('Transact writes a non-array', async () => {
      clientMock.mockResolvedValueOnce({})

      await writeSome(new Set([{ table: 'itemCloudGreen', record: {} }]))

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          TransactItems: [
            {
              Put: {
                Item: {},
              },
            },
          ],
        },
      })
    })

    it('Batch writes a non-array', async () => {
      clientMock.mockResolvedValueOnce({})

      await writeSome(new Set([{ table: 'itemCloudGreen', record: {} }]), { transact: false })

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          RequestItems: {
            'mock-table': [
              {
                PutRequest: {
                  Item: {},
                },
              },
            ],
          },
        },
      })
    })
  })

  describe('writeMany()', () => {
    it('Chunks an array', async () => {
      clientMock.mockResolvedValue({})
      const data = Array.from({ length: 28 }).map<WriteEntry>(() => ({
        table: 'itemCloudGreen',
        record: {},
      }))

      await writeMany(data)

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock).toHaveBeenCalledTimes(2)
    })

    it('Chunks a non-array', async () => {
      clientMock.mockResolvedValue({})
      const data = new Set(
        Array.from({ length: 28 }).map<WriteEntry>(() => ({ table: 'itemCloudGreen', record: {} }))
      )

      await writeMany(data)

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock).toHaveBeenCalledTimes(2)
    })
  })

  describe('updateOne()', () => {
    it('Returns the modified item', async () => {
      clientMock.mockResolvedValueOnce({
        Attributes: {},
      })

      const result = await updateOne('itemCloudGreen', {
        hk: 'hash',
        sk: 'sort',
      })

      expect(clientMock).toHaveBeenCalledTimes(1)
      expect(result).toEqual({})
    })

    it('Handles schema validation', async () => {
      clientMock.mockResolvedValueOnce({})

      const schema = z.object({
        hk: z.string(),
        sk: z.string(),
        something: z.boolean().default(false),
      })

      const result = await updateOne(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
        },
        { schema }
      )

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          ExpressionAttributeNames: {
            '#i_0': 'something',
          },
          UpdateExpression: 'set #i_0 = :i_0',
        },
      })
    })
  })

  describe('deleteOne()', () => {
    it('Includes other data when desired', async () => {
      clientMock.mockResolvedValueOnce({})

      const result = await deleteOne(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
          other: 'prop',
        },
        { verify: true }
      )

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(result).toEqual(null)
      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          ConditionExpression: '#i_0 = :i_0',
        },
      })
    })

    it('Retrieves on demand', async () => {
      clientMock.mockResolvedValueOnce({
        Attributes: { hk: 'hash', sk: 'sort' },
      })

      const result = await deleteOne(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
          other: 'prop',
        },
        { retrieve: true }
      )

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(result).toEqual({ hk: 'hash', sk: 'sort' })
      expect(clientMock.mock.lastCall[0]).toMatchObject({
        input: {
          ConditionExpression: '',
        },
      })
    })

    it('Handles edge case in retrieval', async () => {
      clientMock.mockResolvedValueOnce({})

      const result = await deleteOne(
        'itemCloudGreen',
        {
          hk: 'hash',
          sk: 'sort',
          other: 'prop',
        },
        { retrieve: true }
      )

      if (!clientMock.mock.lastCall) {
        fail('Client mock not called')
      }

      expect(result).toEqual(null)
    })
  })
})
