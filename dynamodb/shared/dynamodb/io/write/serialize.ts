import type { DeleteCommandInput, UpdateCommandInput } from '@aws-sdk/lib-dynamodb'
import type { TargetTable } from '../types'
import { getCompositeName } from '../utils/keys'
import type { WriteEntry, WriteEntryDelete, WriteEntryUpdate } from './types'

// TODO: Array traversal, maybe.
export function serializeUpdateExpression(
  table: TargetTable,
  props: Record<string, any>
): UpdateCommandInput {
  const { tableName, hash, sort } = getCompositeName(table)

  const pieces: string[] = []
  const ExpressionAttributeValues: Record<string, any> = {}
  const ExpressionAttributeNames: Record<string, any> = {}

  let keyId = 0
  function traverse(object: Record<string, any>, base = '') {
    const entries = Object.entries(object)
    for (const [key, value] of entries) {
      if (base === '' && (key === hash || key === sort)) continue

      const i = keyId++
      const identifier = `:i_${i}`
      const name = `#i_${i}`

      // You still have to alias segments even if you're updating a nested property, so we always register names
      ExpressionAttributeNames[name] = key

      if (!Array.isArray(value) && typeof value === 'object') {
        traverse(value, base + `${name}.`)
      } else {
        const segment = `${base + name} = ${identifier}`
        ExpressionAttributeValues[identifier] = value
        pieces.push(segment)
      }
    }
  }

  traverse(props)

  return {
    TableName: tableName,
    Key: { [hash]: props[hash], [sort]: props[sort] },
    UpdateExpression: `set ${pieces.join(', ')}`,
    ExpressionAttributeNames,
    ExpressionAttributeValues,
  }
}

export function serializeDeleteExpression(
  table: TargetTable,
  props: Record<string, any>,
  verify = false
): DeleteCommandInput {
  const { tableName, hash, sort } = getCompositeName(table)
  const pieces: string[] = []
  const ExpressionAttributeValues: Record<string, any> = {}
  const ExpressionAttributeNames: Record<string, any> = {}

  let keyId = 0
  function traverse(object: Record<string, any>, base = '') {
    const entries = Object.entries(object)
    for (const [key, value] of entries) {
      if (base === '' && (key === hash || key === sort)) continue

      const i = keyId++
      const identifier = `:i_${i}`
      const name = `#i_${i}`

      // You still have to alias segments even if you're updating a nested property, so we always register names
      ExpressionAttributeNames[name] = key

      if (!Array.isArray(value) && typeof value === 'object') {
        traverse(value, base + `${name}.`)
      } else {
        const segment = `${base + name} = ${identifier}`
        ExpressionAttributeValues[identifier] = value
        pieces.push(segment)
      }
    }
  }

  if (verify) {
    traverse(props)
  }

  return {
    TableName: tableName,
    Key: { [hash]: props[hash], [sort]: props[sort] },
    ExpressionAttributeNames,
    ExpressionAttributeValues,
    ConditionExpression: pieces.join(' AND '),
  }
}

export function writeEntryToTransaction(write: WriteEntry & { remove: true }): WriteEntryDelete
export function writeEntryToTransaction(write: WriteEntry): WriteEntryUpdate
export function writeEntryToTransaction(write: WriteEntry) {
  if (write.schema) {
    write.record = write.schema.parse(write.record)
  }

  const { tableName, hash, sort } = getCompositeName(write.table)

  if (write.remove) {
    return {
      Delete: {
        TableName: tableName,
        Key: {
          [hash]: write.record[hash],
          [sort]: write.record[sort],
        },
      },
    }
  }

  return {
    Put: {
      TableName: tableName,
      Item: write.record,
    },
  }
}

export function writeEntryToBatch(write: WriteEntry) {
  if (write.schema) {
    write.record = write.schema.parse(write.record)
  }

  const { tableName, hash, sort } = getCompositeName(write.table)
  if (write.remove) {
    return {
      TableName: tableName,
      DeleteRequest: {
        Key: {
          [hash]: write.record[hash],
          [sort]: write.record[sort],
        },
      },
    }
  }

  return {
    TableName: tableName,
    PutRequest: {
      Item: write.record,
    },
  }
}
