import { isIterable, isRecord } from '@shared/utils/type-guards'
import type { Condition, FunctionConditions, RecordConditions } from './types'

// TODO: Dotpaths not supported yet, even though DynamoDB does
export function validateConditions<T>(record: T, conditions: RecordConditions<T>): boolean
export function validateConditions<T>(
  record: T,
  conditions: RecordConditions<T>,
  failEarly: false
): { success: boolean; issues: string[] }
export function validateConditions<T>(
  record: T,
  conditions: RecordConditions<T>,
  failEarly = true
) {
  const checks = Object.entries(conditions) as Entries<RecordConditions<T>>
  const issues: string[] = []

  for (const [prop, condition] of checks) {
    if (!condition) continue

    const against = record[prop as keyof T]
    const [comparator, value] = condition
    const validator = conditionImplementations[comparator]
    const result = validator(against, value, record, prop)
    // TODO: Handle non-boolean conditions/function conditions correctly

    if (result === true || typeof result !== 'boolean') continue

    if (failEarly) {
      return false
    }

    issues.push(prop)
  }

  if (failEarly) return true

  return { success: !issues.length, issues }
}

type ReferenceFunc = (value: any, compare: any, record: any, prop: any) => any
// Reference implementations for mocking DynamoDB, ignore test coverage because they are reference implementations, duh
/* istanbul ignore next */
const conditionImplementations: Record<Condition | FunctionConditions, ReferenceFunc> = {
  '<': (a, b) => a < b,
  '<=': (a, b) => a <= b,
  '<>': (a, b) => a !== b, // Strict comparisons because DynamoDB doesn't type cast
  '=': (a, b) => a === b, // Strict comparisons because DynamoDB doesn't type cast
  '>': (a, b) => a > b,
  '>=': (a, b) => a >= b,
  begins_with: (a, b) => String(a).startsWith(String(b)),
  between: (a, b) => a >= b[0] && a <= b[1],
  contains: (a: string | unknown[] | Set<unknown>, b) => {
    if (typeof a === 'string') {
      return b.some((option: string) => a.includes(option))
    }

    if (isIterable(a)) {
      let v
      for (v of a) {
        if (b.includes(v)) return true
      }

      return false
    }

    return false
  },
  in: (a, b) => {
    return b.some((option: unknown) => a === option)
  },
  attribute_exists: (_, b, r) => b in r,
  attribute_not_exists: (_, b, r) => !(b in r),
  attribute_type: (a, b) => {
    // String
    if (b === 'S') return typeof a === 'string'
    // Number
    if (b === 'N') return typeof b === 'number'
    // Blob
    if (b === 'B') return a instanceof Uint8Array
    // Boolean
    if (b === 'BOOL') return typeof a === 'boolean'
    // Null
    if (b === 'NULL') return a === null
    // List
    if (b === 'L') return isIterable(a)
    // Map
    if (b === 'M') return isRecord(a)

    // These are all sets now
    if (!isIterable(a)) return false

    // String Set
    if (b === 'SS') {
      for (const v of a) {
        if (typeof v !== 'string') return false
      }

      return true
    }

    // Number Set
    if (b === 'NS') {
      for (const v of a) {
        if (typeof v !== 'number') return false
      }

      return true
    }

    // String Set
    if (b === 'BS') {
      for (const v of a) {
        if (!(v instanceof Uint8Array)) return false
      }

      return true
    }

    return false
  },
  size: (a) => {
    if (typeof a === 'string') return a.length
    if (a instanceof Uint8Array) return a.byteLength
    if (Array.isArray(a)) return a.length

    return Object.keys(a).length
  },
}
