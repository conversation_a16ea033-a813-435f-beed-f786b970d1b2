import { bespokeConditions } from './bespoke'
import type {
  BespokeCondition,
  Condition,
  ConditionConstraint,
  SimpleFunctionConditions,
} from './types'

export interface SerializedCondition {
  names: Record<string, string>
  values: Record<string, any>
  andExpressions: string[]
  orExpressions: string[]
}

/** Turn an array filter into a set of expressions to OR/AND together in the main query */
export function constructExpressionStrings<T extends unknown[]>(
  filter: ConditionConstraint<T>,
  key: string
) {
  const [condition, value = []] = filter
  const values: Record<string, any> = {}
  const expressions = value.map((val, idx) => {
    const dereferencedAttributeValue = `:${key}_${idx}`
    values[dereferencedAttributeValue] = val
    return `${condition}(#${key}, ${dereferencedAttributeValue})`
  })
  return { values, expressions }
}

export function serializeCondition<T>(
  condition: Condition | SimpleFunctionConditions,
  key: string,
  value: T,
  mode: 'and' | 'or' = 'and'
): SerializedCondition {
  const results: SerializedCondition = {
    names: {},
    values: {},
    andExpressions: [],
    orExpressions: [],
  }

  // Some conditions have their own unique implementation
  const bespoke = condition as BespokeCondition
  if (bespokeConditions[bespoke]) {
    const { expr, names, values } = bespokeConditions[bespoke](key, value as any)

    Object.assign(results.names, names)
    Object.assign(results.values, values)

    if (condition === 'contains') {
      results.orExpressions.push(expr)
    } else {
      results.andExpressions.push(expr)
    }

    return results
  }

  // Array values that aren't bespoke are considered a series of OR matches
  if (Array.isArray(value)) {
    const { expressions, values } = constructExpressionStrings([condition as Condition, value], key)
    Object.assign(results.values, values)
    results.orExpressions.push(...expressions)

    return results
  }

  // If it's just a standard condition, we handle it directly
  results.values[`:${key}`] = value
  results[mode == 'and' ? 'andExpressions' : 'orExpressions'].push(`#${key} ${condition} :${key}`)

  return results
}
