import { between, contains, oneOf } from './bespoke'

describe('DynamoIO Conditions', () => {
  describe('between()', () => {
    it('Generates the correct expression', () => {
      const test = between('range', [1, 10])
      expect(test.expr).toEqual('#range BETWEEN :range_a AND :range_b')
      expect(test.values).toEqual({
        ':range_a': 1,
        ':range_b': 10,
      })
    })
  })

  describe('oneOf()', () => {
    it('Generates the correct expression', () => {
      const test = oneOf('status', ['some', 'enum', 'keys', 'here'])
      expect(test.expr).toEqual('#status IN (:status_0, :status_1, :status_2, :status_3)')
      expect(test.values).toEqual({
        ':status_0': 'some',
        ':status_1': 'enum',
        ':status_2': 'keys',
        ':status_3': 'here',
      })
    })
  })

  describe('contains()', () => {
    it('Generates the correct expression', () => {
      const test = contains('fullName', ['alex', 'jenks'])
      expect(test.expr).toEqual(
        'contains(#fullName, :fullName_0) OR contains(#fullName, :fullName_1)'
      )
      expect(test.values).toEqual({
        ':fullName_0': 'alex',
        ':fullName_1': 'jenks',
      })
    })
  })
})
