export type BespokeCondition = keyof typeof bespokeConditions
export const bespokeConditions = {
  between,
  contains,
  in: oneOf,
  attribute_not_exists: isUndefined,
  attribute_exists: isDefined,
  size,
}

export function between<T extends unknown[]>(key: string, value: T) {
  const minimum = `:${key}_a`
  const maximum = `:${key}_b`

  return {
    // e.g. #score BETWEEN :score_a AND :score_b
    expr: `#${key} BETWEEN ${minimum} AND ${maximum}`,
    names: {},
    values: {
      [minimum]: value[0],
      [maximum]: value[1],
    },
  }
}

// "in" is a reserved keyword
export function oneOf<T extends unknown[]>(key: string, value: T) {
  const keys = Array.from({ length: value.length })
  const values = value.reduce<Record<string, unknown>>((values, value, i) => {
    const valueKey = (keys[i] = `:${key}_${i}`)
    values[valueKey] = value
    return values
  }, {})

  return {
    // e.g. #status IN (:status_0, :status_1, :status_2)
    expr: `#${key} IN (${keys.join(', ')})`,
    names: {},
    values,
  }
}

export function contains<T extends unknown[]>(key: string, value: T) {
  const segments = value.map((_, i) => `contains(#${key}, :${key}_${i})`)
  const values = value.reduce<Record<string, any>>((values, value, i) => {
    values[`:${key}_${i}`] = value
    return values
  }, {})

  return {
    // e.g. contains(#firstName, :firstName_0) OR contains(#firstName, :firstName_1)
    expr: segments.join(' OR '),
    names: { [`#${key}`]: key },
    values,
  }
}

export function isDefined(key: string) {
  return {
    expr: `attribute_exists(#${key})`,
    names: { [`#${key}`]: key },
    values: {},
  }
}

export function isUndefined(key: string) {
  return {
    expr: `attribute_not_exists(#${key})`,
    names: { [`#${key}`]: key },
    values: {},
  }
}

export function size(key: string) {
  return {
    expr: `size(#${key})`,
    names: { [`#${key}`]: key },
    values: {},
  }
}
