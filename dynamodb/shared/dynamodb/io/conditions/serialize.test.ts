import type { Table } from 'dynamodb-onetable'
import type { KnownTables } from '@infra/hummingbird/databases'
import type { Database } from '@infra/lib/databases/types'
import { serializeQuery } from '../read'
import type { TargetTable } from '../types'
import { constructExpressionStrings, serializeCondition } from './serialize'
import type { ConditionConstraint } from './types'

jest.mock('@shared/dynamodb/io/utils/keys', () => ({
  getCompositeName: (identifier: TargetTable) => {
    const table = {
      tableName: '',
      tableArn: 'arn:some:mock:table',
      hash: 'hk',
      sort: 'sk',
      baseHash: 'hk',
    }

    if (identifier === mockTableName) {
      table.tableName = 'mock-table'
    } else if (identifier === mockDatabase) {
      table.tableName = 'mockTable'
    } else if (identifier === mockTable) {
      table.tableName = 'mockTable'
    }

    return table
  },
}))

jest.mock

const mockTableName = 'mock-name' as KnownTables
const mockDatabase = 'mock-db' as unknown as Database
const mockTable = 'mock-table' as unknown as Table

describe('DynamoIO Condition Serialization', () => {
  describe('constructExpressionStrings()', () => {
    it('Serializes a basic expression', () => {
      const filter: ConditionConstraint = ['=', ['hi']]
      expect(constructExpressionStrings(filter, 'key')).toEqual({
        expressions: ['=(#key, :key_0)'],
        values: {
          ':key_0': 'hi',
        },
      })
    })
  })

  describe('serializeCondition()', () => {
    it('Generates a simple expression', () => {
      const result = serializeCondition('<>', 'test', 100)
      expect(result).toEqual({
        andExpressions: ['#test <> :test'],
        names: {},
        orExpressions: [],
        values: {
          ':test': 100,
        },
      })
    })

    it('Generates an OR expression with an array', () => {
      const result = serializeCondition('begins_with', 'test', [1, 2, 3])
      expect(result).toEqual({
        andExpressions: [],
        names: {},
        orExpressions: [
          'begins_with(#test, :test_0)',
          'begins_with(#test, :test_1)',
          'begins_with(#test, :test_2)',
        ],
        values: {
          ':test_0': 1,
          ':test_1': 2,
          ':test_2': 3,
        },
      })
    })

    it('Handles bespoke conditions', () => {
      const result = serializeCondition('in', 'test', [1, 2, 3])
      expect(result).toEqual({
        andExpressions: ['#test IN (:test_0, :test_1, :test_2)'],
        names: {},
        orExpressions: [],
        values: {
          ':test_0': 1,
          ':test_1': 2,
          ':test_2': 3,
        },
      })
    })

    it('Treats CONTAINS differently', () => {
      const result = serializeCondition('contains', 'test', [1, 2, 3])
      expect(result).toEqual({
        andExpressions: [],
        names: {
          '#test': 'test',
        },
        orExpressions: [
          'contains(#test, :test_0) OR contains(#test, :test_1) OR contains(#test, :test_2)',
        ],
        values: {
          ':test_0': 1,
          ':test_1': 2,
          ':test_2': 3,
        },
      })
    })
  })

  /*
    describe("serializeQuery()", () => {
        it('should handle bespokeConditions', () => {
            expect(serializeQuery(mockTable, 'pk', { between: ['=', ['a', 'b']] })).toEqual({
                ExpressionAttributeNames: {
                    '#between': 'between',
                },
                ExpressionAttributeValues: {
                    ':between_0': 'a',
                    ':between_1': 'b',
                    ':hk': 'pk',
                },
                FilterExpression: '=(#between, :between_0) OR =(#between, :between_1)',
                IndexName: undefined,
                KeyConditionExpression: 'hk = :hk',
                TableName: 'mockTable',
                ConsistentRead: false,
            })
        })

        it('should handle multiple values in the filter', () => {
            expect(
                serializeQuery(mockTableName, 'pk', { test: ['=', 'test2'], test2: ['>', 'hi'] })
            ).toEqual({
                ExpressionAttributeNames: {
                    '#test': 'test',
                    '#test2': 'test2',
                },
                ExpressionAttributeValues: {
                    ':hk': 'pk',
                    ':test': 'test2',
                    ':test2': 'hi',
                },
                FilterExpression: '#test = :test AND #test2 > :test2',
                IndexName: undefined,
                KeyConditionExpression: 'hk = :hk',
                TableName: 'mock-table',
                ConsistentRead: false,
            })
        })
        it.each([true, false])('should handle consistentRead else', (consistentRead) => {
            expect(serializeQuery(mockDatabase, 'pk', { test: ['=', 'test2'] }, { consistentRead })).toEqual({
                ExpressionAttributeNames: {
                    '#test': 'test',
                },
                ExpressionAttributeValues: {
                    ':hk': 'pk',
                    ':test': 'test2',
                },
                FilterExpression: '#test = :test',
                IndexName: undefined,
                KeyConditionExpression: 'hk = :hk',
                TableName: 'mockTable',
                ConsistentRead: consistentRead,
            })
        })
        it('should handle anything else', () => {
            expect(serializeQuery(mockTable, 'pk', { test: ['=', 'test2'], test2: ['begins_with', 'X'] })).toEqual({
                ExpressionAttributeNames: {
                    '#test': 'test',
                    '#test2': 'test2',
                },
                ExpressionAttributeValues: {
                    ':hk': 'pk',
                    ':test': 'test2',
                    ':test2': 'X',
                },
                FilterExpression: '#test = :test AND #test2 begins_with :test2',
                IndexName: undefined,
                KeyConditionExpression: 'hk = :hk',
                TableName: 'mockTable',
                ConsistentRead: false,
            })
        })
    });
    */
})
