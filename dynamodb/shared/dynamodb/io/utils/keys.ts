import { getDatabaseFromTable, getTableFromName } from '@infra/hummingbird/databases'
import { isDatabase } from '@infra/lib/databases/utils'
import type { TargetTable } from '../types'

export interface CompositeName {
  tableName: string
  tableArn: string
  hash: string
  sort: string
  baseHash?: string
  baseSort?: string
}

export function getCompositeName(
  table: TargetTable,
  index?: string,
  withBase = false
): CompositeName {
  if (isDatabase(table)) {
    const key = table.getKey(index)!
    return {
      tableName: table.name,
      tableArn: table.arn,
      hash: key.hash,
      sort: key.sort!,
      baseHash: withBase ? table.getKey()!.hash : undefined,
      baseSort: withBase ? table.getKey()!.sort : undefined,
    }
  }

  if (typeof table === 'string') {
    const database = getTableFromName(table)
    const key = database.getKey(index)!

    return {
      tableName: database.name,
      tableArn: database.arn,
      hash: key.hash!,
      sort: key.sort!,
      baseHash: withBase ? database.getKey()!.hash : undefined,
      baseSort: withBase ? database.getKey()!.sort : undefined,
    }
  }

  const schema: any = table.getCurrentSchema() // eslint-disable-line @typescript-eslint/no-explicit-any
  const { hash, sort } = schema.indexes[index || 'primary']

  return {
    tableName: table.name,
    tableArn: getDatabaseFromTable(table).arn,
    hash,
    sort,
    baseHash: withBase ? schema.indexes['primary'].hash : undefined,
    baseSort: withBase ? schema.indexes['primary'].sort : undefined,
  }
}

export function stripPartitionKeys<T>(records: T[], table: TargetTable): T[] {
  if (!records.length) return records

  const { hash, sort } = getCompositeName(table)

  return records.map((record) => {
    const clone = { ...record } as Record<string, unknown>
    delete clone[hash]
    delete clone[sort]

    return clone as T
  })
}

export function getRecordKey(table: TargetTable, record: Record<any, any>, index?: string) {
  const { hash, sort, baseHash, baseSort } = getCompositeName(table, index, true)

  return {
    [baseHash!]: record?.[baseHash!],
    [baseSort!]: record?.[baseSort!],
    [sort]: record?.[sort],
    [hash]: record?.[hash],
  }
}
