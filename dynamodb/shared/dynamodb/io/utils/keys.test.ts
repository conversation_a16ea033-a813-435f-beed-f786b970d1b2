import type { Table } from 'dynamodb-onetable'
import type { KnownTables } from '@infra/hummingbird/databases'
import { makeInfraComponent } from '@infra/lib/common/component'
import { type Database, IS_DATABASE } from '@infra/lib/databases/types'
import { getCompositeName, getRecordKey, stripPartitionKeys } from './keys'

jest.mock('@infra/lib/databases/types', () => ({
  isDatabase: (db: any) => db === mockDatabase,
}))

jest.mock('@infra/hummingbird/databases', () => ({
  getTableFromName: () => mockDatabase,
  getDatabaseFromTable: () => mockDatabase,
}))

const mockTableName = 'mock-table'
const mockTableArn = 'arn:mock:table'
const mockIndex = 'mockIndex'
const otherMockIndex = 'otherMockIndex'

const mockDatabase = makeInfraComponent<Database>('database', mockTableName, mockTableArn, {
  [IS_DATABASE]: true,
  getKey: (index?: string) => {
    if (!index) {
      return {
        hash: 'hk',
        sort: 'sk',
      }
    }

    if (index === otherMockIndex) {
      return {
        hash: 'ihk',
        sort: 'sk',
      }
    }

    return {
      hash: 'ihk',
      sort: 'isk',
    }
  },
})

interface Sample {
  hk: string
  sk: string
  ihk?: string
  someField: string
}

const mockTable = {
  name: mockTableName,
  getCurrentSchema: () => ({
    indexes: {
      primary: {
        hash: 'hk',
        sort: 'sk',
      },
      [mockIndex]: {
        hash: 'ihk',
        sort: 'isk',
      },
      [otherMockIndex]: {
        hash: 'ihk',
        sort: 'sk',
      },
    },
  }),
} as unknown as Table

describe('DynamoIO Utils - Keys', () => {
  describe('getCompositeName()', () => {
    it('Handles a Database', () => {
      const result = getCompositeName(mockDatabase)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'hk',
        sort: 'sk',
      })
    })

    it('Handles a Table', () => {
      const result = getCompositeName(mockTable)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'hk',
        sort: 'sk',
      })
    })

    it('Handles a KnownTable', () => {
      const result = getCompositeName('mockTable' as KnownTables)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'hk',
        sort: 'sk',
      })
    })

    it('Handles a Database with an index', () => {
      const result = getCompositeName(mockDatabase, mockIndex)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
      })
    })

    it('Handles a Table with an index', () => {
      const result = getCompositeName(mockTable, mockIndex)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
      })
    })

    it('Handles a KnownTable with an index', () => {
      const result = getCompositeName('mockTable' as KnownTables, mockIndex)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
      })
    })

    it('Handles a Database including base hash', () => {
      const result = getCompositeName(mockDatabase, mockIndex, true)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
        baseHash: 'hk',
        baseSort: 'sk',
      })
    })

    it('Handles a Table including base hash', () => {
      const result = getCompositeName(mockTable, mockIndex, true)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
        baseHash: 'hk',
        baseSort: 'sk',
      })
    })

    it('Handles a KnownTable including base hash', () => {
      const result = getCompositeName('mockTable' as KnownTables, mockIndex, true)
      expect(result).toEqual({
        tableName: mockTableName,
        tableArn: mockTableArn,
        hash: 'ihk',
        sort: 'isk',
        baseHash: 'hk',
        baseSort: 'sk',
      })
    })
  })

  describe('stripPartitionKeys()', () => {
    it('Does the thing', () => {
      const records: Sample[] = [
        {
          hk: 'hk1',
          sk: 'somesk',
          someField: 'FIELD!',
        },
      ]

      const result = stripPartitionKeys(records, mockDatabase)
      expect(result).toEqual([
        {
          someField: 'FIELD!',
        },
      ])
    })

    it('Skips out early if you have no results', () => {
      const reference: string[] = []
      const result = stripPartitionKeys(reference, mockDatabase)
      expect(result).toBe(reference) // Check strict equality for fail-early cond.
    })
  })

  describe('getRecordKey()', () => {
    it('Does the thing', () => {
      const record: Sample = {
        hk: 'hk1',
        sk: 'somesk',
        ihk: 'otherhash',
        someField: 'FIELD!',
      }

      const result = getRecordKey(mockDatabase, record)
      expect(result).toEqual({
        hk: 'hk1',
        sk: 'somesk',
      })
    })

    it('Does the thin with an index', () => {
      const record: Sample = {
        hk: 'hk1',
        sk: 'somesk',
        ihk: 'otherhash',
        someField: 'FIELD!',
      }

      const result = getRecordKey(mockDatabase, record, mockIndex)
      expect(result).toEqual({
        ihk: 'otherhash',
        hk: 'hk1',
        sk: 'somesk',
      })
    })
  })
})
