/* eslint-disable no-constant-binary-expression */
import { createFilterExpression, processFilters, serializeQuery } from './serialize'
import { CONDITION_OR } from './types'

jest.mock('../utils/keys', () => ({
  getCompositeName: () => ({
    tableName: 'mock-table',
    tableArn: 'arn:mock:table',
    hash: 'hk',
    sort: 'sk',
  }),
}))

describe('DynamoIO Query Serialization', () => {
  afterAll(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  it('Removes falsy stuff', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {
      prop: false && ['=', true],
      // @ts-expect-error I KNOW LEAVE ME ALONE
      prop2: null && ['=', true],
      // @ts-expect-error I KNOW LEAVE ME ALONE
      prop3: undefined && ['=', true],
      prop4: 0 && ['=', true],
      // @ts-expect-error I KNOW LEAVE ME ALONE
      prop5: '' && ['=', true],
      // @ts-expect-error I KNOW LEAVE ME ALONE
      prop6: {}, // Explicitly needs an array to work
    })

    expect(serialized.FilterExpression).toBeUndefined()
  })

  it('Handles AND expressions', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {
      prop1: ['=', 1],
      prop2: ['=', 2],
    })

    expect(serialized.FilterExpression).toEqual('#prop1 = :prop1 AND #prop2 = :prop2')
  })

  it('Handles OR expressions', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {
      prop1: ['contains', ['test']],
      prop2: ['contains', ['test2']],
    })

    expect(serialized.FilterExpression).toEqual(
      'contains(#prop1, :prop1_0) OR contains(#prop2, :prop2_0)'
    )
  })

  it('Handles AND + OR expressions', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {
      prop1: ['contains', ['test']],
      prop2: ['contains', ['test2']],
      prop3: ['=', 1],
    })

    expect(serialized.FilterExpression).toEqual(
      '#prop3 = :prop3 AND (contains(#prop1, :prop1_0) OR contains(#prop2, :prop2_0))'
    )
  })

  it('Handles a scan a little differently', () => {
    const serialized = serializeQuery(
      'itemCloudGreen',
      '',
      {
        prop: ['=', 1],
      },
      { scan: true }
    )

    // Scans can't have a key expression
    expect(serialized).not.toHaveProperty('KeyConditionExpression')
    expect(serialized.ExpressionAttributeValues).not.toHaveProperty(':hk')
  })

  it('Handles barebones scans', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {}, { scan: true })
    expect(serialized).not.toHaveProperty('ExpressionAttributeValues')
    expect(serialized).not.toHaveProperty('FilterExpression')
  })

  it('Handles CONDITION_OR expressions', () => {
    const serialized = serializeQuery('itemCloudGreen', '', {
      prop3: ['=', 1],
      [CONDITION_OR]: {
        prop4: ['=', 'some name'],
        prop5: ['=', 'other name'],
      },
    })

    expect(serialized.FilterExpression).toEqual(
      '#prop3 = :prop3 AND (#prop4 = :prop4 OR #prop5 = :prop5)'
    )
  })
})

describe('processFilters', () => {
  it('process filters', () => {
    const mockFilters = {
      type: ['=', 'AssemblyUnit'],
      courseId: ['=', '402'],
      adminYear: ['=', '26'],
      questionType: ['=', 'MULTIPLE_CHOICE_QUESTION'],
      questionTypeNumber: ['=', 0],
      baseForm: ['=', 'F0'],
      [CONDITION_OR]: {
        name: ['=', 'AU26_BIO_F0MC00_S0_P006'],
        label: ['=', 'AU26_BIO_F0MC00_S0_P006'],
      },
    }

    const mode = 'and'

    const mockResponse = {
      andExpressions: [
        '#type = :type',
        '#courseId = :courseId',
        '#adminYear = :adminYear',
        '#questionType = :questionType',
        '#questionTypeNumber = :questionTypeNumber',
        '#baseForm = :baseForm',
      ],
      names: {
        '#adminYear': 'adminYear',
        '#baseForm': 'baseForm',
        '#courseId': 'courseId',
        '#label': 'label',
        '#name': 'name',
        '#questionType': 'questionType',
        '#questionTypeNumber': 'questionTypeNumber',
        '#type': 'type',
      },
      orExpressions: ['#name = :name OR #label = :label'],
      values: {
        ':adminYear': '26',
        ':baseForm': 'F0',
        ':courseId': '402',
        ':label': 'AU26_BIO_F0MC00_S0_P006',
        ':name': 'AU26_BIO_F0MC00_S0_P006',
        ':questionType': 'MULTIPLE_CHOICE_QUESTION',
        ':questionTypeNumber': 0,
        ':type': 'AssemblyUnit',
      },
    }

    expect(processFilters(mockFilters, mode)).toStrictEqual(mockResponse)
  })
})

describe('createFilterExpression', () => {
  it('create filter expression', () => {
    const mockConditions = {
      names: {
        '#type': 'type',
        '#courseId': 'courseId',
        '#adminYear': 'adminYear',
        '#questionType': 'questionType',
        '#questionTypeNumber': 'questionTypeNumber',
        '#baseForm': 'baseForm',
      },
      values: {
        ':assemblyUnitType': '3CC6A',
        ':type': 'AssemblyUnit',
        ':courseId': '402',
        ':adminYear': '26',
        ':questionType': 'MULTIPLE_CHOICE_QUESTION',
        ':questionTypeNumber': 0,
        ':baseForm': 'F0',
      },
      andExpressions: [
        '#type = :type',
        '#courseId = :courseId',
        '#adminYear = :adminYear',
        '#questionType = :questionType',
        '#questionTypeNumber = :questionTypeNumber',
        '#baseForm = :baseForm',
      ],
      orExpressions: [],
    }

    const mockResponse =
      '#type = :type AND #courseId = :courseId AND #adminYear = :adminYear AND #questionType = :questionType AND #questionTypeNumber = :questionTypeNumber AND #baseForm = :baseForm'

    expect(createFilterExpression(mockConditions)).toStrictEqual(mockResponse)
  })
})
