import type { TargetTable } from '../types'
import { augmentResults, omitKeysFromRecord, stripPartitionKeyFromRecord } from './augment'

jest.mock('../utils/keys', () => ({
  getCompositeName: () => ({
    tableName: 'mock-table',
    tableArn: 'arn:mock:table',
    hash: 'hk',
    sort: 'sk',
  }),
}))

const samples = Array.from({ length: 20 }).map((_, i) => ({
  id: i + 1,
  name: `Sample ${i + 1}`,
}))

describe('DynamoIO Augmentation', () => {
  describe('augmentResults()', () => {
    it('Runs through every record', () => {
      const augmenter = jest.fn().mockImplementation((x) => x)
      const results = augmentResults(samples, [augmenter])

      expect(results).toEqual(samples)
      expect(augmenter).toHaveBeenCalledTimes(samples.length)
    })

    it('Works with multiple augmenters', () => {
      const augmenter1 = jest.fn().mockImplementation((x) => x)
      const augmenter2 = jest.fn().mockImplementation((x) => x)
      const results = augmentResults(samples, [augmenter1, augmenter2])

      expect(results).toEqual(samples)
      expect(augmenter1).toHaveBeenCalledTimes(samples.length)
      expect(augmenter2).toHaveBeenCalledTimes(samples.length)
    })

    it('Chains transformations in sequence', () => {
      const augmenter1 = jest.fn().mockImplementation((x) => ({ ...x, newProp: `Hi ${x.id}!` }))
      const augmenter2 = jest.fn().mockImplementation((x) => x.newProp)
      const results = augmentResults(samples, [augmenter1, augmenter2])
      const expectation = samples.map((_, i) => `Hi ${i + 1}!`)
      expect(results).toEqual(expectation)
    })
  })

  describe('stripPartitionKeyFromRecord()', () => {
    it('Removes partition keys', () => {
      const reference = {
        hk: 'test-hk',
        sk: 'test-sk',
        other: 'prop',
      }

      const sample = { ...reference }

      const augmenter = stripPartitionKeyFromRecord('mock-table' as TargetTable)
      const result = augmenter(sample, 0)

      // Do not mutate, clone
      expect(sample).toEqual(reference)
      expect(result).toEqual({ other: 'prop' })
    })
  })

  describe('omitKeysFromRecord()', () => {
    it('Removes the correct keys', () => {
      const reference = {
        hk: 'test-hk',
        sk: 'test-sk',
        other: 'prop',
      }

      const sample = { ...reference }

      const augmenter = omitKeysFromRecord(['sk'])
      const result = augmenter(sample, 0)

      // Do not mutate, clone
      expect(sample).toEqual(reference)
      expect(result).toEqual({ hk: 'test-hk', other: 'prop' })
    })

    // Important due to the use of Function()
    it('Handles weird props', () => {
      const awkward = '"]; throw new Error("BOOM!")'
      const problematic = {
        [awkward]: 'haha',
      }

      const augmenter = omitKeysFromRecord([awkward])
      const result = augmenter(problematic, 0)

      expect(result).toEqual({})
    })
  })
})
