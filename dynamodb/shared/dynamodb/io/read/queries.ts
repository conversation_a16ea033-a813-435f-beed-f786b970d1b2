// Create a standard function so you can find all queries by Ctrl + Shift + F, "readOne<your model here>"

import {
  BatchGetCommand,
  type BatchGetCommandOutput,
  GetCommand,
  QueryCommand,
  ScanCommand,
} from '@aws-sdk/lib-dynamodb'
import { getDocumentClient } from '@infra/hummingbird/databases/client'
import chunk from '@shared/utils/chunk'
import type { RecordConditions } from '../conditions/types'
import type { TargetTable } from '../types'
import { getCompositeName, getRecordKey } from '../utils/keys'
import {
  type Augmentation,
  augmentResults,
  omitKeysFromRecord,
  stripPartitionKeyFromRecord,
} from './augment'
import { serializeQuery } from './serialize'
import type {
  AttributeArgs,
  DirectRead,
  IndexArgs,
  Paginated,
  PaginatedArgs,
  QueryPaginatedArgs,
  Reduce,
} from './types'

// TODO: Get will error if key not found, whereas query will return zero results! Should we query all the time?
export async function readOne<T>(
  table: TargetTable,
  partition: string,
  sortKey?: string | number | undefined,
  params: IndexArgs = {},
  filters?: RecordConditions<T>,
  opts?: {
    ProjectionExpression?: string
    ExpressionAttributeNames?: Record<string, string>
  }
) {
  const documentClient = getDocumentClient()

  // A simple get on the table without a GSI
  if (!params.index) {
    const { tableName, hash, sort } = getCompositeName(table, params.index)
    const hasSortKey = sort && sortKey !== undefined

    const command = new GetCommand({
      TableName: tableName,
      Key: {
        [hash]: partition,
        ...(hasSortKey ? { [sort]: sortKey } : {}),
      },
      ...(opts || {}),
    })

    const query = await documentClient.send(command)
    return query.Item as T | undefined
  }

  const { sort } = getCompositeName(table, params.index)
  const filter = serializeQuery(
    table,
    partition,
    {
      ...filters,
      [sort]: sortKey !== undefined && ['=', sortKey],
    },
    {
      index: params.index,
      consistentRead: params.consistentRead,
    }
  )

  // Can't GetItem on a GSI so we do a very specific query instead :(
  const command = new QueryCommand(filter)
  const query = await documentClient.send(command)
  return query.Items?.[0] as T | undefined
}

// TODO: Is it okay to recurse without an upper bound?
export async function readUnprocessedKeys<T>({
  UnprocessedKeys,
}: Required<Pick<BatchGetCommandOutput, 'UnprocessedKeys'>>) {
  const documentClient = getDocumentClient()
  console.debug(`Attempting to read the following UnprocessedKeys:`, UnprocessedKeys)
  const results: T[] = []
  const result = await documentClient.send(new BatchGetCommand({ RequestItems: UnprocessedKeys }))

  if (result.Responses) results.push(...(Object.values(result.Responses).flat() as T[]))
  if (result.UnprocessedKeys && Object.keys(result.UnprocessedKeys).length)
    results.push(...(await readUnprocessedKeys<T>({ UnprocessedKeys: result.UnprocessedKeys })))
  return results
}

export async function readMany<T>(records: Array<DirectRead>, params: AttributeArgs = {}) {
  if (!records.length) return []

  if (params.limit && params.limit > 100) {
    throw new Error('You made a mistake in your readMany limit, try again.')
  }

  const documentClient = getDocumentClient()
  const chunks = chunk(records, params?.limit || 50)
  const results: T[] = []

  let _page = 0
  for (const page of chunks) {
    const pageId = _page++

    const parts = page.reduce((query, { table, pk, sk }) => {
      const { tableName, hash, sort } = getCompositeName(table)

      if (!query[tableName]) {
        query[tableName] = {
          Keys: [],
          AttributesToGet: params.attributes,
        }
      }

      query[tableName].Keys.push({
        [hash]: pk,
        [sort]: sk,
      })

      return query
    }, {} as Record<string, any>)

    const command = new BatchGetCommand({ RequestItems: parts })
    const result = await documentClient.send(command)

    if (!result.Responses) {
      console.debug(
        `Skipped page ${pageId}/${chunks.length} due to being empty or problematic`,
        result.UnprocessedKeys
      )
      continue
    }

    results.push(...(Object.values(result.Responses).flat() as T[]))

    if (result.UnprocessedKeys && Object.keys(result.UnprocessedKeys).length) {
      console.debug(
        `Read ${results.length} items from page ${pageId}/${chunks.length} leaving ${
          Object.keys(result.UnprocessedKeys).length
        } unprocessed keys`
      )
      const resultOfUnprocessedKeys = await readUnprocessedKeys<T>({
        UnprocessedKeys: result.UnprocessedKeys,
      })
      results.push(...resultOfUnprocessedKeys)
    }
  }

  return results
}

export const DEFAULT_PAGE_SIZE = 25
export const DEFAULT_LARGE_PAGE_SIZE = 100

// TODO: Take a table name instead of a table
export function queryPaginated<T = unknown, R = unknown>(
  table: TargetTable,
  key: string,
  filters: RecordConditions<T> | RecordConditions<T>[],
  options?: PaginatedArgs<T, R> & IndexArgs & QueryPaginatedArgs & { reduce?: never }
): Promise<Paginated<T>>

export function queryPaginated<T = unknown, R = unknown>(
  table: TargetTable,
  key: string,
  filters: RecordConditions<T> | RecordConditions<T>[],
  options?: PaginatedArgs<T, R> & IndexArgs & QueryPaginatedArgs & { reduce: Reduce<T, R> }
): Promise<R>

// Queries and paginates on the number of RESULTS, not the number of scanned objects
export async function queryPaginated<T = unknown, R = unknown>(
  table: TargetTable, // TODO: Eventually just use table names? - ajenks
  key: string,
  filters: RecordConditions<T> | RecordConditions<T>[],
  {
    limit = 100,
    next,
    props,
    omit,
    index,
    sort,
    augment = [],
    filter,
    reduce,
    postSort,
    scan = false,
    softValidate,
    stripPartitionKeys: stripKeys = false,
    smallRecords = false,
  }: PaginatedArgs<T, R> & IndexArgs & QueryPaginatedArgs = {}
) {
  const documentClient = getDocumentClient()
  const serialized = serializeQuery(table, key, filters, { index, scan })
  let results = [] as Paginated<T>
  let failed: T[]

  const CommandClass = scan ? ScanCommand : QueryCommand
  // Define a query limit independent of result limit so you aren't sending dozens of queries to find one thing
  const queryLimit =
    typeof limit === 'string' ? (smallRecords ? DEFAULT_LARGE_PAGE_SIZE : DEFAULT_PAGE_SIZE) : limit

  async function more(): Promise<number | undefined> {
    const command = new CommandClass({
      ...serialized,
      ExclusiveStartKey: next,
      Limit: queryLimit,
      ScanIndexForward: sort === 'ascending', // true for asc, false for desc
      ProjectionExpression: props?.join(', '),
    })

    const query = await documentClient.send(command)
    const rawResults = (query.Items || []) as T[]

    next = query.LastEvaluatedKey

    if (!rawResults.length && !next) return // No results whatsoever

    let processedResults = rawResults
    if (softValidate) {
      processedResults = processedResults.reduce((aggregate, record) => {
        const result = softValidate.safeParse(record)

        if (result.success) {
          aggregate.push(result.data as T)
        } else {
          ;(failed ??= []).push(record)
          const errors = result.error.errors.map((zodIssue) => JSON.stringify(zodIssue))
          console.debug(`Failed to soft-validate record:`, errors)
        }

        return aggregate
      }, [] as T[])
    }

    if (filter) {
      const found = processedResults.filter(filter)
      if (!found.length) {
        if (next) return more()

        return
      }

      if (limit === 'one') {
        next = getRecordKey(table, found[1] ?? {}, index)
        return results.push(found[0]!)
      }

      results.push(...found)
    } else {
      if (limit === 'one') {
        next = getRecordKey(table, processedResults[1] ?? {}, index)
        return results.push(processedResults[0]!)
      }

      results.push(...processedResults)
    }

    if (typeof limit === 'number' && results.length >= limit) {
      const beginCutoff = results.at(limit - 1)
      results.length = limit // Chop off the accidental extras from query pagination vs result pagination, oops!
      // TODO: Would it matter that you can get more than the limit? - ajenks
      next = getRecordKey(table, beginCutoff ?? {}, index)
      return
    }

    // 'none' limit is passthrough
    if (!next) return

    return more()
  }

  await more()

  if (postSort) {
    results = results.sort(postSort)
  }

  results = augmentResults<T>(
    results,
    [
      stripKeys && stripPartitionKeyFromRecord<T>(table),
      omit && omitKeysFromRecord<T>(omit),
      ...augment,
    ].filter(Boolean) as Augmentation<T>[]
  )

  if (reduce) {
    return results.reduce((a, b) => reduce.each(a, b), reduce.initial)
  }

  results.next = next
  // @ts-expect-error This is a valid pattern
  results.failed = failed
  return results
}

/** @deprecated Alias for {@link queryPaginated} */
export const queryTable = queryPaginated
