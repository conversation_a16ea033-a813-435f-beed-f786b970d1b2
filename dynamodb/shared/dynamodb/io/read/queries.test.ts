import { z } from 'zod'
import { <PERSON>chGetCommand, GetCommand, QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb'
import { getCompositeName } from '@shared/dynamo-hummingbird/tables/io/io'
import { mockManyComments } from '@shared/trpc-lambda/mocks'
import {
  DEFAULT_PAGE_SIZE,
  queryPaginated,
  readMany,
  readOne,
  readUnprocessedKeys,
} from './queries'
import type { DirectRead } from './types'

const clientMock = jest.fn()
jest.mock('@infra/hummingbird/databases/client', () => ({
  getDocumentClient: () => ({
    send: clientMock,
  }),
}))

const getRecordKeyMock = jest.fn()
jest.mock('../utils/keys', () => ({
  getCompositeName: () => ({
    tableName: 'mock-table',
    tableArn: 'arn:mock:table',
    hash: 'hk',
    sort: 'sk',
  }),
  // @ts-expect-error
  getRecordKey: (...args) => getRecordKeyMock(...args),
}))

describe('DynamoIO Queries', () => {
  afterAll(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  afterEach(() => {
    clientMock.mockClear()
    getRecordKeyMock.mockClear()
  })

  describe('readOne()', () => {
    it('Does a GetCommand when no index is supplied', async () => {
      let _command: any
      const result = +new Date()
      clientMock.mockImplementation((command) => {
        _command = command
        return Promise.resolve({
          Item: result,
        })
      })

      // Handle default {} when sort key is missing, because some tables don't have a compound key
      await expect(readOne('itemCloudGreen', 'hash')).resolves.toEqual(result)
      await expect(_command instanceof GetCommand).toEqual(true)

      await expect(readOne('itemCloudGreen', 'hash', 'sort')).resolves.toEqual(result)
      await expect(_command instanceof GetCommand).toEqual(true)
    })

    it('Does a QueryCommand when an index is supplied', async () => {
      let _command: any
      const result = +new Date()
      clientMock.mockImplementationOnce((command) => {
        _command = command
        return Promise.resolve({
          Items: [result],
        })
      })

      await expect(
        readOne('itemCloudGreen', 'key', '', {
          index: 'someIndex',
        })
      ).resolves.toEqual(result)
      await expect(_command instanceof QueryCommand).toEqual(true)
    })
  })

  describe('readUnprocessedKeys()', () => {
    it('Will attempt to BatchGet some results', async () => {
      const record = { hk: 'hk', sk: 'sk' }
      clientMock.mockResolvedValueOnce({
        Responses: {
          'mock-table': [record],
        },
      })

      await expect(
        readUnprocessedKeys({
          UnprocessedKeys: {
            'mock-table': {
              Keys: [{ hk: 'hk', sk: 'sk' }],
            },
          },
        })
      ).resolves.toEqual([record])
    })

    it('Will recurse if problems persist', async () => {
      const record = { hk: 'hk', sk: 'sk' }
      // First time, it still has a problem
      clientMock.mockResolvedValueOnce({
        Responses: {},
        UnprocessedKeys: {
          'mock-table': {
            Keys: [record],
          },
        },
      })

      // Second time it'll succeeded
      clientMock.mockResolvedValueOnce({
        Responses: {
          'mock-table': [record],
        },
      })

      await expect(
        readUnprocessedKeys({
          UnprocessedKeys: {
            'mock-table': {
              Keys: [record],
            },
          },
        })
      ).resolves.toEqual([record])
      await expect(clientMock).toHaveBeenCalledTimes(2)
    })
  })

  describe('readMany()', () => {
    it('Will give up early if you put in weird params', async () => {
      await expect(readMany([])).resolves.toEqual([])
      expect(clientMock).toHaveBeenCalledTimes(0)

      await expect(readMany([{} as DirectRead], { limit: 101 as any })).rejects.toThrowError()
      expect(clientMock).toHaveBeenCalledTimes(0)
    })

    it('Will attempt to BatchGet', async () => {
      let _command: any
      clientMock.mockImplementation((command) => {
        _command = command
        return Promise.resolve({
          Responses: {
            'mock-table': [{}],
          },
        })
      })

      const reads: DirectRead[] = [
        {
          table: 'itemCloudGreen',
          pk: 'hk',
          sk: 'sk',
        },
      ]
      await expect(readMany(reads)).resolves.toEqual([{}])
      await expect(_command instanceof BatchGetCommand).toEqual(true)
      expect(clientMock).toHaveBeenCalledTimes(1)
    })

    it('Will account for unprocessed keys', async () => {
      let _command: any
      clientMock.mockResolvedValueOnce({
        Responses: {},
        UnprocessedKeys: {
          'mock-table': {
            Keys: [{ hk: 'hk', sk: 'sk' }],
          },
        },
      })
      clientMock.mockImplementationOnce((command) => {
        _command = command
        return Promise.resolve({
          Responses: {
            'mock-table': [{}],
          },
        })
      })

      const reads: DirectRead[] = [
        {
          table: 'itemCloudGreen',
          pk: 'hk',
          sk: 'sk',
        },
      ]

      await expect(readMany(reads)).resolves.toEqual([{}])
      // This is specifically checking for the unprocessed batchget
      await expect(_command instanceof BatchGetCommand).toEqual(true)
      // First is from readMany, second from readUnprocessedKeys
      expect(clientMock).toHaveBeenCalledTimes(2)
    })

    it('Will skip a weird page', async () => {
      clientMock.mockResolvedValueOnce({
        UnprocessedKeys: {
          'mock-table': {
            Keys: [{ hk: 'hk', sk: 'sk' }],
          },
        },
      })

      clientMock.mockResolvedValueOnce({
        Responses: {
          'mock-table': [],
        },
      })

      const reads: DirectRead[] = [
        {
          table: 'itemCloudGreen',
          pk: 'hk',
          sk: 'sk',
        },
        {
          table: 'itemCloudGreen',
          pk: 'hk',
          sk: 'sk',
        },
      ]

      // @ts-expect-error I KNOW
      await expect(readMany(reads, { limit: 1 })).resolves.toEqual([])
      // This is specifically checking for the unprocessed batchget
      // First is from readMany, second from readUnprocessedKeys
      expect(clientMock).toHaveBeenCalledTimes(2)
    })
  })

  describe('queryPaginated()', () => {
    it('Has good safety nets', async () => {
      clientMock.mockResolvedValueOnce({})

      const result = await queryPaginated<number>('itemCloudGreen', 'key', {}, {})
      expect(result).toEqual([])
    })

    it('Will do a basic query', async () => {
      let _command: any
      clientMock.mockImplementation((command) => {
        _command = command
        return Promise.resolve({
          Items: [{}],
        })
      })

      await expect(queryPaginated('itemCloudGreen', 'key', {}, {})).resolves.toEqual([{}])
      await expect(_command instanceof QueryCommand).toEqual(true)
      await expect(clientMock).toHaveBeenCalledTimes(1)
    })

    it("Will handle a 'next'", async () => {
      clientMock.mockResolvedValueOnce({
        Items: [{}],
        LastEvaluatedKey: {},
      })

      clientMock.mockResolvedValueOnce({
        Items: [{}],
      })

      await expect(queryPaginated('itemCloudGreen', 'key', {})).resolves.toEqual([{}, {}])
      await expect(clientMock).toHaveBeenCalledTimes(2)
    })

    it("Will handle a 'next' with a limit using actual keys implementations", () => {
      jest.isolateModules(() => {
        jest.unmock('../utils/keys')

        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const keys = require('../utils/keys')
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { queryPaginated } = require('./queries')

        expect(typeof keys.getCompositeName).toBe('function')
        expect(typeof keys.getRecordKey).toBe('function')

        clientMock
          .mockResolvedValueOnce({
            Items: [...mockManyComments.slice(0, 3)],
            LastEvaluatedKey: { dummyKey: 'page1' },
          })
          .mockResolvedValueOnce({
            Items: [mockManyComments[3]],
            LastEvaluatedKey: undefined,
          })

        queryPaginated('authoring', 'key', {}, { limit: 3 }).then((result: any) => {
          expect([result[0], result[1], result[2]]).toEqual([...mockManyComments.slice(0, 3)])
          expect(result.next).toStrictEqual({
            pk: 'Asset#FRQ000000',
            sk: 'Comment#01JQ4E5TVVY3RCFFFQPB5N4166',
          })
          expect(clientMock).toHaveBeenCalledTimes(1)
          clientMock.mockReset()
        })
      })
    })

    it('Will correctly handle ScanIndexForward', async () => {
      let _command: any
      clientMock.mockImplementation((command) => {
        _command = command
        return Promise.resolve({
          Items: [{}],
        })
      })

      await expect(
        queryPaginated(
          'itemCloudGreen',
          'key',
          {},
          {
            sort: 'ascending',
          }
        )
      ).resolves.toEqual([{}])
      await expect(_command.input.ScanIndexForward).toEqual(true)

      await expect(
        queryPaginated(
          'itemCloudGreen',
          'key',
          {},
          {
            sort: 'descending',
          }
        )
      ).resolves.toEqual([{}])
      await expect(_command.input.ScanIndexForward).toEqual(false)
    })

    describe('Flags', () => {
      it('smallRecords', async () => {
        let _command: any
        clientMock.mockImplementation((command) => {
          _command = command
          return Promise.resolve({
            Items: [{}],
          })
        })

        await expect(
          queryPaginated(
            'itemCloudGreen',
            'key',
            {},
            {
              limit: 'none',
            }
          )
        ).resolves.toEqual([{}])
        await expect(_command.input.Limit).toEqual(25)

        await expect(
          queryPaginated(
            'itemCloudGreen',
            'key',
            {},
            {
              limit: 'none',
              smallRecords: true,
            }
          )
        ).resolves.toEqual([{}])
        await expect(_command.input.Limit).toEqual(100)
      })

      it('stripPartitionKeys', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [{ hk: 'hk', sk: 'sk', other: 'prop' }],
        })

        await expect(
          queryPaginated(
            'itemCloudGreen',
            'key',
            {},
            {
              stripPartitionKeys: true,
            }
          )
        ).resolves.toEqual([{ other: 'prop' }])
      })

      it('softValidate', async () => {
        type Sample = z.infer<typeof sampleSchema>
        const sampleSchema = z.object({
          hk: z.string(),
          sk: z.string(),
          test: z.number(),
        })

        const goodRecord: Sample = {
          hk: 'hk',
          sk: 'sk',
          test: 1234,
        }

        const badRecord = {
          hk: 'hk',
          sk: 'sk',
          test: '1234',
        }

        clientMock.mockResolvedValueOnce({
          Items: [goodRecord, badRecord],
        })

        const result = await queryPaginated(
          'itemCloudGreen',
          'key',
          {},
          {
            softValidate: sampleSchema,
          }
        )

        expect([...result]).toEqual([goodRecord])
        expect(result).toHaveProperty('failed')
      })

      it('scan', async () => {
        let _command: any
        clientMock.mockImplementation((command) => {
          _command = command
          return Promise.resolve({
            Items: [{}],
          })
        })

        await queryPaginated(
          'itemCloudGreen',
          'key',
          {},
          {
            scan: true,
          }
        )
        expect(_command instanceof ScanCommand).toEqual(true)
      })

      it('postSort', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, 4, 5],
        })

        const result = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            postSort: (a, b) => b - a,
          }
        )

        expect(result).toEqual([5, 4, 3, 2, 1])
      })

      it('reduce', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, 4, 5],
        })

        const result = await queryPaginated<number, number>(
          'itemCloudGreen',
          'key',
          {},
          {
            reduce: {
              initial: 0,
              each: (sum, num) => sum + num,
            },
          }
        )

        expect(result).toEqual(15)
      })

      it('augment', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, 4, 5],
        })

        const result = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            augment: [(x) => x * 2],
          }
        )

        expect(result).toEqual([2, 4, 6, 8, 10])
      })

      it('omit', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [{ some: 'attributes', prop: 'gone' }],
        })

        const result = await queryPaginated(
          'itemCloudGreen',
          'key',
          {},
          {
            omit: ['prop'],
          }
        )

        expect(result).toEqual([{ some: 'attributes' }])
      })
    })

    describe('Filters', () => {
      it('DynamoDB filters', async () => {
        let _command: any
        clientMock.mockImplementation((command) => {
          _command = command
          return Promise.resolve({
            Items: [{}],
          })
        })

        await queryPaginated(
          'itemCloudGreen',
          'key',
          {
            prop: ['=', 10],
          },
          {}
        )

        expect(_command.input).toMatchObject({
          ExpressionAttributeNames: { '#prop': 'prop' },
          ExpressionAttributeValues: { ':hk': 'key', ':prop': 10 },
        })
      })

      it('Code filters', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, 4, 5],
        })

        await expect(
          queryPaginated<number>(
            'itemCloudGreen',
            'key',
            {},
            {
              filter: (x) => x < 4,
            }
          )
        ).resolves.toEqual([1, 2, 3])
      })

      it('Paginates with code filter and odd results', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2],
          LastEvaluatedKey: {},
        })

        clientMock.mockResolvedValueOnce({
          Items: [3, 4],
          LastEvaluatedKey: {},
        })

        clientMock.mockResolvedValueOnce({
          Items: [5],
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 4,
            filter: (x) => x < 4,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(3)
        expect(results).toEqual([1, 2, 3])
      })

      // Scenario:
      // Limit is 25
      // Dynamo query gives 33 results + next
      // Code filter gives 19 results
      // Does another query to see if you can get to 25 results
      it('Paginates with soft validation and odd results', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2],
          LastEvaluatedKey: {},
        })

        clientMock.mockResolvedValueOnce({
          Items: [3, 4],
          LastEvaluatedKey: {},
        })

        clientMock.mockResolvedValueOnce({
          Items: [5],
        })

        const schema = z.number().max(3)
        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            softValidate: schema as any,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(3)
        expect([...results]).toEqual([1, 2, 3])
        expect(results).toHaveProperty('failed')
      })

      it('Paginates with code filter with no initial results', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2],
          LastEvaluatedKey: {},
        })

        clientMock.mockResolvedValueOnce({
          Items: [3, 4],
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 4,
            filter: (x) => x > 3,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(2)
        expect(results).toEqual([4])
      })

      it('Code filters with limit "one"', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, 4, null],
          LastEvaluatedKey: {},
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 'one',
            filter: (x) => x > 3,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(1)
        expect(results).toEqual([4])
      })
    })

    describe('Limits', () => {
      it('Small number', async () => {
        clientMock.mockResolvedValueOnce({
          Items: [1, 2, 3, null, 5, 6, 7, 8, 9, 10],
          LastEvaluatedKey: {}, // Simulate next page
        })

        getRecordKeyMock.mockReturnValueOnce({
          next: 'key',
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 3,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(1)
        expect(getRecordKeyMock).toHaveBeenCalledTimes(1)
        expect([...results]).toEqual([1, 2, 3])
        expect(results.next).toEqual({ next: 'key' })
      })

      it('Big number', async () => {
        const items = Array.from({ length: 500 }).map((_, i) => i)

        clientMock.mockResolvedValueOnce({
          Items: items.slice(0, 250),
          LastEvaluatedKey: {}, // Simulate next page
        })

        clientMock.mockResolvedValueOnce({
          Items: items.slice(250),
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 255,
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(2)
        expect([...results]).toEqual(items.slice(0, 255))
      })

      describe('one', () => {
        it('Gives a single result when one is found', async () => {
          clientMock.mockResolvedValueOnce({
            Items: [1],
          })

          const results = await queryPaginated<number>(
            'itemCloudGreen',
            'key',
            {},
            {
              limit: 'one',
            }
          )

          expect(clientMock).toHaveBeenCalledTimes(1)
          expect([...results]).toEqual([1])
        })

        it('Gives the first result when multiple are found', async () => {
          clientMock.mockResolvedValueOnce({
            Items: [1, 2, 3, 4, 5],
          })

          getRecordKeyMock.mockReturnValueOnce({ next: 'key' })

          const results = await queryPaginated<number>(
            'itemCloudGreen',
            'key',
            {},
            {
              limit: 'one',
            }
          )

          expect(clientMock).toHaveBeenCalledTimes(1)
          expect(getRecordKeyMock).toHaveBeenCalledTimes(1)
          expect([...results]).toEqual([1])
          expect(results.next).toEqual({ next: 'key' })
        })
      })

      it('none', async () => {
        const items = Array.from({ length: DEFAULT_PAGE_SIZE * 2 + 3 }).map((_, i) => i)

        clientMock.mockResolvedValueOnce({
          Items: items.slice(0, DEFAULT_PAGE_SIZE),
          LastEvaluatedKey: {}, // Simulate next page
        })

        clientMock.mockResolvedValueOnce({
          Items: items.slice(DEFAULT_PAGE_SIZE, DEFAULT_PAGE_SIZE * 2),
          LastEvaluatedKey: {}, // Simulate next page
        })

        clientMock.mockResolvedValueOnce({
          Items: items.slice(DEFAULT_PAGE_SIZE * 2),
        })

        const results = await queryPaginated<number>(
          'itemCloudGreen',
          'key',
          {},
          {
            limit: 'none',
          }
        )

        expect(clientMock).toHaveBeenCalledTimes(3)
        expect([...results]).toEqual(items)
      })
    })
  })
})
