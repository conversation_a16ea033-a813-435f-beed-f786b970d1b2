import type { QueryCommandInput, ScanCommandInput } from '@aws-sdk/client-dynamodb'
import {
  type ConditionConstraint,
  type RecordConditions,
  type SerializedCondition,
  serializeCondition,
} from '../conditions'
import type { TargetTable } from '../types'
import { getCompositeName } from '../utils/keys'
import { CONDITION_OR, type IndexArgs, type QueryPaginatedArgs } from './types'

export function processFilters(filters: RecordConditions, mode: 'and' | 'or' = 'and') {
  const conditions: SerializedCondition = {
    names: {},
    values: {},
    andExpressions: [],
    orExpressions: [],
  }

  if (CONDITION_OR in filters) {
    const append = processFilters(filters[CONDITION_OR], 'or')
    Object.assign(conditions.names, append.names)
    Object.assign(conditions.values, append.values)
    const serialized = createFilterExpression(append)
    conditions.orExpressions.push(serialized)
  }

  const keys = Object.keys(filters) as (keyof RecordConditions)[]

  for (const key of keys) {
    const filter = filters[key]
    // Filter out all the falsy stuff
    if (!filter || !Array.isArray(filter)) continue

    const [condition, value] = filter as ConditionConstraint
    conditions.names[`#${key}`] = key

    const serialized = serializeCondition(condition, key, value, mode)
    Object.assign(conditions.names, serialized.names)
    Object.assign(conditions.values, serialized.values)

    if (serialized.andExpressions.length) {
      conditions.andExpressions.push(...serialized.andExpressions)
    }

    if (serialized.orExpressions.length) {
      conditions.orExpressions.push(...serialized.orExpressions)
    }
  }

  return conditions
}

export function createFilterExpression(conditions: SerializedCondition) {
  let FilterExpression = conditions.andExpressions?.join(' AND ')

  if (conditions.orExpressions?.length) {
    const orExpressions = conditions.orExpressions?.join(' OR ')

    if (FilterExpression) {
      FilterExpression = FilterExpression?.concat(` AND (${orExpressions})`)
    } else {
      FilterExpression = FilterExpression?.concat(orExpressions)
    }
  }

  return FilterExpression
}

// TODO: OR, CONTAINS, SIZE, EXISTS, BETWEEN - ajenks
export function serializeQuery(
  table: TargetTable,
  key: string,
  filters: RecordConditions | RecordConditions[],
  params: IndexArgs & QueryPaginatedArgs = {}
): QueryCommandInput | ScanCommandInput {
  const { tableName, hash } = getCompositeName(table, params.index)
  const conditions: SerializedCondition = {
    names: {},
    values: {
      [`:${hash}`]: key,
    },
    andExpressions: [],
    orExpressions: [],
  }

  if (Array.isArray(filters)) {
    for (const group of filters) {
      const append = processFilters(group)

      Object.assign(conditions.names, append.names)
      Object.assign(conditions.values, append.values)
      const serialized = createFilterExpression(append)
      conditions.orExpressions.push(serialized)
    }
  } else {
    const append = processFilters(filters)
    Object.assign(conditions.names, append.names)
    Object.assign(conditions.values, append.values)
    conditions.andExpressions.push(...append.andExpressions)
    conditions.orExpressions.push(...append.orExpressions)
  }

  if (!Object.keys(conditions.names).length) {
    // @ts-expect-error This is fine
    delete conditions.names
    // @ts-expect-error This is fine
    delete conditions.andExpressions
  }

  const FilterExpression = createFilterExpression(conditions)

  const serialized: QueryCommandInput | ScanCommandInput = {
    TableName: tableName,
    IndexName: params.index,
    KeyConditionExpression: `${hash} = :${hash}`,
    ExpressionAttributeNames: conditions.names,
    ExpressionAttributeValues: conditions.values,
    FilterExpression,
    ConsistentRead: !!params.consistentRead,
  }

  if (params.scan) {
    delete serialized.KeyConditionExpression
    delete conditions.values[`:${hash}`]

    if (!Object.keys(conditions.values).length) {
      delete serialized.ExpressionAttributeValues
    }

    if (!FilterExpression) {
      delete serialized.FilterExpression
    }
  }

  return serialized
}
