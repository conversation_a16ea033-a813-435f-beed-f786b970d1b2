import type { TargetTable } from '../types'
import { getCompositeName } from '../utils/keys'
import type { Paginated } from './types'

export type Augmentation<T> = (item: T, index: number) => T

/** Take in a set of records and run a set of transformations against them */
export function augmentResults<T>(results: Paginated<T>, funcs: Augmentation<T>[]) {
  if (!funcs.length) return results

  const length = results.length
  const cloned: Paginated<T> = Array.from({ length })
  cloned.next = results.next

  for (let i = 0; i < length; i++) {
    let item = results[i] as T
    for (const func of funcs) {
      item = func(item, i)
    }

    cloned[i] = item
  }

  return cloned
}

/** An augmentor function to remove partition keys from resulting records */
export function stripPartitionKeyFromRecord<T>(
  table: TargetTable,
  index?: string
): Augmentation<T> {
  const { hash, sort } = getCompositeName(table, index)

  return (record: T) => {
    const clone = { ...record } as Record<string, unknown>
    delete clone[hash]
    delete clone[sort]
    return clone as T
  }
}

/** An augmentor function to remove specific properties from resulting records */
export function omitKeysFromRecord<T>(props: string[]) {
  const deleteLines = props.map((x) => `delete clone[${JSON.stringify(x)}]`).join(';')
  // This insane optimization is actually faster than looping lol
  // TODO: attach benchmark here so you know I'm not insane
  const deletionFunc = Function('record', `const clone = {...record};${deleteLines};return clone;`)
  return deletionFunc as Augmentation<T>
}
