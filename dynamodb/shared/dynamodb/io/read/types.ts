import type { z } from 'zod'
import type { TargetTable } from '../types'
import type { Augmentation } from './augment'

export const CONDITION_OR = Symbol('query:or')

export type Paginated<T> = T[] & {
  next?: any
  failed?: any[]
}

export type IndexArgs = {
  index?: string
  consistentRead?: boolean
}

export type QueryPaginatedArgs = {
  scan?: boolean
  softValidate?: z.AnyZodObject
  stripPartitionKeys?: boolean
}

export type Reduce<T, R> = {
  initial: R
  each(aggregate: R, record: T): R
}

export type AttributeArgs = {
  attributes?: string[]
  limit?: 100 | 50
}

export type PaginatedArgs<T = any, R = any> = {
  /**
   * Semi-sarcastic "one" will paginate query @ 25, results at 1 for query efficiency
   *
   * 'none' will pull the full partition, which is useful for fetching an entity and associations
   */
  limit?: number | 'one' | 'none'
  next?: any
  /** Dynamo-based sorting */
  sort?: 'ascending' | 'descending'
  /** Code-based filter that will keep querying if the limit hasn't been hit yet */
  filter?: (record: T) => boolean
  reduce?: Reduce<T, R>
  /** Record attributes you want to include (forwarded to DynamoDB request config) */
  props?: string[]
  /** Props that may come up from a query, but you do not want in the results */
  omit?: string[]
  /** Custom transformations you want to perform */
  augment?: Augmentation<T>[]
  /** Code-based sorting after all querying has concluded */
  postSort?: (a: T, b: T) => number
  /** Controls 'none' behavior to query larger page sizes */
  smallRecords?: boolean
}

export type DirectRead = { table: TargetTable; pk: string; sk: string | number }
