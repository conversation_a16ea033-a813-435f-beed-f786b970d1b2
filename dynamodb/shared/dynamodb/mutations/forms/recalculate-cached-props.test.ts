import { mockOneWrite } from '@shared/dynamo-hummingbird/tables/io/write.mock'
import {
  uniqueAssemblyUnitAssociation,
  uniqueDirectionAssociation,
} from '@shared/trpc-lambda/mocks'
import { DirectionsTypeId } from '@shared/types/model'
import { recalculateAssemblyUnitCount } from './recalculate-cached-props'

const queryFormByIdSpy = jest.spyOn(
  jest.requireActual('@shared/dynamodb/queries/forms/query-form-by-id'),
  'queryFormById'
)

const formId = 'ABC000000'
const pk = `Form#${formId}`
const sk = pk

describe('recalculateAssemblyUnitCount', () => {
  it('will throw an error if associations have anything but AU associations', () => {
    queryFormByIdSpy.mockResolvedValueOnce({
      associations: [
        uniqueDirectionAssociation('Form', DirectionsTypeId.DIRECTIONS_SECTION),
        uniqueAssemblyUnitAssociation({ parentType: 'Form', parentId: formId }),
      ],
    })
    expect(recalculateAssemblyUnitCount(formId)).rejects.toThrowError()
  })
  it('will update the assemblyUnitCount property', async () => {
    const assemblyUnitCount = 4
    queryFormByIdSpy.mockResolvedValueOnce({
      associations: Array.from({ length: assemblyUnitCount }, () =>
        uniqueAssemblyUnitAssociation({ parentType: 'Form', parentId: formId })
      ),
    })
    const updateOneMock = mockOneWrite('updateOne', () => null)
    const result = await recalculateAssemblyUnitCount(formId)
    expect(result).toEqual({ assemblyUnitCount })
    expect(updateOneMock).toHaveBeenCalledWith('authoring', {
      pk,
      sk,
      assemblyUnitCount,
    })
  })
})
