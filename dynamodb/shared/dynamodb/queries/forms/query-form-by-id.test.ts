import {
  clearReadMocks,
  mockOneRead,
  mockRead,
} from '@shared/dynamo-hummingbird/tables/io/read.mock'
import * as queryAssetAssociations from '@shared/dynamodb/queries/assets/query-asset-associations'
import { convertAssemblyUnitModel } from '@shared/schema/split-models'
import { convertFormModel } from '@shared/schema/split-models/forms'
import * as mocks from '@shared/trpc-lambda/mocks'
import { DirectionsTypeId } from '@shared/types/model'
import { queryCompleteFormById } from './query-form-by-id'

const form = mocks.uniqueForm()
const formId = form.id

const formSections = Array.from({ length: 6 }, () => mocks.uniqueFormSection(formId))

const examDirection = mocks.uniqueDirectionAssociation(
  'Form',
  DirectionsTypeId.DIRECTIONS_EXAM,
  undefined,
  undefined,
  formId
)

const sectionOneAUs = Array.from({ length: 2 }, (_, i) =>
  mocks.uniqueAssemblyUnitAssociation({
    parentId: formId,
    sectionId: formSections[0]?.id,
    ordinal: i + 1,
  })
)
const sectionOneDirection = mocks.uniqueDirectionAssociation(
  'Form',
  DirectionsTypeId.DIRECTIONS_SECTION,
  formSections[0]?.id,
  undefined,
  formId
)

const sectionTwoDirection = mocks.uniqueDirectionAssociation(
  'Form',
  DirectionsTypeId.DIRECTIONS_SECTION,
  formSections[1]?.id,
  undefined,
  formId
)

const sectionThreeAUs = Array.from({ length: 3 }, (_, i) =>
  mocks.uniqueAssemblyUnitAssociation({
    parentId: formId,
    sectionId: formSections[2]?.id,
    ordinal: i + 1,
  })
)

const uniqueAssemblyUnit1 = mocks.uniqueAssemblyUnit(sectionOneAUs[0]?.associationChildId)
const uniqueAssemblyUnit2 = mocks.uniqueAssemblyUnit(sectionOneAUs[1]?.associationChildId)
const uniqueAssemblyUnit3 = mocks.uniqueAssemblyUnit(sectionThreeAUs[0]?.associationChildId)
const uniqueAssemblyUnit4 = mocks.uniqueAssemblyUnit(sectionThreeAUs[1]?.associationChildId)
const uniqueAssemblyUnit5 = mocks.uniqueAssemblyUnit(sectionThreeAUs[2]?.associationChildId)

const allAssemblyUnits = [
  uniqueAssemblyUnit1,
  uniqueAssemblyUnit2,
  uniqueAssemblyUnit3,
  uniqueAssemblyUnit4,
  uniqueAssemblyUnit5,
]

const assetAssociations = allAssemblyUnits.map((au) =>
  mocks.uniqueAssetAssociation({
    parentType: 'AssemblyUnit',
    parentId: au.id,
    ordinal: 1,
  })
)

const parentAssociations = allAssemblyUnits.map((au, index) =>
  mocks.uniqueParentFormAssociation({
    childId: au.id,
    childType: 'AssemblyUnit',
    parentId: index === 1 ? formId : undefined,
  })
)

const associations = [
  examDirection,
  ...[...sectionOneAUs].reverse(),
  sectionOneDirection,
  sectionTwoDirection,
  ...[...sectionThreeAUs].reverse(),
]

const queryAssociationsSpy = jest
  .spyOn(queryAssetAssociations, 'queryAssetAssociations')
  .mockImplementation(() => Promise.resolve({}))

const injectUpdatedSpy = jest
  .spyOn(jest.requireActual('@shared/utils/event-emitting-functions'), 'injectLatestUpdatedInfo')
  .mockImplementation((input) => input)

describe('Forms API - formsReadOne', () => {
  afterEach(() => {
    clearReadMocks()
  })

  it('Should fail when no form is found', async () => {
    mockOneRead('queryPaginated', () => [...formSections, ...associations])
    expect(queryCompleteFormById(formId)).rejects.toThrowError(/not found/)
  })
  it.each([formId, [formId]])(
    'Should return a form, sections, directions, and assemblyUnits',
    async (formId) => {
      mockOneRead('queryPaginated', () => [form, ...formSections, ...associations])
      mockRead('readOne', (_table, hk) => {
        const directions = [
          { ...mocks.examDirection, id: examDirection.associationChildId },
          { ...mocks.sectionDirection, id: sectionOneDirection.associationChildId },
          { ...mocks.sectionDirection, id: sectionTwoDirection.associationChildId },
        ]
        return Promise.resolve(directions.find((direction) => direction.id === hk.split('#')[1]))
      })
      mockRead('queryPaginated', (_, key) => {
        if (key === uniqueAssemblyUnit1.pk) return [uniqueAssemblyUnit1, assetAssociations[0]]
        if (key === uniqueAssemblyUnit2.pk) return [uniqueAssemblyUnit2, assetAssociations[1]]
        if (key === uniqueAssemblyUnit3.pk) return [uniqueAssemblyUnit3, assetAssociations[2]]
        if (key === uniqueAssemblyUnit4.pk) return [uniqueAssemblyUnit4, assetAssociations[3]]
        if (key === uniqueAssemblyUnit5.pk) return [uniqueAssemblyUnit5, assetAssociations[4]]
        if (key === uniqueAssemblyUnit1.id) return [parentAssociations[0]]
        if (key === uniqueAssemblyUnit2.id) return [parentAssociations[1]]
        if (key === uniqueAssemblyUnit3.id) return [parentAssociations[2]]
        if (key === uniqueAssemblyUnit4.id) return [parentAssociations[3]]
        return [parentAssociations[4]]
      })
      const resultRaw = await queryCompleteFormById(formId as unknown as string & string[], {
        retrieveAUParentAssociations: true,
        excludeTimestamps: true,
        queryDirections: true,
      })
      expect(queryAssociationsSpy).not.toHaveBeenCalled()
      expect(injectUpdatedSpy).not.toHaveBeenCalled()
      const result = Array.isArray(resultRaw) ? resultRaw[0]! : resultRaw
      expect(result).toEqual({
        form: expect.any(Object),
        sections: expect.any(Array),
        directions: {
          [examDirection.associationChildId]: expect.any(Object),
          [sectionOneDirection.associationChildId]: expect.any(Object),
          [sectionTwoDirection.associationChildId]: expect.any(Object),
        },
        assemblyUnits: expect.any(Object),
      })
      expect(result.form).toEqual(
        expect.objectContaining({
          ...convertFormModel(form),
          direction: examDirection.associationChildId,
        })
      )
      expect(result.sections).toEqual(
        expect.objectContaining(
          formSections.map((x, i) => {
            const sectionObject = {
              id: x.id,
              name: x.name,
              ordinal: x.ordinal,
              assemblyUnits: expect.any(Array),
            }
            if (i < 2) {
              return expect.objectContaining({
                ...sectionObject,
                direction: expect.any(String),
              })
            } else {
              return expect.objectContaining(sectionObject)
            }
          })
        )
      )
      expect(result.assemblyUnits).toEqual(
        expect.objectContaining(
          allAssemblyUnits.reduce((acc, au, index) => {
            return {
              ...acc,
              [au.id]: {
                ...convertAssemblyUnitModel(au),
                assetIds: [assetAssociations[index]?.associationChildId as string],
                deactivatedItems: assetAssociations[index]?.on.items
                  .filter((item) => !item.active)
                  .map((item) => item.id),
                customItemOrder: {
                  [assetAssociations[index]?.associationChildId as string]: assetAssociations[
                    index
                  ]?.on.items.map((item) => item.id),
                },
                parentAssociations: [parentAssociations[index]].flatMap((assoc) => {
                  if (!assoc) return []
                  const [parentType, parentId] = assoc.pk.split('#')
                  return { id: parentId as string, type: parentType as 'AssemblyUnit' | 'Form' }
                }),
                useCodesByItemIds: {
                  ABC496431: undefined,
                  ABC496432: undefined,
                  ABC496433: undefined,
                  ABC496434: undefined,
                },
              },
            }
          }, {})
        )
      )
    }
  )

  it('Should throw an error if an assembly unit is not found', async () => {
    mockOneRead('queryPaginated', () => [form, ...formSections, ...associations])
    mockRead('readOne', (_table, hk) => {
      const directions = [
        { ...mocks.examDirection, id: examDirection.associationChildId },
        { ...mocks.sectionDirection, id: sectionOneDirection.associationChildId },
        { ...mocks.sectionDirection, id: sectionTwoDirection.associationChildId },
      ]
      return Promise.resolve(directions.find((direction) => direction.id === hk.split('#')[1]))
    })
    mockRead('queryPaginated', (_, key) => {
      if (key === uniqueAssemblyUnit1.pk) return [uniqueAssemblyUnit1, assetAssociations[0]]
      if (key === uniqueAssemblyUnit2.pk) return [uniqueAssemblyUnit2, assetAssociations[1]]
      if (key === uniqueAssemblyUnit3.pk) return [assetAssociations[2]]
      if (key === uniqueAssemblyUnit4.pk) return [assetAssociations[3]]
      if (key === uniqueAssemblyUnit5.pk) return [uniqueAssemblyUnit5, assetAssociations[4]]
      if (key === uniqueAssemblyUnit1.id) return [parentAssociations[0]]
      if (key === uniqueAssemblyUnit2.id) return [parentAssociations[1]]
      if (key === uniqueAssemblyUnit3.id) return [parentAssociations[2]]
      if (key === uniqueAssemblyUnit4.id) return [parentAssociations[3]]
      return [parentAssociations[4]]
    })
    try {
      await queryCompleteFormById(formId)
      expect(false)
    } catch (e) {
      expect((e as Error).message).toMatch(/^Cannot read Assembly Units with IDs:/)
    }
  })
})
