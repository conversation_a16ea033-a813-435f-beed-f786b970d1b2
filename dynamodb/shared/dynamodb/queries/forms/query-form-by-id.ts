import { Asset, tables } from '@shared/dynamo-hummingbird'
import { queryPaginated } from '@shared/dynamo-hummingbird/tables/io/read'
import type {
  FormAssociationModel,
  FormModel,
  FormSectionModel,
} from '@shared/dynamo-hummingbird/tables/split-models/forms'
import { convertAssemblyUnitModel } from '@shared/schema/split-models'
import {
  Form,
  FormAssemblyUnit,
  FormSection,
  convertFormModel,
  convertFormSectionModel,
} from '@shared/schema/split-models/forms'
import {
  mapAssetAssociationsToEntityMetadata,
  splitAssociationPartitionKey,
} from '@shared/utils/association-helpers'
import {
  injectLatestUpdatedInfo,
  mapFormAssociationsToSections,
} from '@shared/utils/event-emitting-functions'
import { TRPCError } from '@trpc/server'
import { queryAssemblyUnitsByIds } from '../assembly-units/query-assembly-unit-by-id'
import { queryAssets } from '../assets'

export type FormData<QueryDirections extends boolean = boolean> = {
  form: Form
  sections: FormSection[]
  directions: QueryDirections extends true ? Record<string, Asset> : Record<string, boolean>
  assemblyUnits: Record<string, FormAssemblyUnit>
}

/**
 * Query a form by its ID
 * @returns The Form's {@link FormModel}, {@link FormSectionModel | FormSectionModels},
 *  and {@link FormAssociationModel | FormAssociationModels}.
 */
export async function queryFormById(
  formId: string,
  options?: {
    retrieveAUs?: boolean
    retrieveAssets?: boolean
    retrieveDirections?: boolean
    excludeTimestamps?: boolean
  }
) {
  const response = await queryPaginated<FormModel | FormSectionModel | FormAssociationModel>(
    tables.authoring,
    `Form#${formId}`,
    {
      type:
        options?.retrieveAUs || options?.retrieveAssets || options?.retrieveDirections
          ? ['in', ['Form', 'FormSection', 'Association']]
          : ['in', ['Form', 'FormSection']],
    },
    {
      limit: 'none',
      filter: (item) => {
        if (
          item.type === 'Form' || // Always return the form
          item.type === 'FormSection' || // Always return the form section
          (options?.retrieveAUs && item.associationChildType === 'AssemblyUnit') || // Return AU associations if needed
          (options?.retrieveAssets &&
            item.associationChildType === 'Asset' &&
            !item.on?.directionType) || // Return Asset associations if needed
          (options?.retrieveDirections &&
            item.associationChildType === 'Asset' &&
            item.on?.directionType) // Return Direction associations if needed
        )
          return true
        return false
      },
    }
  )
  console.debug(
    `queryFormById ${formId}: Pulled ${response.length} form, form sections, and associations`
  )
  let form: FormModel | undefined
  const formSections: FormSectionModel[] = []
  const associations: FormAssociationModel[] = []

  response.forEach((item) => {
    if (item.type === 'Form') {
      form = item
    } else if (item.type === 'FormSection') {
      formSections.push(item)
    } else {
      associations.push(item)
    }
  })

  if (!form) {
    console.error(`queryFormById: ${formId} not found`)
    throw new TRPCError({ code: 'NOT_FOUND', message: 'Form not found' })
  }

  const sortedFormSections = formSections.sort((a, b) => a.ordinal - b.ordinal)

  if (options?.excludeTimestamps) {
    return {
      form,
      formSections: sortedFormSections,
      associations,
    }
  }

  const formWithLatestUpdatedInfo = await injectLatestUpdatedInfo<FormModel>(form)

  return {
    form: formWithLatestUpdatedInfo,
    formSections: sortedFormSections,
    associations,
  }
}

export function queryCompleteFormById<QueryDirections extends boolean>(
  formIdsInput: string,
  opts?: {
    retrieveAUParentAssociations: boolean
    excludeTimestamps: boolean
    queryDirections: QueryDirections
  }
): Promise<FormData<QueryDirections>>

export function queryCompleteFormById<QueryDirections extends boolean>(
  formIdsInput: string[],
  opts?: {
    retrieveAUParentAssociations: boolean
    excludeTimestamps: boolean
    queryDirections: QueryDirections
  }
): Promise<FormData<QueryDirections>[]>

/**
 * Query a form by its ID
 * @returns The {@link Form}, {@link FormSection | Form Sections}, full Directions as {@link Asset|Assets},
 * and full {@link FormAssemblyUnit | Form Assembly Units} with associations.
 */
export async function queryCompleteFormById<
  T extends string | string[],
  QueryDirections extends boolean
>(
  formIdsInput: T,
  {
    retrieveAUParentAssociations,
    excludeTimestamps,
    queryDirections,
  }: {
    retrieveAUParentAssociations: boolean
    excludeTimestamps: boolean
    queryDirections: boolean
  } = {
    retrieveAUParentAssociations: false,
    excludeTimestamps: false,
    queryDirections: true,
  }
): Promise<T extends string ? FormData<QueryDirections> : FormData<QueryDirections>[]> {
  const formIds = Array.isArray(formIdsInput) ? formIdsInput : [formIdsInput]
  const allDirections: Set<string> = new Set(),
    allAssemblyUnits: Set<string> = new Set()
  const formData = await Promise.all(
    formIds.map(async (formId) => {
      const { form, formSections, associations } = await queryFormById(formId, {
        retrieveDirections: true,
        retrieveAUs: true,
        excludeTimestamps,
      })
      if (!form) throw new Error(`Form ${formId} not found`)
      const {
        sectionMapping,
        allDirections: dirs,
        allAssemblyUnits: aus,
      } = mapFormAssociationsToSections(associations)

      const dto = convertFormModel(form)
      dto.direction = sectionMapping?.[formId]?.direction
      const sections = formSections.map((section) => ({
        ...convertFormSectionModel(section),
        ...(sectionMapping[section.id] ?? {}),
      }))
      dirs.forEach((dir) => allDirections.add(dir))
      aus.forEach((au) => allAssemblyUnits.add(au))

      return {
        form,
        dto,
        sections,
        allDirections: dirs,
        allAssemblyUnits: aus,
      }
    })
  )

  // fetch and transform directions
  const directions = queryDirections
    ? (await queryAssets(allDirections.values(), 0, { queryAssociations: false })).reduce<
        Record<string, Asset>
      >((acc, curr) => {
        if (curr?.id) {
          return {
            ...acc,
            [curr.id]: curr,
          }
        }
        return acc
      }, {})
    : Array.from(allDirections).reduce<Record<string, boolean>>(
        (accum, dirId) => ({ ...accum, [dirId]: true }),
        {}
      )
  // fetch and transform assembly units
  const assemblyUnits: Record<string, FormAssemblyUnit> = {}
  const fetchedAssemblyUnits = await queryAssemblyUnitsByIds(allAssemblyUnits, {
    retrieveAssets: true,
    retrieveParentAssociations: retrieveAUParentAssociations,
    excludeTimestamps,
  })
  for (const { assemblyUnit, associations, parentAssociations } of fetchedAssemblyUnits) {
    const { customItemOrder, deactivatedItemIDs, assetIds } =
      mapAssetAssociationsToEntityMetadata(associations)
    assemblyUnits[assemblyUnit.id] = {
      ...convertAssemblyUnitModel(assemblyUnit),
      assetIds,
      deactivatedItems: Object.keys(JSON.parse(deactivatedItemIDs)),
      customItemOrder: JSON.parse(customItemOrder),
      parentAssociations: parentAssociations.map((assoc) => {
        const { parentType, parentId } = splitAssociationPartitionKey(assoc.pk)
        return { id: parentId, type: parentType as 'AssemblyUnit' | 'Form' }
      }),
      useCodesByItemIds: associations.reduce((acc, assoc) => {
        if (assoc.associationChildType !== 'Asset') return acc
        return assoc.on.items.reduce((acc, item) => {
          return {
            ...acc,
            [item.id as string]: item.useCode?.label,
          }
        }, acc)
      }, {}),
    }
  }

  const res = formData.reduce<FormData<QueryDirections>[]>((accum, formDatum) => {
    const directionsForForm = formDatum.allDirections.reduce<
      FormData<QueryDirections>['directions']
    >((accum, directionsId) => {
      const direction = directions[directionsId]
      if (direction) accum[directionsId] = direction
      return accum
    }, {})
    const assemblyUnitsForForm = formDatum.allAssemblyUnits.reduce<
      FormData<QueryDirections>['assemblyUnits']
    >((accum, assemblyUnitId) => {
      const au = assemblyUnits[assemblyUnitId]
      if (au) accum[assemblyUnitId] = au
      return accum
    }, {})
    return accum.concat({
      form: formDatum.dto,
      sections: formDatum.sections,
      directions: directionsForForm,
      assemblyUnits: assemblyUnitsForForm,
    })
  }, [])
  if (Array.isArray(formIdsInput)) {
    console.info(`queryCompleteFormById: Retrieved ${res.length} forms`)
    return res as T extends string ? FormData<QueryDirections> : FormData<QueryDirections>[]
  }
  const singleForm = res[0]
  if (!singleForm) throw new Error(`Form ${formIds[0]} not found`)
  console.info(`queryCompleteFormById: Retrieved Form ${singleForm.form.id}`)
  return singleForm as T extends string ? FormData<QueryDirections> : FormData<QueryDirections>[]
}
