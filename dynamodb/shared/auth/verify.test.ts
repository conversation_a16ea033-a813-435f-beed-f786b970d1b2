/* eslint-disable @typescript-eslint/no-explicit-any */
import { AUTH_HEADER, authWithHeader, verifyToken } from './verify'
import { decode, verify } from 'jsonwebtoken'
import { getUserInfo } from './user-info'

const TOKEN =
  'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8reqQh-hSrM5OPkusSxQdNsmZgLNgUr4XOG0VihSqSAk20TbJ4WVoCv1m1DMHA6sl5slKEpRc3goXMrWcbcjcdcnnEpcpNoxdPx5WprMu45rmMB8OfEXA8fwQJnKLjBbY0KpMsOS4bg7izonxi4nUCRqn38h5cb42MAgwL91kBMgjtD0rVgybAjbPxiyTxCTF7wmHLBEOrv3ILw2ds2It8Z74Xnc_UQFzOTPyVhewdZldUBf8ElYI5lZe_8eWeIH_Vv-OzEpYNoJU-_HSdKlCzTj15lQGCE4Rc3iYCnBeSNtQsd1Tl32ycdV9FYX_OpkBDwEvt7PUEu1ynh03CtpA'

const mockClaims = {
  roles: [],
  email: '<EMAIL>',
}

const mockParsedJwt = {
  header: {
    kid: 'some kid',
  },
}

jest.mock('jwks-rsa', () => () => ({
  getSigningKey: jest.fn(() => ({ getPublicKey: jest.fn(() => 'some key') })),
}))
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(() => mockClaims),
  decode: jest.fn(() => mockParsedJwt),
}))
jest.mock('./user-info', () => ({
  getUserInfo: jest.fn(),
}))

describe('authWithHeader', () => {
  const validHeader = { [AUTH_HEADER]: 'Bearer some.very.real.jwt' }

  it.each([
    ['empty header', { other: 'foo' }],
    ['incorrect type', { [AUTH_HEADER]: ['bad', 'value'] }],
  ])('Throws an error if invalid value is provided for header: %s', (_, header) => {
    expect(async () => {
      await authWithHeader(header)
    }).rejects.toThrow(`Invalid value provided to ${AUTH_HEADER} header`)
  })

  it('Throws if JWT is invalid', () => {
    ;(decode as unknown as jest.Mock).mockImplementationOnce(() => {
      throw 'some error'
    })
    expect(async () => {
      await authWithHeader(validHeader)
    }).rejects.toThrow('Invalid JWT')
  })

  it('If jwt is valid, returns claims from getUserInfo', async () => {
    const expectedClaims = {
      some: 'value',
      claims: 'foo',
    }
    ;(getUserInfo as unknown as jest.Mock).mockReturnValue(expectedClaims)
    const claims = await authWithHeader(validHeader)
    expect(claims).toEqual(expectedClaims)
  })
})

describe('verifyToken', () => {
  describe('Running local', () => {
    const originalEnv = process.env.ENV

    beforeEach(() => {
      process.env.ENV = 'local'
    })

    afterAll(() => {
      process.env.ENV = originalEnv
    })

    it('Returns false if jwt is invalid', async () => {
      ;(decode as unknown as jest.Mock).mockReturnValueOnce(null)
      const invalid = await verifyToken('some invalid jwt')
      expect(invalid).toBe(false)
    })

    it('Returns the decoded claims', async () => {
      const claims = await verifyToken(TOKEN)
      expect(claims).toEqual(mockParsedJwt)
    })
  })

  describe('Running non-local', () => {
    let originalEnv: any

    beforeAll(() => {
      originalEnv = process.env.ENV
      process.env.ENV = 'nonprod'
    })

    afterAll(() => {
      process.env.ENV = originalEnv
    })

    it('Returns false if the jwt is invalid', async () => {
      ;(verify as unknown as jest.Mock).mockImplementationOnce(() => {
        throw 'wow'
      })

      const invalid = await verifyToken(TOKEN)
      expect(invalid).toBe(false)
    })

    it('Returns the jwt claims if the jwt is valid', async () => {
      const claims = await verifyToken(TOKEN)
      expect(claims).toEqual(mockClaims)
    })
  })
})
