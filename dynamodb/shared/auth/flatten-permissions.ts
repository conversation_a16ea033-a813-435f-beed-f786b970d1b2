import type { FlattenedPermissionName, Permission } from '@shared/rbac'

// TODO: Call this less often, cache the results to a variable on load
export function flattenPermissions(permissions: Partial<Permission>): FlattenedPermissionName[] {
  const output: string[] = []

  function step(target: any, prev?: any) {
    Object.keys(target).forEach((key) => {
      const node = prev ? `${prev}.${key}` : key
      const value = target[key]

      // Individual permission
      if (value.description) {
        return output.push(node)
      }

      // Permission group
      if (typeof value === 'object') {
        return step(value, node)
      }
    })
  }

  step(permissions)
  return output as FlattenedPermissionName[]
}
