import type { FlattenedPermissionName } from '@shared/rbac'
import { AZURE_BASIC_ROLE, AzureRole, Role } from '@shared/rbac/roles'
import { flattenPermissions } from './flatten-permissions'
import {
  azureRolesToHummingbirdRoles,
  emailToUsername,
  hummingbirdRolesToPermissions,
} from './utils'

jest.mock('./flatten-permissions')
const mockFlattenPermissions = flattenPermissions as jest.MockedFn<typeof flattenPermissions>

describe('shared auth utils', () => {
  describe('emailToUsername', () => {
    it('turns a email into a username', () => {
      const mockEmail = '<EMAIL>'
      const username = emailToUsername(mockEmail)
      expect(username).toBe('mock')
    })
  })

  describe('azureRolesToHummingbirdRoles', () => {
    it('positive test', () => {
      const roles: AzureRole[] = ['icg/admin', 'icg/content_creator']
      const converted = azureRolesToHummingbirdRoles(roles)
      expect(converted).toEqual([Role.ADMIN, Role.CONTENT_CREATOR])
    })

    it('negative test', () => {
      const roles = ['icg/form_previewer', AZURE_BASIC_ROLE] as AzureRole[]
      const converted = azureRolesToHummingbirdRoles(roles)
      expect(converted).toEqual([Role.FORM_PREVIEWER])
    })
  })

  describe('hummingbirdRolesToPermissions', () => {
    it('does the thing', () => {
      mockFlattenPermissions.mockImplementationOnce(() => ['assets.edit.own'])
      const roles: Role[] = [Role.ADMIN]
      const result = hummingbirdRolesToPermissions(roles)

      expect(result).toEqual(['assets.edit.own'])
    })

    it('deduplicates', () => {
      mockFlattenPermissions.mockImplementationOnce(
        () => ['assets.create.permitted', 'assets.delete.visible'] as FlattenedPermissionName[]
      )

      mockFlattenPermissions.mockImplementationOnce(
        () => ['assets.delete.visible', 'assets.edit.own'] as FlattenedPermissionName[]
      )

      const roles: Role[] = [Role.ADMIN, Role.CONTENT_CREATOR, Role.CONTENT_EDITOR]
      const result = hummingbirdRolesToPermissions(roles)

      expect(result).toEqual([
        'assets.create.permitted',
        'assets.delete.visible',
        'assets.edit.own',
      ])
    })
  })
})
