import type { Permission } from '@shared/rbac'
import { flattenPermissions } from './flatten-permissions'

describe('flattenPermissions', () => {
  it('should handle empty permissions object', () => {
    const permissions: Partial<Permission> = {}
    const result = flattenPermissions(permissions)
    expect(result).toEqual([])
  })

  it('should flatten permissions', () => {
    const permissions: Partial<Permission> = {
      assets: {
        create: {
          permitted: { description: 'Create items/sets' },
        },
        edit: { description: 'edit items' },
      },
      workflow: {
        transition: {
          internalItemReview: {
            internalContentReview1: {
              description:
                'Has permission to transition an asset to Internal Content Review 1 step',
            },
            internalContentReview: {
              description: 'Has permission to transition an asset to Internal Content Review step',
            },
          },
        },
      },
    }
    const result = flattenPermissions(permissions)
    expect(result).toEqual([
      'assets.create.permitted',
      'assets.edit',
      'workflow.transition.internalItemReview.internalContentReview1',
      'workflow.transition.internalItemReview.internalContentReview',
    ])
  })
})
