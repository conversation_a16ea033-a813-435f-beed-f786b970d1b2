import { DynamoDBClient, DynamoDBClientConfig, QueryCommand } from '@aws-sdk/client-dynamodb'
import { unmarshall } from '@aws-sdk/util-dynamodb'
import { type AzureUser, User } from '@shared/dynamo-hummingbird/tables/split-models/auth'
// import type { User as OldUser } from 'shared/dynamo-hummingbird/types/users'
import type { Role } from '@shared/rbac/roles'
import {
  azureRolesToHummingbirdRoles,
  emailToUsername,
  hummingbirdRolesToPermissions,
} from './utils'
import type { Claims } from './verify'

// TODO: Clean up UserContext references
export type UserContext = User

//! Keep this up-to-date with `bin/data/users.ts`
const developers = [
  'mmorales',
  'kyang',
  'nluther',
  'lloustaunau',
  'dhawes',
  'skidd',
  'japatterson',
  'ikhov',
  'erios',
  'bstrong',
  'heng',
  'ajenks',
  'ddolan',
  'aguan',
  'plowe',
]

const mergeArrays = (a: any[], b: any[]) => [...new Set(a.concat(b))]

const config: DynamoDBClientConfig =
  process.env.ENV === 'local'
    ? {
        endpoint: 'http://127.0.0.1:8000/',
      }
    : {}
const client = new DynamoDBClient(config)

export async function getUserInfo(decodedToken: Claims): Promise<AzureUser> {
  const username = emailToUsername(decodedToken.upn)
  if (!username) throw new Error('Invalid user')

  const azureRoles = azureRolesToHummingbirdRoles(decodedToken.roles)
  // get the user data from db
  const foundUserCommandOutput = await client.send(
    new QueryCommand({
      TableName: `item-cloud-green-core-infra-dynamo-${process.env.ENV}`,
      KeyConditionExpression: `hk = :u`,
      ExpressionAttributeValues: { [':u']: { S: `User#${username}` } },
    })
  )
  const userRecord = foundUserCommandOutput?.Items?.[0]
  const foundUser = userRecord && User.safeParse(unmarshall(userRecord)).data
  if (!foundUser) throw new Error(`Invalid user "${username}"`)

  // TODO: When we do the round 2 permissions/roles refactor, these three are going to change a fair bit
  // It'll let you do roles on a per-course basis, will have "feature flags", and permission redactions
  const roles: Role[] = mergeArrays(azureRoles, foundUser.roles || [])
  const permissions = hummingbirdRolesToPermissions(roles)
  const userPermissions = mergeArrays(permissions, foundUser.permissions || [])

  const user: AzureUser = {
    ...foundUser,
    permissions: userPermissions,
    roles,
    currentAzureRoles: decodedToken.roles,
  }

  // TODO: Re-seed users in non-ephemerals so this doesn't have to exist
  if (developers.includes(user.username!)) {
    user.isDev = true
  }

  return user
}
