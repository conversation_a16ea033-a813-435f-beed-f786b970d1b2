import { JwtPayload, decode, verify } from 'jsonwebtoken'
import jwksClient, { type SigningKey } from 'jwks-rsa'
import type { AzureRole } from '@shared/rbac/roles'
import { getUserInfo } from './user-info'

export type Claims = JwtPayload & {
  name: string
  upn: string // User email
  roles: AzureRole[]
}

let key: SigningKey

export const AUTH_HEADER = 'authorization'

const PUBLIC_KEY_URL =
  'https://login.microsoftonline.com/7530bded-fd6e-4f58-b5d2-ea681eb07663/discovery/v2.0/keys'

const CLAIM_AUD = process.env?.CLAIM_AUD

export async function authWithHeader(headers: Record<string, string | string[] | undefined>) {
  // Get token from header
  const token = headers['authorization'] || headers['Authorization']
  if (!token || typeof token !== 'string')
    throw new Error('Invalid value provided to authorization header')
  // Verify token and get claims
  const jwt = token.replace('Bearer ', '')
  const claims = await verifyToken(jwt)
  if (!claims) throw new Error('Invalid JWT provided to auth')
  // Get user info from claims
  return await getUserInfo(claims)
}

export async function verifyToken(token: string): Promise<Claims | false> {
  try {
    if (process?.env?.ENV === 'local') {
      // When running locally, just parse jwt claims
      // Optional chain because not all execution contexts will have environment variables (e.g. graphics authorizer)
      const decodedToken = decode(token)
      if (!decodedToken || typeof decodedToken === 'string') {
        return false
      }
      return decodedToken as Claims
    }

    // Otherwise verify jwt
    const decoded = decode(token, { complete: true })
    const kid = decoded?.header?.kid

    // Get public key
    console.debug('Fetching public key')
    const client = jwksClient({
      jwksUri: PUBLIC_KEY_URL,
    })
    key ??= await client.getSigningKey(kid)
    const signingKey = key.getPublicKey()

    // Verify token - will throw if invalid
    console.debug('Verifying token')
    return verify(token, signingKey, {
      audience: CLAIM_AUD,
    }) as Claims
  } catch (e) {
    console.error('Error while verifying token', e)
    troubleshootTokenValidity(token)
    return false
  }
}

export function troubleshootTokenValidity(token: string) {
  const decoded = decode(token, { complete: true })
  console.log(decoded)
}
