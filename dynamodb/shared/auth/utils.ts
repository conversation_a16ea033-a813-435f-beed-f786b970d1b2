import { FlattenedPermissionName, roles } from '@shared/rbac'
import type { AzureRole, Role } from '@shared/rbac/roles'
import { flattenPermissions } from './flatten-permissions'

export const azureRolesToHummingbirdRoles = (inputRoles: AzureRole[]): Role[] => {
  return inputRoles.reduce<Role[]>((accum, azureRole) => {
    const hbRole = roles.AzureRoleMapping[azureRole]
    if (hbRole) accum.push(hbRole)
    return accum
  }, [])
}

export const hummingbirdRolesToPermissions = (inputRoles: Role[]): FlattenedPermissionName[] => {
  return Array.from(
    new Set(
      inputRoles.flatMap((hbRole) => {
        const rolePermission = roles.Roles?.[hbRole]
        const flattenedRole = flattenPermissions(rolePermission)
        return flattenedRole
      })
    )
  )
}

export function emailToUsername(email: string) {
  return email.replace(/@.+$/, '').toLowerCase()
}
