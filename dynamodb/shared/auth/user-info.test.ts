import { DynamoDBClient } from '@aws-sdk/client-dynamodb'
import { marshall } from '@aws-sdk/util-dynamodb'
import { flattenPermissions } from '@shared/auth/flatten-permissions'
import { type AzureUser, User } from '@shared/dynamo-hummingbird/tables/split-models/auth'
import { roles } from '@shared/rbac'
import { getUserInfo } from './user-info'

describe('flattenPermissions', () => {
  const mockUserEntity: Partial<AzureUser> = {
    roles: [roles.Role.ADMIN],
    permissions: [],
  }
  jest
    .spyOn(DynamoDBClient.prototype, 'send')
    .mockImplementation(() => Promise.resolve({ Items: [marshall(mockUserEntity)] }))
  // @ts-expect-error: incomplete mock response
  jest.spyOn(User, 'safeParse').mockImplementation((args) => ({ data: args }))
  it('flattens permissions into a string array with dot notation', () => {
    const flattenedPermissions = flattenPermissions(roles.Permissions.EXTERNAL_REVIEWER_L1)

    expect(flattenedPermissions).toContain('assets.view.all')
    expect(flattenedPermissions).not.toContain('assets.create.permitted')
  })

  it('returns an empty array when empty permissions are provided', () => {
    const flattenedPermissions = flattenPermissions({})
    expect(flattenedPermissions).toHaveLength(0)
  })

  it('should get the user info', async () => {
    const expectedPermissions = flattenPermissions(roles.Permissions.ADMIN)

    const mockDecodedToken = {
      name: 'mocky mock',
      upn: '<EMAIL>',
      roles: ['icg/admin' as roles.AzureRole],
    }

    const userInfo = await getUserInfo(mockDecodedToken)
    expect(userInfo.permissions).toEqual(expectedPermissions)
    expect(userInfo.currentAzureRoles).toEqual(['icg/admin'])
  })
})
