metadataItems:
  - id: 'a1'
    itemType: 'Metadata Category'
    description: 'Big Idea'
    isMultiSelect: TRUE
    scope: [item, frqPoint]
  - id: 'a2'
    itemType: 'Metadata Category'
    description: 'Enduring Understanding'
    isMultiSelect: TRUE
    scope: [item, frqPoint]
  - id: 'a3'
    itemType: 'Metadata Category'
    description: 'Essential Knowledge'
    isMultiSelect: TRUE
    scope: [item, frqPoint]
  - id: 'a4'
    itemType: 'Metadata Category'
    description: 'Skill Category'
    isMultiSelect: TRUE
    scope: [item, frqPoint]
  - id: 'a5'
    itemType: 'Metadata Category'
    description: 'Skill'
    isMultiSelect: TRUE
    scope: [item, frqPoint]
  - id: 'a6'
    itemType: 'Metadata Category'
    description: 'Unit'
    isMultiSelect: TRUE
    scope: [item]
  - id: 'a7'
    itemType: 'Metadata Category'
    description: 'Question Type'
    isMultiSelect: FALSE
    scope: [item]
  - id: 'a8'
    itemType: 'Metadata Category'
    description: 'Stimulus Type'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a9'
    itemType: 'Metadata Category'
    description: 'Stimulus Text Origin'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a10'
    itemType: 'Metadata Category'
    description: 'Stimulus Difficulty'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a11'
    itemType: 'Metadata Category'
    description: 'Stimulus Time Period'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a12'
    itemType: 'Metadata Category'
    description: 'Stimulus Theme'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a13'
    itemType: 'Metadata Category'
    description: 'Stimulus Title'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: 'a14'
    itemType: 'Metadata Category'
    description: 'Stimulus Author'
    isMultiSelect: FALSE
    scope: [stimulus]
  - id: '194571'
    description: 'Item Difficulty'
    isMultiSelect: FALSE
    scope: [item]
  - id: 'b1'
    itemType: 'Big Idea'
    label: 'CHR'
    description: 'Character'
  - id: 'b2'
    itemType: 'Big Idea'
    label: 'SET'
    description: 'Setting'
  - id: 'b3'
    itemType: 'Big Idea'
    label: 'STR'
    description: 'Structure'
  - id: 'b4'
    itemType: 'Big Idea'
    label: 'NAR'
    description: 'Narration'
  - id: 'b5'
    itemType: 'Big Idea'
    label: 'FIG'
    description: 'Figurative Language'
  - id: 'b6'
    itemType: 'Big Idea'
    label: 'LAN'
    description: 'Literary Argumentation'
  - id: 'b7'
    itemType: 'Enduring Understanding'
    label: 'CHR-1'
    description:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
  - id: 'b8'
    itemType: 'Enduring Understanding'
    label: 'SET-1'
    description:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
  - id: 'b9'
    itemType: 'Enduring Understanding'
    label: 'STR-1'
    description:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
  - id: 'b10'
    itemType: 'Enduring Understanding'
    label: 'NAR-1'
    description:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
  - id: 'b11'
    itemType: 'Enduring Understanding'
    label: 'FIG-1'
    description:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
  - id: 'b12'
    itemType: 'Enduring Understanding'
    label: 'LAN-1'
    description:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
  - id: 'b13'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.A'
    description: 'Description, dialogue, and behavior reveal characters to readers.'
  - id: 'b14'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.B'
    description:
      'Descriptions of characters may come from a speaker, narrator, other characters, or the
      characters themselves.'
  - id: 'b15'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.C'
    description:
      'Perspective is how narrators, characters, or speakers understand their circumstances, and is
      informed by background, personality traits, biases, and relationships.'
  - id: 'b16'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.D'
    description:
      'A character’s perspective is both shaped and revealed by relationships with other characters,
      the environment, the events of the plot, and the ideas expressed in the text.'
  - id: 'b17'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.E'
    description:
      'Characters reveal their perspectives and biases through the words they use, the details they
      provide in the text, the organization of their thinking, the decisions they make, and the
      actions they take.'
  - id: 'b18'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.F'
    description:
      'The description of a character creates certain expectations for that character’s behaviors;
      how a character does or does not meet those expectations affects a reader’s interpretation of
      that character.'
  - id: 'b19'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.G'
    description:
      'Details associated with a character and/or used to describe a character contribute to a
      reader’s interpretation of that character.'
  - id: 'b20'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.H'
    description:
      'Readers’ understanding of a character’s perspective may depend on the perspective of the
      narrator or speaker.'
  - id: 'b21'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.I'
    description: 'A character’s perspective may shift during the course of a narrative.'
  - id: 'b22'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.J'
    description:
      'When narrators, characters, or speakers compare another character to something or someone
      else, they reveal their perspective on the compared character and may also reveal something
      innate about the compared character.'
  - id: 'b23'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.K'
    description:
      'Readers can infer a character’s motives from that character’s actions or inactions.'
  - id: 'b24'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.L'
    description:
      'A dynamic character who develops over the course of the narrative often makes choices that
      directly or indirectly affect the climax and/or the resolution of that narrative.'
  - id: 'b25'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.M'
    description:
      'Character changes can be visible and external, such as changes to health or wealth, or can be
      internal, psychological or emotional changes; external changes can lead to internal changes,
      and vice versa.'
  - id: 'b26'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.N'
    description:
      'Some characters remain unchanged or are largely unaffected by the events of the narrative.'
  - id: 'b27'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.O'
    description:
      'The significance of characters is often revealed through their agency and through nuanced
      descriptions.'
  - id: 'b28'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.P'
    description: 'Characters’ choices—in speech, action, and inaction—reveal what they value.'
  - id: 'b29'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.Q'
    description:
      'The main character in a narrative is the protagonist; the antagonist in the narrative opposes
      the protagonist and may be another character, the internal conflicts of the protagonist, a
      collective (such as society), or nature.'
  - id: 'b30'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.R'
    description: 'Protagonists and antagonists may represent contrasting values.'
  - id: 'b31'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.S'
    description:
      'Conflict among characters often arises from tensions generated by their different value
      systems.'
  - id: 'b32'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.T'
    description:
      'Different character, narrator, or speaker perspectives often reveal different information,
      develop different attitudes, and influence different interpretations of a text and the ideas
      in it.'
  - id: 'b33'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.U'
    description:
      'Foil characters (foils) serve to illuminate, through contrast, the traits, attributes, or
      values of another character.'
  - id: 'b34'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.V'
    description:
      'Inconsistencies between the private thoughts of characters and their actual behavior reveal
      tensions and complexities between private and professed values.'
  - id: 'b35'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.W'
    description:
      'A character’s competing, conflicting, or inconsistent choices or actions contribute to
      complexity in a text.'
  - id: 'b36'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.X'
    description:
      'Often the change in a character emerges directly from a conflict of values represented in the
      narrative.'
  - id: 'b37'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.Y'
    description: 'Changes in a character’s circumstances may lead to changes in that character.'
  - id: 'b38'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.Z'
    description:
      'While characters can change gradually over the course of a narrative, they can also change
      suddenly as the result of a moment of realization, known as an epiphany. An epiphany allows a
      character to see things in a new light and is often directly related to a central conflict of
      the narrative.'
  - id: 'b39'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AA'
    description:
      'An epiphany may affect the plot by causing a character to act on his or her sudden
      realization.'
  - id: 'b40'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AB'
    description: 'A group or force can function as a character.'
  - id: 'b41'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AC'
    description:
      'When readers consider a character, they should examine how that character interacts with
      other characters, groups, or forces and what those interactions may indicate about the
      character.'
  - id: 'b42'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AD'
    description:
      'The relationship between a character and a group, including the inclusion or exclusion of
      that character, reveals the collective attitude of the group toward that character and
      possibly the character’s attitude toward the group.'
  - id: 'b43'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AE'
    description:
      'Minor characters often remain unchanged because the narrative doesn’t focus on them. They may
      only be part of the narrative to advance the plot or to interact with major characters.'
  - id: 'b44'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AF'
    description:
      'Readers’ interpretations of a text are often affected by a character changing—or not—and the
      meaning conveyed by such changes or lack thereof.'
  - id: 'b45'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AG'
    description:
      'A character’s responses to the resolution of the narrative - in their words or in their
      actions - reveal something about that character’s own values; these responses may be
      inconsistent with the previously established behaviors or perspectives of that character.'
  - id: 'b46'
    itemType: 'Essential Knowledge'
    label: 'CHR-1.AH'
    description:
      'Inconsistencies and unexpected developments in a character affect readers’ interpretation of
      that character; other characters; events in the plot; conflicts; the perspective of the
      narrator, character, or speaker; and/or setting.'
  - id: 'b47'
    itemType: 'Essential Knowledge'
    label: 'SET-1.A'
    description: 'Setting includes the time and place during which the events of the text occur.'
  - id: 'b48'
    itemType: 'Essential Knowledge'
    label: 'SET-1.B'
    description:
      'Setting includes the social, cultural, and historical situation during which the events of
      the text occur.'
  - id: 'b49'
    itemType: 'Essential Knowledge'
    label: 'SET-1.C'
    description: 'A setting may help establish the mood and atmosphere of a narrative.'
  - id: 'b50'
    itemType: 'Essential Knowledge'
    label: 'SET-1.D'
    description: 'The environment a character inhabits provides information about that character.'
  - id: 'b51'
    itemType: 'Essential Knowledge'
    label: 'SET-1.E'
    description:
      'When a setting changes, it may suggest other movements, changes, or shifts in the narrative.'
  - id: 'b52'
    itemType: 'Essential Knowledge'
    label: 'SET-1.F'
    description:
      'Settings may be contrasted in order to establish a conflict of values or ideas associated
      with those settings.'
  - id: 'b53'
    itemType: 'Essential Knowledge'
    label: 'SET-1.G'
    description:
      'The way characters interact with their surroundings provides insights about those characters
      and the setting(s) they inhabit.'
  - id: 'b54'
    itemType: 'Essential Knowledge'
    label: 'SET-1.H'
    description:
      'The way characters behave in or describe their surroundings reveals an attitude about those
      surroundings and contributes to the development of those characters and readers’
      interpretations of them.'
  - id: 'b55'
    itemType: 'Essential Knowledge'
    label: 'STR-1.A'
    description:
      'Plot is the sequence of events in a narrative; events throughout a narrative are connected,
      with each event building on the others, often with a cause-and-effect relationship.'
  - id: 'b56'
    itemType: 'Essential Knowledge'
    label: 'STR-1.B'
    description:
      'The dramatic situation of a narrative includes the setting and action of the plot and how
      that narrative develops to place characters in conflict(s), and often involves the rising or
      falling fortunes of a main character or set of characters.'
  - id: 'b57'
    itemType: 'Essential Knowledge'
    label: 'STR-1.C'
    description:
      'Plot and the exposition that accompanies it focus readers’ attention on the parts of the
      narrative that matter most to its development, including characters, their relationships, and
      their roles in the narrative, as well as setting and the relationship between characters and
      setting.'
  - id: 'b58'
    itemType: 'Essential Knowledge'
    label: 'STR-1.D'
    description:
      'Line and stanza breaks contribute to the development and relationship of ideas in a poem.'
  - id: 'b59'
    itemType: 'Essential Knowledge'
    label: 'STR-1.E'
    description:
      'The arrangement of lines and stanzas contributes to the development and relationship of ideas
      in a poem.'
  - id: 'b60'
    itemType: 'Essential Knowledge'
    label: 'STR-1.F'
    description:
      'A text’s structure affects readers’ reactions and expectations by presenting the
      relationships among the ideas of the text via their relative positions and their placement
      within the text as a whole.'
  - id: 'b61'
    itemType: 'Essential Knowledge'
    label: 'STR-1.G'
    description:
      'Contrast can be introduced through focus; tone; point of view; character, narrator, or
      speaker perspective; dramatic situation or moment; settings or time; or imagery.'
  - id: 'b62'
    itemType: 'Essential Knowledge'
    label: 'STR-1.H'
    description: 'Contrasts are the result of shifts or juxtapositions or both.'
  - id: 'b63'
    itemType: 'Essential Knowledge'
    label: 'STR-1.I'
    description: 'Shifts may be signaled by a word, a structural convention, or punctuation.'
  - id: 'b64'
    itemType: 'Essential Knowledge'
    label: 'STR-1.J'
    description: 'Shifts may emphasize contrasts between particular segments of a text.'
  - id: 'b65'
    itemType: 'Essential Knowledge'
    label: 'STR-1.K'
    description:
      'A story, or narrative, is delivered through a series of events that relate to a conflict.'
  - id: 'b66'
    itemType: 'Essential Knowledge'
    label: 'STR-1.L'
    description:
      'Events include episodes, encounters, and scenes in a narrative that can introduce and develop
      a plot.'
  - id: 'b67'
    itemType: 'Essential Knowledge'
    label: 'STR-1.M'
    description:
      'The significance of an event depends on its relationship to the narrative, the conflict, and
      the development of characters.'
  - id: 'b68'
    itemType: 'Essential Knowledge'
    label: 'STR-1.N'
    description:
      'Conflict is tension between competing values either within a character, known as internal or
      psychological conflict, or with outside forces that obstruct a character in some way, known as
      external conflict.'
  - id: 'b69'
    itemType: 'Essential Knowledge'
    label: 'STR-1.O'
    description:
      'A text may contain multiple conflicts. Often two or more conflicts in a text intersect.'
  - id: 'b70'
    itemType: 'Essential Knowledge'
    label: 'STR-1.P'
    description:
      'A primary conflict can be heightened by the presence of additional conflicts that intersect
      with it.'
  - id: 'b71'
    itemType: 'Essential Knowledge'
    label: 'STR-1.Q'
    description:
      'Inconsistencies in a text may create contrasts that represent conflicts of values or
      perspectives.'
  - id: 'b72'
    itemType: 'Essential Knowledge'
    label: 'STR-1.R'
    description:
      'Some patterns in dramatic situations are so common that they are considered archetypes, and
      these archetypes create certain expectations for how the dramatic situations will progress and
      be resolved.'
  - id: 'b73'
    itemType: 'Essential Knowledge'
    label: 'STR-1.S'
    description:
      'The differences highlighted by a contrast emphasize the particular traits, aspects, or
      characteristics important for comparison of the things being contrasted.'
  - id: 'b74'
    itemType: 'Essential Knowledge'
    label: 'STR-1.T'
    description:
      'Contrasts often represent conflicts in values related to character, narrator, or speaker
      perspectives on ideas represented by a text.'
  - id: 'b75'
    itemType: 'Essential Knowledge'
    label: 'STR-1.U'
    description:
      'Closed forms of poetry include predictable patterns in the structure of lines, stanzas,
      meter, and rhyme, which develop relationships among ideas in the poem.'
  - id: 'b76'
    itemType: 'Essential Knowledge'
    label: 'STR-1.V'
    description:
      'Open forms of poetry may not follow expected or predictable patterns in the structure of
      their lines or stanzas, but they may still have structures that develop relationships between
      ideas in the poem.'
  - id: 'b77'
    itemType: 'Essential Knowledge'
    label: 'STR-1.W'
    description: 'Structures combine in texts to emphasize certain ideas and concepts.'
  - id: 'b78'
    itemType: 'Essential Knowledge'
    label: 'STR-1.X'
    description:
      'Some narrative structures interrupt the chronology of a plot; such structures include
      flashback, foreshadowing, in medias res, and stream of consciousness.'
  - id: 'b79'
    itemType: 'Essential Knowledge'
    label: 'STR-1.Y'
    description:
      'Narrative structures that interrupt the chronology of a plot, such as flashback,
      foreshadowing, in medias res, and stream of consciousness, can directly affect readers’
      experiences with a text by creating anticipation or suspense or building tension.'
  - id: 'b80'
    itemType: 'Essential Knowledge'
    label: 'STR-1.Z'
    description:
      'Contrasts often represent contradictions or inconsistencies that introduce nuance, ambiguity,
      or contradiction into a text. As a result, contrasts make texts more complex.'
  - id: 'b81'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AA'
    description:
      'Pacing is the manipulation of time in a text. Several factors contribute to the pace of a
      narrative, including arrangement of details, frequency of events, narrative structures,
      syntax, the tempo or speed at which events occur, or shifts in tense and chronology in the
      narrative.'
  - id: 'b82'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AB'
    description:
      'Narrative pacing may evoke an emotional reaction in readers by the order in which information
      is revealed; the relationships between the information, when it is provided, and other parts
      of the narrative; and the significance of the revealed information to other parts of the
      narrative.'
  - id: 'b83'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AC'
    description: 'Ideas and images in a poem may extend beyond a single line or stanza.'
  - id: 'b84'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AD'
    description: 'Punctuation is often crucial to the understanding of a text.'
  - id: 'b85'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AE'
    description:
      'When structural patterns are created in a text, any interruption in the pattern creates a
      point of emphasis.'
  - id: 'b86'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AF'
    description: 'Juxtaposition may create or demonstrate an antithesis.'
  - id: 'b87'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AG'
    description:
      'Situational or verbal irony is created when events or statements in a text are inconsistent
      with either the expectations readers bring to a text or the expectations established by the
      text itself.'
  - id: 'b88'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AH'
    description:
      'Paradox occurs when seemingly contradictory elements are juxtaposed, but the
      contradiction—which may or may not be reconciled—can reveal a hidden or unexpected idea.'
  - id: 'b89'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AI'
    description:
      'Significant events often illustrate competing value systems that relate to a conflict present
      in the text.'
  - id: 'b90'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AJ'
    description:
      'Events in a plot collide and accumulate to create a sense of anticipation and suspense.'
  - id: 'b91'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AK'
    description:
      'The resolution of the anticipation, suspense, or central conflicts of a plot may be referred
      to as the moment of catharsis or emotional release.'
  - id: 'b92'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AL'
    description:
      'Sometimes things not actually shown in a narrative, such as an unseen character or a
      preceding action, may be in conflict with or result in conflict for a character.'
  - id: 'b93'
    itemType: 'Essential Knowledge'
    label: 'STR-1.AM'
    description:
      'Although most plots end in resolution of the central conflicts, some have unresolved endings,
      and the lack of resolution may contribute to interpretations of the text.'
  - id: 'b94'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.A'
    description:
      'Narrators or speakers relate accounts to readers and establish a relationship between the
      text and the reader.'
  - id: 'b95'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.B'
    description:
      'Perspective refers to how narrators, characters, or speakers see their circumstances, while
      point of view refers to the position from which a narrator or speaker relates the events of a
      narrative.'
  - id: 'b96'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.C'
    description: 'A speaker or narrator is not necessarily the author.'
  - id: 'b97'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.D'
    description:
      'The point of view contributes to what narrators, characters, or speakers can and cannot
      provide in a text based on their level of involvement and intimacy with the details, events,
      or characters.'
  - id: 'b98'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.E'
    description:
      'Narrators may also be characters, and their role as characters may influence their
      perspective.'
  - id: 'b99'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.F'
    description:
      'First-person narrators are involved in the narrative; their relationship to the events of the
      plot and the other characters shapes their perspective.'
  - id: 'b100'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.G'
    description: 'Third-person narrators are outside observers.'
  - id: 'b101'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.H'
    description:
      'Third-person narrators’ knowledge about events and characters may range from observational to
      all-knowing, which shapes their perspective.'
  - id: 'b102'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.I'
    description:
      'The outside perspective of third-person narrators may not be affected by the events of the
      narrative.'
  - id: 'b103'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.J'
    description:
      'Narrators may function as characters in the narrative who directly address readers and either
      recall events or describe them as they occur.'
  - id: 'b104'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.K'
    description:
      'Narrative distance refers to the physical distance, chronological distance, relationships, or
      emotional investment of the narrator to the events or characters of the narrative.'
  - id: 'b105'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.L'
    description:
      'Stream of consciousness is a type of narration in which a character’s thoughts are related
      through a continuous dialogue or description.'
  - id: 'b106'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.M'
    description:
      'The narrators’, characters’, or speakers’ backgrounds and perspectives shape the tone they
      convey about subjects or events in the text.'
  - id: 'b107'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.N'
    description:
      'Descriptive words, such as adjectives and adverbs, not only qualify or modify the things they
      describe but also convey a perspective toward those things.'
  - id: 'b108'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.O'
    description:
      'The attitude of narrators, characters, or speakers toward an idea, character, or situation
      emerges from their perspective and may be referred to as tone.'
  - id: 'b109'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.P'
    description:
      "The narrator’s or speaker’s tone toward events or characters in a text influences readers'
      interpretation of the ideas associated with those things."
  - id: 'b110'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.Q'
    description:
      "The syntactical arrangement of phrases and clauses in a sentence can emphasize details or
      ideas and convey a narrator’s or speaker's tone."
  - id: 'b111'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.R'
    description:
      'Information included and/or not included in a text conveys the perspective of characters,
      narrators, and/or speakers.'
  - id: 'b112'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.S'
    description:
      "A narrator's or speaker's perspective may influence the details and amount of detail in a
      text and may reveal biases, motivations, or understandings."
  - id: 'b113'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.T'
    description:
      'Readers can infer narrators’ biases by noting which details they choose to include in a
      narrative and which they choose to omit.'
  - id: 'b114'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.U'
    description: 'Readers who detect bias in a narrator may find that narrator less reliable.'
  - id: 'b115'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.V'
    description:
      'The reliability of a narrator may influence a reader’s understanding of a character’s
      motives.'
  - id: 'b116'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.W'
    description:
      'Some narrators or speakers may provide details and information that others do not or cannot
      provide. Multiple narrators or speakers may provide contradictory information in a text.'
  - id: 'b117'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.X'
    description:
      'Multiple, and even contrasting, perspectives can occur within a single text and contribute to
      the complexity of the text.'
  - id: 'b118'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.Y'
    description:
      'A narrator or speaker may change over the course of a text as a result of actions and
      interactions.'
  - id: 'b119'
    itemType: 'Essential Knowledge'
    label: 'NAR-1.Z'
    description:
      'Changes and inconsistencies in a narrator’s or speaker’s perspective may contribute to irony
      or the complexity of the text.'
  - id: 'b120'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.A'
    description:
      'An antecedent is a word, phrase, or clause that precedes its referent. Referents may include
      pronouns, nouns, phrases, or clauses.'
  - id: 'b121'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.B'
    description:
      'Referents are ambiguous if they can refer to more than one antecedent, which affects
      interpretation.'
  - id: 'b122'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.C'
    description: 'Words or phrases may be repeated to emphasize ideas or associations.'
  - id: 'b123'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.D'
    description:
      'Alliteration is the repetition of the same letter sound at the beginning of adjacent or
      nearby words to emphasize those words and their associations or representations.'
  - id: 'b124'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.E'
    description:
      'A simile uses the words “like” or “as” to liken two objects or concepts to each other.'
  - id: 'b125'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.F'
    description:
      'Similes liken two different things to transfer the traits or qualities of one to the other.'
  - id: 'b126'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.G'
    description:
      'In a simile, the thing being compared is the main subject; the thing to which it is being
      compared is the comparison subject.'
  - id: 'b127'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.H'
    description:
      'A metaphor implies similarities between two (usually unrelated) concepts or objects in order
      to reveal or emphasize one or more things about one of them, though the differences between
      the two may also be revealing.'
  - id: 'b128'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.I'
    description:
      'In a metaphor, as in a simile, the thing being compared is the main subject; the thing to
      which it is being compared is the comparison subject.'
  - id: 'b129'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.J'
    description:
      'Comparisons between objects or concepts draw on the experiences and associations readers
      already have with those objects and concepts.'
  - id: 'b130'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.K'
    description:
      'Interpretation of a metaphor may depend on the context of its use; that is, what is happening
      in a text may determine what is transferred in the comparison.'
  - id: 'b131'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.L'
    description:
      'Words with multiple meanings or connotations add nuance or complexity that can contribute to
      interpretations of a text.'
  - id: 'b132'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.M'
    description:
      'Descriptive words, such as adjectives and adverbs, qualify or modify the things they describe
      and affect readers’ interaction with the text.'
  - id: 'b133'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.N'
    description:
      'Hyperbole exaggerates while understatement minimizes. Exaggerating or minimizing an aspect of
      an object focuses attention on that trait and conveys a perspective about the object.'
  - id: 'b134'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.O'
    description: 'Descriptive words, such as adjectives and adverbs, contribute to sensory imagery.'
  - id: 'b135'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.P'
    description:
      'An image can be literal or it can be a form of a comparison that represents something in a
      text through associations with the senses.'
  - id: 'b136'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.Q'
    description:
      'A collection of images, known as imagery, may emphasize ideas in parts of or throughout a
      text.'
  - id: 'b137'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.R'
    description:
      'Metaphorical comparisons do not focus solely on the objects being compared; they focus on the
      particular traits, qualities, or characteristics of the things being compared.'
  - id: 'b138'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.S'
    description:
      'Comparisons not only communicate literal meaning but may also convey figurative meaning or
      transmit a perspective.'
  - id: 'b139'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.T'
    description:
      'An extended metaphor is created when the comparison of a main subject and comparison subject
      persists through parts of or an entire text, and when the comparison is expanded through
      additional details, similes, and images.'
  - id: 'b140'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.U'
    description:
      'Interpretation of an extended metaphor may depend on the context of its use; that is, what is
      happening in a text may determine what is transferred in the comparison.'
  - id: 'b141'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.V'
    description:
      'Personification is a type of comparison that assigns a human trait or quality to a nonhuman
      object, entity, or idea, thus characterizing that object, entity, or idea.'
  - id: 'b142'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.W'
    description:
      'Allusions in a text can reference literary works including myths and sacred texts; other
      works of art including paintings and music; or people, places, or events outside the text.'
  - id: 'b143'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.X'
    description:
      'When a material object comes to represent, or stand for, an idea or concept, it becomes a
      symbol.'
  - id: 'b144'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.Y'
    description:
      'A symbol is an object that represents a meaning, so it is said to be symbolic or
      representative of that meaning. A symbol can represent different things depending on the
      experiences of a reader or the context of its use in a text.'
  - id: 'b145'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.Z'
    description:
      'Certain symbols are so common and recurrent that many readers have associations with them
      prior to reading a text. Other symbols are more contextualized and only come to represent
      certain things through their use in a particular text.'
  - id: 'b146'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AA'
    description:
      'When a character comes to represent, or stand for, an idea or concept, that character becomes
      symbolic; some symbolic characters have become so common they are archetypal.'
  - id: 'b147'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AB'
    description:
      'A setting may become symbolic when it is, or comes to be, associated with abstractions such
      as emotions, ideologies, and beliefs.'
  - id: 'b148'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AC'
    description:
      'Over time, some settings have developed certain associations such that they almost
      universally symbolize particular concepts.'
  - id: 'b149'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AD'
    description:
      'A motif is a unified pattern of recurring objects or images used to emphasize a significant
      idea in large parts of or throughout a text.'
  - id: 'b150'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AE'
    description:
      'The function of a simile relies on the selection of the objects being compared as well as the
      traits of the objects.'
  - id: 'b151'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AF'
    description:
      'By assigning the qualities of a nonhuman object, entity, or idea to a person or character,
      the narrator, character, or speaker communicates an attitude about that person or character.'
  - id: 'b152'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AG'
    description:
      'Ambiguity allows for different readings and understandings of a text by different readers.'
  - id: 'b153'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AH'
    description:
      'Symbols in a text and the way they are used may imply that a narrator, character, or speaker
      has a particular attitude or perspective.'
  - id: 'b154'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AI'
    description:
      'A conceit is a form of extended metaphor that often appears in poetry. Conceits develop
      complex comparisons that present images, concepts, and associations in surprising or
      paradoxical ways.'
  - id: 'b155'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AJ'
    description:
      'Often, conceits are used to make complex comparisons between the natural world and an
      individual.'
  - id: 'b156'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AK'
    description:
      'Multiple comparisons, representations, or associations may combine to affect one another in
      complex ways.'
  - id: 'b157'
    itemType: 'Essential Knowledge'
    label: 'FIG-1.AL'
    description:
      'Because of shared knowledge about a reference, allusions create emotional or intellectual
      associations and understandings.'
  - id: 'b158'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.A'
    description:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
  - id: 'b159'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.B'
    description: 'A claim is a statement that requires defense with evidence from the text.'
  - id: 'b160'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.C'
    description:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
  - id: 'b161'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.D'
    description:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
  - id: 'b162'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.E'
    description:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
  - id: 'b163'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.F'
    description:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
  - id: 'b164'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.G'
    description:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
  - id: 'b165'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.H'
    description:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
  - id: 'b166'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.I'
    description:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
  - id: 'b167'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.J'
    description:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
  - id: 'b168'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.K'
    description:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
  - id: 'b169'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.L'
    description:
      'Grammar and mechanics that follow established conventions of language allow writers to
      clearly communicate their interpretation of a text.'
  - id: 'b170'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.M'
    description:
      'The body paragraphs of a written argument develop the reasoning and justify claims using
      evidence and providing commentary that links the evidence to the overall thesis.'
  - id: 'b171'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.N'
    description:
      'Effective paragraphs are cohesive and often use topic sentences to state a claim and explain
      the reasoning that connects the various claims and evidence that make up the body of an essay.'
  - id: 'b172'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.O'
    description:
      'Coherence occurs at different levels in a piece of writing. In a sentence, the idea in one
      clause logically links to an idea in the next. In a paragraph, the idea in one sentence
      logically links to an idea in the next. In a text, the ideas in one paragraph logically link
      to the ideas in the next.'
  - id: 'b173'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.P'
    description:
      'Writers achieve coherence when the arrangement and organization of reasons, evidence, ideas,
      or details is logical. Writers may use transitions, repetition, synonyms, pronoun references,
      or parallel structure to indicate relationships between and among those reasons, evidence,
      ideas, or details.'
  - id: 'b174'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.Q'
    description:
      'Transitional elements are words or other elements (phrases, clauses, sentences, or
      paragraphs) that assist in creating coherence between sentences and paragraphs by showing
      relationships between ideas.'
  - id: 'b175'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.R'
    description:
      'Writers convey their ideas in a sentence through strategic selection and placement of phrases
      and clauses. Writers may use coordination to illustrate a balance or equality between ideas or
      subordination to illustrate an imbalance or inequality.'
  - id: 'b176'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.S'
    description: 'Writers use words that enhance the clear communication of an interpretation.'
  - id: 'b177'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.T'
    description: 'Punctuation conveys relationships between and among parts of a sentence.'
  - id: 'b178'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.U'
    description:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
  - id: 'b179'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.V'
    description:
      'Textual evidence may require revision to an interpretation and a line of reasoning if the
      evidence does not sufficiently support the initial interpretation and line of reasoning.'
  - id: 'b180'
    itemType: 'Essential Knowledge'
    label: 'LAN-1.W'
    description:
      'Writers must acknowledge words, ideas, images, texts, and other intellectual property of
      others through attribution, citation, or reference.'
  - id: 'b181'
    itemType: 'Skill Category'
    label: '1'
    description: 'Explain the function of character.'
  - id: 'b182'
    itemType: 'Skill Category'
    label: '2'
    description: 'Explain the function of setting.'
  - id: 'b183'
    itemType: 'Skill Category'
    label: '3'
    description: 'Explain the function of plot and structure.'
  - id: 'b184'
    itemType: 'Skill Category'
    label: '4'
    description: 'Explain the function of the narrator or speaker.'
  - id: 'b185'
    itemType: 'Skill Category'
    label: '5'
    description: 'Explain the function of word choice, imagery, and symbols.'
  - id: 'b186'
    itemType: 'Skill Category'
    label: '6'
    description: 'Explain the function of comparison.'
  - id: 'b187'
    itemType: 'Skill Category'
    label: '7'
    description:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
  - id: 'b188'
    itemType: 'Skill'
    label: '1.A'
    description:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
  - id: 'b189'
    itemType: 'Skill'
    label: '1.B'
    description: 'Explain the function of a character changing or remaining unchanged.'
  - id: 'b190'
    itemType: 'Skill'
    label: '1.C'
    description: 'Explain the function of contrasting characters.'
  - id: 'b191'
    itemType: 'Skill'
    label: '1.D'
    description:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
  - id: 'b192'
    itemType: 'Skill'
    label: '1.E'
    description:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
  - id: 'b193'
    itemType: 'Skill'
    label: '2.A'
    description: 'Identify and describe specific textual details that convey or reveal a setting.'
  - id: 'b194'
    itemType: 'Skill'
    label: '2.B'
    description: 'Explain the function of setting in a narrative.'
  - id: 'b195'
    itemType: 'Skill'
    label: '2.C'
    description: 'Describe the relationship between a character and a setting.'
  - id: 'b196'
    itemType: 'Skill'
    label: '3.A'
    description: 'Identify and describe how plot orders events in a narrative.'
  - id: 'b197'
    itemType: 'Skill'
    label: '3.B'
    description: 'Explain the function of a particular sequence of events in a plot.'
  - id: 'b198'
    itemType: 'Skill'
    label: '3.C'
    description: 'Explain the function of structure in a text.'
  - id: 'b199'
    itemType: 'Skill'
    label: '3.D'
    description: 'Explain the function of contrasts within a text.'
  - id: 'b200'
    itemType: 'Skill'
    label: '3.E'
    description:
      'Explain the function of a significant event or related set of significant events in a plot.'
  - id: 'b201'
    itemType: 'Skill'
    label: '3.F'
    description: 'Explain the function of conflict in a text.'
  - id: 'b202'
    itemType: 'Skill'
    label: '4.A'
    description: 'Identify and describe the narrator or speaker of a text.'
  - id: 'b203'
    itemType: 'Skill'
    label: '4.B'
    description: 'Identify and explain the function of point of view in a narrative.'
  - id: 'b204'
    itemType: 'Skill'
    label: '4.C'
    description:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
  - id: 'b205'
    itemType: 'Skill'
    label: '4.D'
    description: 'Explain how a narrator’s reliability affects a narrative.'
  - id: 'b206'
    itemType: 'Skill'
    label: '5.A'
    description: 'Distinguish between the literal and figurative meanings of words and phrases.'
  - id: 'b207'
    itemType: 'Skill'
    label: '5.B'
    description: 'Explain the function of specific words and phrases in a text.'
  - id: 'b208'
    itemType: 'Skill'
    label: '5.C'
    description: 'Identify and explain the function of a symbol.'
  - id: 'b209'
    itemType: 'Skill'
    label: '5.D'
    description: 'Identify and explain the function of an image or imagery.'
  - id: 'b210'
    itemType: 'Skill'
    label: '6.A'
    description: 'Identify and explain the function of a simile.'
  - id: 'b211'
    itemType: 'Skill'
    label: '6.B'
    description: 'Identify and explain the function of a metaphor.'
  - id: 'b212'
    itemType: 'Skill'
    label: '6.C'
    description: 'Identify and explain the function of personification.'
  - id: 'b213'
    itemType: 'Skill'
    label: '6.D'
    description: 'Identify and explain the function of an allusion.'
  - id: 'b214'
    itemType: 'Skill'
    label: '7.A'
    description:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
  - id: 'b215'
    itemType: 'Skill'
    label: '7.B'
    description:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
  - id: 'b216'
    itemType: 'Skill'
    label: '7.C'
    description:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
  - id: 'b217'
    itemType: 'Skill'
    label: '7.D'
    description:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
  - id: 'b218'
    itemType: 'Skill'
    label: '7.E'
    description: 'Demonstrate control over the elements of composition to communicate clearly.'
  - id: 'b219'
    itemType: 'Unit'
    label: '1'
    description: 'Short Fiction I'
  - id: 'b220'
    itemType: 'Unit'
    label: '2'
    description: 'Poetry I'
  - id: 'b221'
    itemType: 'Unit'
    label: '3'
    description: 'Longer Fiction or Drama I'
  - id: 'b222'
    itemType: 'Unit'
    label: '4'
    description: 'Short Fiction II'
  - id: 'b223'
    itemType: 'Unit'
    label: '5'
    description: 'Poetry II'
  - id: 'b224'
    itemType: 'Unit'
    label: '6'
    description: 'Longer Fiction or Drama II'
  - id: 'b225'
    itemType: 'Unit'
    label: '7'
    description: 'Short Fiction III'
  - id: 'b226'
    itemType: 'Unit'
    label: '8'
    description: 'Poetry III'
  - id: 'b227'
    itemType: 'Unit'
    label: '9'
    description: 'Longer Fiction or Drama III'
  - id: 'b228'
    itemType: 'Question Type'
    description: 'MCQ'
  - id: 'b229'
    itemType: 'Question Type'
    description: 'FRQ: Poetry'
  - id: 'b230'
    itemType: 'Question Type'
    description: 'FRQ: Prose'
  - id: 'b231'
    itemType: 'Question Type'
    description: 'FRQ: Open'
  - id: 'b232'
    itemType: 'Stimulus Type'
    description: 'Poetry'
  - id: 'b233'
    itemType: 'Stimulus Type'
    description: 'Prose'
  - id: 'b234'
    itemType: 'Stimulus Text Origin'
    description: 'American'
  - id: 'b235'
    itemType: 'Stimulus Text Origin'
    description: 'British'
  - id: 'b236'
    itemType: 'Stimulus Text Origin'
    description: 'Other'
  - id: 'b237'
    itemType: 'Stimulus Difficulty'
    description: 'Easy'
  - id: 'b238'
    itemType: 'Stimulus Difficulty'
    description: 'Moderate'
  - id: 'b239'
    itemType: 'Stimulus Difficulty'
    description: 'Challenging'
  - id: 'b240'
    itemType: 'Stimulus Time Period'
    description: '18th century and earlier (prior to 1800)'
  - id: 'b241'
    itemType: 'Stimulus Time Period'
    description: 'Early 19th century (1800 to 1850)'
  - id: 'b242'
    itemType: 'Stimulus Time Period'
    description: 'Mid-19th century (1851 to 1870)'
  - id: 'b243'
    itemType: 'Stimulus Time Period'
    description: 'Late 19th century (1871 to 1910)'
  - id: 'b244'
    itemType: 'Stimulus Time Period'
    description: 'Early 20th century (1911 to 1945)'
  - id: 'b245'
    itemType: 'Stimulus Time Period'
    description: 'Mid-20th century (1946 to 1980)'
  - id: 'b246'
    itemType: 'Stimulus Time Period'
    description: 'Late 20th century (1981 to 2000)'
  - id: 'b247'
    itemType: 'Stimulus Time Period'
    description: 'Contemporary (post 2000)'
  - id: 'b248'
    itemType: 'Stimulus Theme'
    description: 'Anxiety'
  - id: 'b249'
    itemType: 'Stimulus Theme'
    description: 'Class'
  - id: 'b250'
    itemType: 'Stimulus Theme'
    description: 'Childhood'
  - id: 'b251'
    itemType: 'Stimulus Theme'
    description: 'Culture Education / Schooling'
  - id: 'b252'
    itemType: 'Stimulus Theme'
    description: 'Culture'
  - id: 'b253'
    itemType: 'Stimulus Theme'
    description: 'Education / Schooling'
  - id: 'b254'
    itemType: 'Stimulus Theme'
    description: 'Exploration'
  - id: 'b255'
    itemType: 'Stimulus Theme'
    description: 'Family'
  - id: 'b256'
    itemType: 'Stimulus Theme'
    description: 'Friendship'
  - id: 'b257'
    itemType: 'Stimulus Theme'
    description: 'Gender Roles / Rights'
  - id: 'b258'
    itemType: 'Stimulus Theme'
    description: 'Growing-up / Growing-Older'
  - id: 'b259'
    itemType: 'Stimulus Theme'
    description: 'Home / Homeland'
  - id: 'b260'
    itemType: 'Stimulus Theme'
    description: 'Humor / Satire Identity'
  - id: 'b261'
    itemType: 'Stimulus Theme'
    description: 'Humor'
  - id: 'b262'
    itemType: 'Stimulus Theme'
    description: 'Identity'
  - id: 'b263'
    itemType: 'Stimulus Theme'
    description: 'Innocence Lost'
  - id: 'b264'
    itemType: 'Stimulus Theme'
    description: 'Knowledge is Power'
  - id: 'b265'
    itemType: 'Stimulus Theme'
    description: 'Nature / Environment'
  - id: 'b266'
    itemType: 'Stimulus Theme'
    description: 'Law & Justice'
  - id: 'b267'
    itemType: 'Stimulus Theme'
    description: 'Life & Death'
  - id: 'b268'
    itemType: 'Stimulus Theme'
    description: 'Love & Longing'
  - id: 'b269'
    itemType: 'Stimulus Theme'
    description: 'Monsters & Heroes'
  - id: 'b270'
    itemType: 'Stimulus Theme'
    description: 'Philosophy Pop Culture'
  - id: 'b271'
    itemType: 'Stimulus Theme'
    description: 'Philosophy'
  - id: 'b272'
    itemType: 'Stimulus Theme'
    description: 'Pop Culture'
  - id: 'b273'
    itemType: 'Stimulus Theme'
    description: 'Psychology & Behavior'
  - id: 'b274'
    itemType: 'Stimulus Theme'
    description: 'Race'
  - id: 'b275'
    itemType: 'Stimulus Theme'
    description: 'Religion / Spirituality'
  - id: 'b276'
    itemType: 'Stimulus Theme'
    description: 'Right & Wrong'
  - id: 'b277'
    itemType: 'Stimulus Theme'
    description: 'Science'
  - id: 'b278'
    itemType: 'Stimulus Theme'
    description: 'Self-Discovery'
  - id: 'b279'
    itemType: 'Stimulus Theme'
    description: 'Self-Reliance'
  - id: 'b280'
    itemType: 'Stimulus Theme'
    description: 'Work'
  - id: 'c1'
    itemType: ''
    description: 'Big Ideas and Enduring Understanding'
  - id: 'c2'
    itemType: ''
    description: 'Course Skills'
  - id: 'c3'
    itemType: ''
    description: 'Units'
  - id: 'c4'
    itemType: ''
    description: 'Additional Metadata'
  - id: '79298333'
    itemType: 'Item Difficulty'
    description: 'Emerging'
  - id: '2921795'
    itemType: 'Item Difficulty'
    description: 'Proficient'
  - id: '3624495'
    itemType: 'Item Difficulty'
    description: 'Advanced'

associations:
  - originID: 'c1'
    originDesc: 'Big Ideas and Enduring Understanding'
    targetID: 'a1'
    targetDesc: 'Big Idea'
    associationType: 'isDefinedBy'
  - originID: 'c2'
    originDesc: 'Course Skills'
    targetID: 'a4'
    targetDesc: 'Skill Category'
    associationType: 'isDefinedBy'
  - originID: 'c3'
    originDesc: 'Units'
    targetID: 'a6'
    targetDesc: 'Unit'
    associationType: 'isDefinedBy'
  - originID: 'a1'
    originDesc: 'Big Idea'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a2'
    originDesc: 'Enduring Understanding'
    targetID: 'a1'
    targetDesc: 'Big Idea'
    associationType: 'isChildOf'
  - originID: 'a3'
    originDesc: 'Essential Knowledge'
    targetID: 'a2'
    targetDesc: 'Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'a4'
    originDesc: 'Skill Category'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a5'
    originDesc: 'Skill'
    targetID: 'a4'
    targetDesc: 'Skill Category'
    associationType: 'isChildOf'
  - originID: 'a6'
    originDesc: 'Unit'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a7'
    originDesc: 'Question Type'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a8'
    originDesc: 'Stimulus Type'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a9'
    originDesc: 'Stimulus Text Origin'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a10'
    originDesc: 'Stimulus Difficulty'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a11'
    originDesc: 'Stimulus Time Period'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a12'
    originDesc: 'Stimulus Theme'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a13'
    originDesc: 'Stimulus Title'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'a14'
    originDesc: 'Stimulus Author'
    targetID: 'c4'
    targetDesc: 'Additional Metadata'
    associationType: 'isChildOf'
  - originID: 'b1'
    originLabel: 'CHR'
    originDesc: 'Character'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b2'
    originLabel: 'SET'
    originDesc: 'Setting'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b3'
    originLabel: 'STR'
    originDesc: 'Structure'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b4'
    originLabel: 'NAR'
    originDesc: 'Narration'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b5'
    originLabel: 'FIG'
    originDesc: 'Figurative Language'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b6'
    originLabel: 'LAN'
    originDesc: 'Literary Argumentation'
    targetID: 'c1'
    targetDesc: 'Big Ideas and Enduring Understanding'
    associationType: 'isChildOf'
  - originID: 'b7'
    originLabel: 'CHR-1'
    originDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    targetID: 'b1'
    targetLabel: 'CHR'
    targetDesc: 'Character'
    associationType: 'isChildOf'
  - originID: 'b8'
    originLabel: 'SET-1'
    originDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    targetID: 'b2'
    targetLabel: 'SET'
    targetDesc: 'Setting'
    associationType: 'isChildOf'
  - originID: 'b9'
    originLabel: 'STR-1'
    originDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    targetID: 'b3'
    targetLabel: 'STR'
    targetDesc: 'Structure'
    associationType: 'isChildOf'
  - originID: 'b10'
    originLabel: 'NAR-1'
    originDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    targetID: 'b4'
    targetLabel: 'NAR'
    targetDesc: 'Narration'
    associationType: 'isChildOf'
  - originID: 'b11'
    originLabel: 'FIG-1'
    originDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    targetID: 'b5'
    targetLabel: 'FIG'
    targetDesc: 'Figurative Language'
    associationType: 'isChildOf'
  - originID: 'b12'
    originLabel: 'LAN-1'
    originDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    targetID: 'b6'
    targetLabel: 'LAN'
    targetDesc: 'Literary Argumentation'
    associationType: 'isChildOf'
  - originID: 'b13'
    originLabel: 'CHR-1.A'
    originDesc: 'Description, dialogue, and behavior reveal characters to readers.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b14'
    originLabel: 'CHR-1.B'
    originDesc:
      'Descriptions of characters may come from a speaker, narrator, other characters, or the
      characters themselves.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b15'
    originLabel: 'CHR-1.C'
    originDesc:
      'Perspective is how narrators, characters, or speakers understand their circumstances, and is
      informed by background, personality traits, biases, and relationships.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b16'
    originLabel: 'CHR-1.D'
    originDesc:
      'A character’s perspective is both shaped and revealed by relationships with other characters,
      the environment, the events of the plot, and the ideas expressed in the text.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b17'
    originLabel: 'CHR-1.E'
    originDesc:
      'Characters reveal their perspectives and biases through the words they use, the details they
      provide in the text, the organization of their thinking, the decisions they make, and the
      actions they take.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b18'
    originLabel: 'CHR-1.F'
    originDesc:
      'The description of a character creates certain expectations for that character’s behaviors;
      how a character does or does not meet those expectations affects a reader’s interpretation of
      that character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b19'
    originLabel: 'CHR-1.G'
    originDesc:
      'Details associated with a character and/or used to describe a character contribute to a
      reader’s interpretation of that character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b20'
    originLabel: 'CHR-1.H'
    originDesc:
      'Readers’ understanding of a character’s perspective may depend on the perspective of the
      narrator or speaker.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b21'
    originLabel: 'CHR-1.I'
    originDesc: 'A character’s perspective may shift during the course of a narrative.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b22'
    originLabel: 'CHR-1.J'
    originDesc:
      'When narrators, characters, or speakers compare another character to something or someone
      else, they reveal their perspective on the compared character and may also reveal something
      innate about the compared character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b23'
    originLabel: 'CHR-1.K'
    originDesc:
      'Readers can infer a character’s motives from that character’s actions or inactions.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b24'
    originLabel: 'CHR-1.L'
    originDesc:
      'A dynamic character who develops over the course of the narrative often makes choices that
      directly or indirectly affect the climax and/or the resolution of that narrative.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b25'
    originLabel: 'CHR-1.M'
    originDesc:
      'Character changes can be visible and external, such as changes to health or wealth, or can be
      internal, psychological or emotional changes; external changes can lead to internal changes,
      and vice versa.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b26'
    originLabel: 'CHR-1.N'
    originDesc:
      'Some characters remain unchanged or are largely unaffected by the events of the narrative.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b27'
    originLabel: 'CHR-1.O'
    originDesc:
      'The significance of characters is often revealed through their agency and through nuanced
      descriptions.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b28'
    originLabel: 'CHR-1.P'
    originDesc: 'Characters’ choices—in speech, action, and inaction—reveal what they value.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b29'
    originLabel: 'CHR-1.Q'
    originDesc:
      'The main character in a narrative is the protagonist; the antagonist in the narrative opposes
      the protagonist and may be another character, the internal conflicts of the protagonist, a
      collective (such as society), or nature.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b30'
    originLabel: 'CHR-1.R'
    originDesc: 'Protagonists and antagonists may represent contrasting values.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b31'
    originLabel: 'CHR-1.S'
    originDesc:
      'Conflict among characters often arises from tensions generated by their different value
      systems.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b32'
    originLabel: 'CHR-1.T'
    originDesc:
      'Different character, narrator, or speaker perspectives often reveal different information,
      develop different attitudes, and influence different interpretations of a text and the ideas
      in it.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b33'
    originLabel: 'CHR-1.U'
    originDesc:
      'Foil characters (foils) serve to illuminate, through contrast, the traits, attributes, or
      values of another character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b34'
    originLabel: 'CHR-1.V'
    originDesc:
      'Inconsistencies between the private thoughts of characters and their actual behavior reveal
      tensions and complexities between private and professed values.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b35'
    originLabel: 'CHR-1.W'
    originDesc:
      'A character’s competing, conflicting, or inconsistent choices or actions contribute to
      complexity in a text.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b36'
    originLabel: 'CHR-1.X'
    originDesc:
      'Often the change in a character emerges directly from a conflict of values represented in the
      narrative.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b37'
    originLabel: 'CHR-1.Y'
    originDesc: 'Changes in a character’s circumstances may lead to changes in that character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b38'
    originLabel: 'CHR-1.Z'
    originDesc:
      'While characters can change gradually over the course of a narrative, they can also change
      suddenly as the result of a moment of realization, known as an epiphany. An epiphany allows a
      character to see things in a new light and is often directly related to a central conflict of
      the narrative.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b39'
    originLabel: 'CHR-1.AA'
    originDesc:
      'An epiphany may affect the plot by causing a character to act on his or her sudden
      realization.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b40'
    originLabel: 'CHR-1.AB'
    originDesc: 'A group or force can function as a character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b41'
    originLabel: 'CHR-1.AC'
    originDesc:
      'When readers consider a character, they should examine how that character interacts with
      other characters, groups, or forces and what those interactions may indicate about the
      character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b42'
    originLabel: 'CHR-1.AD'
    originDesc:
      'The relationship between a character and a group, including the inclusion or exclusion of
      that character, reveals the collective attitude of the group toward that character and
      possibly the character’s attitude toward the group.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b43'
    originLabel: 'CHR-1.AE'
    originDesc:
      'Minor characters often remain unchanged because the narrative doesn’t focus on them. They may
      only be part of the narrative to advance the plot or to interact with major characters.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b44'
    originLabel: 'CHR-1.AF'
    originDesc:
      'Readers’ interpretations of a text are often affected by a character changing—or not—and the
      meaning conveyed by such changes or lack thereof.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b45'
    originLabel: 'CHR-1.AG'
    originDesc:
      'A character’s responses to the resolution of the narrative - in their words or in their
      actions - reveal something about that character’s own values; these responses may be
      inconsistent with the previously established behaviors or perspectives of that character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b46'
    originLabel: 'CHR-1.AH'
    originDesc:
      'Inconsistencies and unexpected developments in a character affect readers’ interpretation of
      that character; other characters; events in the plot; conflicts; the perspective of the
      narrator, character, or speaker; and/or setting.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isChildOf'
  - originID: 'b47'
    originLabel: 'SET-1.A'
    originDesc: 'Setting includes the time and place during which the events of the text occur.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b48'
    originLabel: 'SET-1.B'
    originDesc:
      'Setting includes the social, cultural, and historical situation during which the events of
      the text occur.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b49'
    originLabel: 'SET-1.C'
    originDesc: 'A setting may help establish the mood and atmosphere of a narrative.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b50'
    originLabel: 'SET-1.D'
    originDesc: 'The environment a character inhabits provides information about that character.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b51'
    originLabel: 'SET-1.E'
    originDesc:
      'When a setting changes, it may suggest other movements, changes, or shifts in the narrative.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b52'
    originLabel: 'SET-1.F'
    originDesc:
      'Settings may be contrasted in order to establish a conflict of values or ideas associated
      with those settings.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b53'
    originLabel: 'SET-1.G'
    originDesc:
      'The way characters interact with their surroundings provides insights about those characters
      and the setting(s) they inhabit.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b54'
    originLabel: 'SET-1.H'
    originDesc:
      'The way characters behave in or describe their surroundings reveals an attitude about those
      surroundings and contributes to the development of those characters and readers’
      interpretations of them.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isChildOf'
  - originID: 'b55'
    originLabel: 'STR-1.A'
    originDesc:
      'Plot is the sequence of events in a narrative; events throughout a narrative are connected,
      with each event building on the others, often with a cause-and-effect relationship.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b56'
    originLabel: 'STR-1.B'
    originDesc:
      'The dramatic situation of a narrative includes the setting and action of the plot and how
      that narrative develops to place characters in conflict(s), and often involves the rising or
      falling fortunes of a main character or set of characters.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b57'
    originLabel: 'STR-1.C'
    originDesc:
      'Plot and the exposition that accompanies it focus readers’ attention on the parts of the
      narrative that matter most to its development, including characters, their relationships, and
      their roles in the narrative, as well as setting and the relationship between characters and
      setting.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b58'
    originLabel: 'STR-1.D'
    originDesc:
      'Line and stanza breaks contribute to the development and relationship of ideas in a poem.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b59'
    originLabel: 'STR-1.E'
    originDesc:
      'The arrangement of lines and stanzas contributes to the development and relationship of ideas
      in a poem.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b60'
    originLabel: 'STR-1.F'
    originDesc:
      'A text’s structure affects readers’ reactions and expectations by presenting the
      relationships among the ideas of the text via their relative positions and their placement
      within the text as a whole.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b61'
    originLabel: 'STR-1.G'
    originDesc:
      'Contrast can be introduced through focus; tone; point of view; character, narrator, or
      speaker perspective; dramatic situation or moment; settings or time; or imagery.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b62'
    originLabel: 'STR-1.H'
    originDesc: 'Contrasts are the result of shifts or juxtapositions or both.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b63'
    originLabel: 'STR-1.I'
    originDesc: 'Shifts may be signaled by a word, a structural convention, or punctuation.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b64'
    originLabel: 'STR-1.J'
    originDesc: 'Shifts may emphasize contrasts between particular segments of a text.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b65'
    originLabel: 'STR-1.K'
    originDesc:
      'A story, or narrative, is delivered through a series of events that relate to a conflict.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b66'
    originLabel: 'STR-1.L'
    originDesc:
      'Events include episodes, encounters, and scenes in a narrative that can introduce and develop
      a plot.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b67'
    originLabel: 'STR-1.M'
    originDesc:
      'The significance of an event depends on its relationship to the narrative, the conflict, and
      the development of characters.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b68'
    originLabel: 'STR-1.N'
    originDesc:
      'Conflict is tension between competing values either within a character, known as internal or
      psychological conflict, or with outside forces that obstruct a character in some way, known as
      external conflict.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b69'
    originLabel: 'STR-1.O'
    originDesc:
      'A text may contain multiple conflicts. Often two or more conflicts in a text intersect.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b70'
    originLabel: 'STR-1.P'
    originDesc:
      'A primary conflict can be heightened by the presence of additional conflicts that intersect
      with it.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b71'
    originLabel: 'STR-1.Q'
    originDesc:
      'Inconsistencies in a text may create contrasts that represent conflicts of values or
      perspectives.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b72'
    originLabel: 'STR-1.R'
    originDesc:
      'Some patterns in dramatic situations are so common that they are considered archetypes, and
      these archetypes create certain expectations for how the dramatic situations will progress and
      be resolved.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b73'
    originLabel: 'STR-1.S'
    originDesc:
      'The differences highlighted by a contrast emphasize the particular traits, aspects, or
      characteristics important for comparison of the things being contrasted.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b74'
    originLabel: 'STR-1.T'
    originDesc:
      'Contrasts often represent conflicts in values related to character, narrator, or speaker
      perspectives on ideas represented by a text.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b75'
    originLabel: 'STR-1.U'
    originDesc:
      'Closed forms of poetry include predictable patterns in the structure of lines, stanzas,
      meter, and rhyme, which develop relationships among ideas in the poem.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b76'
    originLabel: 'STR-1.V'
    originDesc:
      'Open forms of poetry may not follow expected or predictable patterns in the structure of
      their lines or stanzas, but they may still have structures that develop relationships between
      ideas in the poem.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b77'
    originLabel: 'STR-1.W'
    originDesc: 'Structures combine in texts to emphasize certain ideas and concepts.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b78'
    originLabel: 'STR-1.X'
    originDesc:
      'Some narrative structures interrupt the chronology of a plot; such structures include
      flashback, foreshadowing, in medias res, and stream of consciousness.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b79'
    originLabel: 'STR-1.Y'
    originDesc:
      'Narrative structures that interrupt the chronology of a plot, such as flashback,
      foreshadowing, in medias res, and stream of consciousness, can directly affect readers’
      experiences with a text by creating anticipation or suspense or building tension.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b80'
    originLabel: 'STR-1.Z'
    originDesc:
      'Contrasts often represent contradictions or inconsistencies that introduce nuance, ambiguity,
      or contradiction into a text. As a result, contrasts make texts more complex.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b81'
    originLabel: 'STR-1.AA'
    originDesc:
      'Pacing is the manipulation of time in a text. Several factors contribute to the pace of a
      narrative, including arrangement of details, frequency of events, narrative structures,
      syntax, the tempo or speed at which events occur, or shifts in tense and chronology in the
      narrative.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b82'
    originLabel: 'STR-1.AB'
    originDesc:
      'Narrative pacing may evoke an emotional reaction in readers by the order in which information
      is revealed; the relationships between the information, when it is provided, and other parts
      of the narrative; and the significance of the revealed information to other parts of the
      narrative.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b83'
    originLabel: 'STR-1.AC'
    originDesc: 'Ideas and images in a poem may extend beyond a single line or stanza.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b84'
    originLabel: 'STR-1.AD'
    originDesc: 'Punctuation is often crucial to the understanding of a text.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b85'
    originLabel: 'STR-1.AE'
    originDesc:
      'When structural patterns are created in a text, any interruption in the pattern creates a
      point of emphasis.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b86'
    originLabel: 'STR-1.AF'
    originDesc: 'Juxtaposition may create or demonstrate an antithesis.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b87'
    originLabel: 'STR-1.AG'
    originDesc:
      'Situational or verbal irony is created when events or statements in a text are inconsistent
      with either the expectations readers bring to a text or the expectations established by the
      text itself.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b88'
    originLabel: 'STR-1.AH'
    originDesc:
      'Paradox occurs when seemingly contradictory elements are juxtaposed, but the
      contradiction—which may or may not be reconciled—can reveal a hidden or unexpected idea.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b89'
    originLabel: 'STR-1.AI'
    originDesc:
      'Significant events often illustrate competing value systems that relate to a conflict present
      in the text.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b90'
    originLabel: 'STR-1.AJ'
    originDesc:
      'Events in a plot collide and accumulate to create a sense of anticipation and suspense.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b91'
    originLabel: 'STR-1.AK'
    originDesc:
      'The resolution of the anticipation, suspense, or central conflicts of a plot may be referred
      to as the moment of catharsis or emotional release.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b92'
    originLabel: 'STR-1.AL'
    originDesc:
      'Sometimes things not actually shown in a narrative, such as an unseen character or a
      preceding action, may be in conflict with or result in conflict for a character.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b93'
    originLabel: 'STR-1.AM'
    originDesc:
      'Although most plots end in resolution of the central conflicts, some have unresolved endings,
      and the lack of resolution may contribute to interpretations of the text.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isChildOf'
  - originID: 'b94'
    originLabel: 'NAR-1.A'
    originDesc:
      'Narrators or speakers relate accounts to readers and establish a relationship between the
      text and the reader.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b95'
    originLabel: 'NAR-1.B'
    originDesc:
      'Perspective refers to how narrators, characters, or speakers see their circumstances, while
      point of view refers to the position from which a narrator or speaker relates the events of a
      narrative.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b96'
    originLabel: 'NAR-1.C'
    originDesc: 'A speaker or narrator is not necessarily the author.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b97'
    originLabel: 'NAR-1.D'
    originDesc:
      'The point of view contributes to what narrators, characters, or speakers can and cannot
      provide in a text based on their level of involvement and intimacy with the details, events,
      or characters.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b98'
    originLabel: 'NAR-1.E'
    originDesc:
      'Narrators may also be characters, and their role as characters may influence their
      perspective.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b99'
    originLabel: 'NAR-1.F'
    originDesc:
      'First-person narrators are involved in the narrative; their relationship to the events of the
      plot and the other characters shapes their perspective.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b100'
    originLabel: 'NAR-1.G'
    originDesc: 'Third-person narrators are outside observers.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b101'
    originLabel: 'NAR-1.H'
    originDesc:
      'Third-person narrators’ knowledge about events and characters may range from observational to
      all-knowing, which shapes their perspective.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b102'
    originLabel: 'NAR-1.I'
    originDesc:
      'The outside perspective of third-person narrators may not be affected by the events of the
      narrative.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b103'
    originLabel: 'NAR-1.J'
    originDesc:
      'Narrators may function as characters in the narrative who directly address readers and either
      recall events or describe them as they occur.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b104'
    originLabel: 'NAR-1.K'
    originDesc:
      'Narrative distance refers to the physical distance, chronological distance, relationships, or
      emotional investment of the narrator to the events or characters of the narrative.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b105'
    originLabel: 'NAR-1.L'
    originDesc:
      'Stream of consciousness is a type of narration in which a character’s thoughts are related
      through a continuous dialogue or description.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b106'
    originLabel: 'NAR-1.M'
    originDesc:
      'The narrators’, characters’, or speakers’ backgrounds and perspectives shape the tone they
      convey about subjects or events in the text.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b107'
    originLabel: 'NAR-1.N'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, not only qualify or modify the things they
      describe but also convey a perspective toward those things.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b108'
    originLabel: 'NAR-1.O'
    originDesc:
      'The attitude of narrators, characters, or speakers toward an idea, character, or situation
      emerges from their perspective and may be referred to as tone.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b109'
    originLabel: 'NAR-1.P'
    originDesc:
      "The narrator’s or speaker’s tone toward events or characters in a text influences readers'
      interpretation of the ideas associated with those things."
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b110'
    originLabel: 'NAR-1.Q'
    originDesc:
      "The syntactical arrangement of phrases and clauses in a sentence can emphasize details or
      ideas and convey a narrator’s or speaker's tone."
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b111'
    originLabel: 'NAR-1.R'
    originDesc:
      'Information included and/or not included in a text conveys the perspective of characters,
      narrators, and/or speakers.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b112'
    originLabel: 'NAR-1.S'
    originDesc:
      "A narrator's or speaker's perspective may influence the details and amount of detail in a
      text and may reveal biases, motivations, or understandings."
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b113'
    originLabel: 'NAR-1.T'
    originDesc:
      'Readers can infer narrators’ biases by noting which details they choose to include in a
      narrative and which they choose to omit.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b114'
    originLabel: 'NAR-1.U'
    originDesc: 'Readers who detect bias in a narrator may find that narrator less reliable.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b115'
    originLabel: 'NAR-1.V'
    originDesc:
      'The reliability of a narrator may influence a reader’s understanding of a character’s
      motives.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b116'
    originLabel: 'NAR-1.W'
    originDesc:
      'Some narrators or speakers may provide details and information that others do not or cannot
      provide. Multiple narrators or speakers may provide contradictory information in a text.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b117'
    originLabel: 'NAR-1.X'
    originDesc:
      'Multiple, and even contrasting, perspectives can occur within a single text and contribute to
      the complexity of the text.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b118'
    originLabel: 'NAR-1.Y'
    originDesc:
      'A narrator or speaker may change over the course of a text as a result of actions and
      interactions.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b119'
    originLabel: 'NAR-1.Z'
    originDesc:
      'Changes and inconsistencies in a narrator’s or speaker’s perspective may contribute to irony
      or the complexity of the text.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b120'
    originLabel: 'FIG-1.A'
    originDesc:
      'An antecedent is a word, phrase, or clause that precedes its referent. Referents may include
      pronouns, nouns, phrases, or clauses.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b121'
    originLabel: 'FIG-1.B'
    originDesc:
      'Referents are ambiguous if they can refer to more than one antecedent, which affects
      interpretation.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b122'
    originLabel: 'FIG-1.C'
    originDesc: 'Words or phrases may be repeated to emphasize ideas or associations.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b123'
    originLabel: 'FIG-1.D'
    originDesc:
      'Alliteration is the repetition of the same letter sound at the beginning of adjacent or
      nearby words to emphasize those words and their associations or representations.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b124'
    originLabel: 'FIG-1.E'
    originDesc:
      'A simile uses the words “like” or “as” to liken two objects or concepts to each other.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b125'
    originLabel: 'FIG-1.F'
    originDesc:
      'Similes liken two different things to transfer the traits or qualities of one to the other.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b126'
    originLabel: 'FIG-1.G'
    originDesc:
      'In a simile, the thing being compared is the main subject; the thing to which it is being
      compared is the comparison subject.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b127'
    originLabel: 'FIG-1.H'
    originDesc:
      'A metaphor implies similarities between two (usually unrelated) concepts or objects in order
      to reveal or emphasize one or more things about one of them, though the differences between
      the two may also be revealing.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b128'
    originLabel: 'FIG-1.I'
    originDesc:
      'In a metaphor, as in a simile, the thing being compared is the main subject; the thing to
      which it is being compared is the comparison subject.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b129'
    originLabel: 'FIG-1.J'
    originDesc:
      'Comparisons between objects or concepts draw on the experiences and associations readers
      already have with those objects and concepts.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b130'
    originLabel: 'FIG-1.K'
    originDesc:
      'Interpretation of a metaphor may depend on the context of its use; that is, what is happening
      in a text may determine what is transferred in the comparison.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b131'
    originLabel: 'FIG-1.L'
    originDesc:
      'Words with multiple meanings or connotations add nuance or complexity that can contribute to
      interpretations of a text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b132'
    originLabel: 'FIG-1.M'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, qualify or modify the things they describe
      and affect readers’ interaction with the text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b133'
    originLabel: 'FIG-1.N'
    originDesc:
      'Hyperbole exaggerates while understatement minimizes. Exaggerating or minimizing an aspect of
      an object focuses attention on that trait and conveys a perspective about the object.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b134'
    originLabel: 'FIG-1.O'
    originDesc: 'Descriptive words, such as adjectives and adverbs, contribute to sensory imagery.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b135'
    originLabel: 'FIG-1.P'
    originDesc:
      'An image can be literal or it can be a form of a comparison that represents something in a
      text through associations with the senses.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b136'
    originLabel: 'FIG-1.Q'
    originDesc:
      'A collection of images, known as imagery, may emphasize ideas in parts of or throughout a
      text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b137'
    originLabel: 'FIG-1.R'
    originDesc:
      'Metaphorical comparisons do not focus solely on the objects being compared; they focus on the
      particular traits, qualities, or characteristics of the things being compared.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b138'
    originLabel: 'FIG-1.S'
    originDesc:
      'Comparisons not only communicate literal meaning but may also convey figurative meaning or
      transmit a perspective.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b139'
    originLabel: 'FIG-1.T'
    originDesc:
      'An extended metaphor is created when the comparison of a main subject and comparison subject
      persists through parts of or an entire text, and when the comparison is expanded through
      additional details, similes, and images.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b140'
    originLabel: 'FIG-1.U'
    originDesc:
      'Interpretation of an extended metaphor may depend on the context of its use; that is, what is
      happening in a text may determine what is transferred in the comparison.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b141'
    originLabel: 'FIG-1.V'
    originDesc:
      'Personification is a type of comparison that assigns a human trait or quality to a nonhuman
      object, entity, or idea, thus characterizing that object, entity, or idea.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b142'
    originLabel: 'FIG-1.W'
    originDesc:
      'Allusions in a text can reference literary works including myths and sacred texts; other
      works of art including paintings and music; or people, places, or events outside the text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b143'
    originLabel: 'FIG-1.X'
    originDesc:
      'When a material object comes to represent, or stand for, an idea or concept, it becomes a
      symbol.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b144'
    originLabel: 'FIG-1.Y'
    originDesc:
      'A symbol is an object that represents a meaning, so it is said to be symbolic or
      representative of that meaning. A symbol can represent different things depending on the
      experiences of a reader or the context of its use in a text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b145'
    originLabel: 'FIG-1.Z'
    originDesc:
      'Certain symbols are so common and recurrent that many readers have associations with them
      prior to reading a text. Other symbols are more contextualized and only come to represent
      certain things through their use in a particular text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b146'
    originLabel: 'FIG-1.AA'
    originDesc:
      'When a character comes to represent, or stand for, an idea or concept, that character becomes
      symbolic; some symbolic characters have become so common they are archetypal.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b147'
    originLabel: 'FIG-1.AB'
    originDesc:
      'A setting may become symbolic when it is, or comes to be, associated with abstractions such
      as emotions, ideologies, and beliefs.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b148'
    originLabel: 'FIG-1.AC'
    originDesc:
      'Over time, some settings have developed certain associations such that they almost
      universally symbolize particular concepts.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b149'
    originLabel: 'FIG-1.AD'
    originDesc:
      'A motif is a unified pattern of recurring objects or images used to emphasize a significant
      idea in large parts of or throughout a text.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b150'
    originLabel: 'FIG-1.AE'
    originDesc:
      'The function of a simile relies on the selection of the objects being compared as well as the
      traits of the objects.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b151'
    originLabel: 'FIG-1.AF'
    originDesc:
      'By assigning the qualities of a nonhuman object, entity, or idea to a person or character,
      the narrator, character, or speaker communicates an attitude about that person or character.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b152'
    originLabel: 'FIG-1.AG'
    originDesc:
      'Ambiguity allows for different readings and understandings of a text by different readers.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b153'
    originLabel: 'FIG-1.AH'
    originDesc:
      'Symbols in a text and the way they are used may imply that a narrator, character, or speaker
      has a particular attitude or perspective.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b154'
    originLabel: 'FIG-1.AI'
    originDesc:
      'A conceit is a form of extended metaphor that often appears in poetry. Conceits develop
      complex comparisons that present images, concepts, and associations in surprising or
      paradoxical ways.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b155'
    originLabel: 'FIG-1.AJ'
    originDesc:
      'Often, conceits are used to make complex comparisons between the natural world and an
      individual.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b156'
    originLabel: 'FIG-1.AK'
    originDesc:
      'Multiple comparisons, representations, or associations may combine to affect one another in
      complex ways.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b157'
    originLabel: 'FIG-1.AL'
    originDesc:
      'Because of shared knowledge about a reference, allusions create emotional or intellectual
      associations and understandings.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isChildOf'
  - originID: 'b158'
    originLabel: 'LAN-1.A'
    originDesc:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b159'
    originLabel: 'LAN-1.B'
    originDesc: 'A claim is a statement that requires defense with evidence from the text.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b160'
    originLabel: 'LAN-1.C'
    originDesc:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b169'
    originLabel: 'LAN-1.L'
    originDesc:
      'Grammar and mechanics that follow established conventions of language allow writers to
      clearly communicate their interpretation of a text.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b170'
    originLabel: 'LAN-1.M'
    originDesc:
      'The body paragraphs of a written argument develop the reasoning and justify claims using
      evidence and providing commentary that links the evidence to the overall thesis.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b171'
    originLabel: 'LAN-1.N'
    originDesc:
      'Effective paragraphs are cohesive and often use topic sentences to state a claim and explain
      the reasoning that connects the various claims and evidence that make up the body of an essay.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b172'
    originLabel: 'LAN-1.O'
    originDesc:
      'Coherence occurs at different levels in a piece of writing. In a sentence, the idea in one
      clause logically links to an idea in the next. In a paragraph, the idea in one sentence
      logically links to an idea in the next. In a text, the ideas in one paragraph logically link
      to the ideas in the next.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b173'
    originLabel: 'LAN-1.P'
    originDesc:
      'Writers achieve coherence when the arrangement and organization of reasons, evidence, ideas,
      or details is logical. Writers may use transitions, repetition, synonyms, pronoun references,
      or parallel structure to indicate relationships between and among those reasons, evidence,
      ideas, or details.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b174'
    originLabel: 'LAN-1.Q'
    originDesc:
      'Transitional elements are words or other elements (phrases, clauses, sentences, or
      paragraphs) that assist in creating coherence between sentences and paragraphs by showing
      relationships between ideas.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b175'
    originLabel: 'LAN-1.R'
    originDesc:
      'Writers convey their ideas in a sentence through strategic selection and placement of phrases
      and clauses. Writers may use coordination to illustrate a balance or equality between ideas or
      subordination to illustrate an imbalance or inequality.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b176'
    originLabel: 'LAN-1.S'
    originDesc: 'Writers use words that enhance the clear communication of an interpretation.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b177'
    originLabel: 'LAN-1.T'
    originDesc: 'Punctuation conveys relationships between and among parts of a sentence.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b178'
    originLabel: 'LAN-1.U'
    originDesc:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b179'
    originLabel: 'LAN-1.V'
    originDesc:
      'Textual evidence may require revision to an interpretation and a line of reasoning if the
      evidence does not sufficiently support the initial interpretation and line of reasoning.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b180'
    originLabel: 'LAN-1.W'
    originDesc:
      'Writers must acknowledge words, ideas, images, texts, and other intellectual property of
      others through attribution, citation, or reference.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isChildOf'
  - originID: 'b181'
    originLabel: '1'
    originDesc: 'Explain the function of character.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b182'
    originLabel: '2'
    originDesc: 'Explain the function of setting.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b183'
    originLabel: '3'
    originDesc: 'Explain the function of plot and structure.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b184'
    originLabel: '4'
    originDesc: 'Explain the function of the narrator or speaker.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b185'
    originLabel: '5'
    originDesc: 'Explain the function of word choice, imagery, and symbols.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b186'
    originLabel: '6'
    originDesc: 'Explain the function of comparison.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b187'
    originLabel: '7'
    originDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    targetID: 'c2'
    targetDesc: 'Course Skills'
    associationType: 'isChildOf'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b181'
    targetLabel: '1'
    targetDesc: 'Explain the function of character.'
    associationType: 'isChildOf'
  - originID: 'b189'
    originLabel: '1.B'
    originDesc: 'Explain the function of a character changing or remaining unchanged.'
    targetID: 'b181'
    targetLabel: '1'
    targetDesc: 'Explain the function of character.'
    associationType: 'isChildOf'
  - originID: 'b190'
    originLabel: '1.C'
    originDesc: 'Explain the function of contrasting characters.'
    targetID: 'b181'
    targetLabel: '1'
    targetDesc: 'Explain the function of character.'
    associationType: 'isChildOf'
  - originID: 'b191'
    originLabel: '1.D'
    originDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    targetID: 'b181'
    targetLabel: '1'
    targetDesc: 'Explain the function of character.'
    associationType: 'isChildOf'
  - originID: 'b192'
    originLabel: '1.E'
    originDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    targetID: 'b181'
    targetLabel: '1'
    targetDesc: 'Explain the function of character.'
    associationType: 'isChildOf'
  - originID: 'b193'
    originLabel: '2.A'
    originDesc: 'Identify and describe specific textual details that convey or reveal a setting.'
    targetID: 'b182'
    targetLabel: '2'
    targetDesc: 'Explain the function of setting.'
    associationType: 'isChildOf'
  - originID: 'b194'
    originLabel: '2.B'
    originDesc: 'Explain the function of setting in a narrative.'
    targetID: 'b182'
    targetLabel: '2'
    targetDesc: 'Explain the function of setting.'
    associationType: 'isChildOf'
  - originID: 'b195'
    originLabel: '2.C'
    originDesc: 'Describe the relationship between a character and a setting.'
    targetID: 'b182'
    targetLabel: '2'
    targetDesc: 'Explain the function of setting.'
    associationType: 'isChildOf'
  - originID: 'b196'
    originLabel: '3.A'
    originDesc: 'Identify and describe how plot orders events in a narrative.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b197'
    originLabel: '3.B'
    originDesc: 'Explain the function of a particular sequence of events in a plot.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b198'
    originLabel: '3.C'
    originDesc: 'Explain the function of structure in a text.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b199'
    originLabel: '3.D'
    originDesc: 'Explain the function of contrasts within a text.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b200'
    originLabel: '3.E'
    originDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b201'
    originLabel: '3.F'
    originDesc: 'Explain the function of conflict in a text.'
    targetID: 'b183'
    targetLabel: '3'
    targetDesc: 'Explain the function of plot and structure.'
    associationType: 'isChildOf'
  - originID: 'b202'
    originLabel: '4.A'
    originDesc: 'Identify and describe the narrator or speaker of a text.'
    targetID: 'b184'
    targetLabel: '4'
    targetDesc: 'Explain the function of the narrator or speaker.'
    associationType: 'isChildOf'
  - originID: 'b203'
    originLabel: '4.B'
    originDesc: 'Identify and explain the function of point of view in a narrative.'
    targetID: 'b184'
    targetLabel: '4'
    targetDesc: 'Explain the function of the narrator or speaker.'
    associationType: 'isChildOf'
  - originID: 'b204'
    originLabel: '4.C'
    originDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    targetID: 'b184'
    targetLabel: '4'
    targetDesc: 'Explain the function of the narrator or speaker.'
    associationType: 'isChildOf'
  - originID: 'b205'
    originLabel: '4.D'
    originDesc: 'Explain how a narrator’s reliability affects a narrative.'
    targetID: 'b184'
    targetLabel: '4'
    targetDesc: 'Explain the function of the narrator or speaker.'
    associationType: 'isChildOf'
  - originID: 'b206'
    originLabel: '5.A'
    originDesc: 'Distinguish between the literal and figurative meanings of words and phrases.'
    targetID: 'b185'
    targetLabel: '5'
    targetDesc: 'Explain the function of word choice, imagery, and symbols.'
    associationType: 'isChildOf'
  - originID: 'b207'
    originLabel: '5.B'
    originDesc: 'Explain the function of specific words and phrases in a text.'
    targetID: 'b185'
    targetLabel: '5'
    targetDesc: 'Explain the function of word choice, imagery, and symbols.'
    associationType: 'isChildOf'
  - originID: 'b208'
    originLabel: '5.C'
    originDesc: 'Identify and explain the function of a symbol.'
    targetID: 'b185'
    targetLabel: '5'
    targetDesc: 'Explain the function of word choice, imagery, and symbols.'
    associationType: 'isChildOf'
  - originID: 'b209'
    originLabel: '5.D'
    originDesc: 'Identify and explain the function of an image or imagery.'
    targetID: 'b185'
    targetLabel: '5'
    targetDesc: 'Explain the function of word choice, imagery, and symbols.'
    associationType: 'isChildOf'
  - originID: 'b210'
    originLabel: '6.A'
    originDesc: 'Identify and explain the function of a simile.'
    targetID: 'b186'
    targetLabel: '6'
    targetDesc: 'Explain the function of comparison.'
    associationType: 'isChildOf'
  - originID: 'b211'
    originLabel: '6.B'
    originDesc: 'Identify and explain the function of a metaphor.'
    targetID: 'b186'
    targetLabel: '6'
    targetDesc: 'Explain the function of comparison.'
    associationType: 'isChildOf'
  - originID: 'b212'
    originLabel: '6.C'
    originDesc: 'Identify and explain the function of personification.'
    targetID: 'b186'
    targetLabel: '6'
    targetDesc: 'Explain the function of comparison.'
    associationType: 'isChildOf'
  - originID: 'b213'
    originLabel: '6.D'
    originDesc: 'Identify and explain the function of an allusion.'
    targetID: 'b186'
    targetLabel: '6'
    targetDesc: 'Explain the function of comparison.'
    associationType: 'isChildOf'
  - originID: 'b214'
    originLabel: '7.A'
    originDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    targetID: 'b187'
    targetLabel: '7'
    targetDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    associationType: 'isChildOf'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b187'
    targetLabel: '7'
    targetDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    associationType: 'isChildOf'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b187'
    targetLabel: '7'
    targetDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    associationType: 'isChildOf'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b187'
    targetLabel: '7'
    targetDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    associationType: 'isChildOf'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b187'
    targetLabel: '7'
    targetDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    associationType: 'isChildOf'
  - originID: 'b219'
    originLabel: '1'
    originDesc: 'Short Fiction I'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b220'
    originLabel: '2'
    originDesc: 'Poetry I'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b221'
    originLabel: '3'
    originDesc: 'Longer Fiction or Drama I'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b222'
    originLabel: '4'
    originDesc: 'Short Fiction II'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b223'
    originLabel: '5'
    originDesc: 'Poetry II'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b224'
    originLabel: '6'
    originDesc: 'Longer Fiction or Drama II'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b225'
    originLabel: '7'
    originDesc: 'Short Fiction III'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b226'
    originLabel: '8'
    originDesc: 'Poetry III'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b227'
    originLabel: '9'
    originDesc: 'Longer Fiction or Drama III'
    targetID: 'c3'
    targetDesc: 'Units'
    associationType: 'isChildOf'
  - originID: 'b228'
    originDesc: 'MCQ'
    targetID: 'a7'
    targetDesc: 'Question Type'
    associationType: 'isChildOf'
  - originID: 'b229'
    originDesc: 'FRQ: Poetry'
    targetID: 'a7'
    targetDesc: 'Question Type'
    associationType: 'isChildOf'
  - originID: 'b230'
    originDesc: 'FRQ: Prose'
    targetID: 'a7'
    targetDesc: 'Question Type'
    associationType: 'isChildOf'
  - originID: 'b231'
    originDesc: 'FRQ: Open'
    targetID: 'a7'
    targetDesc: 'Question Type'
    associationType: 'isChildOf'
  - originID: 'b232'
    originDesc: 'Poetry'
    targetID: 'a8'
    targetDesc: 'Stimulus Type'
    associationType: 'isChildOf'
  - originID: 'b233'
    originDesc: 'Prose'
    targetID: 'a8'
    targetDesc: 'Stimulus Type'
    associationType: 'isChildOf'
  - originID: 'b234'
    originDesc: 'American'
    targetID: 'a9'
    targetDesc: 'Stimulus Text Origin'
    associationType: 'isChildOf'
  - originID: 'b235'
    originDesc: 'British'
    targetID: 'a9'
    targetDesc: 'Stimulus Text Origin'
    associationType: 'isChildOf'
  - originID: 'b236'
    originDesc: 'Other'
    targetID: 'a9'
    targetDesc: 'Stimulus Text Origin'
    associationType: 'isChildOf'
  - originID: 'b237'
    originDesc: 'Easy'
    targetID: 'a10'
    targetDesc: 'Stimulus Difficulty'
    associationType: 'isChildOf'
  - originID: 'b238'
    originDesc: 'Moderate'
    targetID: 'a10'
    targetDesc: 'Stimulus Difficulty'
    associationType: 'isChildOf'
  - originID: 'b239'
    originDesc: 'Challenging'
    targetID: 'a10'
    targetDesc: 'Stimulus Difficulty'
    associationType: 'isChildOf'
  - originID: 'b240'
    originDesc: '18th century and earlier (prior to 1800)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b241'
    originDesc: 'Early 19th century (1800 to 1850)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b242'
    originDesc: 'Mid-19th century (1851 to 1870)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b243'
    originDesc: 'Late 19th century (1871 to 1910)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b244'
    originDesc: 'Early 20th century (1911 to 1945)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b245'
    originDesc: 'Mid-20th century (1946 to 1980)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b246'
    originDesc: 'Late 20th century (1981 to 2000)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b247'
    originDesc: 'Contemporary (post 2000)'
    targetID: 'a11'
    targetDesc: 'Stimulus Time Period'
    associationType: 'isChildOf'
  - originID: 'b248'
    originDesc: 'Anxiety'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b249'
    originDesc: 'Class'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b250'
    originDesc: 'Childhood'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b251'
    originDesc: 'Culture Education / Schooling'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b252'
    originDesc: 'Culture'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b253'
    originDesc: 'Education / Schooling'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b254'
    originDesc: 'Exploration'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b255'
    originDesc: 'Family'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b256'
    originDesc: 'Friendship'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b257'
    originDesc: 'Gender Roles / Rights'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b258'
    originDesc: 'Growing-up / Growing-Older'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b259'
    originDesc: 'Home / Homeland'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b260'
    originDesc: 'Humor / Satire Identity'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b261'
    originDesc: 'Humor'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b262'
    originDesc: 'Identity'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b263'
    originDesc: 'Innocence Lost'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b264'
    originDesc: 'Knowledge is Power'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b265'
    originDesc: 'Nature / Environment'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b266'
    originDesc: 'Law & Justice'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b267'
    originDesc: 'Life & Death'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b268'
    originDesc: 'Love & Longing'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b269'
    originDesc: 'Monsters & Heroes'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b270'
    originDesc: 'Philosophy Pop Culture'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b271'
    originDesc: 'Philosophy'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b272'
    originDesc: 'Pop Culture'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b273'
    originDesc: 'Psychology & Behavior'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b274'
    originDesc: 'Race'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b275'
    originDesc: 'Religion / Spirituality'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b276'
    originDesc: 'Right & Wrong'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b277'
    originDesc: 'Science'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b278'
    originDesc: 'Self-Discovery'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b279'
    originDesc: 'Self-Reliance'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'b280'
    originDesc: 'Work'
    targetID: 'a12'
    targetDesc: 'Stimulus Theme'
    associationType: 'isChildOf'
  - originID: 'a4'
    originDesc: 'Skill Category'
    targetID: 'a2'
    targetDesc: 'Enduring Understanding'
    associationType: 'isRelatedTo'
  - originID: 'a3'
    originDesc: 'Essential Knowledge'
    targetID: 'a5'
    targetDesc: 'Skill'
    associationType: 'isRelatedTo'
  - originID: 'a5'
    originDesc: 'Skill'
    targetID: 'a6'
    targetDesc: 'Unit'
    associationType: 'isRelatedTo'
  - originID: 'a3'
    originDesc: 'Essential Knowledge'
    targetID: 'a6'
    targetDesc: 'Unit'
    associationType: 'isRelatedTo'
  - originID: 'b181'
    originLabel: '1'
    originDesc: 'Explain the function of character.'
    targetID: 'b7'
    targetLabel: 'CHR-1'
    targetDesc:
      'Characters in literature allow readers to study and explore a range of values, beliefs,
      assumptions, biases, and cultural norms represented by those characters.'
    associationType: 'isRelatedTo'
  - originID: 'b182'
    originLabel: '2'
    originDesc: 'Explain the function of setting.'
    targetID: 'b8'
    targetLabel: 'SET-1'
    targetDesc:
      'Setting and the details associated with it not only depict a time and place, but also convey
      values associated with that setting.'
    associationType: 'isRelatedTo'
  - originID: 'b183'
    originLabel: '3'
    originDesc: 'Explain the function of plot and structure.'
    targetID: 'b9'
    targetLabel: 'STR-1'
    targetDesc:
      "The arrangement of the parts and sections of a text, the relationship of the parts to each
      other, and the sequence in which the text reveals information are all structural choices made
      by a writer that contribute to the reader's interpretation of a text."
    associationType: 'isRelatedTo'
  - originID: 'b184'
    originLabel: '4'
    originDesc: 'Explain the function of the narrator or speaker.'
    targetID: 'b10'
    targetLabel: 'NAR-1'
    targetDesc:
      'A narrator’s or speaker’s perspective controls the details and emphases that affect how
      readers experience and interpret a text.'
    associationType: 'isRelatedTo'
  - originID: 'b185'
    originLabel: '5'
    originDesc: 'Explain the function of word choice, imagery, and symbols.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isRelatedTo'
  - originID: 'b186'
    originLabel: '6'
    originDesc: 'Explain the function of comparison.'
    targetID: 'b11'
    targetLabel: 'FIG-1'
    targetDesc:
      'Comparisons, representations, and associations shift meaning from the literal to the
      figurative and invite readers to interpret a text.'
    associationType: 'isRelatedTo'
  - originID: 'b187'
    originLabel: '7'
    originDesc:
      'Develop textually substantiated arguments about interpretations of part or all of a text.'
    targetID: 'b12'
    targetLabel: 'LAN-1'
    targetDesc:
      'Readers establish and communicate their interpretations of literature through arguments
      supported by textual evidence.'
    associationType: 'isRelatedTo'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b188'
    originLabel: '1.A'
    originDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b189'
    originLabel: '1.B'
    originDesc: 'Explain the function of a character changing or remaining unchanged.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b189'
    originLabel: '1.B'
    originDesc: 'Explain the function of a character changing or remaining unchanged.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b189'
    originLabel: '1.B'
    originDesc: 'Explain the function of a character changing or remaining unchanged.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b190'
    originLabel: '1.C'
    originDesc: 'Explain the function of contrasting characters.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b190'
    originLabel: '1.C'
    originDesc: 'Explain the function of contrasting characters.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b191'
    originLabel: '1.D'
    originDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b191'
    originLabel: '1.D'
    originDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b192'
    originLabel: '1.E'
    originDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b192'
    originLabel: '1.E'
    originDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b193'
    originLabel: '2.A'
    originDesc: 'Identify and describe specific textual details that convey or reveal a setting.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b193'
    originLabel: '2.A'
    originDesc: 'Identify and describe specific textual details that convey or reveal a setting.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b194'
    originLabel: '2.B'
    originDesc: 'Explain the function of setting in a narrative.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b194'
    originLabel: '2.B'
    originDesc: 'Explain the function of setting in a narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b195'
    originLabel: '2.C'
    originDesc: 'Describe the relationship between a character and a setting.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b195'
    originLabel: '2.C'
    originDesc: 'Describe the relationship between a character and a setting.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b196'
    originLabel: '3.A'
    originDesc: 'Identify and describe how plot orders events in a narrative.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b196'
    originLabel: '3.A'
    originDesc: 'Identify and describe how plot orders events in a narrative.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b196'
    originLabel: '3.A'
    originDesc: 'Identify and describe how plot orders events in a narrative.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b196'
    originLabel: '3.A'
    originDesc: 'Identify and describe how plot orders events in a narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b197'
    originLabel: '3.B'
    originDesc: 'Explain the function of a particular sequence of events in a plot.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b197'
    originLabel: '3.B'
    originDesc: 'Explain the function of a particular sequence of events in a plot.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b197'
    originLabel: '3.B'
    originDesc: 'Explain the function of a particular sequence of events in a plot.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b198'
    originLabel: '3.C'
    originDesc: 'Explain the function of structure in a text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b198'
    originLabel: '3.C'
    originDesc: 'Explain the function of structure in a text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b198'
    originLabel: '3.C'
    originDesc: 'Explain the function of structure in a text.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b199'
    originLabel: '3.D'
    originDesc: 'Explain the function of contrasts within a text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b199'
    originLabel: '3.D'
    originDesc: 'Explain the function of contrasts within a text.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b199'
    originLabel: '3.D'
    originDesc: 'Explain the function of contrasts within a text.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b199'
    originLabel: '3.D'
    originDesc: 'Explain the function of contrasts within a text.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b200'
    originLabel: '3.E'
    originDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b200'
    originLabel: '3.E'
    originDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b201'
    originLabel: '3.F'
    originDesc: 'Explain the function of conflict in a text.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b201'
    originLabel: '3.F'
    originDesc: 'Explain the function of conflict in a text.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b202'
    originLabel: '4.A'
    originDesc: 'Identify and describe the narrator or speaker of a text.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b202'
    originLabel: '4.A'
    originDesc: 'Identify and describe the narrator or speaker of a text.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b203'
    originLabel: '4.B'
    originDesc: 'Identify and explain the function of point of view in a narrative.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b203'
    originLabel: '4.B'
    originDesc: 'Identify and explain the function of point of view in a narrative.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b204'
    originLabel: '4.C'
    originDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b204'
    originLabel: '4.C'
    originDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b204'
    originLabel: '4.C'
    originDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b205'
    originLabel: '4.D'
    originDesc: 'Explain how a narrator’s reliability affects a narrative.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b205'
    originLabel: '4.D'
    originDesc: 'Explain how a narrator’s reliability affects a narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b206'
    originLabel: '5.A'
    originDesc: 'Distinguish between the literal and figurative meanings of words and phrases.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b207'
    originLabel: '5.B'
    originDesc: 'Explain the function of specific words and phrases in a text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b207'
    originLabel: '5.B'
    originDesc: 'Explain the function of specific words and phrases in a text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b207'
    originLabel: '5.B'
    originDesc: 'Explain the function of specific words and phrases in a text.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b208'
    originLabel: '5.C'
    originDesc: 'Identify and explain the function of a symbol.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b208'
    originLabel: '5.C'
    originDesc: 'Identify and explain the function of a symbol.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b208'
    originLabel: '5.C'
    originDesc: 'Identify and explain the function of a symbol.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b209'
    originLabel: '5.D'
    originDesc: 'Identify and explain the function of an image or imagery.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b209'
    originLabel: '5.D'
    originDesc: 'Identify and explain the function of an image or imagery.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b210'
    originLabel: '6.A'
    originDesc: 'Identify and explain the function of a simile.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b210'
    originLabel: '6.A'
    originDesc: 'Identify and explain the function of a simile.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b211'
    originLabel: '6.B'
    originDesc: 'Identify and explain the function of a metaphor.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b211'
    originLabel: '6.B'
    originDesc: 'Identify and explain the function of a metaphor.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b211'
    originLabel: '6.B'
    originDesc: 'Identify and explain the function of a metaphor.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b212'
    originLabel: '6.C'
    originDesc: 'Identify and explain the function of personification.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b212'
    originLabel: '6.C'
    originDesc: 'Identify and explain the function of personification.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b213'
    originLabel: '6.D'
    originDesc: 'Identify and explain the function of an allusion.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b213'
    originLabel: '6.D'
    originDesc: 'Identify and explain the function of an allusion.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b214'
    originLabel: '7.A'
    originDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b214'
    originLabel: '7.A'
    originDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b214'
    originLabel: '7.A'
    originDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b215'
    originLabel: '7.B'
    originDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b216'
    originLabel: '7.C'
    originDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b217'
    originLabel: '7.D'
    originDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b218'
    originLabel: '7.E'
    originDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b13'
    originLabel: 'CHR-1.A'
    originDesc: 'Description, dialogue, and behavior reveal characters to readers.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b14'
    originLabel: 'CHR-1.B'
    originDesc:
      'Descriptions of characters may come from a speaker, narrator, other characters, or the
      characters themselves.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b15'
    originLabel: 'CHR-1.C'
    originDesc:
      'Perspective is how narrators, characters, or speakers understand their circumstances, and is
      informed by background, personality traits, biases, and relationships.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b16'
    originLabel: 'CHR-1.D'
    originDesc:
      'A character’s perspective is both shaped and revealed by relationships with other characters,
      the environment, the events of the plot, and the ideas expressed in the text.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b17'
    originLabel: 'CHR-1.E'
    originDesc:
      'Characters reveal their perspectives and biases through the words they use, the details they
      provide in the text, the organization of their thinking, the decisions they make, and the
      actions they take.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b18'
    originLabel: 'CHR-1.F'
    originDesc:
      'The description of a character creates certain expectations for that character’s behaviors;
      how a character does or does not meet those expectations affects a reader’s interpretation of
      that character.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b19'
    originLabel: 'CHR-1.G'
    originDesc:
      'Details associated with a character and/or used to describe a character contribute to a
      reader’s interpretation of that character.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b20'
    originLabel: 'CHR-1.H'
    originDesc:
      'Readers’ understanding of a character’s perspective may depend on the perspective of the
      narrator or speaker.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b21'
    originLabel: 'CHR-1.I'
    originDesc: 'A character’s perspective may shift during the course of a narrative.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b22'
    originLabel: 'CHR-1.J'
    originDesc:
      'When narrators, characters, or speakers compare another character to something or someone
      else, they reveal their perspective on the compared character and may also reveal something
      innate about the compared character.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b23'
    originLabel: 'CHR-1.K'
    originDesc:
      'Readers can infer a character’s motives from that character’s actions or inactions.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b24'
    originLabel: 'CHR-1.L'
    originDesc:
      'A dynamic character who develops over the course of the narrative often makes choices that
      directly or indirectly affect the climax and/or the resolution of that narrative.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b25'
    originLabel: 'CHR-1.M'
    originDesc:
      'Character changes can be visible and external, such as changes to health or wealth, or can be
      internal, psychological or emotional changes; external changes can lead to internal changes,
      and vice versa.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b26'
    originLabel: 'CHR-1.N'
    originDesc:
      'Some characters remain unchanged or are largely unaffected by the events of the narrative.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b27'
    originLabel: 'CHR-1.O'
    originDesc:
      'The significance of characters is often revealed through their agency and through nuanced
      descriptions.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b28'
    originLabel: 'CHR-1.P'
    originDesc: 'Characters’ choices—in speech, action, and inaction—reveal what they value.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b29'
    originLabel: 'CHR-1.Q'
    originDesc:
      'The main character in a narrative is the protagonist; the antagonist in the narrative opposes
      the protagonist and may be another character, the internal conflicts of the protagonist, a
      collective (such as society), or nature.'
    targetID: 'b190'
    targetLabel: '1.C'
    targetDesc: 'Explain the function of contrasting characters.'
    associationType: 'isRelatedTo'
  - originID: 'b30'
    originLabel: 'CHR-1.R'
    originDesc: 'Protagonists and antagonists may represent contrasting values.'
    targetID: 'b190'
    targetLabel: '1.C'
    targetDesc: 'Explain the function of contrasting characters.'
    associationType: 'isRelatedTo'
  - originID: 'b31'
    originLabel: 'CHR-1.S'
    originDesc:
      'Conflict among characters often arises from tensions generated by their different value
      systems.'
    targetID: 'b191'
    targetLabel: '1.D'
    targetDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    associationType: 'isRelatedTo'
  - originID: 'b32'
    originLabel: 'CHR-1.T'
    originDesc:
      'Different character, narrator, or speaker perspectives often reveal different information,
      develop different attitudes, and influence different interpretations of a text and the ideas
      in it.'
    targetID: 'b188'
    targetLabel: '1.A'
    targetDesc:
      'Identify and describe what specific textual details reveal about a character, that
      character’s perspective, and that character’s motives.'
    associationType: 'isRelatedTo'
  - originID: 'b33'
    originLabel: 'CHR-1.U'
    originDesc:
      'Foil characters (foils) serve to illuminate, through contrast, the traits, attributes, or
      values of another character.'
    targetID: 'b190'
    targetLabel: '1.C'
    targetDesc: 'Explain the function of contrasting characters.'
    associationType: 'isRelatedTo'
  - originID: 'b34'
    originLabel: 'CHR-1.V'
    originDesc:
      'Inconsistencies between the private thoughts of characters and their actual behavior reveal
      tensions and complexities between private and professed values.'
    targetID: 'b192'
    targetLabel: '1.E'
    targetDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    associationType: 'isRelatedTo'
  - originID: 'b35'
    originLabel: 'CHR-1.W'
    originDesc:
      'A character’s competing, conflicting, or inconsistent choices or actions contribute to
      complexity in a text.'
    targetID: 'b192'
    targetLabel: '1.E'
    targetDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    associationType: 'isRelatedTo'
  - originID: 'b36'
    originLabel: 'CHR-1.X'
    originDesc:
      'Often the change in a character emerges directly from a conflict of values represented in the
      narrative.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b37'
    originLabel: 'CHR-1.Y'
    originDesc: 'Changes in a character’s circumstances may lead to changes in that character.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b38'
    originLabel: 'CHR-1.Z'
    originDesc:
      'While characters can change gradually over the course of a narrative, they can also change
      suddenly as the result of a moment of realization, known as an epiphany. An epiphany allows a
      character to see things in a new light and is often directly related to a central conflict of
      the narrative.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b39'
    originLabel: 'CHR-1.AA'
    originDesc:
      'An epiphany may affect the plot by causing a character to act on his or her sudden
      realization.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b40'
    originLabel: 'CHR-1.AB'
    originDesc: 'A group or force can function as a character.'
    targetID: 'b191'
    targetLabel: '1.D'
    targetDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    associationType: 'isRelatedTo'
  - originID: 'b41'
    originLabel: 'CHR-1.AC'
    originDesc:
      'When readers consider a character, they should examine how that character interacts with
      other characters, groups, or forces and what those interactions may indicate about the
      character.'
    targetID: 'b191'
    targetLabel: '1.D'
    targetDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    associationType: 'isRelatedTo'
  - originID: 'b42'
    originLabel: 'CHR-1.AD'
    originDesc:
      'The relationship between a character and a group, including the inclusion or exclusion of
      that character, reveals the collective attitude of the group toward that character and
      possibly the character’s attitude toward the group.'
    targetID: 'b191'
    targetLabel: '1.D'
    targetDesc:
      'Describe how textual details reveal nuances and complexities in characters’ relationships
      with one another.'
    associationType: 'isRelatedTo'
  - originID: 'b43'
    originLabel: 'CHR-1.AE'
    originDesc:
      'Minor characters often remain unchanged because the narrative doesn’t focus on them. They may
      only be part of the narrative to advance the plot or to interact with major characters.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b44'
    originLabel: 'CHR-1.AF'
    originDesc:
      'Readers’ interpretations of a text are often affected by a character changing—or not—and the
      meaning conveyed by such changes or lack thereof.'
    targetID: 'b189'
    targetLabel: '1.B'
    targetDesc: 'Explain the function of a character changing or remaining unchanged.'
    associationType: 'isRelatedTo'
  - originID: 'b45'
    originLabel: 'CHR-1.AG'
    originDesc:
      'A character’s responses to the resolution of the narrative - in their words or in their
      actions - reveal something about that character’s own values; these responses may be
      inconsistent with the previously established behaviors or perspectives of that character.'
    targetID: 'b192'
    targetLabel: '1.E'
    targetDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    associationType: 'isRelatedTo'
  - originID: 'b46'
    originLabel: 'CHR-1.AH'
    originDesc:
      'Inconsistencies and unexpected developments in a character affect readers’ interpretation of
      that character; other characters; events in the plot; conflicts; the perspective of the
      narrator, character, or speaker; and/or setting.'
    targetID: 'b192'
    targetLabel: '1.E'
    targetDesc:
      'Explain how a character’s own choices, actions, and speech reveal complexities in that
      character, and explain the function of those complexities.'
    associationType: 'isRelatedTo'
  - originID: 'b13'
    originLabel: 'CHR-1.A'
    originDesc: 'Description, dialogue, and behavior reveal characters to readers.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b14'
    originLabel: 'CHR-1.B'
    originDesc:
      'Descriptions of characters may come from a speaker, narrator, other characters, or the
      characters themselves.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b15'
    originLabel: 'CHR-1.C'
    originDesc:
      'Perspective is how narrators, characters, or speakers understand their circumstances, and is
      informed by background, personality traits, biases, and relationships.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b16'
    originLabel: 'CHR-1.D'
    originDesc:
      'A character’s perspective is both shaped and revealed by relationships with other characters,
      the environment, the events of the plot, and the ideas expressed in the text.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b17'
    originLabel: 'CHR-1.E'
    originDesc:
      'Characters reveal their perspectives and biases through the words they use, the details they
      provide in the text, the organization of their thinking, the decisions they make, and the
      actions they take.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b18'
    originLabel: 'CHR-1.F'
    originDesc:
      'The description of a character creates certain expectations for that character’s behaviors;
      how a character does or does not meet those expectations affects a reader’s interpretation of
      that character.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b19'
    originLabel: 'CHR-1.G'
    originDesc:
      'Details associated with a character and/or used to describe a character contribute to a
      reader’s interpretation of that character.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b20'
    originLabel: 'CHR-1.H'
    originDesc:
      'Readers’ understanding of a character’s perspective may depend on the perspective of the
      narrator or speaker.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b21'
    originLabel: 'CHR-1.I'
    originDesc: 'A character’s perspective may shift during the course of a narrative.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b22'
    originLabel: 'CHR-1.J'
    originDesc:
      'When narrators, characters, or speakers compare another character to something or someone
      else, they reveal their perspective on the compared character and may also reveal something
      innate about the compared character.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b23'
    originLabel: 'CHR-1.K'
    originDesc:
      'Readers can infer a character’s motives from that character’s actions or inactions.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b24'
    originLabel: 'CHR-1.L'
    originDesc:
      'A dynamic character who develops over the course of the narrative often makes choices that
      directly or indirectly affect the climax and/or the resolution of that narrative.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b25'
    originLabel: 'CHR-1.M'
    originDesc:
      'Character changes can be visible and external, such as changes to health or wealth, or can be
      internal, psychological or emotional changes; external changes can lead to internal changes,
      and vice versa.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b26'
    originLabel: 'CHR-1.N'
    originDesc:
      'Some characters remain unchanged or are largely unaffected by the events of the narrative.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b27'
    originLabel: 'CHR-1.O'
    originDesc:
      'The significance of characters is often revealed through their agency and through nuanced
      descriptions.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b28'
    originLabel: 'CHR-1.P'
    originDesc: 'Characters’ choices—in speech, action, and inaction—reveal what they value.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b29'
    originLabel: 'CHR-1.Q'
    originDesc:
      'The main character in a narrative is the protagonist; the antagonist in the narrative opposes
      the protagonist and may be another character, the internal conflicts of the protagonist, a
      collective (such as society), or nature.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b30'
    originLabel: 'CHR-1.R'
    originDesc: 'Protagonists and antagonists may represent contrasting values.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b31'
    originLabel: 'CHR-1.S'
    originDesc:
      'Conflict among characters often arises from tensions generated by their different value
      systems.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b32'
    originLabel: 'CHR-1.T'
    originDesc:
      'Different character, narrator, or speaker perspectives often reveal different information,
      develop different attitudes, and influence different interpretations of a text and the ideas
      in it.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b33'
    originLabel: 'CHR-1.U'
    originDesc:
      'Foil characters (foils) serve to illuminate, through contrast, the traits, attributes, or
      values of another character.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b34'
    originLabel: 'CHR-1.V'
    originDesc:
      'Inconsistencies between the private thoughts of characters and their actual behavior reveal
      tensions and complexities between private and professed values.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b35'
    originLabel: 'CHR-1.W'
    originDesc:
      'A character’s competing, conflicting, or inconsistent choices or actions contribute to
      complexity in a text.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b36'
    originLabel: 'CHR-1.X'
    originDesc:
      'Often the change in a character emerges directly from a conflict of values represented in the
      narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b37'
    originLabel: 'CHR-1.Y'
    originDesc: 'Changes in a character’s circumstances may lead to changes in that character.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b38'
    originLabel: 'CHR-1.Z'
    originDesc:
      'While characters can change gradually over the course of a narrative, they can also change
      suddenly as the result of a moment of realization, known as an epiphany. An epiphany allows a
      character to see things in a new light and is often directly related to a central conflict of
      the narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b39'
    originLabel: 'CHR-1.AA'
    originDesc:
      'An epiphany may affect the plot by causing a character to act on his or her sudden
      realization.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b40'
    originLabel: 'CHR-1.AB'
    originDesc: 'A group or force can function as a character.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b41'
    originLabel: 'CHR-1.AC'
    originDesc:
      'When readers consider a character, they should examine how that character interacts with
      other characters, groups, or forces and what those interactions may indicate about the
      character.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b42'
    originLabel: 'CHR-1.AD'
    originDesc:
      'The relationship between a character and a group, including the inclusion or exclusion of
      that character, reveals the collective attitude of the group toward that character and
      possibly the character’s attitude toward the group.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b43'
    originLabel: 'CHR-1.AE'
    originDesc:
      'Minor characters often remain unchanged because the narrative doesn’t focus on them. They may
      only be part of the narrative to advance the plot or to interact with major characters.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b44'
    originLabel: 'CHR-1.AF'
    originDesc:
      'Readers’ interpretations of a text are often affected by a character changing—or not—and the
      meaning conveyed by such changes or lack thereof.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b45'
    originLabel: 'CHR-1.AG'
    originDesc:
      'A character’s responses to the resolution of the narrative - in their words or in their
      actions - reveal something about that character’s own values; these responses may be
      inconsistent with the previously established behaviors or perspectives of that character.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b46'
    originLabel: 'CHR-1.AH'
    originDesc:
      'Inconsistencies and unexpected developments in a character affect readers’ interpretation of
      that character; other characters; events in the plot; conflicts; the perspective of the
      narrator, character, or speaker; and/or setting.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b47'
    originLabel: 'SET-1.A'
    originDesc: 'Setting includes the time and place during which the events of the text occur.'
    targetID: 'b193'
    targetLabel: '2.A'
    targetDesc: 'Identify and describe specific textual details that convey or reveal a setting.'
    associationType: 'isRelatedTo'
  - originID: 'b48'
    originLabel: 'SET-1.B'
    originDesc:
      'Setting includes the social, cultural, and historical situation during which the events of
      the text occur.'
    targetID: 'b193'
    targetLabel: '2.A'
    targetDesc: 'Identify and describe specific textual details that convey or reveal a setting.'
    associationType: 'isRelatedTo'
  - originID: 'b49'
    originLabel: 'SET-1.C'
    originDesc: 'A setting may help establish the mood and atmosphere of a narrative.'
    targetID: 'b194'
    targetLabel: '2.B'
    targetDesc: 'Explain the function of setting in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b50'
    originLabel: 'SET-1.D'
    originDesc: 'The environment a character inhabits provides information about that character.'
    targetID: 'b195'
    targetLabel: '2.C'
    targetDesc: 'Describe the relationship between a character and a setting.'
    associationType: 'isRelatedTo'
  - originID: 'b51'
    originLabel: 'SET-1.E'
    originDesc:
      'When a setting changes, it may suggest other movements, changes, or shifts in the narrative.'
    targetID: 'b194'
    targetLabel: '2.B'
    targetDesc: 'Explain the function of setting in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b52'
    originLabel: 'SET-1.F'
    originDesc:
      'Settings may be contrasted in order to establish a conflict of values or ideas associated
      with those settings.'
    targetID: 'b194'
    targetLabel: '2.B'
    targetDesc: 'Explain the function of setting in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b53'
    originLabel: 'SET-1.G'
    originDesc:
      'The way characters interact with their surroundings provides insights about those characters
      and the setting(s) they inhabit.'
    targetID: 'b195'
    targetLabel: '2.C'
    targetDesc: 'Describe the relationship between a character and a setting.'
    associationType: 'isRelatedTo'
  - originID: 'b54'
    originLabel: 'SET-1.H'
    originDesc:
      'The way characters behave in or describe their surroundings reveals an attitude about those
      surroundings and contributes to the development of those characters and readers’
      interpretations of them.'
    targetID: 'b195'
    targetLabel: '2.C'
    targetDesc: 'Describe the relationship between a character and a setting.'
    associationType: 'isRelatedTo'
  - originID: 'b47'
    originLabel: 'SET-1.A'
    originDesc: 'Setting includes the time and place during which the events of the text occur.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b48'
    originLabel: 'SET-1.B'
    originDesc:
      'Setting includes the social, cultural, and historical situation during which the events of
      the text occur.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b49'
    originLabel: 'SET-1.C'
    originDesc: 'A setting may help establish the mood and atmosphere of a narrative.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b50'
    originLabel: 'SET-1.D'
    originDesc: 'The environment a character inhabits provides information about that character.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b51'
    originLabel: 'SET-1.E'
    originDesc:
      'When a setting changes, it may suggest other movements, changes, or shifts in the narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b52'
    originLabel: 'SET-1.F'
    originDesc:
      'Settings may be contrasted in order to establish a conflict of values or ideas associated
      with those settings.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b53'
    originLabel: 'SET-1.G'
    originDesc:
      'The way characters interact with their surroundings provides insights about those characters
      and the setting(s) they inhabit.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b54'
    originLabel: 'SET-1.H'
    originDesc:
      'The way characters behave in or describe their surroundings reveals an attitude about those
      surroundings and contributes to the development of those characters and readers’
      interpretations of them.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b55'
    originLabel: 'STR-1.A'
    originDesc:
      'Plot is the sequence of events in a narrative; events throughout a narrative are connected,
      with each event building on the others, often with a cause-and-effect relationship.'
    targetID: 'b196'
    targetLabel: '3.A'
    targetDesc: 'Identify and describe how plot orders events in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b56'
    originLabel: 'STR-1.B'
    originDesc:
      'The dramatic situation of a narrative includes the setting and action of the plot and how
      that narrative develops to place characters in conflict(s), and often involves the rising or
      falling fortunes of a main character or set of characters.'
    targetID: 'b196'
    targetLabel: '3.A'
    targetDesc: 'Identify and describe how plot orders events in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b57'
    originLabel: 'STR-1.C'
    originDesc:
      'Plot and the exposition that accompanies it focus readers’ attention on the parts of the
      narrative that matter most to its development, including characters, their relationships, and
      their roles in the narrative, as well as setting and the relationship between characters and
      setting.'
    targetID: 'b197'
    targetLabel: '3.B'
    targetDesc: 'Explain the function of a particular sequence of events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b58'
    originLabel: 'STR-1.D'
    originDesc:
      'Line and stanza breaks contribute to the development and relationship of ideas in a poem.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b59'
    originLabel: 'STR-1.E'
    originDesc:
      'The arrangement of lines and stanzas contributes to the development and relationship of ideas
      in a poem.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b60'
    originLabel: 'STR-1.F'
    originDesc:
      'A text’s structure affects readers’ reactions and expectations by presenting the
      relationships among the ideas of the text via their relative positions and their placement
      within the text as a whole.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b61'
    originLabel: 'STR-1.G'
    originDesc:
      'Contrast can be introduced through focus; tone; point of view; character, narrator, or
      speaker perspective; dramatic situation or moment; settings or time; or imagery.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b62'
    originLabel: 'STR-1.H'
    originDesc: 'Contrasts are the result of shifts or juxtapositions or both.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b63'
    originLabel: 'STR-1.I'
    originDesc: 'Shifts may be signaled by a word, a structural convention, or punctuation.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b64'
    originLabel: 'STR-1.J'
    originDesc: 'Shifts may emphasize contrasts between particular segments of a text.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b65'
    originLabel: 'STR-1.K'
    originDesc:
      'A story, or narrative, is delivered through a series of events that relate to a conflict.'
    targetID: 'b200'
    targetLabel: '3.E'
    targetDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b66'
    originLabel: 'STR-1.L'
    originDesc:
      'Events include episodes, encounters, and scenes in a narrative that can introduce and develop
      a plot.'
    targetID: 'b200'
    targetLabel: '3.E'
    targetDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b67'
    originLabel: 'STR-1.M'
    originDesc:
      'The significance of an event depends on its relationship to the narrative, the conflict, and
      the development of characters.'
    targetID: 'b200'
    targetLabel: '3.E'
    targetDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b68'
    originLabel: 'STR-1.N'
    originDesc:
      'Conflict is tension between competing values either within a character, known as internal or
      psychological conflict, or with outside forces that obstruct a character in some way, known as
      external conflict.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b69'
    originLabel: 'STR-1.O'
    originDesc:
      'A text may contain multiple conflicts. Often two or more conflicts in a text intersect.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b70'
    originLabel: 'STR-1.P'
    originDesc:
      'A primary conflict can be heightened by the presence of additional conflicts that intersect
      with it.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b71'
    originLabel: 'STR-1.Q'
    originDesc:
      'Inconsistencies in a text may create contrasts that represent conflicts of values or
      perspectives.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b72'
    originLabel: 'STR-1.R'
    originDesc:
      'Some patterns in dramatic situations are so common that they are considered archetypes, and
      these archetypes create certain expectations for how the dramatic situations will progress and
      be resolved.'
    targetID: 'b196'
    targetLabel: '3.A'
    targetDesc: 'Identify and describe how plot orders events in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b73'
    originLabel: 'STR-1.S'
    originDesc:
      'The differences highlighted by a contrast emphasize the particular traits, aspects, or
      characteristics important for comparison of the things being contrasted.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b74'
    originLabel: 'STR-1.T'
    originDesc:
      'Contrasts often represent conflicts in values related to character, narrator, or speaker
      perspectives on ideas represented by a text.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b75'
    originLabel: 'STR-1.U'
    originDesc:
      'Closed forms of poetry include predictable patterns in the structure of lines, stanzas,
      meter, and rhyme, which develop relationships among ideas in the poem.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b76'
    originLabel: 'STR-1.V'
    originDesc:
      'Open forms of poetry may not follow expected or predictable patterns in the structure of
      their lines or stanzas, but they may still have structures that develop relationships between
      ideas in the poem.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b77'
    originLabel: 'STR-1.W'
    originDesc: 'Structures combine in texts to emphasize certain ideas and concepts.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b78'
    originLabel: 'STR-1.X'
    originDesc:
      'Some narrative structures interrupt the chronology of a plot; such structures include
      flashback, foreshadowing, in medias res, and stream of consciousness.'
    targetID: 'b196'
    targetLabel: '3.A'
    targetDesc: 'Identify and describe how plot orders events in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b79'
    originLabel: 'STR-1.Y'
    originDesc:
      'Narrative structures that interrupt the chronology of a plot, such as flashback,
      foreshadowing, in medias res, and stream of consciousness, can directly affect readers’
      experiences with a text by creating anticipation or suspense or building tension.'
    targetID: 'b197'
    targetLabel: '3.B'
    targetDesc: 'Explain the function of a particular sequence of events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b80'
    originLabel: 'STR-1.Z'
    originDesc:
      'Contrasts often represent contradictions or inconsistencies that introduce nuance, ambiguity,
      or contradiction into a text. As a result, contrasts make texts more complex.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b81'
    originLabel: 'STR-1.AA'
    originDesc:
      'Pacing is the manipulation of time in a text. Several factors contribute to the pace of a
      narrative, including arrangement of details, frequency of events, narrative structures,
      syntax, the tempo or speed at which events occur, or shifts in tense and chronology in the
      narrative.'
    targetID: 'b196'
    targetLabel: '3.A'
    targetDesc: 'Identify and describe how plot orders events in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b82'
    originLabel: 'STR-1.AB'
    originDesc:
      'Narrative pacing may evoke an emotional reaction in readers by the order in which information
      is revealed; the relationships between the information, when it is provided, and other parts
      of the narrative; and the significance of the revealed information to other parts of the
      narrative.'
    targetID: 'b197'
    targetLabel: '3.B'
    targetDesc: 'Explain the function of a particular sequence of events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b83'
    originLabel: 'STR-1.AC'
    originDesc: 'Ideas and images in a poem may extend beyond a single line or stanza.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b84'
    originLabel: 'STR-1.AD'
    originDesc: 'Punctuation is often crucial to the understanding of a text.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b85'
    originLabel: 'STR-1.AE'
    originDesc:
      'When structural patterns are created in a text, any interruption in the pattern creates a
      point of emphasis.'
    targetID: 'b198'
    targetLabel: '3.C'
    targetDesc: 'Explain the function of structure in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b86'
    originLabel: 'STR-1.AF'
    originDesc: 'Juxtaposition may create or demonstrate an antithesis.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b87'
    originLabel: 'STR-1.AG'
    originDesc:
      'Situational or verbal irony is created when events or statements in a text are inconsistent
      with either the expectations readers bring to a text or the expectations established by the
      text itself.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b88'
    originLabel: 'STR-1.AH'
    originDesc:
      'Paradox occurs when seemingly contradictory elements are juxtaposed, but the
      contradiction—which may or may not be reconciled—can reveal a hidden or unexpected idea.'
    targetID: 'b199'
    targetLabel: '3.D'
    targetDesc: 'Explain the function of contrasts within a text.'
    associationType: 'isRelatedTo'
  - originID: 'b89'
    originLabel: 'STR-1.AI'
    originDesc:
      'Significant events often illustrate competing value systems that relate to a conflict present
      in the text.'
    targetID: 'b200'
    targetLabel: '3.E'
    targetDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b90'
    originLabel: 'STR-1.AJ'
    originDesc:
      'Events in a plot collide and accumulate to create a sense of anticipation and suspense.'
    targetID: 'b200'
    targetLabel: '3.E'
    targetDesc:
      'Explain the function of a significant event or related set of significant events in a plot.'
    associationType: 'isRelatedTo'
  - originID: 'b91'
    originLabel: 'STR-1.AK'
    originDesc:
      'The resolution of the anticipation, suspense, or central conflicts of a plot may be referred
      to as the moment of catharsis or emotional release.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b92'
    originLabel: 'STR-1.AL'
    originDesc:
      'Sometimes things not actually shown in a narrative, such as an unseen character or a
      preceding action, may be in conflict with or result in conflict for a character.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b93'
    originLabel: 'STR-1.AM'
    originDesc:
      'Although most plots end in resolution of the central conflicts, some have unresolved endings,
      and the lack of resolution may contribute to interpretations of the text.'
    targetID: 'b201'
    targetLabel: '3.F'
    targetDesc: 'Explain the function of conflict in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b55'
    originLabel: 'STR-1.A'
    originDesc:
      'Plot is the sequence of events in a narrative; events throughout a narrative are connected,
      with each event building on the others, often with a cause-and-effect relationship.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b56'
    originLabel: 'STR-1.B'
    originDesc:
      'The dramatic situation of a narrative includes the setting and action of the plot and how
      that narrative develops to place characters in conflict(s), and often involves the rising or
      falling fortunes of a main character or set of characters.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b57'
    originLabel: 'STR-1.C'
    originDesc:
      'Plot and the exposition that accompanies it focus readers’ attention on the parts of the
      narrative that matter most to its development, including characters, their relationships, and
      their roles in the narrative, as well as setting and the relationship between characters and
      setting.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b58'
    originLabel: 'STR-1.D'
    originDesc:
      'Line and stanza breaks contribute to the development and relationship of ideas in a poem.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b59'
    originLabel: 'STR-1.E'
    originDesc:
      'The arrangement of lines and stanzas contributes to the development and relationship of ideas
      in a poem.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b60'
    originLabel: 'STR-1.F'
    originDesc:
      'A text’s structure affects readers’ reactions and expectations by presenting the
      relationships among the ideas of the text via their relative positions and their placement
      within the text as a whole.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b61'
    originLabel: 'STR-1.G'
    originDesc:
      'Contrast can be introduced through focus; tone; point of view; character, narrator, or
      speaker perspective; dramatic situation or moment; settings or time; or imagery.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b62'
    originLabel: 'STR-1.H'
    originDesc: 'Contrasts are the result of shifts or juxtapositions or both.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b63'
    originLabel: 'STR-1.I'
    originDesc: 'Shifts may be signaled by a word, a structural convention, or punctuation.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b64'
    originLabel: 'STR-1.J'
    originDesc: 'Shifts may emphasize contrasts between particular segments of a text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b65'
    originLabel: 'STR-1.K'
    originDesc:
      'A story, or narrative, is delivered through a series of events that relate to a conflict.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b66'
    originLabel: 'STR-1.L'
    originDesc:
      'Events include episodes, encounters, and scenes in a narrative that can introduce and develop
      a plot.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b67'
    originLabel: 'STR-1.M'
    originDesc:
      'The significance of an event depends on its relationship to the narrative, the conflict, and
      the development of characters.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b68'
    originLabel: 'STR-1.N'
    originDesc:
      'Conflict is tension between competing values either within a character, known as internal or
      psychological conflict, or with outside forces that obstruct a character in some way, known as
      external conflict.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b69'
    originLabel: 'STR-1.O'
    originDesc:
      'A text may contain multiple conflicts. Often two or more conflicts in a text intersect.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b70'
    originLabel: 'STR-1.P'
    originDesc:
      'A primary conflict can be heightened by the presence of additional conflicts that intersect
      with it.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b71'
    originLabel: 'STR-1.Q'
    originDesc:
      'Inconsistencies in a text may create contrasts that represent conflicts of values or
      perspectives.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b72'
    originLabel: 'STR-1.R'
    originDesc:
      'Some patterns in dramatic situations are so common that they are considered archetypes, and
      these archetypes create certain expectations for how the dramatic situations will progress and
      be resolved.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b73'
    originLabel: 'STR-1.S'
    originDesc:
      'The differences highlighted by a contrast emphasize the particular traits, aspects, or
      characteristics important for comparison of the things being contrasted.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b74'
    originLabel: 'STR-1.T'
    originDesc:
      'Contrasts often represent conflicts in values related to character, narrator, or speaker
      perspectives on ideas represented by a text.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b75'
    originLabel: 'STR-1.U'
    originDesc:
      'Closed forms of poetry include predictable patterns in the structure of lines, stanzas,
      meter, and rhyme, which develop relationships among ideas in the poem.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b76'
    originLabel: 'STR-1.V'
    originDesc:
      'Open forms of poetry may not follow expected or predictable patterns in the structure of
      their lines or stanzas, but they may still have structures that develop relationships between
      ideas in the poem.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b77'
    originLabel: 'STR-1.W'
    originDesc: 'Structures combine in texts to emphasize certain ideas and concepts.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b78'
    originLabel: 'STR-1.X'
    originDesc:
      'Some narrative structures interrupt the chronology of a plot; such structures include
      flashback, foreshadowing, in medias res, and stream of consciousness.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b79'
    originLabel: 'STR-1.Y'
    originDesc:
      'Narrative structures that interrupt the chronology of a plot, such as flashback,
      foreshadowing, in medias res, and stream of consciousness, can directly affect readers’
      experiences with a text by creating anticipation or suspense or building tension.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b80'
    originLabel: 'STR-1.Z'
    originDesc:
      'Contrasts often represent contradictions or inconsistencies that introduce nuance, ambiguity,
      or contradiction into a text. As a result, contrasts make texts more complex.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b81'
    originLabel: 'STR-1.AA'
    originDesc:
      'Pacing is the manipulation of time in a text. Several factors contribute to the pace of a
      narrative, including arrangement of details, frequency of events, narrative structures,
      syntax, the tempo or speed at which events occur, or shifts in tense and chronology in the
      narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b82'
    originLabel: 'STR-1.AB'
    originDesc:
      'Narrative pacing may evoke an emotional reaction in readers by the order in which information
      is revealed; the relationships between the information, when it is provided, and other parts
      of the narrative; and the significance of the revealed information to other parts of the
      narrative.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b83'
    originLabel: 'STR-1.AC'
    originDesc: 'Ideas and images in a poem may extend beyond a single line or stanza.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b84'
    originLabel: 'STR-1.AD'
    originDesc: 'Punctuation is often crucial to the understanding of a text.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b85'
    originLabel: 'STR-1.AE'
    originDesc:
      'When structural patterns are created in a text, any interruption in the pattern creates a
      point of emphasis.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b86'
    originLabel: 'STR-1.AF'
    originDesc: 'Juxtaposition may create or demonstrate an antithesis.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b87'
    originLabel: 'STR-1.AG'
    originDesc:
      'Situational or verbal irony is created when events or statements in a text are inconsistent
      with either the expectations readers bring to a text or the expectations established by the
      text itself.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b88'
    originLabel: 'STR-1.AH'
    originDesc:
      'Paradox occurs when seemingly contradictory elements are juxtaposed, but the
      contradiction—which may or may not be reconciled—can reveal a hidden or unexpected idea.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b89'
    originLabel: 'STR-1.AI'
    originDesc:
      'Significant events often illustrate competing value systems that relate to a conflict present
      in the text.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b90'
    originLabel: 'STR-1.AJ'
    originDesc:
      'Events in a plot collide and accumulate to create a sense of anticipation and suspense.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b91'
    originLabel: 'STR-1.AK'
    originDesc:
      'The resolution of the anticipation, suspense, or central conflicts of a plot may be referred
      to as the moment of catharsis or emotional release.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b92'
    originLabel: 'STR-1.AL'
    originDesc:
      'Sometimes things not actually shown in a narrative, such as an unseen character or a
      preceding action, may be in conflict with or result in conflict for a character.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b93'
    originLabel: 'STR-1.AM'
    originDesc:
      'Although most plots end in resolution of the central conflicts, some have unresolved endings,
      and the lack of resolution may contribute to interpretations of the text.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b94'
    originLabel: 'NAR-1.A'
    originDesc:
      'Narrators or speakers relate accounts to readers and establish a relationship between the
      text and the reader.'
    targetID: 'b202'
    targetLabel: '4.A'
    targetDesc: 'Identify and describe the narrator or speaker of a text.'
    associationType: 'isRelatedTo'
  - originID: 'b95'
    originLabel: 'NAR-1.B'
    originDesc:
      'Perspective refers to how narrators, characters, or speakers see their circumstances, while
      point of view refers to the position from which a narrator or speaker relates the events of a
      narrative.'
    targetID: 'b202'
    targetLabel: '4.A'
    targetDesc: 'Identify and describe the narrator or speaker of a text.'
    associationType: 'isRelatedTo'
  - originID: 'b96'
    originLabel: 'NAR-1.C'
    originDesc: 'A speaker or narrator is not necessarily the author.'
    targetID: 'b202'
    targetLabel: '4.A'
    targetDesc: 'Identify and describe the narrator or speaker of a text.'
    associationType: 'isRelatedTo'
  - originID: 'b97'
    originLabel: 'NAR-1.D'
    originDesc:
      'The point of view contributes to what narrators, characters, or speakers can and cannot
      provide in a text based on their level of involvement and intimacy with the details, events,
      or characters.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b98'
    originLabel: 'NAR-1.E'
    originDesc:
      'Narrators may also be characters, and their role as characters may influence their
      perspective.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b99'
    originLabel: 'NAR-1.F'
    originDesc:
      'First-person narrators are involved in the narrative; their relationship to the events of the
      plot and the other characters shapes their perspective.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b100'
    originLabel: 'NAR-1.G'
    originDesc: 'Third-person narrators are outside observers.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b101'
    originLabel: 'NAR-1.H'
    originDesc:
      'Third-person narrators’ knowledge about events and characters may range from observational to
      all-knowing, which shapes their perspective.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b102'
    originLabel: 'NAR-1.I'
    originDesc:
      'The outside perspective of third-person narrators may not be affected by the events of the
      narrative.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b103'
    originLabel: 'NAR-1.J'
    originDesc:
      'Narrators may function as characters in the narrative who directly address readers and either
      recall events or describe them as they occur.'
    targetID: 'b202'
    targetLabel: '4.A'
    targetDesc: 'Identify and describe the narrator or speaker of a text.'
    associationType: 'isRelatedTo'
  - originID: 'b104'
    originLabel: 'NAR-1.K'
    originDesc:
      'Narrative distance refers to the physical distance, chronological distance, relationships, or
      emotional investment of the narrator to the events or characters of the narrative.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b105'
    originLabel: 'NAR-1.L'
    originDesc:
      'Stream of consciousness is a type of narration in which a character’s thoughts are related
      through a continuous dialogue or description.'
    targetID: 'b203'
    targetLabel: '4.B'
    targetDesc: 'Identify and explain the function of point of view in a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b106'
    originLabel: 'NAR-1.M'
    originDesc:
      'The narrators’, characters’, or speakers’ backgrounds and perspectives shape the tone they
      convey about subjects or events in the text.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b107'
    originLabel: 'NAR-1.N'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, not only qualify or modify the things they
      describe but also convey a perspective toward those things.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b108'
    originLabel: 'NAR-1.O'
    originDesc:
      'The attitude of narrators, characters, or speakers toward an idea, character, or situation
      emerges from their perspective and may be referred to as tone.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b109'
    originLabel: 'NAR-1.P'
    originDesc:
      "The narrator’s or speaker’s tone toward events or characters in a text influences readers'
      interpretation of the ideas associated with those things."
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b110'
    originLabel: 'NAR-1.Q'
    originDesc:
      "The syntactical arrangement of phrases and clauses in a sentence can emphasize details or
      ideas and convey a narrator’s or speaker's tone."
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b111'
    originLabel: 'NAR-1.R'
    originDesc:
      'Information included and/or not included in a text conveys the perspective of characters,
      narrators, and/or speakers.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b112'
    originLabel: 'NAR-1.S'
    originDesc:
      "A narrator's or speaker's perspective may influence the details and amount of detail in a
      text and may reveal biases, motivations, or understandings."
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b113'
    originLabel: 'NAR-1.T'
    originDesc:
      'Readers can infer narrators’ biases by noting which details they choose to include in a
      narrative and which they choose to omit.'
    targetID: 'b205'
    targetLabel: '4.D'
    targetDesc: 'Explain how a narrator’s reliability affects a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b114'
    originLabel: 'NAR-1.U'
    originDesc: 'Readers who detect bias in a narrator may find that narrator less reliable.'
    targetID: 'b205'
    targetLabel: '4.D'
    targetDesc: 'Explain how a narrator’s reliability affects a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b115'
    originLabel: 'NAR-1.V'
    originDesc:
      'The reliability of a narrator may influence a reader’s understanding of a character’s
      motives.'
    targetID: 'b205'
    targetLabel: '4.D'
    targetDesc: 'Explain how a narrator’s reliability affects a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b116'
    originLabel: 'NAR-1.W'
    originDesc:
      'Some narrators or speakers may provide details and information that others do not or cannot
      provide. Multiple narrators or speakers may provide contradictory information in a text.'
    targetID: 'b205'
    targetLabel: '4.D'
    targetDesc: 'Explain how a narrator’s reliability affects a narrative.'
    associationType: 'isRelatedTo'
  - originID: 'b117'
    originLabel: 'NAR-1.X'
    originDesc:
      'Multiple, and even contrasting, perspectives can occur within a single text and contribute to
      the complexity of the text.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b118'
    originLabel: 'NAR-1.Y'
    originDesc:
      'A narrator or speaker may change over the course of a text as a result of actions and
      interactions.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b119'
    originLabel: 'NAR-1.Z'
    originDesc:
      'Changes and inconsistencies in a narrator’s or speaker’s perspective may contribute to irony
      or the complexity of the text.'
    targetID: 'b204'
    targetLabel: '4.C'
    targetDesc:
      'Identify and describe details, diction, or syntax in a text that reveal a narrator’s or
      speaker’s perspective.'
    associationType: 'isRelatedTo'
  - originID: 'b94'
    originLabel: 'NAR-1.A'
    originDesc:
      'Narrators or speakers relate accounts to readers and establish a relationship between the
      text and the reader.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b95'
    originLabel: 'NAR-1.B'
    originDesc:
      'Perspective refers to how narrators, characters, or speakers see their circumstances, while
      point of view refers to the position from which a narrator or speaker relates the events of a
      narrative.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b96'
    originLabel: 'NAR-1.C'
    originDesc: 'A speaker or narrator is not necessarily the author.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b97'
    originLabel: 'NAR-1.D'
    originDesc:
      'The point of view contributes to what narrators, characters, or speakers can and cannot
      provide in a text based on their level of involvement and intimacy with the details, events,
      or characters.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b98'
    originLabel: 'NAR-1.E'
    originDesc:
      'Narrators may also be characters, and their role as characters may influence their
      perspective.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b99'
    originLabel: 'NAR-1.F'
    originDesc:
      'First-person narrators are involved in the narrative; their relationship to the events of the
      plot and the other characters shapes their perspective.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b100'
    originLabel: 'NAR-1.G'
    originDesc: 'Third-person narrators are outside observers.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b101'
    originLabel: 'NAR-1.H'
    originDesc:
      'Third-person narrators’ knowledge about events and characters may range from observational to
      all-knowing, which shapes their perspective.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b102'
    originLabel: 'NAR-1.I'
    originDesc:
      'The outside perspective of third-person narrators may not be affected by the events of the
      narrative.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b103'
    originLabel: 'NAR-1.J'
    originDesc:
      'Narrators may function as characters in the narrative who directly address readers and either
      recall events or describe them as they occur.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b104'
    originLabel: 'NAR-1.K'
    originDesc:
      'Narrative distance refers to the physical distance, chronological distance, relationships, or
      emotional investment of the narrator to the events or characters of the narrative.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b105'
    originLabel: 'NAR-1.L'
    originDesc:
      'Stream of consciousness is a type of narration in which a character’s thoughts are related
      through a continuous dialogue or description.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b106'
    originLabel: 'NAR-1.M'
    originDesc:
      'The narrators’, characters’, or speakers’ backgrounds and perspectives shape the tone they
      convey about subjects or events in the text.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b107'
    originLabel: 'NAR-1.N'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, not only qualify or modify the things they
      describe but also convey a perspective toward those things.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b108'
    originLabel: 'NAR-1.O'
    originDesc:
      'The attitude of narrators, characters, or speakers toward an idea, character, or situation
      emerges from their perspective and may be referred to as tone.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b109'
    originLabel: 'NAR-1.P'
    originDesc:
      "The narrator’s or speaker’s tone toward events or characters in a text influences readers'
      interpretation of the ideas associated with those things."
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b110'
    originLabel: 'NAR-1.Q'
    originDesc:
      "The syntactical arrangement of phrases and clauses in a sentence can emphasize details or
      ideas and convey a narrator’s or speaker's tone."
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b111'
    originLabel: 'NAR-1.R'
    originDesc:
      'Information included and/or not included in a text conveys the perspective of characters,
      narrators, and/or speakers.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b112'
    originLabel: 'NAR-1.S'
    originDesc:
      "A narrator's or speaker's perspective may influence the details and amount of detail in a
      text and may reveal biases, motivations, or understandings."
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b113'
    originLabel: 'NAR-1.T'
    originDesc:
      'Readers can infer narrators’ biases by noting which details they choose to include in a
      narrative and which they choose to omit.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b114'
    originLabel: 'NAR-1.U'
    originDesc: 'Readers who detect bias in a narrator may find that narrator less reliable.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b115'
    originLabel: 'NAR-1.V'
    originDesc:
      'The reliability of a narrator may influence a reader’s understanding of a character’s
      motives.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b116'
    originLabel: 'NAR-1.W'
    originDesc:
      'Some narrators or speakers may provide details and information that others do not or cannot
      provide. Multiple narrators or speakers may provide contradictory information in a text.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b117'
    originLabel: 'NAR-1.X'
    originDesc:
      'Multiple, and even contrasting, perspectives can occur within a single text and contribute to
      the complexity of the text.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b118'
    originLabel: 'NAR-1.Y'
    originDesc:
      'A narrator or speaker may change over the course of a text as a result of actions and
      interactions.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b119'
    originLabel: 'NAR-1.Z'
    originDesc:
      'Changes and inconsistencies in a narrator’s or speaker’s perspective may contribute to irony
      or the complexity of the text.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b120'
    originLabel: 'FIG-1.A'
    originDesc:
      'An antecedent is a word, phrase, or clause that precedes its referent. Referents may include
      pronouns, nouns, phrases, or clauses.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b121'
    originLabel: 'FIG-1.B'
    originDesc:
      'Referents are ambiguous if they can refer to more than one antecedent, which affects
      interpretation.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b122'
    originLabel: 'FIG-1.C'
    originDesc: 'Words or phrases may be repeated to emphasize ideas or associations.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b123'
    originLabel: 'FIG-1.D'
    originDesc:
      'Alliteration is the repetition of the same letter sound at the beginning of adjacent or
      nearby words to emphasize those words and their associations or representations.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b124'
    originLabel: 'FIG-1.E'
    originDesc:
      'A simile uses the words “like” or “as” to liken two objects or concepts to each other.'
    targetID: 'b210'
    targetLabel: '6.A'
    targetDesc: 'Identify and explain the function of a simile.'
    associationType: 'isRelatedTo'
  - originID: 'b125'
    originLabel: 'FIG-1.F'
    originDesc:
      'Similes liken two different things to transfer the traits or qualities of one to the other.'
    targetID: 'b210'
    targetLabel: '6.A'
    targetDesc: 'Identify and explain the function of a simile.'
    associationType: 'isRelatedTo'
  - originID: 'b126'
    originLabel: 'FIG-1.G'
    originDesc:
      'In a simile, the thing being compared is the main subject; the thing to which it is being
      compared is the comparison subject.'
    targetID: 'b210'
    targetLabel: '6.A'
    targetDesc: 'Identify and explain the function of a simile.'
    associationType: 'isRelatedTo'
  - originID: 'b127'
    originLabel: 'FIG-1.H'
    originDesc:
      'A metaphor implies similarities between two (usually unrelated) concepts or objects in order
      to reveal or emphasize one or more things about one of them, though the differences between
      the two may also be revealing.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b128'
    originLabel: 'FIG-1.I'
    originDesc:
      'In a metaphor, as in a simile, the thing being compared is the main subject; the thing to
      which it is being compared is the comparison subject.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b129'
    originLabel: 'FIG-1.J'
    originDesc:
      'Comparisons between objects or concepts draw on the experiences and associations readers
      already have with those objects and concepts.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b130'
    originLabel: 'FIG-1.K'
    originDesc:
      'Interpretation of a metaphor may depend on the context of its use; that is, what is happening
      in a text may determine what is transferred in the comparison.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b131'
    originLabel: 'FIG-1.L'
    originDesc:
      'Words with multiple meanings or connotations add nuance or complexity that can contribute to
      interpretations of a text.'
    targetID: 'b206'
    targetLabel: '5.A'
    targetDesc: 'Distinguish between the literal and figurative meanings of words and phrases.'
    associationType: 'isRelatedTo'
  - originID: 'b132'
    originLabel: 'FIG-1.M'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, qualify or modify the things they describe
      and affect readers’ interaction with the text.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b133'
    originLabel: 'FIG-1.N'
    originDesc:
      'Hyperbole exaggerates while understatement minimizes. Exaggerating or minimizing an aspect of
      an object focuses attention on that trait and conveys a perspective about the object.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b134'
    originLabel: 'FIG-1.O'
    originDesc: 'Descriptive words, such as adjectives and adverbs, contribute to sensory imagery.'
    targetID: 'b209'
    targetLabel: '5.D'
    targetDesc: 'Identify and explain the function of an image or imagery.'
    associationType: 'isRelatedTo'
  - originID: 'b135'
    originLabel: 'FIG-1.P'
    originDesc:
      'An image can be literal or it can be a form of a comparison that represents something in a
      text through associations with the senses.'
    targetID: 'b209'
    targetLabel: '5.D'
    targetDesc: 'Identify and explain the function of an image or imagery.'
    associationType: 'isRelatedTo'
  - originID: 'b136'
    originLabel: 'FIG-1.Q'
    originDesc:
      'A collection of images, known as imagery, may emphasize ideas in parts of or throughout a
      text.'
    targetID: 'b209'
    targetLabel: '5.D'
    targetDesc: 'Identify and explain the function of an image or imagery.'
    associationType: 'isRelatedTo'
  - originID: 'b137'
    originLabel: 'FIG-1.R'
    originDesc:
      'Metaphorical comparisons do not focus solely on the objects being compared; they focus on the
      particular traits, qualities, or characteristics of the things being compared.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b138'
    originLabel: 'FIG-1.S'
    originDesc:
      'Comparisons not only communicate literal meaning but may also convey figurative meaning or
      transmit a perspective.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b139'
    originLabel: 'FIG-1.T'
    originDesc:
      'An extended metaphor is created when the comparison of a main subject and comparison subject
      persists through parts of or an entire text, and when the comparison is expanded through
      additional details, similes, and images.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b140'
    originLabel: 'FIG-1.U'
    originDesc:
      'Interpretation of an extended metaphor may depend on the context of its use; that is, what is
      happening in a text may determine what is transferred in the comparison.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b141'
    originLabel: 'FIG-1.V'
    originDesc:
      'Personification is a type of comparison that assigns a human trait or quality to a nonhuman
      object, entity, or idea, thus characterizing that object, entity, or idea.'
    targetID: 'b212'
    targetLabel: '6.C'
    targetDesc: 'Identify and explain the function of personification.'
    associationType: 'isRelatedTo'
  - originID: 'b142'
    originLabel: 'FIG-1.W'
    originDesc:
      'Allusions in a text can reference literary works including myths and sacred texts; other
      works of art including paintings and music; or people, places, or events outside the text.'
    targetID: 'b213'
    targetLabel: '6.D'
    targetDesc: 'Identify and explain the function of an allusion.'
    associationType: 'isRelatedTo'
  - originID: 'b143'
    originLabel: 'FIG-1.X'
    originDesc:
      'When a material object comes to represent, or stand for, an idea or concept, it becomes a
      symbol.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b144'
    originLabel: 'FIG-1.Y'
    originDesc:
      'A symbol is an object that represents a meaning, so it is said to be symbolic or
      representative of that meaning. A symbol can represent different things depending on the
      experiences of a reader or the context of its use in a text.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b145'
    originLabel: 'FIG-1.Z'
    originDesc:
      'Certain symbols are so common and recurrent that many readers have associations with them
      prior to reading a text. Other symbols are more contextualized and only come to represent
      certain things through their use in a particular text.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b146'
    originLabel: 'FIG-1.AA'
    originDesc:
      'When a character comes to represent, or stand for, an idea or concept, that character becomes
      symbolic; some symbolic characters have become so common they are archetypal.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b147'
    originLabel: 'FIG-1.AB'
    originDesc:
      'A setting may become symbolic when it is, or comes to be, associated with abstractions such
      as emotions, ideologies, and beliefs.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b148'
    originLabel: 'FIG-1.AC'
    originDesc:
      'Over time, some settings have developed certain associations such that they almost
      universally symbolize particular concepts.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b149'
    originLabel: 'FIG-1.AD'
    originDesc:
      'A motif is a unified pattern of recurring objects or images used to emphasize a significant
      idea in large parts of or throughout a text.'
    targetID: 'b209'
    targetLabel: '5.D'
    targetDesc: 'Identify and explain the function of an image or imagery.'
    associationType: 'isRelatedTo'
  - originID: 'b150'
    originLabel: 'FIG-1.AE'
    originDesc:
      'The function of a simile relies on the selection of the objects being compared as well as the
      traits of the objects.'
    targetID: 'b210'
    targetLabel: '6.A'
    targetDesc: 'Identify and explain the function of a simile.'
    associationType: 'isRelatedTo'
  - originID: 'b151'
    originLabel: 'FIG-1.AF'
    originDesc:
      'By assigning the qualities of a nonhuman object, entity, or idea to a person or character,
      the narrator, character, or speaker communicates an attitude about that person or character.'
    targetID: 'b212'
    targetLabel: '6.C'
    targetDesc: 'Identify and explain the function of personification.'
    associationType: 'isRelatedTo'
  - originID: 'b152'
    originLabel: 'FIG-1.AG'
    originDesc:
      'Ambiguity allows for different readings and understandings of a text by different readers.'
    targetID: 'b207'
    targetLabel: '5.B'
    targetDesc: 'Explain the function of specific words and phrases in a text.'
    associationType: 'isRelatedTo'
  - originID: 'b153'
    originLabel: 'FIG-1.AH'
    originDesc:
      'Symbols in a text and the way they are used may imply that a narrator, character, or speaker
      has a particular attitude or perspective.'
    targetID: 'b208'
    targetLabel: '5.C'
    targetDesc: 'Identify and explain the function of a symbol.'
    associationType: 'isRelatedTo'
  - originID: 'b154'
    originLabel: 'FIG-1.AI'
    originDesc:
      'A conceit is a form of extended metaphor that often appears in poetry. Conceits develop
      complex comparisons that present images, concepts, and associations in surprising or
      paradoxical ways.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b155'
    originLabel: 'FIG-1.AJ'
    originDesc:
      'Often, conceits are used to make complex comparisons between the natural world and an
      individual.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b156'
    originLabel: 'FIG-1.AK'
    originDesc:
      'Multiple comparisons, representations, or associations may combine to affect one another in
      complex ways.'
    targetID: 'b211'
    targetLabel: '6.B'
    targetDesc: 'Identify and explain the function of a metaphor.'
    associationType: 'isRelatedTo'
  - originID: 'b157'
    originLabel: 'FIG-1.AL'
    originDesc:
      'Because of shared knowledge about a reference, allusions create emotional or intellectual
      associations and understandings.'
    targetID: 'b213'
    targetLabel: '6.D'
    targetDesc: 'Identify and explain the function of an allusion.'
    associationType: 'isRelatedTo'
  - originID: 'b120'
    originLabel: 'FIG-1.A'
    originDesc:
      'An antecedent is a word, phrase, or clause that precedes its referent. Referents may include
      pronouns, nouns, phrases, or clauses.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b121'
    originLabel: 'FIG-1.B'
    originDesc:
      'Referents are ambiguous if they can refer to more than one antecedent, which affects
      interpretation.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b122'
    originLabel: 'FIG-1.C'
    originDesc: 'Words or phrases may be repeated to emphasize ideas or associations.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b123'
    originLabel: 'FIG-1.D'
    originDesc:
      'Alliteration is the repetition of the same letter sound at the beginning of adjacent or
      nearby words to emphasize those words and their associations or representations.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b124'
    originLabel: 'FIG-1.E'
    originDesc:
      'A simile uses the words “like” or “as” to liken two objects or concepts to each other.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b125'
    originLabel: 'FIG-1.F'
    originDesc:
      'Similes liken two different things to transfer the traits or qualities of one to the other.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b126'
    originLabel: 'FIG-1.G'
    originDesc:
      'In a simile, the thing being compared is the main subject; the thing to which it is being
      compared is the comparison subject.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b127'
    originLabel: 'FIG-1.H'
    originDesc:
      'A metaphor implies similarities between two (usually unrelated) concepts or objects in order
      to reveal or emphasize one or more things about one of them, though the differences between
      the two may also be revealing.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b128'
    originLabel: 'FIG-1.I'
    originDesc:
      'In a metaphor, as in a simile, the thing being compared is the main subject; the thing to
      which it is being compared is the comparison subject.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b129'
    originLabel: 'FIG-1.J'
    originDesc:
      'Comparisons between objects or concepts draw on the experiences and associations readers
      already have with those objects and concepts.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b130'
    originLabel: 'FIG-1.K'
    originDesc:
      'Interpretation of a metaphor may depend on the context of its use; that is, what is happening
      in a text may determine what is transferred in the comparison.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b131'
    originLabel: 'FIG-1.L'
    originDesc:
      'Words with multiple meanings or connotations add nuance or complexity that can contribute to
      interpretations of a text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b132'
    originLabel: 'FIG-1.M'
    originDesc:
      'Descriptive words, such as adjectives and adverbs, qualify or modify the things they describe
      and affect readers’ interaction with the text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b133'
    originLabel: 'FIG-1.N'
    originDesc:
      'Hyperbole exaggerates while understatement minimizes. Exaggerating or minimizing an aspect of
      an object focuses attention on that trait and conveys a perspective about the object.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b134'
    originLabel: 'FIG-1.O'
    originDesc: 'Descriptive words, such as adjectives and adverbs, contribute to sensory imagery.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b135'
    originLabel: 'FIG-1.P'
    originDesc:
      'An image can be literal or it can be a form of a comparison that represents something in a
      text through associations with the senses.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b136'
    originLabel: 'FIG-1.Q'
    originDesc:
      'A collection of images, known as imagery, may emphasize ideas in parts of or throughout a
      text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b137'
    originLabel: 'FIG-1.R'
    originDesc:
      'Metaphorical comparisons do not focus solely on the objects being compared; they focus on the
      particular traits, qualities, or characteristics of the things being compared.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b138'
    originLabel: 'FIG-1.S'
    originDesc:
      'Comparisons not only communicate literal meaning but may also convey figurative meaning or
      transmit a perspective.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b139'
    originLabel: 'FIG-1.T'
    originDesc:
      'An extended metaphor is created when the comparison of a main subject and comparison subject
      persists through parts of or an entire text, and when the comparison is expanded through
      additional details, similes, and images.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b140'
    originLabel: 'FIG-1.U'
    originDesc:
      'Interpretation of an extended metaphor may depend on the context of its use; that is, what is
      happening in a text may determine what is transferred in the comparison.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b141'
    originLabel: 'FIG-1.V'
    originDesc:
      'Personification is a type of comparison that assigns a human trait or quality to a nonhuman
      object, entity, or idea, thus characterizing that object, entity, or idea.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b142'
    originLabel: 'FIG-1.W'
    originDesc:
      'Allusions in a text can reference literary works including myths and sacred texts; other
      works of art including paintings and music; or people, places, or events outside the text.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b143'
    originLabel: 'FIG-1.X'
    originDesc:
      'When a material object comes to represent, or stand for, an idea or concept, it becomes a
      symbol.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b144'
    originLabel: 'FIG-1.Y'
    originDesc:
      'A symbol is an object that represents a meaning, so it is said to be symbolic or
      representative of that meaning. A symbol can represent different things depending on the
      experiences of a reader or the context of its use in a text.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b145'
    originLabel: 'FIG-1.Z'
    originDesc:
      'Certain symbols are so common and recurrent that many readers have associations with them
      prior to reading a text. Other symbols are more contextualized and only come to represent
      certain things through their use in a particular text.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b146'
    originLabel: 'FIG-1.AA'
    originDesc:
      'When a character comes to represent, or stand for, an idea or concept, that character becomes
      symbolic; some symbolic characters have become so common they are archetypal.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b147'
    originLabel: 'FIG-1.AB'
    originDesc:
      'A setting may become symbolic when it is, or comes to be, associated with abstractions such
      as emotions, ideologies, and beliefs.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b148'
    originLabel: 'FIG-1.AC'
    originDesc:
      'Over time, some settings have developed certain associations such that they almost
      universally symbolize particular concepts.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b149'
    originLabel: 'FIG-1.AD'
    originDesc:
      'A motif is a unified pattern of recurring objects or images used to emphasize a significant
      idea in large parts of or throughout a text.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b150'
    originLabel: 'FIG-1.AE'
    originDesc:
      'The function of a simile relies on the selection of the objects being compared as well as the
      traits of the objects.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b151'
    originLabel: 'FIG-1.AF'
    originDesc:
      'By assigning the qualities of a nonhuman object, entity, or idea to a person or character,
      the narrator, character, or speaker communicates an attitude about that person or character.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b152'
    originLabel: 'FIG-1.AG'
    originDesc:
      'Ambiguity allows for different readings and understandings of a text by different readers.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b153'
    originLabel: 'FIG-1.AH'
    originDesc:
      'Symbols in a text and the way they are used may imply that a narrator, character, or speaker
      has a particular attitude or perspective.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b154'
    originLabel: 'FIG-1.AI'
    originDesc:
      'A conceit is a form of extended metaphor that often appears in poetry. Conceits develop
      complex comparisons that present images, concepts, and associations in surprising or
      paradoxical ways.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b155'
    originLabel: 'FIG-1.AJ'
    originDesc:
      'Often, conceits are used to make complex comparisons between the natural world and an
      individual.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b156'
    originLabel: 'FIG-1.AK'
    originDesc:
      'Multiple comparisons, representations, or associations may combine to affect one another in
      complex ways.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b157'
    originLabel: 'FIG-1.AL'
    originDesc:
      'Because of shared knowledge about a reference, allusions create emotional or intellectual
      associations and understandings.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b158'
    originLabel: 'LAN-1.A'
    originDesc:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
    targetID: 'b214'
    targetLabel: '7.A'
    targetDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    associationType: 'isRelatedTo'
  - originID: 'b159'
    originLabel: 'LAN-1.B'
    originDesc: 'A claim is a statement that requires defense with evidence from the text.'
    targetID: 'b214'
    targetLabel: '7.A'
    targetDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    associationType: 'isRelatedTo'
  - originID: 'b160'
    originLabel: 'LAN-1.C'
    originDesc:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
    targetID: 'b214'
    targetLabel: '7.A'
    targetDesc:
      'Develop a paragraph comprised of 1) a claim that requires defense with evidence from the
      text, and 2) the evidence itself.'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b215'
    targetLabel: '7.B'
    targetDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b215'
    targetLabel: '7.B'
    targetDesc:
      'Develop a thesis statement that conveys a defensible claim about an interpretation of
      literature and that may establish a line of reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b216'
    targetLabel: '7.C'
    targetDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b216'
    targetLabel: '7.C'
    targetDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b217'
    targetLabel: '7.D'
    targetDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b217'
    targetLabel: '7.D'
    targetDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b217'
    targetLabel: '7.D'
    targetDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b217'
    targetLabel: '7.D'
    targetDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b169'
    originLabel: 'LAN-1.L'
    originDesc:
      'Grammar and mechanics that follow established conventions of language allow writers to
      clearly communicate their interpretation of a text.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b170'
    originLabel: 'LAN-1.M'
    originDesc:
      'The body paragraphs of a written argument develop the reasoning and justify claims using
      evidence and providing commentary that links the evidence to the overall thesis.'
    targetID: 'b216'
    targetLabel: '7.C'
    targetDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    associationType: 'isRelatedTo'
  - originID: 'b171'
    originLabel: 'LAN-1.N'
    originDesc:
      'Effective paragraphs are cohesive and often use topic sentences to state a claim and explain
      the reasoning that connects the various claims and evidence that make up the body of an essay.'
    targetID: 'b216'
    targetLabel: '7.C'
    targetDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    associationType: 'isRelatedTo'
  - originID: 'b172'
    originLabel: 'LAN-1.O'
    originDesc:
      'Coherence occurs at different levels in a piece of writing. In a sentence, the idea in one
      clause logically links to an idea in the next. In a paragraph, the idea in one sentence
      logically links to an idea in the next. In a text, the ideas in one paragraph logically link
      to the ideas in the next.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b173'
    originLabel: 'LAN-1.P'
    originDesc:
      'Writers achieve coherence when the arrangement and organization of reasons, evidence, ideas,
      or details is logical. Writers may use transitions, repetition, synonyms, pronoun references,
      or parallel structure to indicate relationships between and among those reasons, evidence,
      ideas, or details.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b174'
    originLabel: 'LAN-1.Q'
    originDesc:
      'Transitional elements are words or other elements (phrases, clauses, sentences, or
      paragraphs) that assist in creating coherence between sentences and paragraphs by showing
      relationships between ideas.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b175'
    originLabel: 'LAN-1.R'
    originDesc:
      'Writers convey their ideas in a sentence through strategic selection and placement of phrases
      and clauses. Writers may use coordination to illustrate a balance or equality between ideas or
      subordination to illustrate an imbalance or inequality.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b176'
    originLabel: 'LAN-1.S'
    originDesc: 'Writers use words that enhance the clear communication of an interpretation.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b177'
    originLabel: 'LAN-1.T'
    originDesc: 'Punctuation conveys relationships between and among parts of a sentence.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b178'
    originLabel: 'LAN-1.U'
    originDesc:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
    targetID: 'b216'
    targetLabel: '7.C'
    targetDesc:
      'Develop commentary that establishes and explains relationships among textual evidence, the
      line of reasoning, and the claim.'
    associationType: 'isRelatedTo'
  - originID: 'b179'
    originLabel: 'LAN-1.V'
    originDesc:
      'Textual evidence may require revision to an interpretation and a line of reasoning if the
      evidence does not sufficiently support the initial interpretation and line of reasoning.'
    targetID: 'b217'
    targetLabel: '7.D'
    targetDesc:
      'Select and use relevant and sufficient evidence to both develop and support a line of
      reasoning.'
    associationType: 'isRelatedTo'
  - originID: 'b180'
    originLabel: 'LAN-1.W'
    originDesc:
      'Writers must acknowledge words, ideas, images, texts, and other intellectual property of
      others through attribution, citation, or reference.'
    targetID: 'b218'
    targetLabel: '7.E'
    targetDesc: 'Demonstrate control over the elements of composition to communicate clearly.'
    associationType: 'isRelatedTo'
  - originID: 'b158'
    originLabel: 'LAN-1.A'
    originDesc:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b159'
    originLabel: 'LAN-1.B'
    originDesc: 'A claim is a statement that requires defense with evidence from the text.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b160'
    originLabel: 'LAN-1.C'
    originDesc:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
    targetID: 'b219'
    targetLabel: '1'
    targetDesc: 'Short Fiction I'
    associationType: 'isRelatedTo'
  - originID: 'b158'
    originLabel: 'LAN-1.A'
    originDesc:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b159'
    originLabel: 'LAN-1.B'
    originDesc: 'A claim is a statement that requires defense with evidence from the text.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b160'
    originLabel: 'LAN-1.C'
    originDesc:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
    targetID: 'b220'
    targetLabel: '2'
    targetDesc: 'Poetry I'
    associationType: 'isRelatedTo'
  - originID: 'b158'
    originLabel: 'LAN-1.A'
    originDesc:
      'In literary analysis, writers read a text closely to identify details that, in combination,
      enable them to make and defend a claim about an aspect of the text.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b159'
    originLabel: 'LAN-1.B'
    originDesc: 'A claim is a statement that requires defense with evidence from the text.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b160'
    originLabel: 'LAN-1.C'
    originDesc:
      'In literary analysis, the initial components of a paragraph are the claim and textual
      evidence that defends the claim.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b169'
    originLabel: 'LAN-1.L'
    originDesc:
      'Grammar and mechanics that follow established conventions of language allow writers to
      clearly communicate their interpretation of a text.'
    targetID: 'b221'
    targetLabel: '3'
    targetDesc: 'Longer Fiction or Drama I'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b170'
    originLabel: 'LAN-1.M'
    originDesc:
      'The body paragraphs of a written argument develop the reasoning and justify claims using
      evidence and providing commentary that links the evidence to the overall thesis.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b171'
    originLabel: 'LAN-1.N'
    originDesc:
      'Effective paragraphs are cohesive and often use topic sentences to state a claim and explain
      the reasoning that connects the various claims and evidence that make up the body of an essay.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b172'
    originLabel: 'LAN-1.O'
    originDesc:
      'Coherence occurs at different levels in a piece of writing. In a sentence, the idea in one
      clause logically links to an idea in the next. In a paragraph, the idea in one sentence
      logically links to an idea in the next. In a text, the ideas in one paragraph logically link
      to the ideas in the next.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b173'
    originLabel: 'LAN-1.P'
    originDesc:
      'Writers achieve coherence when the arrangement and organization of reasons, evidence, ideas,
      or details is logical. Writers may use transitions, repetition, synonyms, pronoun references,
      or parallel structure to indicate relationships between and among those reasons, evidence,
      ideas, or details.'
    targetID: 'b222'
    targetLabel: '4'
    targetDesc: 'Short Fiction II'
    associationType: 'isRelatedTo'
  - originID: 'b174'
    originLabel: 'LAN-1.Q'
    originDesc:
      'Transitional elements are words or other elements (phrases, clauses, sentences, or
      paragraphs) that assist in creating coherence between sentences and paragraphs by showing
      relationships between ideas.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b175'
    originLabel: 'LAN-1.R'
    originDesc:
      'Writers convey their ideas in a sentence through strategic selection and placement of phrases
      and clauses. Writers may use coordination to illustrate a balance or equality between ideas or
      subordination to illustrate an imbalance or inequality.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b176'
    originLabel: 'LAN-1.S'
    originDesc: 'Writers use words that enhance the clear communication of an interpretation.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b177'
    originLabel: 'LAN-1.T'
    originDesc: 'Punctuation conveys relationships between and among parts of a sentence.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b178'
    originLabel: 'LAN-1.U'
    originDesc:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b179'
    originLabel: 'LAN-1.V'
    originDesc:
      'Textual evidence may require revision to an interpretation and a line of reasoning if the
      evidence does not sufficiently support the initial interpretation and line of reasoning.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b180'
    originLabel: 'LAN-1.W'
    originDesc:
      'Writers must acknowledge words, ideas, images, texts, and other intellectual property of
      others through attribution, citation, or reference.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b223'
    targetLabel: '5'
    targetDesc: 'Poetry II'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b224'
    targetLabel: '6'
    targetDesc: 'Longer Fiction or Drama II'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b225'
    targetLabel: '7'
    targetDesc: 'Short Fiction III'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b178'
    originLabel: 'LAN-1.U'
    originDesc:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b226'
    targetLabel: '8'
    targetDesc: 'Poetry III'
    associationType: 'isRelatedTo'
  - originID: 'b161'
    originLabel: 'LAN-1.D'
    originDesc:
      'A thesis statement expresses an interpretation of a literary text, and requires a defense,
      through use of textual evidence and a line of reasoning, both of which are explained in an
      essay through commentary.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b162'
    originLabel: 'LAN-1.E'
    originDesc:
      'A thesis statement may preview the development or line of reasoning of an interpretation.
      This is not to say that a thesis statement must list the points of an interpretation, literary
      elements to be analyzed, or specific evidence to be used in the argument.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b163'
    originLabel: 'LAN-1.F'
    originDesc:
      'A line of reasoning is the logical sequence of claims that work together to defend the
      overarching thesis statement.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b164'
    originLabel: 'LAN-1.G'
    originDesc:
      'A line of reasoning is communicated through commentary that explains the logical relationship
      between the overarching thesis statement and the claims/evidence within the body of an essay.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b178'
    originLabel: 'LAN-1.U'
    originDesc:
      'More sophisticated literary arguments may explain the significance or relevance of an
      interpretation within a broader context, discuss alternative interpretations of a text, or use
      relevant analogies to help an audience better understand an interpretation.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b165'
    originLabel: 'LAN-1.H'
    originDesc:
      'Writers use evidence strategically and purposefully to illustrate, clarify, exemplify,
      associate, amplify, or qualify a point.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b166'
    originLabel: 'LAN-1.I'
    originDesc:
      'Evidence is effective when the writer of the essay uses commentary to explain a logical
      relationship between the evidence and the claim.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b167'
    originLabel: 'LAN-1.J'
    originDesc:
      'Evidence is sufficient when its quantity and quality provide apt support for the line of
      reasoning.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: 'b168'
    originLabel: 'LAN-1.K'
    originDesc:
      'Developing and supporting an interpretation of a text is a recursive process; an
      interpretation can emerge from analyzing evidence and then forming a line of reasoning, or the
      interpretation can emerge from forming a line of reasoning and then identifying relevant
      evidence to support that line of reasoning.'
    targetID: 'b227'
    targetLabel: '9'
    targetDesc: 'Longer Fiction or Drama III'
    associationType: 'isRelatedTo'
  - originID: '79298333'
    originDesc: 'Emerging'
    targetID: '194571'
    targetDesc: 'Item Difficulty'
    associationType: 'isChildOf'
  - originID: '2921795'
    originDesc: 'Proficient'
    targetID: '194571'
    targetDesc: 'Item Difficulty'
    associationType: 'isChildOf'
  - originID: '3624495'
    originDesc: 'Advanced'
    targetID: '194571'
    targetDesc: 'Item Difficulty'
    associationType: 'isChildOf'

datatypes:
  hummingbird:
    'Course Content': 'c1'
    'Course Skills': 'c2'
    'Units': 'c3'
    'Additional Metadata': 'c4'
    'Question Type': 'a7'
    'Stimulus Type': 'a8'
    'Stimulus Text Origin': 'a9'
    'Stimulus Difficulty': 'a10'
    'Stimulus Time Period': 'a11'
    'Stimulus Theme': 'a12'
    'Stimulus Title': 'a13'
    'Stimulus Author': 'a14'
