import type {
  AssemblyUnitEvent,
  AssemblyUnitEventType,
  AssemblyUnitSnapshot,
  CollectionEvent,
  CollectionEventType,
  CollectionSnapshot,
  FormEvent,
  FormEventType,
  FormSnapshot,
} from '@shared/dynamo-hummingbird'

export type {
  AssemblyUnitArchived,
  AssemblyUnitAssetItemsReordered,
  AssemblyUnitAssetsAdded,
  AssemblyUnitAssetsRemoved,
  AssemblyUnitAssetsReordered,
  AssemblyUnitCreated,
  AssemblyUnitEvent,
  AssemblyUnitEventType,
  AssemblyUnitItemActivated,
  AssemblyUnitItemDeactivated,
  AssemblyUnitMigrated,
  AssemblyUnitRenamed,
  AssemblyUnitRestored,
  AssemblyUnitSnapshot,
  CollectionArchived,
  CollectionAssetsAdded,
  CollectionAssetsRemoved,
  CollectionAssetsReordered,
  CollectionCreated,
  CollectionEvent,
  CollectionEventType,
  CollectionMigrated,
  CollectionRenamed,
  CollectionRestored,
  CollectionSnapshot,
  FormArchived,
  FormAssemblyUnitsAdded,
  FormAssemblyUnitsRemoved,
  FormCreated,
  FormDirectionsAdded,
  FormDirectionsRemoved,
  FormEvent,
  FormEventType,
  FormRenamed,
  FormSectionRenamed,
  FormRestored,
  FormSnapshot,
  FormStatusUpdated,
  Form<PERSON>odesUpdated,
  FormSubF<PERSON><PERSON>odeUpdated,
} from '@shared/dynamo-hummingbird'

export const assemblyUnitEventTypes: Record<AssemblyUnitEventType, AssemblyUnitEventType> = {
  AssemblyUnitSnapshot: 'AssemblyUnitSnapshot',
  AssemblyUnitCreated: 'AssemblyUnitCreated',
  AssemblyUnitRenamed: 'AssemblyUnitRenamed',
  AssemblyUnitAssetsAdded: 'AssemblyUnitAssetsAdded',
  AssemblyUnitItemActivated: 'AssemblyUnitItemActivated',
  AssemblyUnitItemDeactivated: 'AssemblyUnitItemDeactivated',
  AssemblyUnitAssetsReordered: 'AssemblyUnitAssetsReordered',
  AssemblyUnitAssetItemsReordered: 'AssemblyUnitAssetItemsReordered',
  AssemblyUnitRestored: 'AssemblyUnitRestored',
  AssemblyUnitAssetsRemoved: 'AssemblyUnitAssetsRemoved',
  AssemblyUnitArchived: 'AssemblyUnitArchived',
  AssemblyUnitMigrated: 'AssemblyUnitMigrated',
}

export const collectionEventTypes: Record<CollectionEventType, CollectionEventType> = {
  CollectionSnapshot: 'CollectionSnapshot',
  CollectionCreated: 'CollectionCreated',
  CollectionRenamed: 'CollectionRenamed',
  CollectionAssetsAdded: 'CollectionAssetsAdded',
  CollectionAssetsReordered: 'CollectionAssetsReordered',
  CollectionRestored: 'CollectionRestored',
  CollectionAssetsRemoved: 'CollectionAssetsRemoved',
  CollectionArchived: 'CollectionArchived',
  CollectionMigrated: 'CollectionMigrated',
}

export const formEventTypes: Record<FormEventType, FormEventType> = {
  FormSnapshot: 'FormSnapshot',
  FormCreated: 'FormCreated',
  FormRenamed: 'FormRenamed',
  FormSectionRenamed: 'FormSectionRenamed',
  FormMigrated: 'FormMigrated',
  FormRestored: 'FormRestored',
  FormArchived: 'FormArchived',
  FormAssemblyUnitsAdded: 'FormAssemblyUnitsAdded',
  FormAssemblyUnitsRemoved: 'FormAssemblyUnitsRemoved',
  FormDirectionsAdded: 'FormDirectionsAdded',
  FormDirectionsRemoved: 'FormDirectionsRemoved',
  FormStatusUpdated: 'FormStatusUpdated',
  FormSubFormCodeUpdated: 'FormSubFormCodeUpdated',
  FormCodesUpdated: 'FormCodesUpdated',
}

export interface OrderedAsset {
  id: string
  ordinal: number
}

export type EntityEventType = AssemblyUnitEventType | CollectionEventType | FormEventType

export type EventType = EntityEventType

export type Event = AssemblyUnitEvent | CollectionEvent | FormEvent

export type SnapshotType<S extends string> = S extends EventType
  ? Extract<S, 'AssemblyUnitSnapshot' | 'CollectionSnapshot' | 'FormSnapshot'>
  : never

export type EntitySnapshot = AssemblyUnitSnapshot | CollectionSnapshot | FormSnapshot

export type Snapshot = EntitySnapshot
