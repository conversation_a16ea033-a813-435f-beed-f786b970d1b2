/**
 * Store all event name related types, constants, and functions
 */
// used for tests
export const EventNamePrefixList = ['Collection', 'AssemblyUnit', 'Form'] as const

export type EventNamePrefix = (typeof EventNamePrefixList)[number]

export const EventNamePrefixConstants = EventNamePrefixList.reduce(
  (acc, prefix) => ({
    ...acc,
    [prefix]: prefix,
  }),
  {} as Record<EventNamePrefix, EventNamePrefix>
)

export const NextGenEventNamePrefix = {
  [EventNamePrefixConstants.AssemblyUnit]: 'au',
} as const

const splitPascalCase = (word: string) => {
  const regex = /($[a-z])|[A-Z][^A-Z]+/g
  return word.match(regex)?.join(' ') || word
}

export const EventNameLabel = EventNamePrefixList.reduce(
  (acc, prefix) => ({
    ...acc,
    [prefix]: splitPascalCase(prefix).toLowerCase(),
  }),
  {} as Record<EventNamePrefix, string>
)

export const getEventNameLabel = (eventNamePrefix: EventNamePrefix) =>
  EventNameLabel[eventNamePrefix] || EventNameLabel[EventNamePrefixConstants.Collection]
