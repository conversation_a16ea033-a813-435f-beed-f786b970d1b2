// import collectionEventHandlers from './events-collection'
import { composeSnapshot } from './snapshot-base'

export const id = 'XER-158713'

jest.mock('.', () => {
  const originalModule = jest.requireActual('.')
  return {
    ...originalModule,
    AppendEvent: {
      ...originalModule.AppendEvent,
      Created: jest.fn(),
      Updated: jest.fn(),
    },
  }
})

const randomTimestamps = new Array(5).map(() => Math.random() * 1256783217).sort((a, b) => a - b)

describe('generic snapshot tests', () => {
  const blankSnapshot = {
    id,
    timestamp: randomTimestamps[0],
    modelVersion: 0,
    sequenceNum: 0,
    eventSequenceNum: 0,
  }

  const testEvents = [
    {
      type: 'Created',
      id,
      timestamp: randomTimestamps[1],
      modelVersion: 0,
      sequenceNum: 1,
      // appendEvent: mockAppendEvent,
    },
    {
      type: 'Updated',
      id,
      timestamp: randomTimestamps[2],
      modelVersion: 0,
      sequenceNum: 2,
      // appendEvent: mockAppendEvent,
    },
  ]
  // beforeEach(() => {
  //   collectionEventHandlers['Created'].mockImplementation((event, snapshot) => snapshot)
  //   collectionEventHandlers['Updated'].mockImplementation((event, snapshot) => snapshot)
  // })
  afterEach(() => {
    jest.resetAllMocks()
  })
  // it('should successfully attempt to construct a basic snapshot from a creation event', () => {
  //   const newSnapshot = composeSnapshot(testEvents)
  //   expect(AppendEvent['Created']).toBeCalledTimes(1)
  //   expect(AppendEvent['Updated']).toBeCalledTimes(1)
  //   expect(newSnapshot).toStrictEqual({})
  // })
  // it('should successfully construct a basic snapshot from a starting snapshot', () => {
  //   const newSnapshot = composeSnapshot(testEvents, blankSnapshot)
  //   expect(AppendEvent['Created']).toBeCalledTimes(1)
  //   expect(AppendEvent['Updated']).toBeCalledTimes(1)
  //   expect(newSnapshot).toStrictEqual(blankSnapshot)
  // })
  it('should return the original snapshot if the event array is empty', () => {
    expect(composeSnapshot([], blankSnapshot)).toBe(blankSnapshot)
  })
  it('should throw an error if duplicate event sequence numbers are detected', () => {
    expect(() => composeSnapshot([...testEvents, ...testEvents])).toThrowError(
      'Bad event array detected, aborting'
    )
  })
  it('should throw an error if there is no point of creation (event or snapshot)', () => {
    expect(() => composeSnapshot([testEvents[1]])).toThrowError(
      'No creation event or snapshot found, aborting'
    )
  })
  it('should throw an error if there are mismatched IDs', () => {
    expect(() => composeSnapshot([testEvents[1]], { ...blankSnapshot, id: 'random' })).toThrowError(
      'Bad event array detected, aborting'
    )
  })
})
