import castArray from 'lodash/castArray'
import type { Event, Snapshot } from '..'
import { assemblyUnitEventHandlers } from './assembly-unit'
import { collectionEventHandlers } from './collection'
import { formEventHandlers } from './form'
import type { EventType } from './types'

const eventHandlers = {
  ...assemblyUnitEventHandlers,
  ...collectionEventHandlers,
  ...formEventHandlers,
}

export function composeSnapshot<E extends Event>(
  eventOrEvents: E | Array<E>,
  lastSnapshot?: Snapshot
) {
  const events = castArray(eventOrEvents)
  const id = lastSnapshot?.id ?? events[0]?.id
  const eventsSorted =
    events
      .filter((e, i, a) => a.indexOf(e) === i && e.id === id)
      .sort((a, b) => (a.sequenceNum as number) - (b.sequenceNum as number)) ?? []
  if (eventsSorted.length !== events.length) {
    throw new Error('Bad event array detected, aborting')
  }
  if (
    !lastSnapshot &&
    eventsSorted.findIndex((event) => (event.type as string).indexOf('Created') !== -1) !== 0
  ) {
    throw new Error('No creation event or snapshot found, aborting')
  }
  return events.reduce<Snapshot>((currentSnapshot, currentEvent) => {
    const eventHandler = eventHandlers[currentEvent.type as EventType]
    return eventHandler ? eventHandler(currentEvent, currentSnapshot) : currentSnapshot
  }, lastSnapshot || ({} as Snapshot))
}
