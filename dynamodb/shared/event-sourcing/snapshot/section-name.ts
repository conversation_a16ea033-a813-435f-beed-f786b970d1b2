import { capitalCase, pascalCase } from 'case-anything'
/**
 * Store all section name related types, constants, and functions
 */
import { DirectionsTypeId } from '@shared/types/model'

/**
 * FORMS SECTION NAMES
 */
export const FormSectionNameList = [
  'DETAILS',
  'EXAM_DIRECTIONS',
  'SECTION_1',
  'SECTION_2',
  'SECTION_3',
  'SECTION_4',
  'SECTION_5',
  'SECTION_6',
  'METRICS',
] as const

export type FormSectionNameType = (typeof FormSectionNameList)[number]

export const FormSectionName = FormSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: label,
  }
}, {} as Record<FormSectionNameType, FormSectionNameType>)

export const FormSectionNameLabel = FormSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: capitalCase(pascalCase(label)),
  }
}, {} as Record<FormSectionNameType, string>)

export const getFormSectionIdx = (label: FormSectionNameType) => {
  if (label === FormSectionName.SECTION_1) {
    return 0
  } else if (label === FormSectionName.SECTION_2) {
    return 1
  } else if (label === FormSectionName.SECTION_3) {
    return 2
  } else if (label === FormSectionName.SECTION_4) {
    return 3
  } else if (label === FormSectionName.SECTION_5) {
    return 4
  } else if (label === FormSectionName.SECTION_6) {
    return 5
  }
  return -1
}

export const FormSectionNameToDirectionTypeId = {
  [FormSectionName.EXAM_DIRECTIONS]: DirectionsTypeId.DIRECTIONS_EXAM,
  [FormSectionName.SECTION_1]: DirectionsTypeId.DIRECTIONS_SECTION,
  [FormSectionName.SECTION_2]: DirectionsTypeId.DIRECTIONS_SECTION,
  [FormSectionName.SECTION_3]: DirectionsTypeId.DIRECTIONS_SECTION,
  [FormSectionName.SECTION_4]: DirectionsTypeId.DIRECTIONS_SECTION,
  [FormSectionName.SECTION_5]: DirectionsTypeId.DIRECTIONS_SECTION,
  [FormSectionName.SECTION_6]: DirectionsTypeId.DIRECTIONS_SECTION,
} as const

/**
 * COLLECTIONS SECTION NAMES
 */
export const CollectionSectionNameList = ['DETAILS', 'ASSETS', 'METRICS'] as const

export type CollectionSectionNameType = (typeof CollectionSectionNameList)[number]

export const CollectionSectionName = CollectionSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: label,
  }
}, {} as Record<CollectionSectionNameType, CollectionSectionNameType>)

export const CollectionSectionNameLabel = CollectionSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: capitalCase(pascalCase(label)),
  }
}, {} as Record<CollectionSectionNameType, string>)

/**
 * ASSEMBLY UNITS SECTION NAMES
 */
export const AssemblyUnitSectionNameList = ['DETAILS', 'ASSETS', 'METRICS'] as const

export type AssemblyUnitSectionNameType = (typeof AssemblyUnitSectionNameList)[number]

export const AssemblyUnitSectionName = AssemblyUnitSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: label,
  }
}, {} as Record<AssemblyUnitSectionNameType, AssemblyUnitSectionNameType>)

export const AssemblyUnitSectionNameLabel = AssemblyUnitSectionNameList.reduce((acc, label) => {
  return {
    ...acc,
    [label]: capitalCase(pascalCase(label)),
  }
}, {} as Record<AssemblyUnitSectionNameType, string>)

/**
 * COMBINED MAPPINGS AND TYPES FOR SECTION NAMES
 */
export type EntitySectionNameType =
  | CollectionSectionNameType
  | FormSectionNameType
  | AssemblyUnitSectionNameType

export const EntitySectionNameLabels: Record<EntitySectionNameType, string> = {
  ...FormSectionNameLabel,
  ...AssemblyUnitSectionNameLabel,
  ...CollectionSectionNameLabel,
}
