import { validCustomItemOrderA, validCustomItemOrderB } from '../snapshot/assembly-unit/fixtures'
import type { AssemblyUnitAssetItemsReordered } from '../snapshot/types'
import {
  addToCustomItemOrder,
  removeFromCustomItemOrder,
  reorderCustomItemOrder,
} from './update-custom-item-order'

const customItemOrderFunctions = [
  addToCustomItemOrder,
  removeFromCustomItemOrder,
  reorderCustomItemOrder,
]
const validItemOrderStringA = JSON.stringify(validCustomItemOrderA)
const validItemOrderStringB = JSON.stringify(validCustomItemOrderB)
const validItemOrderCombinedString = JSON.stringify({
  ...validCustomItemOrderB,
  ...validCustomItemOrderA,
})
const reorderedEvent = {
  assetIDs: ['DEF000000'],
  customItemOrder: '{"DEF000000":["DEF000002","DEF000001"]}',
} as AssemblyUnitAssetItemsReordered

describe('update custom item order tests', () => {
  it.each(customItemOrderFunctions)('they should all return undefined', (action) => {
    expect(action(undefined, undefined)).toBe(undefined)
  })

  it('will add to the custom item order', () => {
    expect(addToCustomItemOrder(validItemOrderStringA, undefined)).toEqual(validItemOrderStringA)
    expect(addToCustomItemOrder(undefined, validItemOrderStringB)).toEqual(validItemOrderStringB)
    expect(addToCustomItemOrder(validItemOrderStringA, validItemOrderStringB)).toEqual(
      validItemOrderCombinedString
    )
  })
  it('will remove entries from the custom item order', () => {
    expect(removeFromCustomItemOrder(['ABC000000'], validItemOrderStringA)).toBe(undefined)
    expect(removeFromCustomItemOrder(['ABC000000'], undefined)).toBe(undefined)
    expect(removeFromCustomItemOrder(['ABC000000'], validItemOrderCombinedString)).toEqual(
      validItemOrderStringB
    )
  })
  it('will reorder the custom item order', () => {
    const reorderdItemOrderBParsed = JSON.parse(reorderedEvent.customItemOrder || '{}')
    expect(reorderCustomItemOrder(reorderedEvent, undefined)).toBe(reorderedEvent.customItemOrder)
    expect(reorderCustomItemOrder(undefined, validItemOrderStringA)).toEqual(validItemOrderStringA)
    expect(reorderCustomItemOrder(reorderedEvent, validItemOrderCombinedString)).toEqual(
      JSON.stringify({ ...reorderdItemOrderBParsed, ...validCustomItemOrderA })
    )
  })
})
