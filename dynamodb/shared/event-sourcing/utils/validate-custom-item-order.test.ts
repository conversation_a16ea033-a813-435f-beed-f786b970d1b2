import {
  fixturesAssemblyUnits,
  validCustomItemOrderA,
  validCustomItemOrderB,
} from '../snapshot/assembly-unit/fixtures'
import validateCustomItemOrder from './validate-custom-item-order'

describe('validate customItemOrder tests', () => {
  it('should throw an error', () => {
    const { customItemOrder } = fixturesAssemblyUnits
    const expectedError = new Error(
      `Invalid structure for customItemOrder was provided ${JSON.stringify(customItemOrder)}`
    )
    expect(() => validateCustomItemOrder(customItemOrder)).toThrow(expectedError)
  })
  it('should return the stringified version of the passed item order', () => {
    const validItemOrder = { ...validCustomItemOrderA, ...validCustomItemOrderB }
    expect(validateCustomItemOrder(validItemOrder)).toEqual(JSON.stringify(validItemOrder))
  })
})
