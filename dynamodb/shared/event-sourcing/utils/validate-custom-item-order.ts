import { validateId } from '@shared/schema'

export default function validateCustomItemOrder(
  customItemOrder: Record<string, Iterable<string> | ArrayLike<string>>
): string {
  Object.entries(customItemOrder).forEach(([assetID, itemIDSetOrArray]) => {
    const validAssetID = validateId(assetID)
    const itemIDArray = Array.from(itemIDSetOrArray)
    const validItemIDs =
      itemIDArray?.length > 0 && itemIDArray.every((itemID) => validateId(itemID) === true)
    if (!(validAssetID && validItemIDs))
      throw new Error(
        `Invalid structure for customItemOrder was provided ${JSON.stringify(customItemOrder)}`
      )
  })
  return JSON.stringify(customItemOrder)
}
