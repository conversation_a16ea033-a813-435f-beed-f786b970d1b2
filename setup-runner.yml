 
name: Setup Custom Runner
description: Setting up the custom runner
inputs:
  squash-to-base:
    required: false
    type: boolean
    default: false
  prod:
    required: false
    type: boolean
    default: false
  binaries:
    required: false
    type: boolean
    default: false
  install:
    required: false
    type: boolean
    default: false
  ignore_scripts:
    type: boolean
    required: false
    default: false
  build:
    required: false
    type: boolean
    default: false
  prepdestroy:
    required: false
    type: boolean
    default: false
  assume-role-duration:
    required: false
    type: number
    default: 3600
    description:
      The duration in seconds for which the assumed role session is valid. The default is 3600
      seconds (1 hour). The maximum value is 43200 seconds (12 hours).
 
runs:
  using: composite
  steps:
    - name: Setup Binaries
      if: ${{ inputs.binaries == 'true' }}
      uses: ./.github/actions/setup-binaries
 
    - name: Setup environment variables
      shell: bash
      run: |
        echo "PROFILE=${{ inputs.prod == 'true' && 'cb-itemcloudgreen-prod-cli' || 'cb-itemcloudgreen-nonprod-cli' }}" >> $GITHUB_ENV
 
    - name: Setup git configs
      shell: bash
      working-directory: ${{ github.workspace }}
      run: |
        git config --global user.name svc-icgdev
        git config --global user.email <EMAIL>
        git config --global http.postBuffer 157286400
 
    - name: Configure AWS Credentials
      env:
        AWS_ROLE:
          "${{ inputs.prod == 'true' &&
          'arn:aws:iam::029432040222:role/GitHubAction-AssumeRoleWithAction-prod' ||
          'arn:aws:iam::452019990826:role/GitHubAction-AssumeRoleWithAction' }}"
      uses: aws-actions/configure-aws-credentials@v3
      with:
        role-to-assume: ${{ env.AWS_ROLE }}
        aws-region: us-east-1
        role-duration-seconds: ${{ inputs.assume-role-duration }}
    - name: Setup AWS Profile
      shell: bash
      run: |
        aws configure set profile.$PROFILE.aws_access_key_id $AWS_ACCESS_KEY_ID
        aws configure set profile.$PROFILE.aws_secret_access_key $AWS_SECRET_ACCESS_KEY
        aws configure set profile.$PROFILE.aws_session_token $AWS_SESSION_TOKEN
 
        CB_JFROG_USERNAME=BLANK
        CB_JFROG_EMAIL=BLANK
        CB_JFROG_AUTH=BLANK
 
        echo "AWS_PROFILE=$PROFILE" >> $GITHUB_ENV
 
    - name: Squash and Merge
      if: ${{ inputs.squash-to-base == 'true' }}
      shell: bash
      env:
        HEAD_REF: ${{ github.head_ref }}
        BASE_REF: ${{ github.base_ref }}
      run: |
        git fetch --unshallow
        git checkout $BASE_REF
        git pull
        git merge --squash "origin/$HEAD_REF" --no-commit
 
    - name: Install Node Modules
      if: ${{ inputs.install == 'true' }}
      uses: ./.github/actions/install-node-modules
      with:
        ignore_scripts: ${{ inputs.ignore_scripts }}
 
    - name: Build
      if: ${{ inputs.build == 'true' || inputs.prepdestroy == 'true' }}
      uses: ./.github/actions/build-lambdas
      with:
        prepdestroy: ${{ inputs.prepdestroy }}