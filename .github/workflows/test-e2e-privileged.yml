name: 🚀 E2E Tests (Privileged)
run-name: E2E Privileged - Processing Results
on:
  workflow_run:
    workflows: ["🧪 E2E Tests (Unprivileged)"]
    types: [completed]

concurrency:
  group: e2e-privileged-${{ github.event.workflow_run.head_branch }}
  cancel-in-progress: false

jobs:
  process-e2e-results:
    if: github.event.workflow_run.conclusion == 'success'
    runs-on: icg-arc-runner-xlarge-set
    
    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          ref: main
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          
      - name: Download artifacts from unprivileged workflow
        uses: actions/download-artifact@v4
        with:
          run-id: ${{ github.event.workflow_run.id }}
          path: downloaded-artifacts
          
      - name: Validate artifacts
        id: validate
        run: |
          echo "Validating artifacts from unprivileged workflow..."

          # Make validation script executable
          chmod +x .github/scripts/validate-artifacts.sh

          # Run comprehensive validation
          if .github/scripts/validate-artifacts.sh downloaded-artifacts validation-results.txt; then
            echo "Artifact validation passed"

            # Extract validated values from results
            source validation-results.txt

            echo "pr-number=$PR_NUMBER" >> $GITHUB_OUTPUT
            echo "head-sha=$HEAD_SHA" >> $GITHUB_OUTPUT
            echo "opensearch=$OPENSEARCH" >> $GITHUB_OUTPUT
            echo "valid=true" >> $GITHUB_OUTPUT

            # Display validation summary
            echo "=== Validation Summary ==="
            cat validation-results.txt
            echo "=========================="
          else
            echo "Artifact validation failed"
            echo "valid=false" >> $GITHUB_OUTPUT

            # Display validation errors
            echo "=== Validation Errors ==="
            cat validation-results.txt || echo "No validation results file found"
            echo "========================="
            exit 1
          fi
          
      - name: Setup privileged environment
        if: steps.validate.outputs.valid == 'true'
        uses: ./.github/actions/setup-runner
        with:
          binaries: true
          install: true
          
      - name: Set environment variables
        if: steps.validate.outputs.valid == 'true'
        run: |
          PR_NUMBER="${{ steps.validate.outputs.pr-number }}"
          ENV="pr$PR_NUMBER"
          echo "ENV=$ENV" >> $GITHUB_ENV
          echo "DEFAULT_BUCKET_NAME=$ENV-hummingbird-ui" >> $GITHUB_ENV
          echo "S3_SUBFOLDER_NAME=deploy" >> $GITHUB_ENV
          
      - name: Verify environment accessibility
        if: steps.validate.outputs.valid == 'true'
        run: |
          # Verify the target environment is accessible
          echo "Checking environment: ${{ env.ENV }}"
          curl -k --retry 3 --retry-delay 10 --retry-connrefused --retry-all-errors \
            https://external-${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org || {
            echo "Environment ${{ env.ENV }} is not accessible"
            exit 1
          }
          
      - name: Merge test reports
        if: steps.validate.outputs.valid == 'true'
        working-directory: e2e
        run: |
          # Merge all blob reports from different shards
          mkdir -p merged-reports
          
          # Copy all blob reports to a single directory
          for artifact_dir in ../downloaded-artifacts/e2e-results-shard-*; do
            if [ -d "$artifact_dir" ]; then
              cp -r "$artifact_dir"/* merged-reports/ 2>/dev/null || true
            fi
          done
          
          # Generate HTML report (if playwright reports exist)
          if [ -d "merged-reports" ] && [ "$(ls -A merged-reports)" ]; then
            npx playwright merge-reports --reporter html merged-reports || {
              echo "Failed to merge reports, creating summary instead"
              mkdir -p playwright-report
              echo "<h1>E2E Test Results Summary</h1>" > playwright-report/index.html
              echo "<p>Tests completed for PR #${{ steps.validate.outputs.pr-number }}</p>" >> playwright-report/index.html
            }
          else
            echo "No test reports found to merge"
            mkdir -p playwright-report
            echo "<h1>No Test Results</h1>" > playwright-report/index.html
          fi
          
      - name: Upload merged HTML report
        if: steps.validate.outputs.valid == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: html-report-pr-${{ steps.validate.outputs.pr-number }}
          path: e2e/playwright-report
          retention-days: 90
          
      - name: Upload to S3
        if: steps.validate.outputs.valid == 'true' && always()
        working-directory: e2e
        run: |
          REPORT_PATH="pr${{ steps.validate.outputs.pr-number }}/${{ github.run_id }}-${{ github.run_attempt }}"
          
          # Upload to S3 with proper error handling
          if [ -d "playwright-report" ]; then
            aws s3 sync ./playwright-report s3://${{ env.DEFAULT_BUCKET_NAME }}/playwright/$REPORT_PATH || {
              echo "Failed to upload to S3, but continuing..."
            }
            aws s3 sync ./playwright-report s3://${{ env.DEFAULT_BUCKET_NAME }}/playwright/latest || {
              echo "Failed to upload latest to S3, but continuing..."
            }
            
            echo "Report uploaded to: https://${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org/playwright/$REPORT_PATH/index.html"
          else
            echo "No report directory found to upload"
          fi
          
      - name: Comment on PR
        if: steps.validate.outputs.valid == 'true' && always()
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = ${{ steps.validate.outputs.pr-number }};
            const reportUrl = `https://${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org/playwright/pr${prNumber}/${{ github.run_id }}-${{ github.run_attempt }}/index.html`;
            
            const comment = `## 🧪 E2E Test Results
            
            E2E tests have completed for this PR.
            
            **📊 View Report:** [Test Results](${reportUrl})
            **🔧 Environment:** \`${{ env.ENV }}\`
            **🔄 OpenSearch:** \`${{ steps.validate.outputs.opensearch }}\`
            **📝 Commit:** \`${{ steps.validate.outputs.head-sha }}\`
            
            > This comment was generated by the secure E2E testing workflow.`;
            
            github.rest.issues.createComment({
              issue_number: prNumber,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
