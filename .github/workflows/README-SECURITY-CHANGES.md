# E2E Testing Security Improvements

## Overview

This document describes the security improvements made to address the CodeQL security vulnerability "Checkout of untrusted code in a privileged context" (rule ID: `actions/untrusted-checkout/critical`).

## Security Vulnerability

The original workflow had a critical security vulnerability where:
- It used `pull_request_target` trigger (or similar privileged context)
- It checked out untrusted code from pull requests
- It had access to repository secrets and write permissions
- Malicious code from external contributors could execute with elevated privileges

## Solution: Two-Workflow Pattern

We implemented the recommended two-workflow pattern to separate untrusted code execution from privileged operations:

### 1. Unprivileged Workflow (`test-e2e-unprivileged.yml`)

**Purpose**: Run E2E tests in an unprivileged context
- **Trigger**: `pull_request` (unprivileged)
- **Code Source**: PR head (untrusted code)
- **Permissions**: No access to secrets or write permissions
- **Actions**: 
  - Run E2E tests
  - Store results as artifacts
  - No privileged operations

### 2. Privileged Workflow (`test-e2e-privileged.yml`)

**Purpose**: Process test results in a privileged context
- **Trigger**: `workflow_run` (triggered by unprivileged workflow completion)
- **Code Source**: Main branch only (trusted code)
- **Permissions**: Full access to secrets and write permissions
- **Actions**:
  - Download and validate artifacts from unprivileged workflow
  - Merge test reports
  - Upload to S3
  - Comment on PR

### 3. Updated Main Workflow (`test-e2e.yml`)

**Purpose**: Handle manual/scheduled E2E runs
- **Triggers**: `workflow_dispatch`, `schedule`, `workflow_call`
- **Code Source**: Main branch only (trusted code)
- **Security**: Removed pull request handling to eliminate vulnerability

## Security Features

### Artifact Validation

The privileged workflow includes comprehensive artifact validation:

1. **Metadata Validation**:
   - PR number format and range validation
   - Git SHA format validation
   - Boolean flag validation

2. **Content Security**:
   - Suspicious file type detection (executables, scripts)
   - File size limits (prevents DoS attacks)
   - Total artifact size limits

3. **Input Sanitization**:
   - Environment name format validation
   - Parameter range checking
   - Safe string handling

### Validation Script

The `.github/scripts/validate-artifacts.sh` script provides:
- Comprehensive artifact validation
- Detailed logging of validation results
- Fail-safe behavior (fails closed on validation errors)
- Protection against common attack vectors

## Workflow Triggers

| Workflow | Trigger | Context | Code Source | Secrets Access |
|----------|---------|---------|-------------|----------------|
| `test-e2e-unprivileged.yml` | `pull_request` | Unprivileged | PR head | ❌ No |
| `test-e2e-privileged.yml` | `workflow_run` | Privileged | Main branch | ✅ Yes |
| `test-e2e.yml` | `workflow_dispatch`, `schedule` | Privileged | Main branch | ✅ Yes |

## Usage

### For Pull Requests

1. Add the `Run E2E` or `Run E2E Opensearch` label to a PR
2. The unprivileged workflow runs E2E tests
3. The privileged workflow processes results and comments on the PR

### For Manual/Scheduled Runs

1. Use the `workflow_dispatch` trigger with required parameters
2. The main workflow runs with full privileges

## Migration Notes

### Removed Features

- Direct pull request E2E testing in privileged context
- `pull_request_target` trigger usage
- Untrusted code checkout in privileged workflows

### Added Features

- Secure two-workflow pattern
- Comprehensive artifact validation
- Enhanced security logging
- Fail-safe validation behavior

## Files Changed

1. **New Files**:
   - `.github/workflows/test-e2e-unprivileged.yml` - Unprivileged E2E workflow
   - `.github/workflows/test-e2e-privileged.yml` - Privileged result processing
   - `.github/scripts/validate-artifacts.sh` - Artifact validation script

2. **Modified Files**:
   - `.github/workflows/test-e2e.yml` - Secured main workflow
   - `test-e2e.yml` - Moved to backup location

3. **Backup Files**:
   - `.github/workflows/test-e2e-original-backup.yml` - Original workflow backup

## Security Compliance

This implementation addresses:
- ✅ CodeQL rule `actions/untrusted-checkout/critical`
- ✅ Separation of untrusted code execution from privileged operations
- ✅ Proper artifact validation and sanitization
- ✅ Fail-safe security behavior
- ✅ Principle of least privilege

## Testing

To test the new workflow pattern:

1. Create a test PR with the `Run E2E` label
2. Verify the unprivileged workflow runs without secrets
3. Verify the privileged workflow processes artifacts safely
4. Check that validation catches malicious artifacts
5. Confirm PR comments are posted correctly

## Monitoring

Monitor the workflows for:
- Validation failures (may indicate attack attempts)
- Artifact size anomalies
- Unexpected file types in artifacts
- Failed privileged workflow runs
