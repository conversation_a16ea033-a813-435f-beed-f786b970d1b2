name: 🧪 E2E Tests (Unprivileged)
run-name: E2E Unprivileged - PR #${{ github.event.pull_request.number }}
on:
  pull_request:
    types: [opened, synchronize, reopened, labeled]

concurrency:
  group: e2e-unprivileged-${{ github.event.pull_request.number }}
  cancel-in-progress: true

env:
  IS_EXTERNAL: true
  IS_CICD_E2E: true
  ENV: pr${{ github.event.pull_request.number }}

jobs:
  should-run-e2e:
    runs-on: ubuntu-latest
    outputs:
      should-run: ${{ steps.check.outputs.should-run }}
      opensearch: ${{ steps.check.outputs.opensearch }}
    steps:
      - name: Check if E2E should run
        id: check
        run: |
          echo "should-run=true" >> $GITHUB_OUTPUT

          if [[ "${{ contains(github.event.pull_request.labels.*.name, 'Run E2E Opensearch') }}" == "true" ]]; then
            echo "opensearch=true" >> $GITHUB_OUTPUT
          else
            echo "opensearch=false" >> $GITHUB_OUTPUT
          fi

  run-e2e-tests:
    needs: should-run-e2e
    if: needs.should-run-e2e.outputs.should-run == 'true'
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1,2,3,4,5,6,7,8]
        shardTotal: [8]
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout PR code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          
      - name: Setup Playwright
        working-directory: e2e
        run: |
          npx playwright install chrome
          echo "AWS_PROFILE=''" >> .env
          
      - name: Create test environment info
        run: |
          mkdir -p test-metadata
          echo "${{ github.event.pull_request.number }}" > test-metadata/pr-number.txt
          echo "${{ github.event.pull_request.head.sha }}" > test-metadata/head-sha.txt
          echo "${{ needs.should-run-e2e.outputs.opensearch }}" > test-metadata/opensearch.txt
          echo "${{ matrix.shardIndex }}" > test-metadata/shard-index.txt
          echo "${{ matrix.shardTotal }}" > test-metadata/shard-total.txt
          
      - name: Run E2E Tests
        env:
          AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE: 1
        run: |
          # Note: This runs in unprivileged context without access to production secrets
          # Tests run against the PR environment which should be accessible without secrets

          if [[ "${{ needs.should-run-e2e.outputs.opensearch }}" == "true" ]]; then
            pnpm --filter e2e ui --project=e2e-opensearch-on --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          else
            pnpm --filter e2e ui --project=ui --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          fi
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-results-shard-${{ matrix.shardIndex }}
          path: |
            e2e/blob-report/
            test-metadata/
          retention-days: 5
          
      - name: Upload test metadata
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-metadata-shard-${{ matrix.shardIndex }}
          path: test-metadata/
          retention-days: 5
