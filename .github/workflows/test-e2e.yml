name: 🚀 Run E2E Tests with Sharding 🚀
run-name: E2E - ${{ github.event.inputs.env || 'dev' }}

# SECURE VERSION: This workflow has been updated to address the security vulnerability
# "Checkout of untrusted code in a privileged context" by implementing the two-workflow pattern

on:
  schedule:
    - cron: '30 7 * * 1-5'
  workflow_dispatch:
    inputs:
      env:
        required: true
        type: string
        description: 'Target environment (e.g. dev, pr6888, etc)'
      opensearch:
        required: false
        type: boolean
        default: false
        description: 'Set OpenSearch feature flag ON for this run'
      testFolder:
        required: false
        type: string
        description: 'Comma-separated list of test folders to run (e.g. collections,assembly-units,asset-editor) or leave blank for all'
      ref:
        required: false
        type: string
        description: 'Git ref to test (optional, defaults to main)'
  workflow_call:
    inputs:
      ref:
        required: false
        type: string
        description: 'Git ref to test (optional)'
      env:
        required: true
        type: string
        description: 'Target environment (e.g. dev, pr6888, etc)'
      hb_ui_bucket:
        required: true
        type: string
        description: 'S3 bucket for UI artifacts'

concurrency:
  cancel-in-progress: ${{ github.event_name == 'workflow_call' }}
  group: e2e-${{ inputs.env || github.event.inputs.env }}-${{ github.event_name == 'workflow_call' && github.event_name || github.run_id }}

env:
  IS_EXTERNAL: true
  ENV: ${{ github.event.inputs.env || inputs.env || 'dev' }}
  IS_CICD_E2E: true
  DEFAULT_BUCKET_NAME: ${{ github.event.inputs.env || inputs.env || 'dev' }}-hummingbird-ui
  S3_SUBFOLDER_NAME: ${{ github.event.inputs.opensearch == 'true' && 'opensearch' || (github.event_name == 'workflow_dispatch' && 'manual' || (github.event_name == 'schedule' && 'cron' || 'deploy')) }}

jobs:
  # SECURITY NOTE: This job has been modified to remove the security vulnerability
  # The original job used pull_request_target with untrusted code checkout
  # Now it only dispatches workflows for manual/scheduled runs
  trigger-manual-e2e:
    # Only run for manual dispatch or scheduled runs (not for pull requests)
    if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' || github.event_name == 'workflow_call'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          # SECURITY: Always checkout from main branch for privileged operations
          ref: main

      - name: Validate input parameters
        run: |
          echo "Validating input parameters..."
          ENV="${{ github.event.inputs.env || inputs.env || 'dev' }}"
          
          # Validate environment name format
          if [[ ! "$ENV" =~ ^[a-zA-Z0-9-]+$ ]]; then
            echo "Invalid environment name format: $ENV"
            exit 1
          fi
          
          echo "Environment: $ENV"
          echo "OpenSearch: ${{ github.event.inputs.opensearch || 'false' }}"
          echo "Test Folder: ${{ github.event.inputs.testFolder || 'all' }}"

  run-e2e:
    # Only run for manual dispatch or scheduled runs (not for pull requests)
    if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' || github.event_name == 'workflow_call'
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1,2,3,4,5,6,7,8]
        shardTotal: [8]
    runs-on: icg-arc-runner-xlarge-set
    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          # SECURITY: Always checkout from main branch for privileged operations
          ref: main
          
      - name: 🛠️ Setup
        uses: ./.github/actions/setup-runner
        with:
          binaries: true
          install: true
          
      - name: 🌐 Curl Site
        run: |
          curl -k --retry 10 --retry-delay 30 --retry-connrefused --retry-all-errors https://external-${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org
          
      - name: 🎭 Playwright Setup
        working-directory: e2e
        run: |
          npx playwright install chrome
          echo "AWS_PROFILE=''" >> .env
          
      - name: 🧪 Run E2E
        env:
          AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE: 1
        run: |
          FOLDER_ARG=""
          if [[ -n "${{ github.event.inputs.testFolder }}" ]]; then
            FOLDERS=$(echo "${{ github.event.inputs.testFolder }}" | tr ',' ' ')
            for folder in $FOLDERS; do
              FOLDER_ARG+=" ui/$folder/**/*.test.ts"
            done
          fi
          if [[ "${{ github.event.inputs.opensearch }}" == "true" ]]; then
            pnpm --filter e2e ui --project=e2e-opensearch-on $FOLDER_ARG --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          else
            pnpm --filter e2e ui --project=ui --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          fi
          
      - name: 📦 Upload blob report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: all-blob-reports-${{ matrix.shardIndex }}
          path: e2e/blob-report/
          retention-days: 5
          overwrite: true

  merge-reports:
    if: ${{ !cancelled() && (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' || github.event_name == 'workflow_call') }}
    needs: [run-e2e]
    runs-on: icg-arc-runner-xlarge-set
    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          # SECURITY: Always checkout from main branch for privileged operations
          ref: main
          
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          
      - name: 🛠️ Setup
        uses: ./.github/actions/setup-runner
        with:
          binaries: true
          install: true
          
      - name: 📥 Download blob reports
        uses: actions/download-artifact@v4
        with:
          path: ${{ github.workspace }}/all-blob-reports
          merge-multiple: true
          
      - name: 📊 Merge into HTML
        working-directory: e2e
        run: npx playwright merge-reports --reporter html ${{ github.workspace }}/all-blob-reports
        
      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        with:
          name: html-report--attempt-${{ github.run_attempt }}
          path: e2e/playwright-report
          retention-days: 90
          overwrite: true
          
      - name: 🚚 Upload HTML report
        if: always()
        working-directory: e2e
        run: |
          REPORT_PATH="${{ env.S3_SUBFOLDER_NAME }}/${{ github.run_id }}-${{ github.run_attempt }}"
          aws s3 sync ./playwright-report s3://${{ inputs.hb_ui_bucket || env.DEFAULT_BUCKET_NAME }}/playwright/$REPORT_PATH
          aws s3 sync ./playwright-report s3://${{ inputs.hb_ui_bucket || env.DEFAULT_BUCKET_NAME }}/playwright/latest
          
      - name: Provide Report Link
        if: always()
        run: |
          echo "View report at: https://${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org/playwright/$REPORT_PATH/index.html"

  # SECURITY NOTE: Pull request E2E testing is now handled by separate workflows
  # - test-e2e-unprivileged.yml: Runs tests in unprivileged context for PRs
  # - test-e2e-privileged.yml: Processes results in privileged context
  security-notice:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Security Notice
        run: |
          echo "🔒 SECURITY NOTICE: Pull request E2E testing has been moved to separate workflows"
          echo "📝 For PR testing, use the 'Run E2E' or 'Run E2E Opensearch' labels"
          echo "🔄 This triggers the secure two-workflow pattern:"
          echo "   1. test-e2e-unprivileged.yml - Runs tests without secrets"
          echo "   2. test-e2e-privileged.yml - Processes results with secrets"
          echo "✅ This eliminates the 'untrusted code in privileged context' vulnerability"
