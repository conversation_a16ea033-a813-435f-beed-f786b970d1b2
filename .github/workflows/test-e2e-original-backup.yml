name: 🚀 Run E2E Tests with Sharding 🚀
run-name: E2E - ${{ github.event.inputs.env || 'dev' }}
 
on:
  schedule:
    - cron: '30 7 * * 1-5'
  workflow_dispatch:
    inputs:
      env:
        required: true
        type: string
        description: 'Target environment (e.g. dev, pr6888, etc)'
      opensearch:
        required: false
        type: boolean
        default: false
        description: 'Set OpenSearch feature flag ON for this run'
      testFolder:
        required: false
        type: string
        description: 'Comma-separated list of test folders to run (e.g. collections,assembly-units,asset-editor) or leave blank for all'
      ref:
        required: false
        type: string
        description: 'Git ref to test (optional, defaults to main)'
  pull_request:
    types: [labeled]
  workflow_call:
    inputs:
      ref:
        required: false
        type: string
        description: 'Git ref to test (optional)'
      env:
        required: true
        type: string
        description: 'Target environment (e.g. dev, pr6888, etc)'
      hb_ui_bucket:
        required: true
        type: string
        description: 'S3 bucket for UI artifacts'
 
concurrency:
  cancel-in-progress: ${{ github.event_name == 'workflow_call' }}
  group: e2e-${{ inputs.env || github.event.inputs.env }}-${{ github.event_name == 'workflow_call' && github.event_name || github.run_id }}
 
env:
  IS_EXTERNAL: true
  ENV: ${{ github.event.inputs.env || inputs.env || 'dev' }}
  IS_CICD_E2E: true
  DEFAULT_BUCKET_NAME: ${{ github.event.inputs.env || inputs.env || 'dev' }}-hummingbird-ui
  S3_SUBFOLDER_NAME: ${{ github.event.inputs.opensearch == 'true' && 'opensearch' || (github.event_name == 'workflow_dispatch' && 'manual' || (github.event_name == 'schedule' && 'cron' || 'deploy')) }}
 
jobs:
  trigger-from-label:
    if: github.event.label.name == 'Run E2E Opensearch'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.ref || inputs.ref || 'main' }}
 
 
      - name: Extract environment from PR number
        id: extract-env
        run: |
          ENV="pr${{ github.event.pull_request.number }}"
          echo "env=$ENV" >> $GITHUB_OUTPUT
          echo "Extracted environment: $ENV"
 
      - name: Dispatch Workflow with OpenSearch ON
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: '🚀 Run E2E Tests with Sharding 🚀'
          token: ${{ secrets.GITHUB_TOKEN }}
          ref: "main"  # Explicitly run workflow from main (latest)
          inputs: |
            {
              "env": "${{ steps.extract-env.outputs.env }}",
              "opensearch": "true",
              "ref": "${{ github.event.pull_request.head.ref }}"
            }
 
 
 
  run-e2e:
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1,2,3,4,5,6,7,8]
        shardTotal: [8]
    runs-on: icg-arc-runner-xlarge-set
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: 🛠️ Setup
        uses: ./.github/actions/setup-runner
        with:
          binaries: true
          install: true
      - name: 🌐 Curl Site
        run: |
          curl -k --retry 10 --retry-delay 30 --retry-connrefused --retry-all-errors https://external-${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org
      - name: 🎭 Playwright Setup
        working-directory: e2e
        run: |
          npx playwright install chrome
          echo "AWS_PROFILE=''" >> .env
      - name: 🧪 Run E2E
        env:
          AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE: 1
        run: |
          FOLDER_ARG=""
          if [[ -n "${{ github.event.inputs.testFolder }}" ]]; then
            FOLDERS=$(echo "${{ github.event.inputs.testFolder }}" | tr ',' ' ')
            for folder in $FOLDERS; do
              FOLDER_ARG+=" ui/$folder/**/*.test.ts"
            done
          fi
          if [[ "${{ github.event.inputs.opensearch }}" == "true" ]]; then
            pnpm --filter e2e ui --project=e2e-opensearch-on $FOLDER_ARG --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          else
            pnpm --filter e2e ui --project=ui --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
          fi
      - name: 📦 Upload blob report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: all-blob-reports-${{ matrix.shardIndex }}
          path: e2e/blob-report/
          retention-days: 5
          overwrite: true
 
  merge-reports:
    if: ${{ !cancelled() }}
    needs: [run-e2e]
    runs-on: icg-arc-runner-xlarge-set
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
      - name: 🛠️ Setup
        uses: ./.github/actions/setup-runner
        with:
          binaries: true
          install: true
      - name: 📥 Download blob reports
        uses: actions/download-artifact@v4
        with:
          path: ${{ github.workspace }}/all-blob-reports
          merge-multiple: true
      - name: 📊 Merge into HTML
        working-directory: e2e
        run: npx playwright merge-reports --reporter html ${{ github.workspace }}/all-blob-reports
      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        with:
          name: html-report--attempt-${{ github.run_attempt }}
          path: e2e/playwright-report
          retention-days: 90
          overwrite: true
      - name: 🚚 Upload HTML report
        if: always()
        working-directory: e2e
        run: |
          REPORT_PATH="${{ env.S3_SUBFOLDER_NAME }}/${{ github.run_id }}-${{ github.run_attempt }}"
          aws s3 sync ./playwright-report s3://${{ inputs.hb_ui_bucket || env.DEFAULT_BUCKET_NAME }}/playwright/$REPORT_PATH
          aws s3 sync ./playwright-report s3://${{ inputs.hb_ui_bucket || env.DEFAULT_BUCKET_NAME }}/playwright/latest
      - name: Provide Report Link
        if: always()
        run: |
          echo "View report at: https://${{ env.ENV }}-hummingbird.itemcloudgreen-nonprod.collegeboard.org/playwright/$REPORT_PATH/index.html"
 
 