#!/bin/bash

# Artifact Validation Script for E2E Test Results
# This script validates artifacts from the unprivileged workflow to ensure they are safe to process

set -euo pipefail

ARTIFACTS_DIR="${1:-downloaded-artifacts}"
OUTPUT_FILE="${2:-validation-results.txt}"

# Initialize validation results
echo "Starting artifact validation..." | tee "$OUTPUT_FILE"
VALIDATION_PASSED=true

# Function to log validation results
log_validation() {
    local status="$1"
    local message="$2"
    echo "[$status] $message" | tee -a "$OUTPUT_FILE"
    if [ "$status" = "FAIL" ]; then
        VALIDATION_PASSED=false
    fi
}

# Check if artifacts directory exists
if [ ! -d "$ARTIFACTS_DIR" ]; then
    log_validation "FAIL" "Artifacts directory not found: $ARTIFACTS_DIR"
    exit 1
fi

log_validation "PASS" "Artifacts directory found: $ARTIFACTS_DIR"

# Validate metadata files
METADATA_DIRS=($(find "$ARTIFACTS_DIR" -name "test-metadata-shard-*" -type d))

if [ ${#METADATA_DIRS[@]} -eq 0 ]; then
    log_validation "FAIL" "No metadata directories found"
    exit 1
fi

log_validation "PASS" "Found ${#METADATA_DIRS[@]} metadata directories"

# Use the first metadata directory for validation
METADATA_DIR="${METADATA_DIRS[0]}"
log_validation "INFO" "Using metadata from: $METADATA_DIR"

# Validate PR number
if [ -f "$METADATA_DIR/pr-number.txt" ]; then
    PR_NUMBER=$(cat "$METADATA_DIR/pr-number.txt")
    if [[ "$PR_NUMBER" =~ ^[0-9]+$ ]] && [ "$PR_NUMBER" -gt 0 ] && [ "$PR_NUMBER" -lt 100000 ]; then
        log_validation "PASS" "Valid PR number: $PR_NUMBER"
        echo "PR_NUMBER=$PR_NUMBER" >> "$OUTPUT_FILE"
    else
        log_validation "FAIL" "Invalid PR number format or range: $PR_NUMBER"
    fi
else
    log_validation "FAIL" "PR number file not found"
fi

# Validate head SHA
if [ -f "$METADATA_DIR/head-sha.txt" ]; then
    HEAD_SHA=$(cat "$METADATA_DIR/head-sha.txt")
    if [[ "$HEAD_SHA" =~ ^[a-f0-9]{40}$ ]]; then
        log_validation "PASS" "Valid head SHA: $HEAD_SHA"
        echo "HEAD_SHA=$HEAD_SHA" >> "$OUTPUT_FILE"
    else
        log_validation "FAIL" "Invalid head SHA format: $HEAD_SHA"
    fi
else
    log_validation "FAIL" "Head SHA file not found"
fi

# Validate OpenSearch flag
if [ -f "$METADATA_DIR/opensearch.txt" ]; then
    OPENSEARCH=$(cat "$METADATA_DIR/opensearch.txt")
    if [[ "$OPENSEARCH" =~ ^(true|false)$ ]]; then
        log_validation "PASS" "Valid OpenSearch flag: $OPENSEARCH"
        echo "OPENSEARCH=$OPENSEARCH" >> "$OUTPUT_FILE"
    else
        log_validation "FAIL" "Invalid OpenSearch flag: $OPENSEARCH"
    fi
else
    log_validation "WARN" "OpenSearch flag not found, defaulting to false"
    echo "OPENSEARCH=false" >> "$OUTPUT_FILE"
fi

# Validate shard information
if [ -f "$METADATA_DIR/shard-index.txt" ] && [ -f "$METADATA_DIR/shard-total.txt" ]; then
    SHARD_INDEX=$(cat "$METADATA_DIR/shard-index.txt")
    SHARD_TOTAL=$(cat "$METADATA_DIR/shard-total.txt")
    
    if [[ "$SHARD_INDEX" =~ ^[0-9]+$ ]] && [[ "$SHARD_TOTAL" =~ ^[0-9]+$ ]]; then
        if [ "$SHARD_INDEX" -ge 1 ] && [ "$SHARD_INDEX" -le "$SHARD_TOTAL" ] && [ "$SHARD_TOTAL" -le 20 ]; then
            log_validation "PASS" "Valid shard info: $SHARD_INDEX/$SHARD_TOTAL"
            echo "SHARD_INDEX=$SHARD_INDEX" >> "$OUTPUT_FILE"
            echo "SHARD_TOTAL=$SHARD_TOTAL" >> "$OUTPUT_FILE"
        else
            log_validation "FAIL" "Invalid shard range: $SHARD_INDEX/$SHARD_TOTAL"
        fi
    else
        log_validation "FAIL" "Invalid shard format: $SHARD_INDEX/$SHARD_TOTAL"
    fi
else
    log_validation "WARN" "Shard information not found"
fi

# Validate test result files
RESULT_DIRS=($(find "$ARTIFACTS_DIR" -name "e2e-results-shard-*" -type d))
log_validation "INFO" "Found ${#RESULT_DIRS[@]} result directories"

for result_dir in "${RESULT_DIRS[@]}"; do
    # Check for suspicious file types
    SUSPICIOUS_FILES=$(find "$result_dir" -type f \( -name "*.exe" -o -name "*.sh" -o -name "*.bat" -o -name "*.ps1" \) 2>/dev/null || true)
    if [ -n "$SUSPICIOUS_FILES" ]; then
        log_validation "FAIL" "Suspicious executable files found in $result_dir"
        echo "$SUSPICIOUS_FILES" | while read -r file; do
            log_validation "FAIL" "  - $file"
        done
    fi
    
    # Check file sizes (prevent extremely large files)
    LARGE_FILES=$(find "$result_dir" -type f -size +100M 2>/dev/null || true)
    if [ -n "$LARGE_FILES" ]; then
        log_validation "WARN" "Large files found in $result_dir (>100MB)"
        echo "$LARGE_FILES" | while read -r file; do
            log_validation "WARN" "  - $file ($(du -h "$file" | cut -f1))"
        done
    fi
done

# Check total artifact size
TOTAL_SIZE=$(du -sm "$ARTIFACTS_DIR" | cut -f1)
if [ "$TOTAL_SIZE" -gt 1000 ]; then  # 1GB limit
    log_validation "FAIL" "Total artifact size too large: ${TOTAL_SIZE}MB"
else
    log_validation "PASS" "Total artifact size acceptable: ${TOTAL_SIZE}MB"
fi

# Final validation result
if [ "$VALIDATION_PASSED" = true ]; then
    log_validation "PASS" "All validations passed"
    echo "VALIDATION_RESULT=PASS" >> "$OUTPUT_FILE"
    exit 0
else
    log_validation "FAIL" "Validation failed"
    echo "VALIDATION_RESULT=FAIL" >> "$OUTPUT_FILE"
    exit 1
fi
