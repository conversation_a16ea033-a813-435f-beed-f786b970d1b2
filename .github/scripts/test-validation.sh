#!/bin/bash

# Test script for artifact validation
# This script creates test artifacts and validates the validation script works correctly

set -euo pipefail

echo "🧪 Testing artifact validation script..."

# Create test directory
TEST_DIR="test-artifacts"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR"

# Test 1: Valid artifacts
echo "📝 Test 1: Valid artifacts"
mkdir -p "$TEST_DIR/test-metadata-shard-1"
echo "12345" > "$TEST_DIR/test-metadata-shard-1/pr-number.txt"
echo "abcdef1234567890abcdef1234567890abcdef12" > "$TEST_DIR/test-metadata-shard-1/head-sha.txt"
echo "true" > "$TEST_DIR/test-metadata-shard-1/opensearch.txt"
echo "1" > "$TEST_DIR/test-metadata-shard-1/shard-index.txt"
echo "8" > "$TEST_DIR/test-metadata-shard-1/shard-total.txt"

mkdir -p "$TEST_DIR/e2e-results-shard-1"
echo "test results" > "$TEST_DIR/e2e-results-shard-1/results.txt"

if .github/scripts/validate-artifacts.sh "$TEST_DIR" "test1-results.txt"; then
    echo "✅ Test 1 PASSED: Valid artifacts accepted"
else
    echo "❌ Test 1 FAILED: Valid artifacts rejected"
fi

# Test 2: Invalid PR number
echo "📝 Test 2: Invalid PR number"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/test-metadata-shard-1"
echo "invalid" > "$TEST_DIR/test-metadata-shard-1/pr-number.txt"
echo "abcdef1234567890abcdef1234567890abcdef12" > "$TEST_DIR/test-metadata-shard-1/head-sha.txt"

if .github/scripts/validate-artifacts.sh "$TEST_DIR" "test2-results.txt" 2>/dev/null; then
    echo "❌ Test 2 FAILED: Invalid PR number accepted"
else
    echo "✅ Test 2 PASSED: Invalid PR number rejected"
fi

# Test 3: Invalid SHA
echo "📝 Test 3: Invalid SHA"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/test-metadata-shard-1"
echo "12345" > "$TEST_DIR/test-metadata-shard-1/pr-number.txt"
echo "invalid-sha" > "$TEST_DIR/test-metadata-shard-1/head-sha.txt"

if .github/scripts/validate-artifacts.sh "$TEST_DIR" "test3-results.txt" 2>/dev/null; then
    echo "❌ Test 3 FAILED: Invalid SHA accepted"
else
    echo "✅ Test 3 PASSED: Invalid SHA rejected"
fi

# Test 4: Suspicious files
echo "📝 Test 4: Suspicious executable files"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/test-metadata-shard-1"
echo "12345" > "$TEST_DIR/test-metadata-shard-1/pr-number.txt"
echo "abcdef1234567890abcdef1234567890abcdef12" > "$TEST_DIR/test-metadata-shard-1/head-sha.txt"

mkdir -p "$TEST_DIR/e2e-results-shard-1"
echo "malicious script" > "$TEST_DIR/e2e-results-shard-1/malware.exe"

if .github/scripts/validate-artifacts.sh "$TEST_DIR" "test4-results.txt" 2>/dev/null; then
    echo "❌ Test 4 FAILED: Suspicious files accepted"
else
    echo "✅ Test 4 PASSED: Suspicious files rejected"
fi

# Test 5: Missing metadata
echo "📝 Test 5: Missing metadata"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/e2e-results-shard-1"
echo "test results" > "$TEST_DIR/e2e-results-shard-1/results.txt"

if .github/scripts/validate-artifacts.sh "$TEST_DIR" "test5-results.txt" 2>/dev/null; then
    echo "❌ Test 5 FAILED: Missing metadata accepted"
else
    echo "✅ Test 5 PASSED: Missing metadata rejected"
fi

# Cleanup
rm -rf "$TEST_DIR"
rm -f test*-results.txt

echo "🎉 Validation script testing completed!"
